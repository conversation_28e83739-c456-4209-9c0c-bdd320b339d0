import { CallH<PERSON><PERSON>, ExecutionContext, HttpStatus, Injectable, NestInterceptor } from '@nestjs/common';
import { ErrorCode, SystemException } from '@shared';
import { Observable } from 'rxjs';
import { ExaminationCoreService } from 'vac-nest-examination';
import { OMSService, OrderStatus } from 'vac-nest-oms';

@Injectable()
export class BlockDepositCancelAffiliateInterceptor implements NestInterceptor {
  constructor(
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly omsService: OMSService,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const isGetRequest = request.method === 'GET';
    const orderCode = request.body.orderCode; // Assuming orderCode is in the request body
    if (orderCode && !isGetRequest) {
      // Đơn hàng hoàn tất không cho hủy cọc
      const order = await this.omsService.getOneOrder(orderCode);
      if (order?.orderStatus === OrderStatus.Completed) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_AFFILIATE_BLOCK_CANCEL_ORDER_COMPLETED,
            message: ErrorCode.getError(ErrorCode.RSA_AFFILIATE_BLOCK_CANCEL_ORDER_COMPLETED),
            details: ErrorCode.getError(ErrorCode.RSA_AFFILIATE_BLOCK_CANCEL_ORDER_COMPLETED),
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Đơn hàng có phiếu khám không cho hủy cọc
      const resExam = await this.examinationCoreService.getTicketByOrderCode({
        orderCodes: [orderCode],
      });
      if (resExam?.items?.length > 0) {
        const message = ErrorCode.getError(ErrorCode.RSA_AFFILIATE_BLOCK_CANCEL_ORDER_DEPOSIT).replace(
          `{ticketCode}`,
          resExam?.items?.[0]?.ticketCode,
        );
        throw new SystemException(
          {
            code: ErrorCode.RSA_AFFILIATE_BLOCK_CANCEL_ORDER_DEPOSIT,
            message: message,
            details: message,
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }
    return next.handle();
  }
}
