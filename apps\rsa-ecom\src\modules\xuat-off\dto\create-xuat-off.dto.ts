import { ApiProperty, PickType } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsOptional } from 'class-validator';
import { XuatOffResponseDto } from './common.dto';
import { CreateXuatOffDetailDto } from './xuat-off-detail';

export class CreateXuatOffDto extends PickType(XuatOffResponseDto, [
  'orderCodeEcom',
  'orderEcomCreateDate',
  'orderEcomPushDate',
  'orderEcomCompleteDate',
  'images',
  'files',
  'notes',
  'reasonCode',
  'reasonName',
  'customerPhone',
  'customerName',
]) {
  @ApiProperty({ type: CreateXuatOffDetailDto, required: false, isArray: true })
  @IsArray()
  @IsOptional()
  @Expose()
  @Type(() => CreateXuatOffDetailDto)
  details?: CreateXuatOffDetailDto[];
}
