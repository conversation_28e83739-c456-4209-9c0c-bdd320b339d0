{"name": "rsa", "version": "1.0.6", "description": "BE RSA v2 service", "author": "quyen<PERSON>", "private": true, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build:env": "ts-node env-script.ts", "build:rsa": "rimraf dist && nest build rsa && tsc --build apps/rsa/tsconfig.app.build.json", "build:rsa-ecom": "rimraf dist && nest build rsa-ecom && tsc --build apps/rsa-ecom/tsconfig.app.build.json", "build:rsa-affiliate": "rimraf dist && nest build rsa-affiliate && tsc --build apps/rsa-affiliate/tsconfig.app.build.json", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:rsa": "nest start rsa --watch --debug", "start:rsa-ecom": "nest start rsa-ecom --watch --debug", "start:rsa-affiliate": "nest start rsa-affiliate --watch --debug", "start:rsa-ecom:dev": "nest start rsa-ecom --watch", "start:consumer:dev": "PORT=3001 nest start --config=consumer.nest-cli.json --watch", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate:rsa": "npm run typeorm -- migration:generate -d apps/rsa/src/config/migration.config.ts apps/rsa/src/migrations/$npm_config_name", "migration:generate:rsa-ecom": "npm run typeorm -- migration:generate -d apps/rsa-ecom/src/config/migration.config.ts apps/rsa-ecom/src/migrations/$npm_config_name", "migration:create:rsa": "npm run typeorm -- migration:create ./apps/rsa/src/migrations/$npm_config_name", "migration:create:rsa-ecom": "npm run typeorm -- migration:create ./apps/rsa-ecom/src/migrations/$npm_config_name", "migration:up:rsa": "npm run typeorm -- migration:run -d ./apps/rsa/src/config/migration.config.ts", "migration:up:rsa-affiliate": "npm run typeorm -- migration:run -d ./apps/rsa-affiliate/src/config/migration.config.ts", "migration:down:rsa-affiliate": "npm run typeorm -- migration:revert -d ./apps/rsa-affiliate/src/config/migration.config.ts", "migration:down:rsa": "npm run typeorm -- migration:revert -d ./apps/rsa/src/config/migration.config.ts", "migration:up:rsa-ecom": "npm run typeorm -- migration:run -d apps/rsa-ecom/src/config/migration.config.ts", "migration:down:rsa-ecom": "npm run typeorm -- migration:revert -d apps/rsa-ecom/src/config/migration.config.ts", "migration:up": "npm run typeorm -- migration:run -d dist/src/config/migration.config.js", "migration:down": "npm run typeorm -- migration:revert -d dist/config/migration.config.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "cross-env NODE_ENV=test jest --passWithNoTests", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/rsa/test/jest-e2e.json", "prepare": "is-ci || husky install"}, "dependencies": {"@elastic/elasticsearch": "^8.10.0", "@frt/nestjs-api": "^3.2.43", "@liaoliaots/nestjs-redis": "^9.0.5", "@liaoliaots/nestjs-redis-health": "^9.0.0", "@nestjs/axios": "^1.0.1", "@nestjs/common": "^9.0.5", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.5", "@nestjs/microservices": "^9.0.8", "@nestjs/platform-express": "^9.0.5", "@nestjs/swagger": "^6.0.4", "@nestjs/terminus": "^9.0.0", "@nestjs/typeorm": "^9.0.1", "@opentelemetry/auto-instrumentations-node": "^0.31.2", "@opentelemetry/exporter-trace-otlp-http": "^0.31.0", "@opentelemetry/propagator-b3": "^1.5.0", "@opentelemetry/resources": "^1.5.0", "@opentelemetry/sdk-node": "^0.31.0", "@opentelemetry/semantic-conventions": "^1.5.0", "amqp-connection-manager": "^4.1.5", "amqplib": "^0.10.2", "aws-sdk": "^2.1231.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "compression": "^1.7.4", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "elastic-apm-node": "^3.37.0", "exceljs": "^4.4.0", "helmet": "^5.1.1", "ict-nest-assign-job": "^1.0.25", "ict-nest-call-center": "^1.1.2", "ict-nest-inside": "^1.0.27", "ict-nest-s3": "^1.1.18", "ict-nest-user-role": "^1.1.7", "image-size": "^1.0.2", "ioredis": "^5.3.2", "jsonpath-plus": "^7.2.0", "jwt-decode": "^3.1.2", "lc-nest-http-client": "^1.1.6", "lc-nest-inside": "^1.1.1", "lc-nest-print-center": "^1.0.13", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "nest-winston": "^1.7.0", "pg": "^8.11.3", "qs": "^6.11.0", "randomstring": "^1.3.0", "reflect-metadata": "^0.1.13", "remove-accents": "^0.5.0", "request-ip": "^3.3.0", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "tsconfig-paths": "4.0.0", "typeorm": "^0.3.17", "unidecode": "^0.1.8", "uuid": "^9.0.0", "vac-commons": "2.1.43", "vac-integration": "^1.0.3", "vac-nest-affiliate": "^1.0.24", "vac-nest-assign-job": "^2.0.4", "vac-nest-call-center": "^1.0.0", "vac-nest-cart-app": "^3.2.4", "vac-nest-contract": "^1.0.19", "vac-nest-contract-integration": "^1.0.2", "vac-nest-customer-app": "^1.0.1", "vac-nest-customer-core": "^1.0.34", "vac-nest-dsms": "^1.2.4", "vac-nest-esm": "^1.7.10", "vac-nest-examination": "^3.7.10", "vac-nest-family": "^1.8.16", "vac-nest-family-package": "^1.1.3", "vac-nest-family-rule": "^0.2.0", "vac-nest-ftel": "^1.0.18", "vac-nest-history": "^1.4.8", "vac-nest-iam": "^1.1.0", "vac-nest-ims": "^1.0.19", "vac-nest-ims-booking": "^1.0.11", "vac-nest-inside": "^1.3.6", "vac-nest-inventory": "^1.0.16", "vac-nest-inventory-history": "^1.0.5", "vac-nest-invoice-app": "^1.1.3", "vac-nest-journey": "^2.3.5", "vac-nest-log-storage": "^1.0.7", "vac-nest-logger": "^1.0.44", "vac-nest-loyalty-app": "^1.1.0", "vac-nest-loyalty-rule": "^1.0.7", "vac-nest-monitor": "^1.1.2", "vac-nest-notification": "^1.0.3", "vac-nest-oms": "^2.4.5", "vac-nest-order-injection": "^2.1.6", "vac-nest-order-rule-engine": "^1.2.9", "vac-nest-osr": "^3.5.13", "vac-nest-payment-gateway": "^2.2.1", "vac-nest-payment-portal": "^1.0.14", "vac-nest-pim-app": "^1.3.0", "vac-nest-pos": "^1.0.1", "vac-nest-pricing": "^1.0.6", "vac-nest-print-center": "^1.3.8", "vac-nest-product": "^1.0.14", "vac-nest-product-item": "^1.1.2", "vac-nest-promotion": "^1.3.18", "vac-nest-regimen": "^2.3.10", "vac-nest-report": "^1.3.8", "vac-nest-return-home": "^1.1.0", "vac-nest-rsa-be-v2": "^1.1.1", "vac-nest-rsa-ecom": "^0.1.7", "vac-nest-rsa-integration": "^1.3.43", "vac-nest-rsa-report": "^1.0.6", "vac-nest-schedule": "^2.5.2", "vac-nest-schedule-engine-app": "^2.0.13", "vac-nest-search": "^1.2.32", "vac-nest-shop-management": "^1.7.10", "vac-nest-speaker": "^1.0.10", "vac-nest-storefont": "^2.2.20", "vac-nest-storefront": "^1.2.34", "vac-nest-supervisor-ai": "^2.0.1", "vac-nest-tcqg-integration": "^1.3.0", "vac-nest-telco": "^1.0.17", "vac-nest-vaccine-book": "^1.0.33", "vac-nest-vaccine-car": "^1.1.3", "vac-nest-voucher-core": "^1.1.2", "vac-order-promising": "^1.0.1", "winston": "^3.8.1", "winston-elasticsearch": "^0.17.1"}, "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.1", "@nestjs/testing": "^9.0.5", "@types/express": "^4.17.13", "@types/jest": "28.1.6", "@types/multer": "^1.4.7", "@types/node": "^18.6.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.31.0", "dotenv": "^16.0.1", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "is-ci": "^3.0.1", "jest": "28.1.3", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "run-script-webpack-plugin": "^0.1.1", "source-map-support": "^0.5.21", "supertest": "^6.2.4", "ts-jest": "28.0.7", "ts-loader": "^9.3.1", "ts-node": "^10.9.1", "typescript": "^4.9.5", "webpack": "^5.73.0", "webpack-node-externals": "^3.0.0"}, "lint-staged": {"*.{ts,js}": ["npm run lint", "npm run format"]}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["modules/**/*.(t|j)s"], "coverageThreshold": {"global": {"lines": 90}}, "detectOpenHandles": true, "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"@shared": "<rootDir>/libs/shared/src/index.ts", "@shared/(|/.*)$": "<rootDir>/libs/shared/src/$1", "@libs/(|/.*)$": "<rootDir>/libs/modules/src/$1", "@libs/modules/customers/enum": "<rootDir>/libs/modules/src/modules/customers/enum/index.ts", "^shared/shared(|/.*)$": "<rootDir>/libs/shared/src/$1", "^modules/modules(|/.*)$": "<rootDir>/libs/modules/src/$1", "apps/rsa-ecom/src/modules/shifts/constants/shift.constants": "<rootDir>/apps/rsa-ecom/src/modules/shifts/constants/shift.constants", "apps/rsa-ecom/src/modules/schedule-requests/enums/schedule-request-type.enum": "<rootDir>/apps/rsa-ecom/src/modules/schedule-requests/enums/schedule-request-type.enum", "apps/rsa-ecom/(|/.*)$": "<rootDir>/apps/rsa-ecom/$1", "apps/rsa-ecom/src/modules/schedule-requests/services/schedule-requests.service": "<rootDir>apps/rsa-ecom/src/modules/schedule-requests/services/schedule-requests.service"}}}