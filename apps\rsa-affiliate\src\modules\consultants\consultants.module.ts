import { CartAppModule } from 'vac-nest-cart-app';
import { FamilyModule } from 'vac-nest-family';
import { JourneyModule } from 'vac-nest-journey';
import { VacOrderInjectionModule } from 'vac-nest-order-injection';
import { OrderRuleEngineModule } from 'vac-nest-order-rule-engine';
import { PIMAppModule } from 'vac-nest-pim-app';
import { RegimenModule } from 'vac-nest-regimen';
import { ReturnHomeModule } from 'vac-nest-return-home';

import { CustomersModule } from '@libs/modules/customers/customers.module';
import { RegimensModule } from '@libs/modules/regimens/regimens.module';
import { Module } from '@nestjs/common';

import { ConsultantsController } from './controllers/consultants.controller';
import { ConsultantsService } from './services/consultants.service';
import { ConsultantsModule as ConsultantsLibModule } from '@libs/modules/consultants/consultants.module';

@Module({
  imports: [
    ConsultantsLibModule,
    CartAppModule,
    JourneyModule,
    PIMAppModule,
    RegimenModule,
    FamilyModule,
    CustomersModule,
    RegimensModule,
    OrderRuleEngineModule,
    VacOrderInjectionModule,
    ReturnHomeModule,
  ],
  controllers: [ConsultantsController],
  providers: [ConsultantsService],
})
export class ConsultantsModule {}
