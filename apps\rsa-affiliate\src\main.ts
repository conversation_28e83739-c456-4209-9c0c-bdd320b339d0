import { RequestMethod, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { API_PREFIX, sdk as tracer } from '@shared';
import { json } from 'body-parser';
import { AppModule } from './app.module';

import compression from 'compression';
import Helmet from 'helmet';

import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { setupSwagger } from './swagger';

import RequestIP from 'request-ip';
import { SWAGGER_API_ENDPOINT } from './swagger/swagger.constant';

async function bootstrap() {
  await tracer.start();
  const app = await NestFactory.create(AppModule);
  app.enableVersioning({
    type: VersioningType.URI,
  });
  app.setGlobalPrefix(API_PREFIX, {
    exclude: [{ path: 'health', method: RequestMethod.GET }],
  });
  setupSwagger(app);
  const configService = app.get(ConfigService);
  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));
  app.use(Helmet());
  // app.enableCors();
  app.use(compression());
  app.use(RequestIP.mw());
  app.use(json({ limit: '50mb' }));
  await app.listen(configService.get<number>('PORT'));
  console.log(`http://127.0.0.1:${configService.get<number>('PORT')}${SWAGGER_API_ENDPOINT}`);
  app.enableShutdownHooks();
}

bootstrap().catch((error) => {
  console.log(error);
});
