import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional, IsString } from 'class-validator';
import { OrderTypeXuatOff } from '../constants';

export class XuatOffStepInfoResponseDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty({ nullable: true })
  @Expose()
  xoId?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  xoCode?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  employeeCode?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  employeeName?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  step?: number;

  @ApiProperty({ nullable: true })
  @Expose()
  notes?: string;

  @ApiProperty()
  @Expose()
  createdDate: Date;

  @ApiProperty()
  @Expose()
  modifiedDate: Date;
}

export class XuatOffDetailResponseDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty({ nullable: true })
  @Expose()
  xoId?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  xoCode?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  orderCodeShop?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  @Expose()
  orderShopCreateDate?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  @Expose()
  orderShopCompleteDate?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  customerPhone?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  customerName?: string;

  @ApiProperty({ enum: OrderTypeXuatOff, required: false })
  @IsOptional()
  @Expose()
  @IsEnum(OrderTypeXuatOff)
  type?: OrderTypeXuatOff;

  @ApiProperty()
  @Expose()
  isDelete?: boolean;

  @ApiProperty({ nullable: true })
  @Expose()
  status?: number;

  @ApiProperty({ nullable: true })
  @Expose()
  reasonCancel?: string;

  @ApiProperty()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  createdByName?: string;

  @ApiProperty()
  @Expose()
  createdDate: Date;

  @ApiProperty({ nullable: true })
  @Expose()
  modifiedBy?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  modifiedByName?: string;

  @ApiProperty()
  @Expose()
  modifiedDate: Date;
}

export class XuatOffResponseDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty({ nullable: true })
  @Expose()
  xoCode?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  orderCodeEcom?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  @Expose()
  orderEcomCreateDate?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  @Expose()
  orderEcomPushDate?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  @Expose()
  orderEcomCompleteDate?: Date;

  @ApiProperty({ type: [String], nullable: true })
  @Expose()
  images?: string[];

  @ApiProperty({ nullable: true })
  @Expose()
  files?: string[];

  @ApiProperty({ nullable: true })
  @Expose()
  notes?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  reasonCode?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  reasonName?: string;

  @ApiProperty()
  @Expose()
  status?: number;

  @ApiProperty()
  @Expose()
  type?: number;

  @ApiProperty()
  @Expose()
  isDelete?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  customerPhone?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  customerName?: string;

  @ApiProperty()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  createdByName?: string;

  @ApiProperty()
  @Expose()
  createdDate: Date;

  @ApiProperty({ nullable: true })
  @Expose()
  modifiedBy?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  modifiedByName?: string;

  @ApiProperty()
  @Expose()
  modifiedDate: Date;

  @ApiProperty({ nullable: true })
  @Expose()
  orderEcomCreatedBy?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  orderEcomCreatedByName?: string;

  @ApiProperty({ type: [XuatOffDetailResponseDto] })
  @Expose()
  @IsOptional()
  @Type(() => XuatOffDetailResponseDto)
  details?: XuatOffDetailResponseDto[];

  @ApiProperty({ type: [XuatOffStepInfoResponseDto] })
  @Expose()
  @IsOptional()
  @Type(() => XuatOffStepInfoResponseDto)
  steps?: XuatOffStepInfoResponseDto[];
}
