import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsDate, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { GetEmployeeForFundClosure } from 'vac-nest-inside';

export class GetListEmployeeShiftDateDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  shopCode: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  shiftDate: string;

  @ApiProperty({ required: false })
  @IsOptional()
  jobTitleCode?: string[];
}

export class GetListEmployeeShiftDateRes {
  @ApiProperty()
  @IsString()
  shopCode: string;

  @ApiProperty()
  @IsString()
  employeeCode: string;

  @ApiProperty()
  @IsString()
  employeeName: string;

  @ApiProperty()
  @IsString()
  jobTitleCode: string;

  @ApiProperty()
  @IsString()
  jobTitleName: string;

  @ApiProperty()
  @IsString()
  phone: string;

  @ApiProperty()
  @IsDate()
  shiftStartTime: Date;

  @ApiProperty()
  @IsDate()
  shiftEndTime: Date;

  @ApiProperty()
  @IsString()
  email: string;
}

export class GetEmployeeWorkingInfoDto {
  // đồng bộ payload với api cũ bên rsa
  @ApiProperty({ required: true })
  @IsString()
  shopCode: string;
}
