/* eslint-disable prefer-const */
import { HttpStatus, Inject, Injectable, LoggerService } from '@nestjs/common';
import {
  CreateHPLLinkDto,
  CreateMomoLLinkDto,
  CreateVnpayLinkDto,
  CreateZalopayLinkDto,
  genSecureHash,
  HOME_PAY_LATER_PAYMENT_TYPE,
  PaymentMethod,
  PaymentPortalService,
  VoucherGenPaymentLinkLibDto,
} from 'vac-nest-payment-portal';
import { SendSmsPaymentDto } from '../dto/send-sms-payment-link.dto';
import { GetOneOrderLibResponse } from 'vac-nest-oms';
import { PaymentGatewayService } from 'vac-nest-payment-gateway';
import { NotificationService, ShortLinkService } from 'vac-nest-notification';
import { omit } from 'lodash';
import { ErrorCode, IS_TEMP_LOGGING, SystemException, VACCINE_PAYMENT_REQUEST_TYPE } from '@shared';
import { fulfillString } from '../utils/payment-link.util';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { WorkerService } from '../../worker/worker.service';
import { InjectRepository } from '@nestjs/typeorm';
import { PaymentMetaData } from '../entities/payment-meta-data.entity';
import { Repository } from 'typeorm';
import { getDateTimeZone } from '@shared/utilities/function-time-zone';
import { ConfigService } from '@nestjs/config';
import moment from 'moment';

@Injectable()
export class PaymentLinkService {
  constructor(
    private readonly paymentPortalService: PaymentPortalService,
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly notificationService: NotificationService,
    private readonly shortLinkService: ShortLinkService,
    private readonly workerService: WorkerService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    @InjectRepository(PaymentMetaData)
    private readonly paymentMetaData: Repository<PaymentMetaData>,
    private readonly configService: ConfigService,
  ) {}
  async genVnpayLink(
    paymentRequestCode: string,
    accountId: string,
    data: SendSmsPaymentDto,
    orderData: GetOneOrderLibResponse,
    createdBy?: string,
  ) {
    const payloadCreateLink: CreateVnpayLinkDto = {
      TerminalCode: process.env.VNPAY_TERMINAL_CODE,
      PaymentCode: orderData?.paymentRequestCode,
      Amount: data.amount,
      OrderCode: orderData?.orderCode,
      AccountId: accountId,
      ShopCode: orderData.shopCode,
      Vouchers: data?.vouchers,
      PromotionCode: data?.promotionCode,
      PaymentRequestType: VACCINE_PAYMENT_REQUEST_TYPE,
      CreatedBy: createdBy,
      BuyerPhone: orderData.phone ?? '',
    };

    const paymentLink = await this.paymentPortalService.createVnpayLink(payloadCreateLink);
    return paymentLink;
  }

  async getDataForPaymentLinkGenerate({
    custCode,
    orderCode,
    totalBill,
    paymentRequestCode,
  }: GetOneOrderLibResponse): Promise<{
    accountId: string;
    paymentRequestCode: string;
  }> {
    console.log('[ LOG ]', orderCode, totalBill, paymentRequestCode);
    const [customerIdRes, { paymentRequestCode: paymentRequestCodeRes }] = await Promise.all([
      this.getAccountIdFromCustomerId(custCode),
      { paymentRequestCode: 'HARD_PAYMENT_CODE' },
    ]);

    return { accountId: customerIdRes, paymentRequestCode: paymentRequestCodeRes };
  }

  async getAccountIdFromCustomerId(customerId: string): Promise<string> {
    let accountPaymentId: string = '';
    // CHECK account
    const getAccData = await this.paymentGatewayService.getAccountBalance(customerId, true);

    if (!getAccData?.id) {
      const createAccount = await this.paymentGatewayService.createAccount({
        currentBalance: 0,
        customerId: customerId,
        createdBy: '',
      });
      accountPaymentId = createAccount.id;
    } else {
      accountPaymentId = getAccData?.id;
    }

    return accountPaymentId;
  }

  async sendSMSPaymentLinkToCustomer({
    paymentLink,
    phone,
    orderCode,
    insideId,
    isCreateShortLink = true,
    paymentType,
    orderData,
    partnerId,
  }: {
    paymentLink: any;
    phone: any;
    orderCode: string;
    insideId: string;
    isCreateShortLink?: boolean;
    paymentType?: PaymentMethod;
    orderData;
    partnerId?: number;
  }) {
    let shortLink: string = '';
    let expireInMinutes = this.configService.get<string>('PAYMENT_LINK_EXPIRE_IN_MINUTES')
      ? Number(this.configService.get<string>('PAYMENT_LINK_EXPIRE_IN_MINUTES'))
      : 15;

    if (paymentType === PaymentMethod.TRANSFER) {
      // link chuyển khoản sẽ hết hạn sau 24h
      expireInMinutes = Number(this.configService.get('PAYMENT_LINK_TRANSFER_EXPIRE_IN_MINUTES'));
    }

    if (isCreateShortLink) {
      const shortLinkResponse = await this.shortLinkService.createShortLink(
        {
          originalUrl: paymentLink,
          userId: insideId,
          expires: moment().utcOffset('+07:00').add(expireInMinutes, 'minutes').format(),
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: process.env.SMS_AUTHEN_BASIC,
          },
        },
      );
      shortLink = shortLinkResponse?.shortLink;
    }

    let templateId = process.env.NOTIFICATION_TEMPLATEID_SMS_PAYMENT_TRANSFER;
    if (paymentType) {
      switch (paymentType) {
        case PaymentMethod.ZALO_PAY:
        case PaymentMethod.VNPAY:
        case PaymentMethod.MOMO:
        case PaymentMethod.ALEPAY:
        case PaymentMethod.WALLET:
          templateId = process.env.NOTIFICATION_TEMPLATEID_SMS_PAYMENT_EWALLET;
          break;
        case PaymentMethod.TRANSFER:
          templateId = process.env.NOTIFICATION_TEMPLATEID_SMS_PAYMENT_TRANSFER;
          break;
        default:
          templateId = process.env.NOTIFICATION_TEMPLATEID_SMS_PAYMENT_TRANSFER;
          break;
      }
    }

    const sendNoti = await this.notificationService.sendNotification({
      FromSys: process.env.APP_NAME, //Hệ thống gửi
      Sender: process.env.APP_NAME, //Tên người gửi
      Messages: [
        {
          TemplateId: templateId, //Tùy vào từng môi trường sẽ có templateId khác nhau
          To: [phone],
          Param: {
            Title: {},
            Content: {
              paymentUrl: shortLink || paymentLink,
              orderCode: orderCode?.slice(-7),
            },
            ContentFailOver: {},
            ExtraProperties: {},
          },
        },
      ],
    });

    await this.workerService.storePaymentCode({
      orderCode: orderData.orderCode,
      paymentCode: orderData.paymentRequestCode,
      insideId: orderData.createdBy,
      name: orderData.custName,
      phone: orderData.phone,
      address: orderData.custAddress,
      // date: orderData.createdDate,
    });

    await this.saveLink({
      orderCode,
      partnerId: partnerId || -1,
      shortLink,
    });

    const paymentLinkStatus = await this.checkPaymentLink(orderCode);

    return {
      ...sendNoti,
      shortLink: shortLink,
      originLink: paymentLink,
      remainingTime: paymentLinkStatus?.remainingTime,
      remainingTimeInMinutes: paymentLinkStatus?.remainingTimeInMinutes,
      status: paymentLinkStatus?.status,
    };
  }

  async genAlepayLink(
    paymentRequestCode: string,
    accountId: string,
    data: SendSmsPaymentDto,
    orderData: GetOneOrderLibResponse,
    createdBy?: string,
  ) {
    const payloadCreateLink: any = {
      AccountId: accountId,
      Amount: data.amount,
      BuyerAddress:
        typeof orderData?.custAddress === 'string' && orderData?.custAddress?.length > 0
          ? orderData?.custAddress
          : 'fptshop',
      BuyerCity: 'ho-chi-minh',
      BuyerEmail: '<EMAIL>',
      BuyerName: orderData.custName ?? '',
      BuyerPhone: orderData.phone ?? '',
      OrderCode: orderData.orderCode,
      PaymentCode: orderData.paymentRequestCode,
      PaymentHours: 0.25,
      ShopCode: orderData.shopCode,
      TerminalCode: process.env.ALEPAY_TERMINAL_CODE,
      TotalItem: orderData?.details?.length,
      Vouchers: data?.vouchers,
      PromotionCode: data?.promotionCode,
      BankCode: data?.bankCode,
      PaymentMethod: data?.paymentMethod,
      PaymentRequestType: VACCINE_PAYMENT_REQUEST_TYPE,
      CreatedBy: createdBy,
      // PaymentRequestCode: paymentRequestCode,
    };
    const paymentLink = await this.paymentPortalService.createAlepayLink(payloadCreateLink);
    return paymentLink;
  }

  async genHPLLink(
    paymentRequestCode: string,
    accountId: string,
    data: SendSmsPaymentDto,
    orderData: GetOneOrderLibResponse,
  ): Promise<string> {
    try {
      const payloadCreateLink: CreateHPLLinkDto = {
        vouchers: this.getVoucherString(data?.vouchers),
        paymentCode: orderData?.paymentRequestCode,
        amount: data.amount,
        phone: orderData.phone,
        orderCode: orderData?.orderCode,
        shopCode: orderData.shopCode,
        terminalCode: process.env.HPL_TERMINAL_CODE,
        secureHash: '',
        accountId: accountId,
        typePayment: HOME_PAY_LATER_PAYMENT_TYPE.VACCINE,
        extensionItem: {
          sellers: [
            {
              code: orderData.shopCode || 'vaccine',
              name: orderData.shopName || 'vaccine',
              address: {
                fullAddress: orderData?.shopName || '',
              },
            },
          ],
          orderDetails: {
            goodsAmount: {
              amount: data.amount,
              currency: 'VND',
            },
            items: orderData?.details
              ?.filter((e) => e?.isPromotion !== 'Y')
              ?.map((e) => ({
                itemCode: e.itemCode,
                itemName: e.itemName,
                commodityType: '',
                manufacturer: '',
                unitPrice: {
                  amount: e?.totalBill,
                  currency: 'VND',
                },
              })),
            discountAmount: {
              amount: 0,
              currency: 'VND',
            },
            deliveryDetails: {
              deliveryDate: '',
              address: {
                addressType: 'Shop',
                fullAddress: orderData?.shopName,
              },
            },
            discountSchemes: data?.promotionCode
              ? [
                  {
                    code: data.promotionCode,
                    name: 'Khuyến mãi',
                    discountAmount: 0,
                  },
                ]
              : [],
          },
          customerdata: {
            fullname: orderData?.custName,
            addresses: [
              {
                fullAddress: orderData?.custAddress ?? '',
                receiverPhoneNumber: orderData?.phone,
              },
            ],
          },
          other: {
            orderCreatedFrom: 'Online',
          },
        },
      };

      let genSecureHashResult = this.genSecureHash(omit(payloadCreateLink, ['SecureHash']), orderData?.orderCode);
      payloadCreateLink.secureHash = genSecureHashResult?.encryptedText;
      const paymentLink = await this.paymentPortalService.createHPLLink(payloadCreateLink);
      if (!paymentLink) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_GEN_LINK_FAIL,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      return paymentLink;
    } catch (error) {
      throw error;
    }
  }

  async genZalopayLink(
    paymentRequestCode: string,
    accountId: string,
    data: SendSmsPaymentDto,
    orderData: GetOneOrderLibResponse,
    createdBy?: string,
  ) {
    try {
      const payloadCreateLink: CreateZalopayLinkDto = {
        TmnCode: process.env.ZALO_TERMINAL_CODE,
        PaymentCode: orderData?.paymentRequestCode,
        Amount: data.amount,
        OrderCode: orderData?.orderCode,
        AccountId: accountId,
        ShopCode: orderData.shopCode,
        Vouchers: this.getVoucherString(data?.vouchers),
        BankCode: '',
        DeviceInfo: '192.168.1.1',
        SecureHash: '',
        PromotionCode: data.promotionCode,
        PaymentRequestType: VACCINE_PAYMENT_REQUEST_TYPE,
        CreatedBy: createdBy,
        BuyerPhone: orderData.phone ?? '',
      };

      let genSecureHashResult = this.genSecureHash(
        omit(payloadCreateLink, ['SecureHash', 'BankCode']),
        orderData?.orderCode,
      );
      payloadCreateLink.SecureHash = genSecureHashResult?.encryptedText;
      const paymentLink = await this.paymentPortalService.createZalopayLink(payloadCreateLink);
      if (!paymentLink) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_GEN_LINK_FAIL,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      return paymentLink;
    } catch (error) {
      throw error;
    }
  }

  async genMomoLink(
    paymentRequestCode: string,
    accountId: string,
    data: SendSmsPaymentDto,
    orderData: GetOneOrderLibResponse,
    createdBy?: string,
  ) {
    try {
      const payloadCreateLink: CreateMomoLLinkDto = {
        vouchers: this.getVoucherString(data?.vouchers),
        shopCode: orderData.shopCode,
        paymentCode: orderData?.paymentRequestCode,
        accountId,
        terminalCode: process.env.MOMO_TERMINAL_CODE,
        amount: data.amount,
        bankCode: '',
        orderCode: orderData.orderCode,
        ipAddress: '**********',
        redirectURL: null,
        storeId: 'web',
        guaranteeAmount: null,
        guaranteeVendorCode: null,
        createdBy,
        paymentRequestType: VACCINE_PAYMENT_REQUEST_TYPE,
        BuyerPhone: orderData.phone ?? '',
      };

      let genSecureHashResult = this.genSecureHash(
        omit(payloadCreateLink, ['secureHash', 'ipAddress']),
        orderData?.orderCode,
      );
      payloadCreateLink.secureHash = genSecureHashResult?.encryptedText;
      const paymentLink = await this.paymentPortalService.createMomoLink(payloadCreateLink);
      if (!paymentLink) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_GEN_LINK_FAIL,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      return paymentLink;
    } catch (error) {
      throw error;
    }
  }

  async genTransferLink(orderData: GetOneOrderLibResponse) {
    try {
      const paymentLink = fulfillString(process.env.TIEM_CHUNG_WEB_URL + 'ttdh/vaccine/{orderCode}', {
        orderCode: orderData.orderCode,
      });
      if (!paymentLink) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_GEN_LINK_FAIL,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      return paymentLink;
    } catch (error) {
      throw error;
    }
  }

  private getVoucherString(vouchers: VoucherGenPaymentLinkLibDto[]): string {
    return vouchers?.map((e) => `${e.voucherCode}:${e.amount}:${e.type}`).join(',') || null;
  }

  private genSecureHash(params: any, orderCode?: string) {
    const data = genSecureHash(params);
    if (IS_TEMP_LOGGING) {
      let jsonString = '';
      let paramsString = '';
      try {
        jsonString = JSON.stringify(data);
        paramsString = JSON.stringify(params);
      } catch (error) {}
      this.logger.log(
        {
          message: `genSecureHash for order: ${orderCode || ''}`,
          fields: {
            info: `genSecureHash: ${jsonString}`,
            method: `GET`,
            url: `genSecureHash for order: ${orderCode || ''}`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: paramsString,
            headers: '{}',
            dataRes: jsonString,
          },
        },
        false,
      );
    }

    return data;
  }

  async saveLink(payload: { orderCode: string; partnerId: number; shortLink: string }) {
    const { orderCode, partnerId, shortLink } = payload;
    if (
      !(await this.paymentMetaData.exist({
        where: {
          orderCode,
          shortLink,
        },
      }))
    ) {
      const paymentMetaData = this.paymentMetaData.create({
        orderCode,
        shortLink,
        partnerId,
        createdAt: getDateTimeZone(),
        updatedAt: getDateTimeZone(),
      });
      await this.paymentMetaData.save(paymentMetaData);
    }

    return true;
  }

  async checkPaymentLink(orderCode: string) {
    const maxTimeInMinutes = this.configService.get('PAYMENT_LINK_EXPIRE_IN_MINUTES') || '15';
    const maxTimeInMiliseconds = Number(maxTimeInMinutes) * 60 * 1000;
    const now = moment();
    const paymentLinks = await this.paymentMetaData.find({
      where: {
        orderCode,
      },
    });

    if (paymentLinks.length <= 0) {
      return null;
    }

    //sort payment link by createdDate desc
    paymentLinks.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    const remainingTime = maxTimeInMiliseconds - now.diff(moment(paymentLinks[0].createdAt), 'milliseconds');
    const remainingTimeInMinutes = Math.ceil(remainingTime / 60000);
    return {
      ...paymentLinks[0],
      remainingTime: remainingTime > 0 ? remainingTime : 0,
      remainingTimeInMinutes: remainingTimeInMinutes > 0 ? remainingTimeInMinutes : 0,
      status: remainingTime > 0 ? 1 : 2,
    };
  }
}
