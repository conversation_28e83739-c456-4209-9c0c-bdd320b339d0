import { HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import {
  CART_CONFIRM_REDIS_KEY,
  ErrorCode,
  ITEM_VACCINE_SO1,
  OrderChannels,
  RedisService,
  SystemException,
  TICKET_CREATE_KEY,
  isSameDate,
  parseJson,
  OrderChanel,
} from '@shared';
import { RSA_ECOM_HARD_DEFAULT_SHOP_CODE } from '@shared/common/hard-code';
import { Channel, ChannelType, SystemBook, TransType } from '@shared/enum';
import { ScheduleRequestsService } from 'apps/rsa-ecom/src/modules/schedule-requests/services/schedule-requests.service';
import { plainToInstance } from 'class-transformer';
import { Request } from 'express';
import { JSONPath } from 'jsonpath-plus';
import * as _ from 'lodash';
import moment from 'moment';
import {
  AddCartConfirmLibDto,
  AddCartConfirmLibResponse,
  CalculatorPriceInfoLib,
  CartAppService,
  CartItemForCartConfirm,
  GetCartLibResponse,
  ItemType,
  ListCartSelectedLib,
  LoyaltyLib,
  OrderAttribute,
  ResPromotionCartTotal,
  Voucher,
  VoucherGenerateCart,
  VoucherResponse,
  VoucherType,
} from 'vac-nest-cart-app';
import { WHS_CODE_NORMAL } from 'vac-nest-customer-core';
import {
  CreateTicketDto,
  CreateTicketNoteDto,
  EnmAssignRule,
  EnmClinicType,
  EnmScheduleStatus,
  EnmStatusTicket,
  ExaminationCoreService,
  HealthCheckQuestion,
  PaymentType,
  ScheduleDto,
  TicketDetailAdjustDto,
  TicketDetailRes,
} from 'vac-nest-examination';
import { FamilyService } from 'vac-nest-family';
import { VacHistoryService } from 'vac-nest-history';
import { IMSService, whsCodeWithShopCodeWhsType } from 'vac-nest-ims';
import {
  BookingRequestDetail,
  CancelBookParams,
  CreateCheckAvailableBody,
  IMSBookingService,
} from 'vac-nest-ims-booking';
import { LoyaltyAppService } from 'vac-nest-loyalty-app';
import { System } from 'vac-nest-loyalty-rule';
import { NotificationService } from 'vac-nest-notification';
import {
  CreateOrderDto,
  CreateOrderRes,
  DetailAttachment,
  DetailLib,
  EcomDisplay,
  EmployeeStep,
  GetListOrderESLibResponse,
  GetOneOrderLibResponse,
  OMSService,
  OrderPaymentCreate,
  OrderProperty,
  OrderStatus,
  OrderType,
  PayloadUpdatedStatusOrderDto,
  UpdateOrderLibDto,
  transformPlaceOrder,
} from 'vac-nest-oms';
import {
  CreateRegimenDetailCloseDto,
  UpdateStatusByAttachmentCodeDto,
  VacOrderInjectionService,
} from 'vac-nest-order-injection';
import {
  CashTransactionTypeId,
  CreateAdjustPaymentRes,
  DepositAllLibDto,
  GetPaymentHistoryESLibResponse,
  PaymentGatewayService,
  PaymentOnlineStatus,
  PaymentRequestType,
  PaymentSourceType,
  TypePayment,
  VoucherDetail,
  VoucherPartnerId,
  getDepositDetailAmountRealAmount,
  getDepositedAmountByMethods,
} from 'vac-nest-payment-gateway';
import { PIMAppService } from 'vac-nest-pim-app';
import { IError } from 'vac-nest-pricing';
import {
  CheckPromotionResponse,
  ConfirmPromotionDto,
  ItemConfirmDto,
  PromotionResponse,
  PromotionService,
} from 'vac-nest-promotion';
import { RsaEcomService } from 'vac-nest-rsa-ecom';
import {
  BookVoucherDto,
  CalculatePriceDto,
  CancelVoucherByPaymentCodeDto,
  GenerateVoucherDto,
  TypeVoucherForPromotion,
  UnbookVoucherByPaymentCodeDto,
  VerifyVoucherDto,
  VerifyVoucherOrderItemLibDto,
  VerifyVoucherResponse,
  VoucherCoreService,
} from 'vac-nest-voucher-core';
import { ATTR_OTP } from '../../customers/enum';
import { NOTE_TYPE, ORDER_ATTRIBUTE } from '../constants';
import { CreatePaymentAndDepositAllDto, PaymentInfoForClient, PlaceOrderDto } from '../dto';
import { JourneyService, OrdersInfo } from 'vac-nest-journey';
import { RegimenItem, RegimenService } from 'vac-nest-regimen';
import { SystemExceptionV2 } from '@shared/common/exceptions/system.exception-v2';
import { ErrorCodeV2 } from '@shared/common/errors/error-code-v2';
@Injectable()
export class OrderUtilsService {
  constructor(
    private readonly paymentGWService: PaymentGatewayService,
    private readonly pimAppService: PIMAppService,
    private readonly omsService: OMSService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly imsService: IMSService,
    private readonly vacOrderInjectionService: VacOrderInjectionService,
    private readonly voucherCoreService: VoucherCoreService,
    private readonly cartAppService: CartAppService,
    private readonly promotionService: PromotionService,
    private readonly notificationService: NotificationService,
    private readonly vacHistoryService: VacHistoryService,
    private readonly imsBookingService: IMSBookingService,
    private readonly loyaltyAppService: LoyaltyAppService,
    private readonly rsaEcomService: RsaEcomService,
    private readonly scheduleRequestsService: ScheduleRequestsService,
    private readonly redisService: RedisService,

    private readonly regimenService: RegimenService,

    private readonly familyCoreService: FamilyService,
    private readonly journeyService: JourneyService,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  async checkRuleLoyaltyGreaterThanTotalOrder(getCart: GetCartLibResponse, placeOrderDto: PlaceOrderDto) {
    const { vouchers } = getCart;
    const voucherLoyalty = getCart?.vouchers?.find(
      (voucherItem) => voucherItem?.channel?.typeCode === ChannelType.Loyalty,
    );
    if (!voucherLoyalty) return { isRemoveLoyalty: false, cartNew: getCart };
    // Có loyalty nhưng tiền voucher lớn hơn maxVoucherAmount => remove loyalty
    if (getCart?.loyalty?.estimateExchange?.maxVoucherAmount < voucherLoyalty?.amount) {
      const cartNew = await this.cartAppService.addVoucher({
        ...placeOrderDto,
        vouchers: vouchers.filter((e) => e?.channel.typeCode !== ChannelType.Loyalty),
        otp: '',
      });
      return { isRemoveLoyalty: true, cartNew };
    }

    if (voucherLoyalty?.amount > getCart?.calculatorPriceInfo?.totalBill) {
      /**
       * @TODO
       *  - Xoá voucher trong cart
       *  - Huỷ loyalty
       *  - Huỷ voucher loyalty
       */
      const cartNew = await this.cartAppService.addVoucher({
        ...placeOrderDto,
        vouchers: vouchers.filter((e) => e?.channel.typeCode !== ChannelType.Loyalty),
        otp: '',
      });

      return { isRemoveLoyalty: true, cartNew };
    }

    return { isRemoveLoyalty: false, cartNew: getCart };
  }

  async createUpdateOrderDto(
    placeOrderDto: PlaceOrderDto,
    getCart: GetCartLibResponse,
    orderData?: GetOneOrderLibResponse,
    isCreatePayment = false,
    isAdjustPayment = false,
    ticketCode?: string,
  ) {
    const { loyalty, calculatorPriceInfo, paymentToday, preOrderDepositAmount } = getCart;
    let createAdjustPayment: CreateAdjustPaymentRes = null;
    let orderCreatePayment = orderData?.orderPaymentCreate || [];
    const getTicket = ticketCode ? await this.examinationCoreService.getTicket({ ticketCode: ticketCode }) : null;
    const isStatus5 = getTicket?.indications?.filter((e) => e.status === 5);
    // Trường hợp này đi mua tiêm lần 2 của đơn than htoán chậm
    // trường hợp đơn confirm mà nó stattus 5 thì vào if đầu tiên trước
    // trường hợp 2 dành cho tất cả các case confirm thì đi lại mới hết.
    if (isStatus5?.length) {
      const orderDetailAttachmentCodes = isStatus5?.map((e) => e?.orderDetailAttachmentCode);
      // get lại payment cũ
      const { orders } = await this.omsService.getOrderByOrderAttachmentCode({
        listAttachmentCode: orderDetailAttachmentCodes,
      });
      if (!orderCreatePayment?.length) {
        orderCreatePayment = orders.at(0).orderPaymentCreate;
      }
      orderCreatePayment.at(0).paymentCode = orders?.at(0)?.paymentRequestCode;
      orderCreatePayment.at(0).paymentStatus = 1;

      // get Tiền đã thanh toán
      const orderGetMoney = await this.omsService.getOrdersByPaymentCode({
        paymentCode: orders?.at(0)?.paymentRequestCode,
      });
      const arrOrderCreatePayment: Partial<OrderPaymentCreate>[] = JSONPath({
        path: '$[*].orderPaymentCreate[?(@ && @.paymentType === 1 && @.paymentStatus === 4)]',
        json: orderGetMoney,
      });
      const minOrderCreatePayment = _.maxBy(arrOrderCreatePayment, 'paymentAmount');

      orderCreatePayment.at(0).paymentAmount = minOrderCreatePayment?.paymentAmount + calculatorPriceInfo.totalBill;
      orderCreatePayment.at(0).orderCode = '';

      createAdjustPayment = await this.paymentGWService.createAdjustPayment({
        amount: orderCreatePayment.at(0).paymentAmount,
        createdBy: placeOrderDto.employeeCode,
        type: TypePayment.PICK_UP,
        paymentCode: orderCreatePayment?.at(0)?.paymentCode || '',
        paymentSourceType: PaymentSourceType.OMS,
        sourceCode: orders?.at(0)?.orderCode || '',
      });
    } else if (isCreatePayment) {
      // tạo payment mới thì + tiền cọc
      createAdjustPayment = await this.paymentGWService.createAdjustPayment({
        amount: calculatorPriceInfo.totalBill + (paymentToday?.depositPrice || 0),
        createdBy: placeOrderDto.employeeCode,
        type: TypePayment.PICK_UP,
        paymentCode: '',
        paymentSourceType: PaymentSourceType.OMS,
        sourceCode: orderData?.orderCode || '',
      });
      orderCreatePayment.push({
        paymentAmount: createAdjustPayment?.total,
        paymentCode: createAdjustPayment?.paymentCode,
        paymentDate: createAdjustPayment?.paymentDate,
        paymentStatus: createAdjustPayment?.status,
        paymentType: +createAdjustPayment?.type,
      });
    } else if (isAdjustPayment) {
      // Tổng tiền thu trước đó - tiền của giỏ hàng
      const totalDeposited = orderCreatePayment?.reduce((prev, curr) => {
        // đã thanh toán
        if (curr.paymentType === TypePayment.PICK_UP && curr?.paymentStatus === PaymentType.PAID) {
          prev += curr.paymentAmount;
        }
        if (curr.paymentType === TypePayment.DROP_OFF && curr?.paymentStatus === PaymentType.PAID) {
          prev -= curr.paymentAmount;
        }
        return prev;
      }, 0);
      createAdjustPayment = await this.paymentGWService.createAdjustPayment({
        amount: Math.abs(calculatorPriceInfo.totalBill + (paymentToday?.depositPrice || 0) - totalDeposited),
        createdBy: placeOrderDto.employeeCode,
        type: TypePayment.PICK_UP,
        paymentCode: orderData?.paymentRequestCode || '',
        paymentSourceType: PaymentSourceType.OMS,
        sourceCode: orderData?.orderCode || '',
      });
      // adjust thì cập nhật lại paymentAmount
      orderCreatePayment?.forEach((orderCreatePaymentEntry) => {
        if (orderCreatePaymentEntry.paymentCode === orderData?.paymentRequestCode) {
          orderCreatePaymentEntry.paymentAmount = createAdjustPayment?.total;
        }
      });
    }

    // Get Thông tin sku của pim
    const { listProduct } = await this.pimAppService.getListProductBySkuNoRule([ITEM_VACCINE_SO1]);
    const item = listProduct.at(0) || null;

    // shopCode sau khi chon shop cua ecom truyen trong header, cua anffiliate truyen trong payload
    let shopCode = (this.req?.headers?.['shop-code'] as string) || placeOrderDto?.shopCode || '';
    if (
      !shopCode &&
      (this.req.headers?.['order-channel'] as string) &&
      OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string)
    ) {
      shopCode = RSA_ECOM_HARD_DEFAULT_SHOP_CODE;
    }

    const createOrderDto = transformPlaceOrder(
      {
        ...placeOrderDto,
        ecomDisplay:
          orderData?.ecomDisplay === EcomDisplay.Save
            ? EcomDisplay.Save
            : +this.req.headers?.['order-channel'] === 14 || orderData?.ecomDisplay === EcomDisplay.AtShop
            ? EcomDisplay.AtShop
            : +EcomDisplay.AtOnline,
        phoneNumber: getCart?.headerData?.phoneNumber || placeOrderDto?.phoneNumber || '',
        orderChanel: orderData?.orderChanel || (this.req.headers?.['order-channel'] as string) || '',
        shopCode,
        orderType: String(placeOrderDto.orderType),
        pointLoyalty: loyalty?.totalPoint || 0,
        details: calculatorPriceInfo?.details as any,
        promotions: calculatorPriceInfo?.promotions?.map((e) => ({ ...e, quantityRoot: 1 })),
        paymentRequestCode: createAdjustPayment?.paymentCode || orderData?.paymentRequestCode || '',
        paymentRequestID: createAdjustPayment?.id || orderData?.paymentRequestID || '',
        customerId: getCart?.headerData?.customerId || '',
        customerAddress: placeOrderDto.customerAddress || '',
        customerName: placeOrderDto.customerName || '',
        orderPaymentCreate: orderCreatePayment,
        email: getCart?.headerData?.email || '',
        companyId: '',
        tenantCode: 'FVAC', // hard code dành cho IMS
        orderAttribute: getCart?.headerData?.orderAttribute || 0,
        totalDeposit: paymentToday?.depositPrice || preOrderDepositAmount?.totalDepositAmount || 0,
        employeeCode: orderData
          ? orderData?.createdBy || placeOrderDto.employeeCode
          : getCart?.headerData?.createdBy || placeOrderDto?.employeeCode || '',
        employeeName: orderData
          ? orderData?.createdByName || placeOrderDto.employeeName
          : getCart?.headerData?.createdByName || placeOrderDto?.employeeName || '',
        journeyId: getCart?.headerData?.journeyId || '',
      },
      {
        itemCode: item?.sku || '',
        itemName: item?.name,
        unitCode: item?.measures?.at(0)?.measureUnitId,
        unitName: item?.measures?.at(0)?.measureUnitName,
      },
    );

    const listDetailAttachments = JSONPath({
      path: `$.details[*].detailAttachments[*]`,
      json: createOrderDto,
    });

    if (!placeOrderDto?.createTicketDto?.length && orderData?.orderCode) {
      // get redis ra
      const ticketCreateCache = await this.redisService.get<Array<CreateTicketDto>>(
        `${TICKET_CREATE_KEY}:${orderData?.orderCode}`,
      );
      if (ticketCreateCache?.length) {
        placeOrderDto.createTicketDto = ticketCreateCache;
      }
    }

    const groupBySku = _.groupBy(listDetailAttachments, 'itemCode');
    const listSchedule = placeOrderDto?.createTicketDto?.at(0)?.schedules || [];
    const detailAttachmentNew = [];
    Object.keys(groupBySku).forEach((key) => {
      const schedules = _.sortBy(
        listSchedule?.filter((f) => f?.sku === key && f?.status === 0) || [],
        'appointmentDate',
      );
      const chulkSchedule = schedules?.slice(0, groupBySku[key]?.length);
      let index = 0;
      groupBySku[key]?.forEach((e) => {
        e.appointmentDate =
          moment(chulkSchedule?.at(index)?.appointmentDate).utcOffset(7).format() ||
          moment().utcOffset(7).add(index, 'days').format();
        e.ticketCode = placeOrderDto?.ticketCode || '';
        detailAttachmentNew.push(e);
        index++;
      });
    });

    createOrderDto.details[createOrderDto?.details?.findIndex((i) => i?.itemCode === item?.sku)].detailAttachments =
      detailAttachmentNew;

    createOrderDto.details?.forEach((entry) => {
      entry.discount = entry?.detailAttachments?.reduce((acc, cur) => acc + cur?.['discount'], 0);
      //   entry?.detailAttachments?.forEach((detailAttachmentEntry) => {
      //     detailAttachmentEntry.ticketCode = placeOrderDto?.ticketCode || '';
      //   });
    });

    createOrderDto.orderProperties =
      getCart?.calculatorPriceInfo?.preOrderTotal > 0 ? OrderProperty.Combo : OrderProperty.Retail;

    return { ...createOrderDto, getTicket, isStatus5 };
  }

  /**
   * @TODO Chỉ Adjust đơn hàng + update ngày hẹn
   */
  async getAdjustDto(placeOrderDto: PlaceOrderDto, getCart: GetCartLibResponse, orderData: GetOneOrderLibResponse) {
    const { calculatorPriceInfo, paymentToday, preOrderDepositAmount } = getCart;
    const orderCreatePayment = orderData?.orderPaymentCreate || [];

    // Get Thông tin sku của pim
    const { listProduct } = await this.pimAppService.getListProductBySkuNoRule([ITEM_VACCINE_SO1]);
    const item = listProduct.at(0) || null;

    // shopCode sau khi chon shop cua ecom truyen trong header, cua anffiliate truyen trong payload
    let shopCode = (this.req?.headers?.['shop-code'] as string) || placeOrderDto?.shopCode || '';
    if (
      !shopCode &&
      (this.req.headers?.['order-channel'] as string) &&
      OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string)
    ) {
      shopCode = RSA_ECOM_HARD_DEFAULT_SHOP_CODE;
    }

    const createOrderDto = transformPlaceOrder(
      {
        ...placeOrderDto,
        ecomDisplay:
          orderData?.ecomDisplay === EcomDisplay.Save
            ? EcomDisplay.Save
            : +this.req.headers?.['order-channel'] === 14 || orderData?.ecomDisplay === EcomDisplay.AtShop
            ? EcomDisplay.AtShop
            : +EcomDisplay.AtOnline,
        phoneNumber: getCart?.headerData?.phoneNumber || placeOrderDto?.phoneNumber || '',
        orderChanel: orderData?.orderChanel || (this.req.headers?.['order-channel'] as string) || '',
        shopCode,
        orderType: String(placeOrderDto.orderType),
        pointLoyalty: getCart?.loyalty?.totalPoint || 0,
        details: calculatorPriceInfo?.details as any,
        promotions: calculatorPriceInfo?.promotions?.map((e) => ({ ...e, quantityRoot: 1 })),
        paymentRequestCode: orderData?.paymentRequestCode || '',
        paymentRequestID: orderData?.paymentRequestID || '',
        customerId: getCart?.headerData?.customerId || '',
        customerAddress: placeOrderDto.customerAddress || '',
        customerName: placeOrderDto.customerName || '',
        orderPaymentCreate: orderCreatePayment,
        email: getCart?.headerData?.email || '',
        companyId: '',
        tenantCode: 'FVAC', // hard code dành cho IMS
        orderAttribute: getCart?.headerData?.orderAttribute || 0,
        totalDeposit:
          orderData?.orderStatus === OrderStatus.FinishDeposit &&
          orderData?.orderAttribute === OrderAttribute.PRE_ORDER &&
          orderData?.totalDeposit
            ? orderData?.totalDeposit
            : paymentToday?.depositPrice || preOrderDepositAmount?.totalDepositAmount || 0,
        employeeCode: orderData?.createdBy || placeOrderDto.employeeCode,
        employeeName: orderData?.createdByName || placeOrderDto.employeeName,
        journeyId: getCart?.headerData?.journeyId || '',
      },
      {
        itemCode: item?.sku || '',
        itemName: item?.name,
        unitCode: item?.measures?.at(0)?.measureUnitId,
        unitName: item?.measures?.at(0)?.measureUnitName,
      },
    );

    const listDetailAttachments = JSONPath({
      path: `$.details[*].detailAttachments[*]`,
      json: createOrderDto,
    });

    if (!placeOrderDto?.createTicketDto?.length && orderData?.orderCode) {
      // get redis ra
      const ticketCreateCache = await this.redisService.get<Array<CreateTicketDto>>(
        `${TICKET_CREATE_KEY}:${orderData?.orderCode}`,
      );
      if (ticketCreateCache?.length) {
        placeOrderDto.createTicketDto = ticketCreateCache;
      }
    }

    const groupBySku = _.groupBy(listDetailAttachments, 'itemCode');
    const listSchedule = placeOrderDto?.createTicketDto?.at(0)?.schedules || [];
    const detailAttachmentNew = [];
    Object.keys(groupBySku).forEach((key) => {
      const schedules = _.sortBy(
        listSchedule?.filter((f) => f?.sku === key && f?.status === 0) || [],
        'appointmentDate',
      );
      const chulkSchedule = schedules?.slice(0, groupBySku[key]?.length);
      let index = 0;
      groupBySku[key]?.forEach((e) => {
        e.appointmentDate =
          moment(chulkSchedule?.at(index)?.appointmentDate).utcOffset(7).format() ||
          moment().utcOffset(7).add(index, 'days').format();
        e.ticketCode = placeOrderDto?.ticketCode || '';
        detailAttachmentNew.push(e);
        index++;
      });
    });

    createOrderDto.details[createOrderDto?.details?.findIndex((i) => i?.itemCode === item?.sku)].detailAttachments =
      detailAttachmentNew;

    createOrderDto.details?.forEach((entry) => {
      entry.discount = entry?.detailAttachments?.reduce((acc, cur) => acc + cur?.['discount'], 0);
    });

    createOrderDto.orderProperties =
      getCart?.calculatorPriceInfo?.preOrderTotal > 0 ? OrderProperty.Combo : OrderProperty.Retail;

    return createOrderDto;
  }

  /**
   * @TODO tạo deposit all
   * @param AdjustPayment
   */
  async createdDepositAllVaccine(adjustPayment: any) {
    const payloadDepositAll = {
      paymentCode: adjustPayment?.paymentCode,
      totalPayment: adjustPayment?.total || 0,
      custCode: adjustPayment?.custCode,
      custName: adjustPayment?.custName,
      accountId: adjustPayment?.accountId,
      shopCode: adjustPayment?.shopCode,
      shopAffiliate: adjustPayment?.shopAffiliate,
      phone: adjustPayment?.phone,
      paymentRequestType: adjustPayment?.paymentRequestType || PaymentRequestType.CANCEL_DEPOSIT,
      paymentSource: {
        detail: [
          {
            type: adjustPayment?.paymentSourceDetailType || 5,
            sourceCode: adjustPayment?.orderCode,
            amount: adjustPayment?.total || 0,
          },
        ],
        createdBy: adjustPayment?.modifiedBy,
        typePayment: 2, // chi
      },
      cash: [
        {
          transactionTypeId: adjustPayment?.transactionTypeId || CashTransactionTypeId.DEPOSIT_DROP_OFF,
          paymentMethodId: 1,
          amount: adjustPayment?.total || 0,
          transactionFee: 0,
          transactionTime: moment().utcOffset(7).format(),
          additionAttributes: '',
          createdBy: adjustPayment?.modifiedBy,
        },
      ],
    };
    const depositAll = await this.paymentGWService.createDepositAllVaccine(payloadDepositAll);
    return depositAll;
  }

  /**
   * @TODO api get order detail
   * @param orderCode string required
   * @returns
   */
  async orderDetail(orderCode: string) {
    const data = await this.omsService.getOneOrder(orderCode);
    return { ...data };
  }

  async createPaymentAndDepositAll(payload: CreatePaymentAndDepositAllDto, isMultiple = true, shopCode?: string) {
    const infoOrder = await this.orderDetail(payload.orderCode);
    // gọi api create adjust payment từ payment
    const payloadCreateAdjustPayment = {
      amount: payload?.totalRefund, //số tiền muốn chi
      type: TypePayment.DROP_OFF, // chi
      sourceCode: payload.orderCode, // orderCode
      createdBy: payload.modifiedBy, // FE TRUYỀN
      paymentSourceType: PaymentSourceType.CANCEL_DEPOSIT, // retrun cancel deposit
      fromSystem: ATTR_OTP.FromSystem,
      isMultiple: isMultiple,
    };
    const resAdjustPayment = await this.paymentGWService.createAdjustPayment(payloadCreateAdjustPayment);

    // gọi api lấy accountId từ payment
    const { id } = await this.paymentGWService.getAccountBalance(infoOrder?.custCode);

    // gọi api create deposit all từ payment
    const payloadDeposit = {
      ...resAdjustPayment,
      custCode: infoOrder?.custCode, // ==> khách hàng ==> check lại có thể lấy từ order oms
      custName: infoOrder?.custName, // ==> khách hàng ==> order
      accountId: id,
      shopCode: shopCode || infoOrder?.shopCode,
      phone: infoOrder?.phone,
      orderCode: payload.orderCode,
      modifiedBy: payload.modifiedBy,
      shopAffiliate: payload?.shopAffiliate || '',
    };
    const depositAll = await this.createdDepositAllVaccine(payloadDeposit);
    return { resAdjustPayment, depositAll };
  }

  async createPaymentAndDepositAllForCashback(
    payload: CreatePaymentAndDepositAllDto,
    isMultiple = true,
    shopCode?: string,
  ) {
    const infoOrder = await this.orderDetail(payload.orderCode);
    // gọi api create adjust payment từ payment
    const payloadCreateAdjustPayment = {
      amount: payload?.totalRefund, //số tiền muốn chi
      type: TypePayment.DROP_OFF, // chi
      sourceCode: payload.cashBackReceiptCode, // cashBackReceiptCode
      createdBy: payload.modifiedBy, // FE TRUYỀN
      paymentSourceType: PaymentSourceType.OMS,
      fromSystem: ATTR_OTP.FromSystem,
      isMultiple: isMultiple,
    };
    const resAdjustPayment = await this.paymentGWService.createAdjustPayment(payloadCreateAdjustPayment);

    // gọi api lấy accountId từ payment
    const { id } = await this.paymentGWService.getAccountBalance(infoOrder?.custCode);

    // gọi api create deposit all từ payment
    const payloadDeposit = {
      ...resAdjustPayment,
      custCode: infoOrder?.custCode, // ==> khách hàng ==> check lại có thể lấy từ order oms
      custName: infoOrder?.custName, // ==> khách hàng ==> order
      accountId: id,
      shopCode: shopCode || infoOrder?.shopCode,
      phone: infoOrder?.phone,
      orderCode: payload.cashBackReceiptCode, // Luồng cashback: order --> cashBackReceiptCode
      modifiedBy: payload.modifiedBy,
      paymentRequestType: 13, // Luồng cashback
      transactionTypeId: 8, // Luồng cashback
      paymentSourceDetailType: 1, // Luồng cashback
    };
    const depositAll = await this.createdDepositAllVaccine(payloadDeposit);
    return { resAdjustPayment, depositAll };
  }

  /**
   * @description Book tồn kho
   */
  async bookingInventory(
    employeeCode: string,
    ticketCode: string,
    listGiftProduct: Array<Partial<DetailLib>>,
    shopCodeParam?: string,
    orderCode?: string,
    isBookGift = false,
  ) {
    const shopCode = shopCodeParam || (this.req.headers?.['shop-code'] as string);

    const createBookingDto: CreateCheckAvailableBody = {
      systemBook: SystemBook.VACCINE,
      bookingRequestDetails: [],
      createdBy: employeeCode,
      shopCode: shopCode,
      transNum: ticketCode || orderCode || '',
      transType: ticketCode ? TransType.TICKET : TransType.ORDER,
    };

    if (ticketCode && !OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string)) {
      const getTicket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });

      getTicket.indications.forEach((element) => {
        const booking: BookingRequestDetail = {
          sku: element.sku,
          skuName: element.vaccineName,
          productItemCode: '',
          quantityTrans: 1,
          unitCode: +element['unitCodeSale'] || +element.unitCode || null,
          whsCode: whsCodeWithShopCodeWhsType(shopCode, WHS_CODE_NORMAL),
          bookType: 1,

          // docEntry: 0,
          // docType: 1,
          // systemType: 14,
          // itemCode: element.sku,
          // whsCode: whsCodeWithShopCodeWhsType(shopCode, WHS_CODE_NORMAL),
          // shopCode,
          // qty_order: 1,
          // createDate: parseDateTimeZone(new Date(), '+07:00'),
          // updateDate: parseDateTimeZone(new Date(), '+07:00'),
          // createby: employeeCode,
          // updateby: employeeCode,
          // status: 1,
          // bookType: 1,
          // orderCode: ticketCode || orderCode || '',
          // unitCode: +element.unitCode,
        };
        createBookingDto.bookingRequestDetails.push(booking);
      });
      // if (createBookingDto.bookingRequestDetails.length) {
      //   await this.imsBookingService.createCheckAvailable(createBookingDto);
      // }
    }

    if (isBookGift) {
      listGiftProduct?.forEach((giftProductEntry) => {
        if (giftProductEntry?.isInventoryManagement === 'N') return;
        const booking: BookingRequestDetail = {
          sku: giftProductEntry.itemCode,
          skuName: giftProductEntry.itemName,
          productItemCode: '',
          quantityTrans: giftProductEntry.quantity,
          unitCode: +giftProductEntry.unitCode,
          whsCode: giftProductEntry?.whsCode || whsCodeWithShopCodeWhsType(shopCode || '', WHS_CODE_NORMAL),
          bookType: 2,
          // docEntry: 0,
          // docType: 1,
          // systemType: 14,
          // itemCode: giftProductEntry.itemCode,
          // whsCode: giftProductEntry?.whsCode || whsCodeWithShopCodeWhsType(this.shopCode, WHS_CODE_NORMAL),
          // shopCode,
          // qty_order: giftProductEntry?.quantity,
          // createDate: parseDateTimeZone(new Date(), '+07:00'),
          // updateDate: parseDateTimeZone(new Date(), '+07:00'),
          // createby: employeeCode,
          // updateby: employeeCode,
          // status: 1,
          // bookType: 1,
          // orderCode: ticketCode || orderCode || '',
          // unitCode: +giftProductEntry.unitCode,
        };
        createBookingDto.bookingRequestDetails.push(booking);
      });
    }

    if (createBookingDto.bookingRequestDetails.length) {
      await this.imsBookingService.createCheckAvailable(createBookingDto);
    }
  }

  /**
   * @description Cancel Book tồn và cancel SO2
   */
  async cancelBook(shopCode: string, modifiedBy: string, ticketCode: string) {
    const cancelBookDto: CancelBookParams = {
      systemBook: SystemBook.VACCINE,
      shopCode: shopCode,
      transNum: ticketCode,
      transType: TransType.TICKET,
      updatedBy: modifiedBy,
    };
    try {
      await this.imsBookingService.cancelBook(cancelBookDto);
    } catch (error) {
      Logger.error({
        fields: {
          info: '[cancelBook] handle error',
          errors: error,
        },
      });
    }

    // orderData?.details?.forEach((detail) => {
    //   if (detail?.isPromotion === 'Y' && detail?.isInventoryManagement === 'Y') {
    //     const booking: CreateBookingDto = {
    //       docEntry: 0,
    //       docType: 1,
    //       systemType: 14,
    //       itemCode: detail.itemCode,
    //       whsCode: detail.whsCode,
    //       shopCode: orderData.shopCode,
    //       qty_order: detail.quantity,
    //       createDate: parseDateTimeZone(new Date(), '+07:00'),
    //       updateDate: parseDateTimeZone(new Date(), '+07:00'),
    //       createby: orderData.createdBy,
    //       updateby: orderData.modifiedBy,
    //       status: orderData.orderStatus,
    //       bookType: 1,
    //       orderCode: ticketCode,
    //       unitCode: detail.unitCode,
    //     };
    //     cancelBookDto.push(booking);
    //   }

    //   detail?.detailAttachments?.forEach((detailAttachment) => {
    //     const booking: CreateBookingDto = {
    //       docEntry: 0,
    //       docType: 1,
    //       systemType: 14,
    //       itemCode: detailAttachment.itemCode,
    //       whsCode: detail.whsCode,
    //       shopCode: orderData.shopCode,
    //       qty_order: detailAttachment.quantity,
    //       createDate: parseDateTimeZone(new Date(), '+07:00'),
    //       updateDate: parseDateTimeZone(new Date(), '+07:00'),
    //       createby: orderData.createdBy,
    //       updateby: orderData.modifiedBy,
    //       status: orderData.orderStatus,
    //       bookType: 1,
    //       orderCode: ticketCode,
    //       unitCode: detailAttachment.unitCode || detail.unitCode,
    //     };
    //     cancelBookDto.push(booking);
    //   });
    // });
  }

  /**
   * @description tạo note và câu hỏi
   */
  async createNoteAndQuestion(healthCheckQuestion: HealthCheckQuestion[], ticketCode: string, personId: string) {
    healthCheckQuestion = healthCheckQuestion?.filter((entry) => !!entry?.note);
    if (!healthCheckQuestion?.length) return true;

    // Mapping question to note
    const createTicketNoteList: CreateTicketNoteDto = {
      items: healthCheckQuestion.map((question) => {
        return {
          ticketCode: ticketCode,
          personId,
          context: question?.note,
          privateInfo: false,
          status: 1,
          noteType: NOTE_TYPE.MEDICAL,
        };
      }),
    };

    await this.examinationCoreService.createTicketNote(createTicketNoteList);
  }

  /**
   * @description map payment info
   */
  _mapPaymentInfo(
    orderInfo: GetOneOrderLibResponse,
    totalDiscountVoucher: number = 0,
    arrPaymentES: GetPaymentHistoryESLibResponse[] = [],
    ticketInfo?: TicketDetailRes,
  ) {
    const totalPaymentThu = orderInfo.orderPaymentCreate
      .filter((e) => e.paymentType === PaymentType.THU)
      .reduce(
        (acc, cur) => {
          if (cur.paymentStatus === PaymentOnlineStatus.Complete) {
            acc.complete += cur.paymentAmount;
          } else if (cur.paymentStatus === PaymentOnlineStatus.Confirm) {
            acc.confirm += cur.paymentAmount;
          }
          return acc;
        },
        { complete: 0, confirm: 0 },
      );

    const totalChiTien = orderInfo.orderPaymentCreate
      .filter((e) => e.paymentType === PaymentType.CHI)
      .reduce(
        (acc, cur) => {
          if (cur.paymentStatus === PaymentOnlineStatus.Complete) {
            acc.complete += cur.paymentAmount;
          } else if (cur.paymentStatus === PaymentOnlineStatus.Confirm) {
            acc.confirm += cur.paymentAmount;
          }
          return acc;
        },
        { complete: 0, confirm: 0 },
      );

    const totalDeposited = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositDetailAmountRealAmount(cur?.detail);
    }, 0);

    let remainingAmount = 0;

    if (orderInfo?.orderAttribute === 7) {
      // lần 2 mã vào bác sĩ thì có tiền chênh lệch
      if (
        orderInfo?.orderPaymentCreate?.length === 1 &&
        orderInfo?.orderPaymentCreate?.at(0)?.paymentStatus === 1 &&
        ticketInfo?.status >= EnmStatusTicket.CHECKED_UP
      ) {
        remainingAmount = orderInfo?.totalBill - totalDeposited;
      } else if (
        orderInfo?.orderPaymentCreate?.length === 1 &&
        orderInfo?.orderPaymentCreate?.at(0)?.paymentStatus === 1 &&
        orderInfo?.orderStatus === OrderStatus.FinishDeposit
      ) {
        remainingAmount = orderInfo?.totalBill - totalDeposited;
      } else {
        remainingAmount = orderInfo.totalBill - (totalPaymentThu.complete - totalChiTien.complete);
      }
    } else if (orderInfo?.orderStatus === OrderStatus.FinishDeposit) {
      remainingAmount = orderInfo.totalBill - (totalPaymentThu.complete - totalChiTien.complete);
    }

    const paymentInfo: PaymentInfoForClient = {
      totalBill: orderInfo?.totalBill,
      total: orderInfo?.total,
      totalDiscount: orderInfo?.totalDiscount,
      totalDiscountVoucher: totalDiscountVoucher,
      totalDiscountAll: orderInfo?.totalDiscount + totalDiscountVoucher,
      serviceFee: orderInfo?.serviceFee,
      depositedAmount:
        orderInfo?.orderAttribute === 7
          ? totalDeposited
          : Math.max(totalPaymentThu.complete - totalDiscountVoucher - totalChiTien.complete, 0),
      remainingAmount: remainingAmount,
      totalBillAfterVoucher: Math.max(0, orderInfo?.totalBill - totalDiscountVoucher),
      totalDeposit: orderInfo?.totalDeposit,
    };

    return paymentInfo;
  }

  async searchOrderInjectionByIds(ids: string[]) {
    return await this.vacOrderInjectionService.searchByIds(ids);
  }

  async getVoucherByOrderCode(orderCode: string) {
    try {
      const orderData = await this.omsService.getOneOrder(orderCode);

      const cartComfirmData = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
      const arrSerialCode = cartComfirmData?.voucherInfo?.map((e) => e?.seriesCode);
      if (!arrSerialCode.length) {
        return {
          isValid: true,
          invalidVouchers: [],
          validVouchers: [],
          messages: { errors: [] },
        };
      }

      const orders: VerifyVoucherOrderItemLibDto[] = [];

      cartComfirmData?.calculatorPriceInfo?.details?.forEach((e) => {
        if (e.isPromotion === 'Y') return;
        orders.push({
          itemCode: e?.itemCode,
          price: e?.price,
          quantity: e?.quantity,
          totalPriceAfterDiscount: e?.total - e?.discountPromotion,
          totalDiscount: e?.discountPromotion,
        });
      });

      const verifyDto: VerifyVoucherDto = {
        orderCode: orderData?.orderCode,
        phoneNumber: orderData?.phone,
        orders: orders,
        seriesCodes: arrSerialCode,
        totalBill: orderData?.totalBill,
        paymentCode: orderData?.paymentRequestCode,
      };

      return this.voucherCoreService.verifyVoucher(verifyDto);
    } catch (error) {
      Logger.error({
        fields: {
          info: '[VOUCHER_IN_ORDER]  handle error',
          errors: error,
        },
      });
      return {
        isValid: true,
        invalidVouchers: [],
        validVouchers: [],
        messages: { errors: [] },
      };
    }
  }

  /**
   * @description common chi tiền thừa cho khách
   */
  async _commonReturnCashOTPOrSM(payload: {
    orderCode: string;
    modifiedBy: string;
    ticketCode: string;
    modifiedByName: string;
  }) {
    const { orderCode, modifiedBy, ticketCode, modifiedByName } = payload;
    const orderInfo = await this.omsService.getOneOrder(orderCode);
    // get order detail từ oms
    if (orderInfo?.orderStatus !== OrderStatus.FinishDeposit) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ORDER_STATUS_NOT_FINISH_DEPOSITED,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    // Người thao với đơn (chi tiền dư)
    await this.omsService.employeeOrder({
      orderID: orderInfo?.orderID,
      orderCode: orderInfo?.orderCode,
      employeeCode: modifiedBy,
      employeeName: modifiedByName,
      step: EmployeeStep.EmployeeReturnCash,
      createdDate: new Date(),
      modifiedDate: new Date(),
      orderCancelChannel: Channel.Offline,
    });

    const arrPaymentCode = _.uniq(
      _.compact(
        orderInfo?.orderPaymentCreate?.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );

    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getPaymentRedis({
      paymentCodes: arrPaymentCode,
    });

    let totalDiscountVoucher = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
    }, 0);

    const paymentInfo = this._mapPaymentInfo(orderInfo, totalDiscountVoucher);

    /**
     * @TODO Luồng deposit thêm voucher
     *
     */
    // Trừ đi voucher không force cancel được va k nam trong cart
    const arrVoucherProvider: number[] = [
      VoucherPartnerId.GotIT,
      VoucherPartnerId.Taptap,
      VoucherPartnerId.Urbox,
      VoucherPartnerId.UTop,
    ];

    const arrVoucherDetail: VoucherDetail[] =
      JSONPath({
        json: arrPaymentES,
        path: '$[*]..vouchersAll[*].voucherDetail',
      })?.filter((e: VoucherDetail) => arrVoucherProvider.includes(e.voucherType)) || [];

    const totalVoucherPartner = arrVoucherDetail?.reduce((prev, curr) => {
      return prev + curr.amount;
    }, 0);
    // tru voucher partner
    totalDiscountVoucher -= totalVoucherPartner;

    // const getVoucherAppliedOnOrder = await this.getVoucherByOrderCode(orderCode);
    const cartComfirmData = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
    let totalVoucherGened = 0;
    let getCart = null;
    try {
      getCart = await this.cartAppService.getCart({
        sessionId: cartComfirmData?.sessionId,
        shopCode: orderInfo.shopCode,
        orderType: +orderInfo.orderType,
        phoneNumber: orderInfo.phone,
      });
      totalVoucherGened = getCart?.calculatorPriceInfo?.totalVoucherPrice - totalDiscountVoucher || 0;
    } catch (error) {
      Logger.error({
        fields: {
          info: '[getCartConfirmByOrderCode] handle error',
          errors: error,
        },
      });
    }

    // getSerial voucher
    // const arrSerialDeposited: string[] = _.compact(
    //   _.uniq(
    //     _.flattenDeep(
    //       arrPaymentES.map((e) => e?.detail?.vouchersAll?.map((detailEntry) => detailEntry?.voucherDetail?.code)),
    //     ),
    //   ),
    // );

    // if (getVoucherAppliedOnOrder?.validVouchers?.length) {
    //   getVoucherAppliedOnOrder.validVouchers = getVoucherAppliedOnOrder?.validVouchers?.filter((e) => {
    //     const serialFind = arrSerialDeposited?.find((serialEntry) => serialEntry === e?.seriesCode);
    //     if (serialFind) return false;
    //     return true;
    //   });
    // }

    // // Tạo tiền => Adjust đơn => Deposit => paid /4
    // const totalVoucherGened = getVoucherAppliedOnOrder?.validVouchers?.reduce((acc, cur) => {
    //   return acc + cur?.amount || 0;
    // }, 0);

    if (totalVoucherGened) {
      paymentInfo.remainingAmount -= totalVoucherGened || 0;
    }

    // case remainingAmount > 0 thì báo lỗi k hợp lệ
    if (paymentInfo?.remainingAmount >= 0) {
      const exception: IError = {
        code: ErrorCode.RSA_DEPOSIT_EXTRA_PAYMENT,
        message: ErrorCode.getError(ErrorCode.RSA_DEPOSIT_EXTRA_PAYMENT),
        details: ErrorCode.getError(ErrorCode.RSA_DEPOSIT_EXTRA_PAYMENT),
        validationErrors: null,
        config: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    for (const orderPayment of orderInfo?.orderPaymentCreate) {
      // cancle hết voucher cũ trước đó gen
      // hủy voucher loyalty chưa deposit
      if (orderPayment?.paymentType === 1 && totalVoucherGened) {
        try {
          await this.loyaltyAppService.cancelExchangePoint(orderInfo?.custCode, {
            system: System,
            orderCode: orderInfo.orderCode,
            paymentCode: orderPayment?.paymentCode,
            phoneNumber: orderInfo?.phone,
            shopCode: orderInfo?.shopCode,
            reason: 'Huỷ voucher loyalty',
          });
        } catch (error) {
          Logger.error({
            fields: {
              info: '[CancelExchangePointLoyalty] handle error',
              errors: error,
            },
          });
        }

        // huỷ voucher cũ
        const payloadCancelVoucher: CancelVoucherByPaymentCodeDto = {
          fromSystem: 'RSA',
          orderCode: orderInfo.orderCode,
          paymentCode: orderPayment?.paymentCode,
          phoneNumber: orderInfo?.phone,
          shopCode: orderInfo?.shopCode,
          reason: 'Huỷ voucher khi huỷ đơn hàng',
          isForceCancel: true,
        };

        try {
          await this.voucherCoreService.cancelVoucherByPaymentCode(payloadCancelVoucher);
        } catch (error) {
          Logger.error({
            fields: {
              info: '[CancelVoucherPayment] handle error',
              errors: error,
            },
          });
        }

        const unbookVoucherDto: UnbookVoucherByPaymentCodeDto = {
          fromSystem: 'RSA',
          phoneNumber: orderInfo.phone,
          orderCode: orderInfo.orderCode,
          paymentCode: orderPayment?.paymentCode,
          shopCode: orderInfo.shopCode,
        };

        try {
          await this.voucherCoreService.unbookVoucherByPaymentCode(unbookVoucherDto);
        } catch (error) {
          Logger.error({
            fields: {
              info: '[UnBookVoucherCore] handle error',
              errors: error,
            },
          });
        }
      }

      if (orderPayment.paymentStatus !== PaymentType.THU) continue;
      await this.omsService.updateStatusOrderPayment(orderInfo.orderCode, orderPayment.paymentCode, {
        ...orderPayment,
        paymentStatus: 2,
        orderCode: orderInfo.orderCode,
      } as any);
      await this.paymentGWService.cancelPaymentRequestCode(orderPayment.paymentCode);
    }

    const totalRefund = Math.abs(paymentInfo?.remainingAmount); // convert to số dương

    if (totalVoucherGened) {
      const createAdjustPayment = await this.paymentGWService.createAdjustPayment({
        amount: totalVoucherGened,
        createdBy: orderInfo.createdBy,
        type: TypePayment.PICK_UP,
        paymentCode: '',
        paymentSourceType: PaymentSourceType.OMS,
        sourceCode: orderInfo?.orderCode || '',
      });
      // tạo payment Request của tổng tiền voucher
      orderInfo?.orderPaymentCreate?.push({
        paymentAmount: createAdjustPayment?.total,
        paymentCode: createAdjustPayment?.paymentCode,
        paymentDate: createAdjustPayment?.paymentDate,
        paymentStatus: createAdjustPayment?.status,
        paymentType: +createAdjustPayment?.type,
      });
      orderInfo.paymentRequestCode = createAdjustPayment.paymentCode;
      orderInfo.paymentRequestID = createAdjustPayment.id;

      // Update đơn xong r
      const updateOrder = await this.omsService.updateOrder(orderInfo.orderCode, {
        ...orderInfo,
        shipmentPlannings: orderInfo?.shipmentPlannings?.map((e) => ({ planningId: e.planningID })),
        // Giữ lại data cũ
        orderType: orderInfo.orderType,
        orderChanel: orderInfo.orderChanel,
        // Giữ lại người tạo cũ
        createdBy: orderInfo.createdBy,
        createdByName: orderInfo.createdByName,
        // Lấy data mới
        modifiedBy: orderInfo?.modifiedBy || orderInfo?.createdBy,
        modifiedByName: orderInfo?.createdByName || orderInfo?.createdByName,
        details: orderInfo?.details?.map((e) => {
          const price = e?.detailAttachments?.reduce((prev, curr) => (prev += curr.price), 0);
          e.totalBill = price || 0;
          e.total = price || 0;
          e.price = price || 0;
          return e;
        }),
        promotions: orderInfo?.promotionGroup || [],
      });

      // genvoucher
      const { errorVoucher, voucher } = await this.generateVoucherCreateUpdateOrder(updateOrder, getCart, {
        employeeCode: modifiedBy,
        employeeName: modifiedByName,
        sessionId: cartComfirmData?.sessionId,
        shopCode: orderInfo.shopCode,
        orderType: +orderInfo.orderType,
        phoneNumber: orderInfo.phone,
      });

      Logger.log({
        fields: {
          info: 'generateVoucherCreateUpdateOrder',
          errors: errorVoucher,
          voucher: voucher,
        },
      });

      // gọi api lấy accountId từ payment
      const { id } = await this.paymentGWService.getAccountBalance(updateOrder?.custCode);

      const depositAll: DepositAllLibDto = {
        paymentCode: updateOrder?.paymentRequestCode,
        totalPayment: totalVoucherGened,
        phone: updateOrder?.phone,
        custCode: updateOrder?.custCode,
        custName: updateOrder?.custName,
        accountId: id,
        shopCode: updateOrder?.shopCode,
        paymentRequestType: 5,
        paymentSource: {
          detail: [
            {
              sourceCode: updateOrder?.orderCode,
              type: 1,
              amount: totalVoucherGened,
            },
          ],
          createdBy: modifiedBy || updateOrder?.createdBy,
          typePayment: 1,
        },
        vouchersAll: [],
      };

      voucher?.validVouchers?.forEach((voucherValidEntry) => {
        depositAll.vouchersAll.push({
          transaction: {
            transactionTypeId: 6,
            paymentMethodId: 4,
            amount: voucherValidEntry?.amount,
            transactionFee: 0,
            transactionTime: moment().utcOffset(7).format(),
            note: '',
            additionAttributes: '',
            createdBy: modifiedBy || updateOrder?.createdBy,
          },
          voucherDetail: {
            code: voucherValidEntry?.seriesCode,
            name: voucherValidEntry?.voucherName,
            amount: voucherValidEntry?.amount,
            voucherType: 1,
            createdBy: modifiedBy || updateOrder?.createdBy,
            createdDate: null,
          } as any,
        });
      });

      await this.paymentGWService.depositAll(depositAll);

      await this.omsService.updateStatusPayment(
        updateOrder?.orderCode,
        PaymentOnlineStatus.Complete,
        updateOrder.paymentRequestCode,
      );

      await this.updateStatusDeposit(
        updateOrder?.orderCode,
        updateOrder?.modifiedBy || updateOrder?.createdBy,
        updateOrder?.modifiedByName || updateOrder?.createdByName,
      );
    }

    // deposit xong thì call /3 để hoàn t hành payment => sau đó get Order lại
    await this.createPaymentAndDepositAll({ orderCode, modifiedBy, totalRefund });

    //-----------------------------------------------------//

    // change status ticket
    const ticket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });

    // tách nợ km
    await this.debitPromotion(orderCode, ticketCode);
    const linkId = await this.getIdLinkFromEcom(orderCode);
    const ticketInfo = await this.examinationCoreService.adjustTicket(
      { ticketCode },
      {
        ...ticket,
        status: ticket?.indications?.length > 0 ? EnmStatusTicket.WAITING_INJECT : EnmStatusTicket.DONE,
        paymentType: PaymentType.PAID, // @todo linkId
        linkId,
      },
    );
    const confirmPromotion = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
    await this.confirmPromotion(orderCode, confirmPromotion);

    // assign room
    const assignRoom = [await this.adjustTicketAndAssignRoom(ticketInfo, modifiedBy)];

    return assignRoom;
  }

  async adjustTicketAndAssignRoom(getTicket: TicketDetailRes, modifiedBy: string) {
    const assignRoomRes = await this.examinationCoreService.assignRoomV2({
      ticketCodes: [getTicket?.ticketCode],
      tableId: getTicket?.tableId,
      shopCode: getTicket?.shopCode,
      roomType: EnmClinicType.PT,
      rule: EnmAssignRule.INDICATION,
      modifiedBy: modifiedBy,
    });

    let adjustTicketDto = {
      ...getTicket,
      indications: getTicket?.indications || [],
      schedules: getTicket?.schedules || [],
    };
    if (getTicket?.indications?.length > 0) {
      adjustTicketDto = {
        ...assignRoomRes.at(0),
        ..._.pickBy(getTicket, _.identity),
        indications: getTicket?.indications || [],
        schedules: getTicket?.schedules || [],
      };
    }

    return this.examinationCoreService.adjustTicket(
      { ticketCode: getTicket.ticketCode },
      { ...adjustTicketDto, journeyId: assignRoomRes?.[0]?.journeyId || '' },
    );
  }

  /**
   * @description payload cho api adjust của bác sĩ
   */
  async payloadAdjustOrderForDoctor(
    placeOrder: PlaceOrderDto,
    getCart: GetCartLibResponse,
    getOrder: GetOneOrderLibResponse,
    totalBillBase: number,
  ): Promise<CreateOrderDto> {
    placeOrder.orderType = OrderType.OrderContract;
    let adjustOrderDto = null;
    if (
      getCart.calculatorPriceInfo.totalBill !== totalBillBase &&
      getCart.calculatorPriceInfo.totalBill > totalBillBase
    ) {
      // Tạo payment mới
      adjustOrderDto = await this.createUpdateOrderDto(
        placeOrder,
        {
          ...getCart,
          calculatorPriceInfo: {
            ...getCart.calculatorPriceInfo,
            totalBill: getCart.calculatorPriceInfo.totalBill - totalBillBase,
          },
        },
        getOrder,
        true,
        false,
      );
    }

    if (!adjustOrderDto) {
      adjustOrderDto = await this.createUpdateOrderDto(placeOrder, getCart, getOrder, false, false);
    }
    return adjustOrderDto;
  }

  async generateVoucherCreateUpdateOrder(
    createOrderData: GetOneOrderLibResponse,
    getCartData: GetCartLibResponse,
    placeOrderDto?: PlaceOrderDto,
    voucherGen?: VoucherGenerateCart,
    config?: any,
    isOverwriteVoucher?: boolean,
  ): Promise<{ voucher: VerifyVoucherResponse; errorVoucher: any }> {
    /**
     * @TODO
     *  - Generate tất cả voucher phân loại voucher nào dùng ở đơn hàng và voucher nào dùng sau
     *  - Những voucher nào dùng sau được in ra bill
     *  - Phân loại voucher bằng voucher valid response của cart
     */
    try {
      const { appliedGiftCodes, vouchers } = getCartData;
      if (voucherGen?.vouchersValid?.length || voucherGen?.vouchersInValid?.length) {
        getCartData?.voucherGenerate?.vouchersValid?.push(...(voucherGen?.vouchersValid || []));
        getCartData?.voucherGenerate?.vouchersInValid?.push(...(voucherGen?.vouchersInValid || []));
      }

      let vouchersInCart: Voucher[] = vouchers;
      /**
       * Gen voucher thật từ loyalty
       * Update voucher mới gen vào cart
       * update state vouchers để verify voucher
       */
      const voucherLoyaltyInCart = vouchers?.find((voucher) => voucher?.channel?.typeCode === ChannelType.Loyalty);
      if (voucherLoyaltyInCart) {
        const voucherLoyalty = await this.loyaltyAppService.exchangePoint(createOrderData?.custCode, {
          system: System,
          phoneNumber: getCartData?.headerData?.phoneNumber,
          shopCode: createOrderData.shopCode,
          orderCode: createOrderData?.orderCode,
          paymentCode: createOrderData?.paymentRequestCode,
          exchange: {
            itemVoucherCode: voucherLoyaltyInCart?.define?.code,
            itemVoucherName: voucherLoyaltyInCart?.define?.name,
            point: voucherLoyaltyInCart?.totalPoint,
          },
        });

        vouchersInCart = vouchers?.map((voucher) => {
          if (voucher?.channel?.typeCode === ChannelType.Loyalty) {
            return {
              ...voucher,
              totalPoint: voucherLoyalty?.exchange?.point,
              seriesCode: voucherLoyalty?.exchange?.series,
              amount: voucherLoyalty?.exchange?.amount,
            };
          } else return voucher;
        });

        await this.cartAppService.addVoucherCreateOrder(
          {
            ...placeOrderDto,
            vouchers: vouchersInCart,
          },
          config ? config : this.req,
        );
      }

      const generateVoucherDto: GenerateVoucherDto = {
        details: [],
        fromSystem: 'RSA',
        orderType: +createOrderData.orderType,
        orderCode: createOrderData.orderCode,
        paymentCode: createOrderData.paymentRequestCode,
        phoneNumber: createOrderData.phone,
      };

      const arrVoucherGenerate: VoucherResponse[] = getCartData.voucherGenerate.vouchersValid.concat(
        getCartData.voucherGenerate.vouchersInValid,
      );

      for (const { amount, promotionCode, quantity, type, voucherCode, sourceVouchers } of arrVoucherGenerate) {
        generateVoucherDto.details.push({
          amount,
          code: voucherCode,
          promotionCode,
          quantity,
          sourceVouchers: sourceVouchers,
          voucherType: type,
        });
      }

      let voucher: VerifyVoucherResponse = null;

      const seriesCodesVoucher: string[] = vouchersInCart
        .filter((e) => e.voucherType !== VoucherType.MUD)
        .map((e) => e.seriesCode);
      let voucherDoiTac = [];

      if (generateVoucherDto.details.length) {
        const voucherResponse = await this.voucherCoreService.generateVoucher(generateVoucherDto);
        voucherResponse.details.forEach(({ code, quantity, voucherType, seriesCodes }) => {
          // Lấy những voucher được áp dụng trong đơn hang
          const voucherValidFind = getCartData.voucherGenerate.vouchersValid.find(
            (e) => e.voucherCode === code && e.quantity === quantity && e.type === voucherType,
          );
          if (!voucherValidFind) {
            return; // Skip to the next iteration
          }
          seriesCodesVoucher.push(...seriesCodes);
        });
      }
      if (seriesCodesVoucher.length) {
        const verifyVoucherData = await this.voucherCoreService.verifyVoucher({
          orderCode: createOrderData.orderCode,
          phoneNumber: createOrderData.phone,
          orders: getCartData.listCartSelected
            .filter((e) => e.itemType === ItemType.Product)
            .map((e) => {
              return {
                itemCode: e.itemCart,
                price: e.detailCalculatorPriceInfo.price,
                quantity: e.quantity,
                totalPriceAfterDiscount:
                  e.detailCalculatorPriceInfo.total - e.detailCalculatorPriceInfo.discountPromotion,
                totalDiscount: e.detailCalculatorPriceInfo.discountPromotion,
              };
            }),
          seriesCodes: seriesCodesVoucher,
          totalBill: createOrderData.totalBill,
          paymentCode: createOrderData.paymentRequestCode,
        });
        voucherDoiTac = verifyVoucherData?.validVouchers?.filter(
          (e) => e.channel?.typeCode === TypeVoucherForPromotion.Partner,
        );
        voucher = verifyVoucherData;
      }

      // xoá những sản phẩm dùng sau
      // for (const voucherInValidEntry of getCartData?.voucherGenerate?.vouchersInValid) {
      //   const { quantity, voucherCode } = voucherInValidEntry;
      //   for (let index = 0; index < quantity; index++) {
      //     const indexVoucher = voucher?.validVouchers.findIndex(
      //       (validVoucherEntry) =>
      //         validVoucherEntry.define.code === voucherCode &&
      //         validVoucherEntry.orderCode === createOrderData.orderCode,
      //     );
      //     if (indexVoucher === -1) continue;
      //     _.pullAt(voucher?.validVouchers, indexVoucher);
      //   }
      // }
      // Gán lại tiền với danh sách được sử dụng ngay

      const voucherCalculatePriceDto: CalculatePriceDto = {
        sessionId: getCartData.headerData?.sessionId,
        orders: getCartData.listCartSelected
          .filter((e) => e.itemType === ItemType.Product)
          .map((e) => {
            const { itemCart, detailCalculatorPriceInfo, quantity } = e;
            const { price, total, discountPromotion, discount } = detailCalculatorPriceInfo;
            const totalPriceAfterDiscount = Math.max(0, total - discountPromotion - discount);
            return {
              itemCode: itemCart,
              price: price,
              quantity,
              totalPriceAfterDiscount: totalPriceAfterDiscount,
              totalDiscount: discountPromotion + discount > total ? total : discountPromotion + discount,
            };
          }),
        coupons: voucher?.validVouchers.map((e) => {
          return {
            coupon: e.define.code,
            pricePerQuantity: e.amount,
            quantity: 1,
            serial: e.seriesCode,
          };
        }),
      };

      if (voucher?.validVouchers?.length && voucherCalculatePriceDto.coupons.length) {
        const { coupons } = await this.voucherCoreService.calculatePrice(voucherCalculatePriceDto);
        const arrSerial = coupons.filter((e) => e.canApply === true && e.voucherDiscount > 0).map((e) => e.serial);
        voucher.validVouchers = voucher.validVouchers.filter((e) => arrSerial.includes(e.seriesCode));
        voucher.validVouchers.forEach((e) => {
          const couponFind = coupons.find((couponEntry) => couponEntry.serial === e.seriesCode);
          if (!couponFind) return;
          e.amount = couponFind.voucherDiscount;
        });
      }

      // book mã ưu đãi
      const bookVoucherDto: BookVoucherDto = {
        fromSystem: 'RSA',
        phoneNumber: createOrderData.phone,
        orderCode: createOrderData.orderCode,
        paymentCode: createOrderData.paymentRequestCode,
        shopCode: createOrderData.shopCode,
        seriesCodes: appliedGiftCodes.map((e) => e.serial),
      };
      if (bookVoucherDto?.seriesCodes?.length) {
        try {
          await this.voucherCoreService.bookVoucher(bookVoucherDto);
        } catch (error) {
          Logger.log({
            fields: {
              info: 'bookVoucher',
              errors: error,
            },
          });
        }
      }

      // add voucher đối tác lại
      voucher.validVouchers = voucher.validVouchers.concat(voucherDoiTac);

      // Handle voucher đối tác FV-16579
      for (const voucherDetail of voucher.validVouchers) {
        if (isOverwriteVoucher) {
          voucherDetail.orderCode = createOrderData?.orderCode || '';
        }
      }

      return { voucher, errorVoucher: null };
    } catch (error) {
      Logger.error({
        fields: {
          info: '[Generate Voucher]  handle error',
          errors: error,
        },
      });
      return { voucher: null, errorVoucher: _.omit(error?.response, ['config']) || null };
    }
  }

  async addCartConfirmUtil(
    sessionId?: string,
    order?: CreateOrderRes,
    loyalty?: LoyaltyLib,
    checkPromotion?: string,
    listCartSelected?: ListCartSelectedLib[],
    voucher?: VerifyVoucherResponse,
    calculatorPriceInfo?: CalculatorPriceInfoLib,
  ) {
    const cartConfirmDto: AddCartConfirmLibDto = {
      sessionId: sessionId,
      calculatorPriceInfo: {
        ...calculatorPriceInfo,
        details: calculatorPriceInfo?.details?.map((detail) => ({ ...detail, metaData: '' })),
      },
      customerId: order?.custCode,
      loyalty: loyalty,
      orderCode: order?.orderCode,
      voucherInfo:
        voucher?.validVouchers?.map((e) => ({
          ...e,
          price: e?.amount,
          partnerName:
            e?.channel?.typeCode === TypeVoucherForPromotion.Loyalty ? e?.channel?.typeCode : e?.provider?.code,
        })) || [],
      checkPromotion: checkPromotion,
      promotionInfo: [],
      cartItems: listCartSelected?.map((e) =>
        plainToInstance(CartItemForCartConfirm, e, {
          exposeUnsetFields: false,
          excludeExtraneousValues: true,
        }),
      ),
      shipmentInfo: null,
    };
    await this.cartAppService.addCartConfirm(cartConfirmDto);
    // save redis
    this.redisService.set(`${CART_CONFIRM_REDIS_KEY}:${order?.orderCode}`, JSON.stringify(cartConfirmDto), 'EX');
  }

  /**
   * @description confirm promotion
   *  - sử dụng cho trường hợp paymentType của ticket = 4
   */
  async confirmPromotion(orderCode: string, cartConfirm: AddCartConfirmLibResponse, orderRes?: GetOneOrderLibResponse) {
    let orderData = orderRes || null;
    if (!orderData) {
      orderData = await this.omsService.getOneOrder(orderCode);
    }
    const checkPromotion = parseJson(cartConfirm?.cartAppInfo)?.checkPromotion || null;
    if (!checkPromotion) return true;

    let shopCode = this.req.headers?.['shop-code'] as string;
    if (
      !shopCode &&
      (this.req.headers?.['order-channel'] as string) &&
      OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string)
    ) {
      shopCode = orderData.shopCode;
    }

    const checkPromotionClone: CheckPromotionResponse = _.cloneDeep(checkPromotion);
    if (checkPromotionClone?.items?.length > 0) {
      checkPromotionClone.items.forEach((e) => {
        e.promotions = e.promotions.filter((promotionEntry) => promotionEntry.applied == true);
      });
    }

    if (checkPromotionClone?.promotions?.length) {
      checkPromotionClone.promotions = checkPromotionClone.promotions.filter((e) => e.applied == true);
    }

    const confirmPromotionDto: ConfirmPromotionDto = {
      ...checkPromotionClone,
      sessionId: cartConfirm?.sessionId || '',
      customerId: orderData.custCode,
      email: orderData?.email || checkPromotionClone?.email || '',
      customerPhone: orderData.phone,
      orderCode: orderData.orderCode,
      orderChannel: 'WebRSA',
      orderType: orderData.orderType,
      orderStore: 'Vaccine',
      shopCode,
      totalAmount: orderData.total,
      totalDiscountAmount: orderData.totalDiscount,
      shopName: orderData.shopName,
      paymentCode: orderData.paymentRequestCode,
      finalAmount: orderData.totalBill,
      items: (checkPromotionClone?.items as unknown as ItemConfirmDto[]) || [],
      promotions: checkPromotionClone?.promotions || [],
    };
    confirmPromotionDto?.items.forEach((e) => {
      const itemFind = orderData.details.find(
        (detailEntry) => detailEntry.itemCode === e.itemCode && detailEntry.isPromotion === 'N',
      );
      if (!itemFind) return;
      e.itemName = itemFind.itemName;
      e.unitName = itemFind.unitName;
    });

    await this.promotionService.confirmPromotion(confirmPromotionDto);
    return true;
  }

  /**
   * @description confirm promotion for family package
   */
  async confirmPromotionForFamilyPackage(
    orderCodeChilds: string[],
    resPromotionOfCartSum: ResPromotionCartTotal,
    orderData: GetOneOrderLibResponse,
  ) {
    const confirmPromotionDto = {
      customerId: orderData?.custCode,
      customerPhone: orderData?.phone || resPromotionOfCartSum?.phone || '',
      email: orderData?.email || resPromotionOfCartSum?.email || '',
      orderCode: '',
      paymentCode: orderData?.paymentRequestCode,
      orderChannel: 'WebRSA',
      orderType: orderData?.orderType,
      orderStore: 'Vaccine',
      shopCode: orderData?.shopCode,
      shopName: orderData?.shopName,
      totalAmount: resPromotionOfCartSum?.orderAmount,
      totalDiscountAmount: 0,
      finalAmount: 0,
      serviceFee: resPromotionOfCartSum?.serviceFee,
      vaccinationCode: '',
      familyRank: resPromotionOfCartSum?.familyRank,
      items: [],
      promotions: resPromotionOfCartSum?.promotions,
      orderCodeChildCarts: orderCodeChilds,
    };
    confirmPromotionDto?.items.forEach((e) => {
      const itemFind = orderData.details.find(
        (detailEntry) => detailEntry.itemCode === e.itemCode && detailEntry.isPromotion === 'N',
      );
      if (!itemFind) return;
      e.itemName = itemFind.itemName;
      e.unitName = itemFind.unitName;
    });

    await this.promotionService.confirmPromotion(confirmPromotionDto as any);
    return true;
  }

  /**
   * @TODO Tách nợ khuyến mãi
   */
  async debitPromotion(orderCode: string, ticketCode: string, orderRes?: GetOneOrderLibResponse) {
    let orderData = orderRes || null;
    if (!orderData) {
      orderData = await this.omsService.getOneOrder(orderCode);
    }
    if (
      [OrderStatus.Cancel, OrderStatus.Completed, OrderStatus.FullReturn, OrderStatus.PartialReturn].includes(
        orderData?.orderStatus,
      )
    ) {
      return true;
    }

    let shopCode = this.req.headers?.['shop-code'] as string;
    if (!shopCode || OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string)) {
      shopCode = orderData.shopCode;
    }
    await this.cancelBook(shopCode, orderData?.modifiedBy || orderData?.createdBy, ticketCode);

    const arrDetailOfPromotion = orderData?.details?.filter((e) => e.isPromotion === 'Y');
    if (!arrDetailOfPromotion?.length) {
      await this.bookingInventory(
        orderData?.modifiedBy || orderData?.createdBy,
        ticketCode,
        [],
        shopCode,
        orderCode,
        true,
      );
      return true;
    }
    const arrItemCode = _.uniq(
      _.compact(
        arrDetailOfPromotion?.map((e) => {
          if (e?.isInventoryManagement === 'Y') return e?.itemCode;
        }),
      ),
    );
    if (!arrItemCode?.length) {
      await this.bookingInventory(
        orderData?.modifiedBy || orderData?.createdBy,
        ticketCode,
        [],
        shopCode,
        orderCode,
        true,
      );
      return true;
    }
    const { inventories } = await this.imsService.getListStockMedicAtShop({
      skuCodes: arrItemCode,
      shopCodes: [shopCode],
    });

    //check tồn + adjust dơn + đơn tách nợ khuyến mãi
    const detailCreateOrderDebitPromotion: Array<Partial<DetailLib>> = [];
    const detailAdjust: Array<Partial<DetailLib>> = [];
    orderData?.details?.forEach((e) => {
      // không có phải khuyến mãi => bỏ hoặc k quản lý tồn kho
      if (e?.isPromotion !== 'Y' || e.isInventoryManagement === 'N') {
        detailAdjust.push(e);
        return;
      }
      const inventoryFind = inventories.find(
        (inventoryEntry) => inventoryEntry.sku === e.itemCode && inventoryEntry.whsCode === e.whsCode,
      );
      // còn tồn kho bỏ qua
      if (inventoryFind?.quantityAvailable >= e.quantity) {
        detailAdjust.push(e);
        return;
      }

      /**
       * @TODO
       *  - Tách nợ khuyến mãi
       *  - 1. Tách hết
       *  - 2. Tách 1 phần
       */

      if (!inventoryFind?.quantityAvailable || inventoryFind?.quantityAvailable < 0) {
        // Tách hết
        detailCreateOrderDebitPromotion.push(e);
      } else if (inventoryFind?.quantityAvailable > 0 && inventoryFind?.quantityAvailable < e.quantity) {
        // Trường hợp Tồn ít hơn số lượng tặng
        // Tách 1 phần
        const detailCreateOrderDebitPromotionEntry = {
          ...e,
          quantity: e.quantity - inventoryFind?.quantityAvailable,
        };
        detailCreateOrderDebitPromotion.push(detailCreateOrderDebitPromotionEntry);
        detailAdjust.push({ ...detailCreateOrderDebitPromotionEntry, quantity: inventoryFind?.quantityAvailable });
      }
    });

    if (!detailCreateOrderDebitPromotion?.length) {
      // Book lại km tặng khi đủ tồn km
      const listGiftProduct = orderData?.details?.filter(
        (e) => e?.isPromotion === 'Y' && e?.isInventoryManagement === 'Y',
      );
      await this.bookingInventory(
        orderData?.modifiedBy || orderData?.createdBy,
        ticketCode,
        listGiftProduct,
        shopCode,
        orderCode,
        true,
      );
      return true;
    }

    const detailDebitPromotionNew: Array<Partial<DetailLib>> = [];
    detailCreateOrderDebitPromotion?.forEach((e) => {
      const indFind = detailDebitPromotionNew?.findIndex((i) => i.itemCode === e.itemCode && e.unitCode === i.unitCode);
      if (indFind === -1) {
        detailDebitPromotionNew.push(e);
      } else {
        detailDebitPromotionNew[indFind].quantity += e.quantity;
      }
    });

    /**
     * @TODO
     * - Tạo payment 0đ
     * - Tạo đơn hàng
     * - Adjust đơn So1
     */

    const createAdjustPayment = await this.paymentGWService.createAdjustPayment({
      amount: 0,
      createdBy: orderData?.modifiedBy || orderData?.createdBy,
      type: TypePayment.PICK_UP,
      paymentCode: '',
      paymentSourceType: PaymentSourceType.OMS,
      sourceCode: orderData?.orderCode || '',
    });
    /**
     * @TODO Tạo đơn tách nợ
     */
    const createOrderDto: CreateOrderDto = {
      ...orderData,
      orderType: String(OrderType.PromotionDebit),
      orderPaymentCreate: [
        {
          paymentAmount: createAdjustPayment?.total,
          paymentCode: createAdjustPayment?.paymentCode,
          paymentDate: createAdjustPayment?.paymentDate,
          paymentStatus: createAdjustPayment?.status,
          paymentType: +createAdjustPayment?.type,
        },
      ],
      orderIdBase: orderData?.orderCode,
      paymentRequestCode: createAdjustPayment?.paymentCode,
      paymentRequestID: createAdjustPayment?.id,
      shipmentPlannings: [],
      details: detailDebitPromotionNew.map((e) => {
        const price = e?.detailAttachments?.reduce((prev, curr) => (prev += curr.price), 0);
        e.totalBill = price || 0;
        e.total = price || 0;
        e.price = price || 0;
        return e;
      }),
      orderAttribute: 0,
      orderIdInter: '',
    };
    const orderDebitPromotion = await this.omsService.createOrder(createOrderDto);

    /**
     * @TODO Cập nhật lại đơn So1
     */
    const adjustOrderDto: UpdateOrderLibDto = {
      ...orderData,
      orderType: String(OrderType.OrderContract),
      details: detailAdjust.map((e) => {
        const price = e?.detailAttachments?.reduce((prev, curr) => (prev += curr.price), 0);
        e.totalBill = price || 0;
        e.total = price || 0;
        e.price = price || 0;
        return e;
      }),
      shipmentPlannings: orderData?.shipmentPlannings?.map((e) => ({ planningId: e.planningID })),
      orderIdBase: orderDebitPromotion?.orderCode,
      modifiedBy: orderData?.modifiedBy || orderData?.createdBy,
      modifiedByName: orderData?.modifiedByName || orderData?.createdByName,
      promotions: orderData?.promotionGroup || [],
    };

    // book lại km khi tách 1 phần
    const listGiftProduct = detailAdjust?.filter((e) => e?.isPromotion === 'Y' && e?.isInventoryManagement === 'Y');
    await this.cancelBook(shopCode, orderData?.modifiedBy, ticketCode);
    await this.bookingInventory(
      orderData?.modifiedBy || orderData?.createdBy,
      ticketCode,
      listGiftProduct,
      shopCode,
      orderCode,
      true,
    );

    /**
     * @TODO create book nợ km
     */
    await this.bookingInventory(
      orderData?.modifiedBy || orderData?.createdBy,
      '',
      detailDebitPromotionNew,
      shopCode,
      orderDebitPromotion?.orderCode,
      true,
    );

    await this.omsService.updateOrder(orderData?.orderCode, adjustOrderDto);
    return true;
  }

  /**
   * @TODO Mapping mũi thứ khi chỉ đỉnh
   */
  async _mappingOrderInjection(tickets: CreateTicketDto[]) {
    const his = await this.vacHistoryService.getByLcvId(tickets?.at(0)?.lcvId, true);
    for (const ticket of tickets) {
      for (const schedule of ticket.schedules) {
        const hisFind = his?.find(
          (e) =>
            e?.diseaseGroupId === schedule?.diseaseGroupId &&
            isSameDate(new Date(), new Date(e.vaccinatedDate)) &&
            isSameDate(new Date(), new Date(schedule.appointmentDate)) &&
            schedule?.status !== 2 &&
            schedule?.status !== 3,
        );
        if (!hisFind) continue;
        throw new SystemException(
          {
            code: ErrorCode.RSA_HISTORY_DUP_INJECTION_DATE,
            message: ErrorCode.getError(ErrorCode.RSA_HISTORY_DUP_INJECTION_DATE)?.replace(
              '{vaccineName}',
              schedule?.vaccineName,
            ),
            details: ErrorCode.getError(ErrorCode.RSA_HISTORY_DUP_INJECTION_DATE)?.replace(
              '{vaccineName}',
              schedule?.vaccineName,
            ),
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    const { items } = await this.vacOrderInjectionService.searchByPerson({
      personCode: tickets?.at(0)?.lcvId,
      skipCount: 0,
    });

    this.filterDupTicketSchedule(tickets);

    tickets?.forEach((ticket) => {
      ticket.orderCodeOld = '';
      const arrChangeValue = [];
      ticket?.indications?.forEach((indication) => {
        if (indication?.orderDetailAttachmentCode) return;
        const orderInjectionFind = items.find(
          (item) =>
            item?.sku === indication?.sku && item?.orderInjections === indication?.orderInjections && item.status === 1,
        );
        // Không tìm thấy mũi  => Lấy mũi thứ thấp nhất chưa mua
        if (!orderInjectionFind) {
          const dataPush = {
            sku: indication.sku,
            orderInjections: indication.orderInjections,
            orderInjectionsChange: null,
          };

          const orderInjectionMin = _.minBy(
            items.filter((e) => e.sku === indication.sku && e.status === 1),
            'orderInjections',
          )?.orderInjections;
          // Có thông tin mũi thứ của SKU này => Mới change
          if (orderInjectionMin) {
            indication.orderInjections = orderInjectionMin;
            dataPush.orderInjectionsChange = indication.orderInjections;
            arrChangeValue.push(dataPush);
          }
        } else {
          indication.orderInjections = orderInjectionFind?.orderInjections || 1;
        }
      });
      ticket?.schedules.forEach((schedule) => {
        if (schedule?.orderDetailAttachmentCode) return;
        const indicationFind = arrChangeValue.find(
          (indication) =>
            indication?.sku === schedule?.sku && indication?.orderInjections === schedule?.orderInjections,
        );
        if (!indicationFind) return;
        schedule.orderInjections = indicationFind?.orderInjectionsChange;
      });
    });

    return tickets;
  }

  /**
   * @TODO filter lại ticket schedule với trùng data nhưng status khác nhau
   */
  filterDupTicketSchedule(tickets: CreateTicketDto[]) {
    //  Mũi có status !== 3 thì Remove all tất cả status = 3
    tickets?.forEach((ticket) => {
      const scheduleStatusRemove = ticket?.schedules?.filter((e) => e.status === EnmScheduleStatus.CANCELED);
      Logger.log(`scheduleStatusRemove: ${JSON.stringify(scheduleStatusRemove)}`);
      Logger.log(`[Before] TicketSchedule: ${JSON.stringify(ticket.schedules)}`);
      scheduleStatusRemove?.forEach((schedule) => {
        const scheduleFind = ticket?.schedules.find(
          (e) =>
            e.sku === schedule.sku &&
            e.orderInjections === schedule.orderInjections &&
            e.regimenId === schedule.regimenId &&
            e?.status !== EnmScheduleStatus.CANCELED,
        );
        if (scheduleFind) {
          _.remove(
            ticket?.schedules,
            (e) =>
              e.sku === schedule.sku &&
              e.orderInjections === schedule.orderInjections &&
              e.status === EnmScheduleStatus.CANCELED,
          );
        }
      });
      Logger.log(`[After] TicketSchedule: ${JSON.stringify(ticket.schedules)}`);
    });
    return tickets;
  }

  async updateStatusDeposit(orderCode: string, modifiedBy?: string, modifiedByName?: string) {
    const { orders } = await this.omsService.getListOrderES({ orderCode: [orderCode] });

    const employeeStep5 = _.orderBy(
      orders?.at(0)?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const updateStatusDto: PayloadUpdatedStatusOrderDto = {
      orderCode: orderCode,
      modifiedBy: employeeStep5?.employeeCode || modifiedBy,
      modifiedByName: employeeStep5?.employeeName || modifiedByName,
      orderStatus: OrderStatus.FinishDeposit,
      ecomDisplay: EcomDisplay.AtShop,
      orderType: orders?.at(0)?.orderType,
      shopCode: orders?.at(0)?.shopCode,
    };
    return await this.omsService.updateStatusOrderDeposit(updateStatusDto);
  }

  async addRegimenDetailClose(createTicketDto: CreateTicketDto, createdBy: string) {
    const { schedules } = createTicketDto;
    const arrCreateRegimenDetailDto: Array<CreateRegimenDetailCloseDto> = [];
    for (const schedule of schedules) {
      if (schedule?.status !== EnmScheduleStatus.CANCELED) continue;
      arrCreateRegimenDetailDto.push({
        appointmentDate: schedule?.appointmentDate,
        sku: schedule?.sku,
        skuName: schedule?.vaccineName,
        regimenId: schedule?.regimenId,
        lcvId: createTicketDto?.lcvId,
        status: 1,
        createdBy: createdBy,
      });
    }
    if (!arrCreateRegimenDetailDto?.length) return true;
    await this.vacOrderInjectionService.createRegimenDetailClose(arrCreateRegimenDetailDto);
  }

  async validateSellRestrictProducts(
    items: Array<ListCartSelectedLib> = [],
    errorCode: string = ErrorCode.RSA_SKU_SELL_RESTRICT,
    arrayCreateTicketDto?: Array<CreateTicketDto>,
  ) {
    //#region Validate sell restrict products
    const sellRestrictAttributeIds = [9665, 9666, 9668];
    const sellRestrictSkus = [];
    const sellRestrictNames = [];
    let products = [];

    const skus = items?.filter((y) => y?.itemType === 1)?.map((x) => x?.itemCart) || [];

    if (!_.isEmpty(_.compact(skus))) {
      try {
        const { listProduct } = await this.pimAppService.getListProductBySku(skus);
        products = listProduct;
      } catch (error) {}
    }

    const productMap = products.reduce((map, item) => {
      map[item.sku] = { attributeOptionId: item?.attributeShop?.attributeOptionId, name: item?.name };
      return map;
    }, {});

    _.forEach(items, (item) => {
      if (
        // !item?.objectGroupId && normal product
        // item?.objectGroupId &&  combo
        _.includes(sellRestrictAttributeIds, productMap?.[item?.itemCart]?.attributeOptionId)
      ) {
        sellRestrictSkus.push(item?.itemCart);
        sellRestrictNames.push(productMap?.[item?.itemCart]?.name);
      }
    });

    if (!_.isEmpty(_.compact(sellRestrictSkus))) {
      throw new SystemException(
        {
          code: errorCode,
          message: ErrorCode.getError(errorCode)?.replace('{name}', sellRestrictNames.join(', ')),
          details: ErrorCode.getError(errorCode)?.replace('{name}', sellRestrictNames.join(', ')),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }
    //#endregion

    //#region chặn 4 mũi hẹn trong cùng 1 ngày
    const arrTicketSchedule: ScheduleDto[] = JSONPath({
      path: '$[*].schedules[*]',
      json: arrayCreateTicketDto,
    });
    const transfromSchedule = arrTicketSchedule?.map((scheduleEntry) => ({
      ...scheduleEntry,
      formatDate: moment(scheduleEntry.appointmentDate).utcOffset(7).format('YYYY-MM-DD'),
    }));
    let isException: boolean = false;
    const groupWithDate = _.groupBy(transfromSchedule, 'formatDate');
    Logger.log(`groupWithDate: ${JSON.stringify(groupWithDate)}`);
    _.forEach(groupWithDate, (scheduleEntry: Array<ScheduleDto>) => {
      if (isException) return;
      const arrScheduleWithStatus = scheduleEntry?.filter((e) => e.status === 0 || e.status === 1);
      if (arrScheduleWithStatus?.length > 4) {
        isException = true;
        return;
      }
    });

    if (isException) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_TICKET_4_IN_DATE,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    //#endregion

    // chặn không cho chuyển đơn vị tính với sku 00038230 VIÊM GAN B, regimen: bf4f4641-890e-44e7-9fae-8dd0cf13ab7e
    // sku chặn khi chuyển đơn vị tính
    const skuBlock = '00038230';
    // regiment ID không cho chọn lo với sku trên
    const regimenBlock = 'bf4f4641-890e-44e7-9fae-8dd0cf13ab7e';
    // chặn không chọn lọ đối với regiemId trên của sku trên với unitCode === 7 lọ
    const itemBlockUnit = {
      isBlockUnit: false,
      unit: '',
      scheduleType: '',
    };
    const itemSku = items?.find((i) => i?.itemCart === skuBlock);
    if (itemSku) {
      // regimenID cho phép chọn liều và unitCode khác liều
      if (itemSku?.regimenId === regimenBlock && itemSku?.unitCode !== 1004) {
        itemBlockUnit.isBlockUnit = true;
        itemBlockUnit.unit = 'Liều';
        itemBlockUnit.scheduleType = itemSku?.scheduleType;
      }
      // regimenId khác cho phép chọn liều và unitCode khác lọ
      else if (itemSku?.regimenId !== regimenBlock && itemSku?.unitCode !== 7) {
        itemBlockUnit.isBlockUnit = true;
        itemBlockUnit.unit = 'Lọ';
        itemBlockUnit.scheduleType = itemSku?.scheduleType;
      }
    }

    if (itemBlockUnit.isBlockUnit) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_BLOCK_UNIT_WITH_REGIMEN,
          message: ErrorCode.getError(ErrorCode.RSA_BLOCK_UNIT_WITH_REGIMEN)
            ?.replace('{unitCode}', itemBlockUnit.unit)
            ?.replace('{scheduleType}', itemBlockUnit?.scheduleType),
          details: ErrorCode.getError(ErrorCode.RSA_BLOCK_UNIT_WITH_REGIMEN)
            ?.replace('{unitCode}', itemBlockUnit.unit)
            ?.replace('{scheduleType}', itemBlockUnit?.scheduleType),
        },
        HttpStatus.FORBIDDEN,
      );
    }

    /// end chặn không cho chuyễn đơn vị tính với sku 00038230 VIÊM GAN B, regimen: bf4f4641-890e-44e7-9fae-8dd0cf13ab7e
    // chặn Gardasil 4  chặn bán mũi 1 đối với Nam
    const skuGardasil = '00038235';
    const findSkuGardasil = items?.find((i) => i?.itemCart === skuGardasil);
    if (findSkuGardasil) {
      // call family để check giới tính
      const person = await this.familyCoreService.getListPrimaryPerson([findSkuGardasil?.lcvId]);
      // nếu là giới tính Nam thì check tiếp mũi thứ
      if (person && person?.length > 0 && person?.at(0)?.gender === 0) {
        // check mũi thứ 1 thì chặn
        const sortOrderInjections = findSkuGardasil?.orderInjections?.sort((a, b) => a - b);
        const orderInjectionFirst = sortOrderInjections?.at(0);
        if (orderInjectionFirst === 1) {
          throw new SystemException(
            {
              code: ErrorCode.RSA_BLOCK_GARDASIL_4_FOR_NAM,
              message: ErrorCode.getError(ErrorCode.RSA_BLOCK_GARDASIL_4_FOR_NAM),
              details: ErrorCode.getError(ErrorCode.RSA_BLOCK_GARDASIL_4_FOR_NAM),
            },
            HttpStatus.FORBIDDEN,
          );
        }
      }
    }
    ///end chặn Gardasil 4  chặn bán mũi 1 đối với Nam
  }

  getStatusForTraTungPhan(
    getOrder: GetOneOrderLibResponse,
    getTicket: TicketDetailAdjustDto,
    modifiedBy: string,
  ): Array<UpdateStatusByAttachmentCodeDto> {
    const updateStatusByAttachmentCodeDto: Array<UpdateStatusByAttachmentCodeDto> = [];
    const ticketIndicationStatus5 = getTicket?.indications?.filter((e) => e.status === 5);

    if (ticketIndicationStatus5?.length > 0) {
      updateStatusByAttachmentCodeDto.push(
        ...ticketIndicationStatus5?.map((e) => ({
          orderDetailAttachmentCode: e.orderDetailAttachmentCode,
          modifiedBy: modifiedBy,
          status: 1,
        })),
      );
    } else if (getOrder?.orderAttribute === 4) {
      const arrPath: Array<DetailAttachment> = JSONPath({
        path: '$.details[*].detailAttachments[*]',
        json: getOrder,
      });
      const arrSku = arrPath.map((e) => e?.itemCode);
      const arrOrderDetailAttachmentCode = arrPath?.map((e) => e.orderDetailAttachmentCode);
      const scheduleFilter = getTicket?.schedules?.filter(
        (e) => arrSku.includes(e?.sku) && arrOrderDetailAttachmentCode.includes(e.orderDetailAttachmentCode),
      );
      const minWithDate = _.minBy(scheduleFilter, (e) => e?.appointmentDate);
      scheduleFilter?.forEach((e) => {
        const isStatus1 = isSameDate(minWithDate?.appointmentDate, e?.appointmentDate);
        if (isStatus1) {
          updateStatusByAttachmentCodeDto.push({
            modifiedBy: modifiedBy,
            orderDetailAttachmentCode: e.orderDetailAttachmentCode,
            status: 1,
          });
          return;
        }
        updateStatusByAttachmentCodeDto.push({
          modifiedBy: modifiedBy,
          orderDetailAttachmentCode: e.orderDetailAttachmentCode,
          status: 5,
        });
      });
    }

    return updateStatusByAttachmentCodeDto;
  }

  async getIdLinkFromEcom(orderCode) {
    try {
      if (!orderCode) return '';
      // check header
      let scheduleRequests;
      if (OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string)) {
        // ecom
        const { items } = await this.scheduleRequestsService.getManyOrders({ orderCodes: [orderCode] });
        scheduleRequests = items;
      } else {
        // rsa
        const { items } = await this.rsaEcomService.getScheduleRequestByOrderCodes([orderCode]);
        scheduleRequests = items;
      }

      const idLink = scheduleRequests?.length ? scheduleRequests?.[0]?.idLink || '' : '';
      return idLink;
    } catch (error) {
      Logger.error({
        fields: {
          info: '[getIdLinkFromEcom] handle error',
          errors: error,
        },
      });
      return '';
    }
  }

  async checkHandlePriorityOrder(
    arrOrderAffiliate: Array<GetOneOrderLibResponse>,
    orderAttribute: number,
    itemCarts: string[] = [],
    orderCurrent: GetOneOrderLibResponse,
    arrOrderWebAppTTOLFailed: OrdersInfo[],
  ) {
    const orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    // if (!OrderChannels.RSA_AFFILIATE.includes(orderChannel)) return;
    let removeOrderCurrent: Array<GetOneOrderLibResponse> = arrOrderAffiliate?.filter(
      (e) => e.orderStatus === OrderStatus.FinishDeposit,
    );

    const arrOrderWebAppTTOLFailedFinal = orderCurrent?.orderCode
      ? arrOrderWebAppTTOLFailed?.filter((e) => e.orderCode !== orderCurrent?.orderCode)
      : arrOrderWebAppTTOLFailed;

    if (orderCurrent) {
      removeOrderCurrent = removeOrderCurrent?.filter((order) => order?.orderCode !== orderCurrent?.orderCode);
    }

    if (OrderChannels.RSA_AFFILIATE.includes(orderChannel)) {
      if (removeOrderCurrent?.length) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_CREATE_ORDER_AFFILIATE,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    } else if (OrderChannels.RSA_ECOM.includes(orderChannel)) {
      // có đơn vệ tin 0 đồng.
      // Chặn nếu có đơn cọc vệ tinh 0đ
      // bypass rule nếu thuộc đơn webapp thanh toán thất bại
      const isWebAppDepositFailed =
        OrderChannels.WEB_APP.includes(orderCurrent?.orderChanel) &&
        orderCurrent?.ecomDisplay === EcomDisplay.AtOnline &&
        orderCurrent?.orderStatus === OrderStatus.Confirmed;
      if (removeOrderCurrent?.length && orderAttribute !== OrderAttribute.PRE_ORDER) {
        // Chặn nếu có đơn cọc vệ tinh đã thanh toán + trùng nhóm bệnh
        const paidOrders = arrOrderAffiliate?.filter((e) =>
          e?.orderPaymentCreate?.some((order) => order?.paymentType === 1 && order?.paymentStatus === 4),
        );

        if (paidOrders?.length) {
          const currentOrdertItems = JSONPath({
            path: '$..detailAttachments..itemCode',
            json: paidOrders,
          });

          const diseaseGroup = await this.regimenService.getListDiseaseGroupBySku(
            _.uniq([...itemCarts, ...currentOrdertItems]),
          );

          const currentDideaseGroupIds =
            diseaseGroup
              ?.filter((disease) => currentOrdertItems.includes(disease?.sku))
              ?.map((disease) => disease?.diseaseGroupId) || [];
          const dideaseGroupCartIds =
            diseaseGroup
              ?.filter((disease) => itemCarts.includes(disease?.sku))
              ?.map((disease) => disease?.diseaseGroupId) || [];

          const commonItems = _.intersection(currentDideaseGroupIds, dideaseGroupCartIds);

          if (commonItems?.length) {
            const diseaseGroupNames =
              _.chain(diseaseGroup)
                ?.filter((disease) => commonItems.includes(disease?.diseaseGroupId))
                ?.uniqBy('diseaseGroupId')
                ?.map('diseaseGroupName')
                ?.join(', ')
                ?.value() || '';

            throw new SystemException(
              {
                code: ErrorCode.ECOM_PENDING_AFFILIATE_WITH_SAME_DISEASE,
                message: ErrorCode.getError(ErrorCode.ECOM_PENDING_AFFILIATE_WITH_SAME_DISEASE).replace(
                  '{diseaseGroupNames}',
                  diseaseGroupNames,
                ),
                details: ErrorCode.getError(ErrorCode.ECOM_PENDING_AFFILIATE_WITH_SAME_DISEASE).replace(
                  '{diseaseGroupNames}',
                  diseaseGroupNames,
                ),
              },
              HttpStatus.FORBIDDEN,
            );
          }
        } else {
          if (!isWebAppDepositFailed) {
            throw new SystemException(
              {
                code: ErrorCode.ECOM_CREATE_ORDER_AFFILIATE_HAS_ZERO_DEPOSIT,
              },
              HttpStatus.FORBIDDEN,
            );
          }
        }
      }

      if (
        arrOrderWebAppTTOLFailedFinal?.length &&
        !isWebAppDepositFailed &&
        orderAttribute !== OrderAttribute.PRE_ORDER
      ) {
        throw new SystemException(
          {
            code: ErrorCode.ECOM_HANDEL_WEB_APP_TTOL_TT,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }
  }

  checkRuleContinueBuyingForAffiliate(
    arrOrderAffiliate: Array<GetOneOrderLibResponse>,
    getOrder: GetOneOrderLibResponse,
  ) {
    const orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    if (
      OrderChannels.RSA_AFFILIATE.includes(getOrder?.orderChanel) &&
      [...OrderChannels.RSA_AFFILIATE].includes(orderChannel)
    ) {
      const removeOrderCurrent: Array<GetOneOrderLibResponse> = arrOrderAffiliate?.filter(
        (e) => e.orderCode !== getOrder?.orderCode,
      );

      if (removeOrderCurrent?.length) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_CREATE_ORDER_AFFILIATE,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }
    return true;
  }

  formatVaccineResponse(
    getCart: GetCartLibResponse,
    detailAttachments: DetailAttachment[],
    regimensInfo: RegimenItem[],
  ) {
    return detailAttachments.map((item) => {
      const cartItem = getCart?.listCartSelected?.find((productItem) => productItem.productInfo?.sku === item.itemCode);
      const cartProductInfo = cartItem?.productInfo;
      const regimen = regimensInfo.find(
        (regimentItem) => cartItem?.regimenId && regimentItem.id === cartItem?.regimenId,
      );
      return {
        ...item,
        sku: item?.itemCode,
        totalWithFee: Math.max(0, (item?.price || 0) - (item?.discountPromotion || 0) - (item?.discount || 0)),
        manufactor: cartProductInfo?.manufactor,
        taxonomies: regimen?.diseaseGroup?.name || cartProductInfo?.taxonomies?.[0]?.taxonomyName || '', // BA NguyenNT confirm
        unitNameSale: item?.unitName,
        vaccineName: regimen?.vaccine?.name || '',
        scheduleType: regimen?.scheduleType || '',
        regimenName: regimen?.diseaseGroup?.name || '',
      };
    });
  }

  blockAdjustTicketWithAnotherVaccine({
    orderChannel,
    createTicketDto,
  }: {
    orderChannel: string;
    createTicketDto: CreateTicketDto[];
  }) {
    if ([...OrderChannels.RSA, ...OrderChannels.RSA_ECOM].includes(orderChannel)) {
      const schedule = createTicketDto?.[0]?.schedules?.find((e) => e.sku === '00047587');
      if (schedule) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_BLOCK_ADJUST_TICKET_ANOTHER_VACCINE,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }

  /**
   * https://reqs.fptshop.com.vn/browse/FV-13047
   * đơn aff/mRsa chặn không cho add sp hàng khan hiếm/tạm hết hàng
   */
  async validateSellRestrictStockProducts(items: Array<ListCartSelectedLib> = []) {
    // 9671 hàng khan hiếm
    // 9665 tạm hết hàng
    // 9668 tạm hết hàng
    const sellRestrictAttributeIds = [9671, 9665, 9668];
    let products = [];

    const skus = items?.filter((y) => y?.itemType === 1)?.map((x) => x?.itemCart) || [];
    if (!_.isEmpty(_.compact(skus))) {
      try {
        const { listProduct } = await this.pimAppService.getListProductBySku(skus);
        products = listProduct;
      } catch (error) {}
    }

    const productMap = products.reduce((map, item) => {
      map[item.sku] = { attributeOptionId: item?.attributeShop?.attributeOptionId, name: item?.name };
      return map;
    }, {});

    _.forEach(items, (item) => {
      if (_.includes(sellRestrictAttributeIds, productMap?.[item?.itemCart]?.attributeOptionId)) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_SKU_STOCK_RESTRICT_AT_PLACE_ORDER,
            message: ErrorCode.getError(ErrorCode.RSA_SKU_STOCK_RESTRICT_AT_PLACE_ORDER)?.replace(
              '{name}',
              productMap?.[item?.itemCart]?.name,
            ),
            details: ErrorCode.getError(ErrorCode.RSA_SKU_STOCK_RESTRICT_AT_PLACE_ORDER)?.replace(
              '{name}',
              productMap?.[item?.itemCart]?.name,
            ),
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    });
  }

  private validateOrderCode(orders, orderCode) {
    // Filter orders by source priority
    const arrOrderAffiliate = orders.filter((item) =>
      [OrderChanel.FromVaccineShopAffiliate, OrderChanel.FromMobileAffiliate].includes(item.source),
    );
    const arrOrderEcom = orders.filter((item) =>
      [OrderChanel.FromWebEcom, OrderChanel.FromAppEcom, OrderChanel.FromRSAEcom].includes(item.source),
    );

    // Check orderCode in priority order
    if (arrOrderAffiliate?.length) {
      // Check in priority 1 first
      if (!arrOrderAffiliate?.find((item) => item?.orderCode === orderCode)) {
        const orderCodes = _.uniq(
          _.compact(
            JSONPath({
              json: arrOrderAffiliate,
              path: '$.[*].orderCode',
            }),
          ),
        ).join(', ');
        throw new SystemException(
          {
            code: ErrorCode.RSA_CHECK_ORDER_PRIORITY_AFFILIATE,
            message: ErrorCode.getError(ErrorCode.RSA_CHECK_ORDER_PRIORITY_AFFILIATE)?.replace('{orders}', orderCodes),
            details: ErrorCode.getError(ErrorCode.RSA_CHECK_ORDER_PRIORITY_AFFILIATE)?.replace('{orders}', orderCodes),
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    } else if (arrOrderEcom?.length) {
      // Then check in priority 2
      if (!arrOrderEcom?.find((item) => item?.orderCode === orderCode)) {
        const orderCodes = _.uniq(
          _.compact(
            JSONPath({
              json: arrOrderEcom,
              path: '$.[*].orderCode',
            }),
          ),
        ).join(', ');
        throw new SystemException(
          {
            code: ErrorCode.RSA_CHECK_ORDER_PRIORITY_WEB_APP,
            message: ErrorCode.getError(ErrorCode.RSA_CHECK_ORDER_PRIORITY_WEB_APP)?.replace('{orders}', orderCodes),
            details: ErrorCode.getError(ErrorCode.RSA_CHECK_ORDER_PRIORITY_WEB_APP)?.replace('{orders}', orderCodes),
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }
    return;
  }

  async checkOrderPriority(lcvId: string[], orderCode: string) {
    const VALID_ORDER_SOURCES = [
      OrderChanel.FromVaccineShopAffiliate,
      OrderChanel.FromMobileAffiliate,
      OrderChanel.FromWebEcom,
      OrderChanel.FromAppEcom,
      OrderChanel.FromRSAEcom,
    ];

    const VALID_ORDER_STATUSES = [OrderStatus.Confirmed, OrderStatus.FinishDeposit];

    const resOrderJourney = await this.journeyService.getOrderByLcvidsAttributes({
      lcvIds: lcvId,
      source: VALID_ORDER_SOURCES,
      orderStatus: VALID_ORDER_STATUSES,
      pageNumber: 1,
      pageSize: 200,
    });

    if (resOrderJourney.totalCount) {
      // Filter orders with ecomDisplay = 1 and totalPayment !== 0 and orderAttribute !== 7
      const ordersJourney = resOrderJourney?.items?.filter(
        (item) =>
          item?.ecomDisplay === 1 &&
          item?.orderAttribute !== ORDER_ATTRIBUTE.DON_HANG_PRE_ORDER &&
          item?.totalPayment !== 0,
      );

      if (!ordersJourney?.length) {
        // Filter orders with ecomDisplay = 1 and orderAttribute !== 7
        const orders = resOrderJourney?.items?.filter(
          (item) => item?.ecomDisplay === 1 && item?.orderAttribute !== ORDER_ATTRIBUTE.DON_HANG_PRE_ORDER,
        );
        if (orders) {
          // If no orders with payment, check orders with ecomDisplay = 1
          this.validateOrderCode(orders, orderCode);
        }
      } else {
        // Validate if current order exists in orders with payment
        if (!ordersJourney?.find((item) => item?.orderCode === orderCode)) {
          const orderCodes = _.uniq(
            _.compact(
              JSONPath({
                json: ordersJourney,
                path: '$.[*].orderCode',
              }),
            ),
          ).join(', ');
          throw new SystemException(
            {
              code: ErrorCode.RSA_CHECK_ORDER_PRIORITY_PAYMENTED_AFFILIATE_WEB_APP,
              message: ErrorCode.getError(ErrorCode.RSA_CHECK_ORDER_PRIORITY_PAYMENTED_AFFILIATE_WEB_APP)?.replace(
                '{orders}',
                orderCodes,
              ),
              details: ErrorCode.getError(ErrorCode.RSA_CHECK_ORDER_PRIORITY_PAYMENTED_AFFILIATE_WEB_APP)?.replace(
                '{orders}',
                orderCodes,
              ),
            },
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    }
    return;
  }
}
