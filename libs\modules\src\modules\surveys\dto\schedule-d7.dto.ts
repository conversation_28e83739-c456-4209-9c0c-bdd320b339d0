import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { AssignSurveysDto, SurveyCustomerD7 } from 'vac-nest-osr';

export class AutoAssignScheduleD7Dto {
  @ApiProperty({ example: '42118' })
  @IsString()
  insideId: string;

  @ApiProperty({ example: 'string' })
  @IsString()
  scheduleId: string;
}

export class HandAssignScheduleD7Dto extends OmitType(AssignSurveysDto, ['assigneeDate', 'reAssigneeDate']) {
  @ApiProperty({ example: false })
  @IsBoolean()
  isReAssign: boolean;
}

export class ConsultantResultTypeDto {
  @ApiProperty({ example: 'd3d3f50e-23ec-4ac1-ae3b-ed7220630b33' })
  @IsString()
  surveyId: string;
}

export class UpdateSurveyCustomerD7 extends SurveyCustomerD7 {
  @ApiProperty()
  @IsOptional()
  @IsString()
  phoneNumberSendSMS?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  customerSendSMS?: string;
}
