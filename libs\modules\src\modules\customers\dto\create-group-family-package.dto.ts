import { ApiProperty, OmitType, PickType } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { Person } from 'vac-nest-family';

import {
  CreateFamilyPackageDto,
  CreateFamilyPackageRes,
  Evidence,
  GetFamilyPackageByLcvIdsRes,
  GetGroupAttributeRes,
  GroupPersonJunctionRes,
} from 'vac-nest-family-package';

export class CreateFamilyPackageConvertDto extends OmitType(CreateFamilyPackageDto, [
  'groupTypeId',
  'groupName',
  'description',
  'groupRankingId',
  'groupRankingName',
]) {
  @ApiProperty({
    example: '123456',
  })
  @Expose()
  otp?: string;

  @ApiProperty({
    example: '8d35ee2d-3a2f-437b-8a29-c94b4d4b71ef',
  })
  @Expose()
  groupTypeCode: string;

  @ApiProperty({
    example: '0918273645',
  })
  @Expose()
  phoneNumber: string;

  @ApiProperty({
    example: 5,
  })
  @Expose()
  templateType: number;

  @ApiProperty({
    example: 2,
  })
  @Expose()
  @IsOptional()
  verifyMethod?: number;

  @ApiProperty({
    example: '123456',
  })
  @Expose()
  @IsOptional()
  insideCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isOtpValid?: boolean;
}

export class PersonInfoRes extends PickType(Person, [
  'name',
  'phoneNumber',
  'dateOfBirth',
  'gender',
  'customerId',
  'customerAge',
  'from',
  'to',
  'ageUnitCode',
]) {}

export class GroupPersonJunctionResponse extends GroupPersonJunctionRes {
  @ApiProperty({ isArray: true, required: false, type: GetFamilyPackageByLcvIdsRes })
  @Expose()
  @IsOptional()
  @Type(() => GetFamilyPackageByLcvIdsRes)
  personAttribute?: GetFamilyPackageByLcvIdsRes[];

  @ApiProperty({ isArray: false, required: false, type: PersonInfoRes })
  @Expose()
  @IsOptional()
  @Type(() => PersonInfoRes)
  personInfo?: PersonInfoRes;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  isOwner?: boolean;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  isAccumulated?: boolean;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  listOrderEcom?: string[];

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  listOrderAff?: string[];

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  listPreOrder?: string[];
}

export class EvidenceExtendRes extends Evidence {
  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  previewUrl?: string;
}

export class CreateFamilyPackageResponse extends CreateFamilyPackageRes {
  @ApiProperty({ isArray: true, required: false, type: GroupPersonJunctionResponse })
  @Expose()
  @Type(() => GroupPersonJunctionResponse)
  groupPersonJunctions?: GroupPersonJunctionResponse[];

  @ApiProperty({ isArray: true, required: false, type: GetGroupAttributeRes })
  @Expose()
  @Type(() => GetGroupAttributeRes)
  groupAttribute?: GetGroupAttributeRes[];

  @ApiProperty({ isArray: true, required: false, type: EvidenceExtendRes })
  @Expose()
  @Type(() => EvidenceExtendRes)
  evidence?: EvidenceExtendRes[];

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  message?: string;
}
