import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FtelService as FtelLibService } from 'modules/modules/modules/ftel/services/ftel.service';
import { FtelSignInDto, FtelSignInResponseDto } from './dto/sign-in.dto';
import { SystemException, RedisService, ErrorCode } from '@shared';
import { HandleGetTokenDto } from './dto/handle-get-token.dto';
import { CreateLogFtelRecordDto, RSAIntegrationService } from 'vac-nest-rsa-integration';
import { REQUEST } from '@nestjs/core';
const redisKey = 'ftel_token';
import { Request } from 'express';

@Injectable()
export class FtelService {
  constructor(
    private readonly ftelLibService: FtelLibService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly rsaIntegrationService: RSAIntegrationService,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  async signIn(dto: FtelSignInDto, shopCode?: string): Promise<FtelSignInResponseDto> {
    try {
      const user_token = await this.getToken();
      const { status, data } = await this.handleGetToken({
        shopCode,
        staffId: dto.staffId,
        userToken: user_token,
        customerId: dto.customerId,
      });

      if (status !== HttpStatus.UNAUTHORIZED) {
        return {
          token: data?.data?.audio_link,
        };
      }
      return this.regetToken(dto, shopCode);
    } catch (e) {
      await this.handleGetTokenError(e);
    }
  }

  async getToken() {
    const token = await this.redisService.getRedisClient().get(redisKey);
    if (token) {
      return token;
    }
    const {
      data: { user_token },
    } = await this.ftelLibService.signIn({
      username: this.configService.get('FTEL_USER_NAME'),
      password: this.configService.get('FTEL_PASSWORD'),
    });
    await this.redisService.getRedisClient().set(redisKey, user_token, 'EX', 60 * 60 * 12);
    return user_token;
  }

  async handleGetToken({ customerId, staffId, userToken, shopCode }: HandleGetTokenDto) {
    const resp = await this.ftelLibService.getAudioLink({
      customer_id: customerId || 'unknown',
      shop_id: this.configService.get<string>('NODE_ENV') === 'CI' ? '123456789' : shopCode,
      staff_id: staffId,
      token: userToken,
    });
    return resp;
  }

  async regetToken(dto: FtelSignInDto, shopCode?: string): Promise<FtelSignInResponseDto> {
    await this.redisService.getRedisClient().del(redisKey);
    const user_token = await this.getToken();
    const { data, status } = await this.handleGetToken({
      shopCode,
      staffId: dto.staffId,
      userToken: user_token,
      customerId: dto.customerId,
    });

    if (status === HttpStatus.UNAUTHORIZED) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_FTEL_UNAUTHORIZED,
          message: ErrorCode.getError(ErrorCode.RSA_FTEL_UNAUTHORIZED),
          details: ErrorCode.getError(ErrorCode.RSA_FTEL_UNAUTHORIZED),
          validationErrors: null,
        },
        HttpStatus.UNAUTHORIZED,
      );
    }

    return {
      token: data?.data?.audio_link,
    };
  }

  async handleGetTokenError(e: any) {
    await this.redisService.getRedisClient().del(redisKey);
    throw new SystemException(
      {
        code: 'FTelError',
        message: e.message,
        details: e.message,
        validationErrors: null,
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }

  createLogFtelRecords(payload: CreateLogFtelRecordDto) {
    const tabId = this.req.headers?.['tab-id'];
    return this.rsaIntegrationService.createLogFtelRecords({
      ...payload,
      detail: {
        ...payload.detail,
        tabId,
      },
    });
  }
}
