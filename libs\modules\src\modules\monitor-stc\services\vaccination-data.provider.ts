import { SearchServiceProductApiService } from '@frt/nestjs-api';
import { Inject, Injectable } from '@nestjs/common';
import { concurrentPromise, EnmCartType, GUID_EMPTY } from '@shared';
import _ from 'lodash';
import { CartAppService, GetCartLibResponse, ItemType } from 'vac-nest-cart-app';
import { FamilyService } from 'vac-nest-family';
import { VaccineHistoryDetailDto, VacHistoryService } from 'vac-nest-history';
import { GetListPreOrderByLcvIdRes, OsrService } from 'vac-nest-osr';
import { PIMAppService } from 'vac-nest-pim-app';
import { RegimenService } from 'vac-nest-regimen';
import { ScheduleEngineAppService } from 'vac-nest-schedule-engine-app';
import { OrderPromissingService } from 'vac-order-promising';
import { GetMonitorStcDto } from '../dto/get-monitor-stc.dto';
import { VaccineRecordStatus } from '../enum/vaccination-book.enum';
import { VaccinationBookRes } from '../dto/response.vaccination-book.dto';
import { FamilyRuleService, SuggestRankingForCartDto } from 'vac-nest-family-rule';
import { CalculateTempPricingDto, SkuItemDto } from '../dto/calculate-temp-pricing.dto';
import { CalculateTempPricingResponseDto, MemberPriceDto } from '../dto/calculate-temp-pricing-response.dto';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { calculateTimeDifference } from 'vac-commons';
import { OrderRuleEngineService } from 'vac-nest-order-rule-engine';
import {
  CalculateDosePricingDto,
  CalculateDosePricingRes,
} from 'vac-nest-order-rule-engine/dist/dto/calculate-storage-fee.dto';
import { JSONPath } from 'jsonpath-plus';
import moment from 'moment';

@Injectable()
export class VaccinationDataProvider {
  constructor(
    private readonly familyService: FamilyService,
    private readonly regimenService: RegimenService,
    private readonly vacHistoryService: VacHistoryService,
    private readonly scheduleEngineAppService: ScheduleEngineAppService,
    private readonly searchServiceProductApiService: SearchServiceProductApiService,
    private readonly orderPromissingService: OrderPromissingService,
    private readonly pimAppService: PIMAppService,
    private readonly cartAppService: CartAppService,
    private readonly familyRuleService: FamilyRuleService,
    private readonly orderRuleEngineService: OrderRuleEngineService,

    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  /**
   * Calculate suggested ranking for cart
   * @param params Parameters for suggest ranking calculation
   * @returns Response from family rule service
   */
  async suggestRankingForCart(params: SuggestRankingForCartDto) {
    try {
      return await this.familyRuleService.suggestRankingForCart(params);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get person information by LCV ID
   */
  async getPersonByLcvId(lcvId: string, birthday: string) {
    const person = await this.familyService.getListPrimaryPerson([lcvId]);
    if (!person?.length) {
      return null;
    }
    const ageRanges = await this.regimenService.getAgeRanges();

    person[0].customerAge = calculateTimeDifference(
      person?.at(0)?.dateOfBirth || birthday,
      person?.at(0)?.from,
      person?.at(0)?.to,
      ageRanges,
      person?.at(0).ageUnitCode,
    );
    if (person[0].customerAge?.month || person[0].customerAge?.week || person[0].customerAge?.day) {
      person[0].customerAge.year += 1;
    }

    return person?.[0];
  }

  /**
   * Fetch all data needed for the vaccination book
   */
  async fetchAllData(personId: string, lcvId: string, query: GetMonitorStcDto) {
    const loadDiseaseGroup = this.regimenService.loadDiseaseGroupFP({
      personId,
      personCode: lcvId,
      dateOfBirth: query?.birthday || undefined,
    });

    const getHistories = this.vacHistoryService.getByPerson({ personId });

    const getSchedules = this.scheduleEngineAppService.getListInjectionScheduleByPersons({
      personIds: [personId],
      status: [0],
    });

    // const getPreOrder = this.osrService.getListPreOrderByLcvId({ listLCVId: [lcvId] });

    const cartType = (this.req.headers?.['cart-type'] as string) || '';
    const cart = this.cartAppService.getCartRedis(
      {
        sessionId: query?.sessionId,
        shopCode: query?.shopCode || (this.req.headers?.['shop-code'] as string) || '',
      },
      +cartType === EnmCartType.FAMILY_PACKAGE && {
        headers: {
          'cart-type': EnmCartType.FAMILY_PACKAGE,
          'lcv-id': lcvId,
        } as any,
      },
    );
    // const sessionIdSplit = query?.sessionId?.split(':');
    // const sessionIdBase = `${sessionIdSplit[0]}:${sessionIdSplit[1]}:${sessionIdSplit[2]}`;
    // const cartSum = this.cartAppService.getCartRedis({
    //   sessionId: sessionIdBase,
    //   shopCode: query?.shopCode,
    //   orderType: 8,
    // });

    const [
      diseaseGroups,
      histories,
      schedulesResponse,
      // listPreOrder,
      cartData,
      // cartDataSum,
    ] = await concurrentPromise(
      loadDiseaseGroup,
      getHistories,
      getSchedules,
      // getPreOrder,
      cart,
      // cartSum,
    );

    return {
      diseaseGroups:
        diseaseGroups?.map((itemDeseaseGroup) => ({
          ...itemDeseaseGroup,
          regimens: itemDeseaseGroup?.regimens?.filter((itemRegimen) => itemRegimen?.isActive),
        })) || [],
      histories,
      schedulesData: schedulesResponse.items || [],
      // listPreOrder,
      cart: cartData,
      // cartDataSum,
    };
  }

  /**
   * Fetch SKU information from different sources
   */
  async fetchSkuInformation(histories: VaccineHistoryDetailDto[], listPreOrder: GetListPreOrderByLcvIdRes[]) {
    const listSkuFromHistory = histories?.map((item) => item?.sku);
    const listSkuFromPreOrder = listPreOrder?.map((item) => item?.itemCode);

    try {
      const [resListSkuInfo, resListSkuHistory] = await concurrentPromise(
        this.regimenService.getRegimenByListSku({
          skus: listSkuFromPreOrder,
        }),
        this.searchServiceProductApiService.getListProductBySku(_.uniq(listSkuFromHistory)),
      );

      const listSkuInfo = resListSkuInfo?.data || [];

      return { listSkuInfo, resListSkuHistory };
    } catch (error) {
      // Log error if needed
      return { listSkuInfo: [], resListSkuHistory: [] };
    }
  }

  /**
   * Get product information by SKUs
   */
  async getProductInfoBySku(skus: string[], shopCode?: string): Promise<any[]> {
    if (!skus.length) {
      return [];
    }

    try {
      const response = await this.pimAppService.getListProductBySku(_.uniq(skus), shopCode);
      return response?.listProduct || [];
    } catch (error) {
      // Log error if needed
      return [];
    }
  }

  /**
   * Get shop details by shop codes
   */
  async getShopDetails(shopCodes: string[]) {
    if (!shopCodes.length) {
      return [];
    }

    try {
      const shopPromises = shopCodes.map((code) => this.orderPromissingService.getShopDetail(code));
      const results = await concurrentPromise(...shopPromises);

      return results;
    } catch (error) {
      // Log error if needed
      return [];
    }
  }

  /**
   * Extract shop codes and SKUs from items
   */
  extractShopCodesAndSkus(items: VaccinationBookRes['items']) {
    const shopCodes: string[] = [];
    // const listSkuNeedToEnrichData: string[] = []; // List sku with status 3: chưa tiêm

    const arrSku = JSONPath({
      json: items,
      path: '$..sku',
    });

    // for (const item of items) {
    //   for (const record of item.vaccinationRecords || []) {
    //     if (record?.shopCode) {
    //       shopCodes.push(record.shopCode);
    //     }

    //     if (record?.status === VaccineRecordStatus.Chua_tiem) {
    //       listSkuNeedToEnrichData.push(record.sku);
    //     }

    //     if (
    //       [VaccineRecordStatus.Da_hen, VaccineRecordStatus.Cart_suggest_da_hen]?.includes(record?.status) &&
    //       !record?.price
    //     ) {
    //       listSkuNeedToEnrichData.push(record.sku);
    //     }
    //   }
    // }

    return {
      shopCodes: _.uniq(shopCodes) as string[],
      listSkuNeedToEnrichData: _.uniq(arrSku) as string[],
    };
  }

  async getStorageFee(dto: CalculateTempPricingDto, carts: GetCartLibResponse[]) {
    // Cart chính
    const cartItems = carts.flatMap((cart) =>
      cart.listCartSelected.map((item) => ({
        sku: item?.itemCart,
        startDateInjection: item.startDateInjection,
        lcvId: item.lcvId,
        orderInjections: item.orderInjections || [],
        unitCode: item?.unitCode,
      })),
    );

    // Cart phụ từ FE
    const dtoItems = dto.arrSku.map((item) => ({
      sku: item.sku,
      startDateInjection: item.startDateInjection,
      lcvId: item.lcvId,
      orderInjections: item.orderInjections || [],
      unitCode: item?.unitCode,
    }));

    // Gom 2 cart lại
    const combinedItems = [...cartItems, ...dtoItems];

    // Gom orderInjections của 2 cart lại thành 1 orderInjections
    const groupedItems = _.groupBy(combinedItems, (item) => `${item.sku}-${item.lcvId}`);
    const uniqueItems = Object.values(groupedItems).map((group: any) => {
      return {
        sku: group[0]?.sku,
        startDateInjection: moment(new Date(_.min(group.map((item) => new Date(item.startDateInjection)))))
          .format('DD/MM/YYYY')
          .toString(),
        lcvId: group[0]?.lcvId,
        orderInjections: _.union(...group.map((item) => item.orderInjections)), // Hợp nhất orderInjections
        unitCode: group[0]?.unitCode,
      };
    });

    const headers = {
      'shop-code': dto?.shopCode || (this.req.headers?.['shop-code'] as string) || '',
    };

    const dosePricingList = await this.orderRuleEngineService.calculateDosePricing(uniqueItems, {
      headers,
    });

    // Chỉ lấy ra những mũi nào thuộc cart tạm thời
    const filteredDosePricingList = dosePricingList.filter((pricing) =>
      dtoItems.some(
        (item) =>
          pricing.sku === item.sku &&
          pricing.lcvId === item.lcvId &&
          item.orderInjections?.includes(pricing.orderInjection),
      ),
    );

    const tempResult = this.aggregatePricing(filteredDosePricingList);
    const allResult = this.aggregatePricing(dosePricingList);

    return {
      tempCart: tempResult,
      allCart: allResult,
    };
  }

  private aggregatePricing(pricingList: CalculateDosePricingRes[]) {
    const doseMap = new Map<string, CalculateDosePricingRes[]>();

    for (const item of pricingList || []) {
      if (!item?.lcvId) continue;
      const group = doseMap.get(item.lcvId) || [];
      group.push(item);
      doseMap.set(item.lcvId, group);
    }

    const pricingByLcvId = new Map<string, number>();
    let totalPrice = 0;

    for (const [lcvId, items] of doseMap.entries()) {
      const price = items.reduce((sum, item) => sum + (item?.totalPrice || 0), 0);
      pricingByLcvId.set(lcvId, price);
      totalPrice += price;
    }

    return { totalPrice, pricingByLcvId };
  }

  /**
   * Calculate temporary pricing for consultation
   * @param dto Calculation parameters
   * @returns Calculation results including member prices and total price
   */
  async calculateTempPricing(dto: CalculateTempPricingDto): Promise<CalculateTempPricingResponseDto> {
    const { lcvIds } = dto;
    try {
      // Group items by lcvId
      const memberItems: { [key: string]: SkuItemDto[] } = _.groupBy(dto.arrSku, 'lcvId');

      const cartType = (this.req.headers?.['cart-type'] as string) || '';
      // Fetch cart information for each lcvId
      const cartPromises = lcvIds?.map((lcvId) =>
        this.cartAppService.getCartRedis(
          {
            sessionId: dto.sessionId,
            shopCode: dto?.shopCode || (this.req.headers?.['shop-code'] as string) || '',
          },
          +cartType === EnmCartType.FAMILY_PACKAGE && {
            headers: {
              'cart-type': EnmCartType.FAMILY_PACKAGE,
              'lcv-id': lcvId,
            } as any,
          },
        ),
      );

      const carts = await concurrentPromise(...(cartPromises?.length ? cartPromises : []));

      // Calculate totals per member
      // Create a map of carts by lcvId for reliable lookup
      const cartsByLcvId = Object.fromEntries(
        carts.map((cart) => {
          // Extract lcvId from headers in request context
          const lcvId = cart.headerData?.vaccinationCode || '';
          return [lcvId, cart];
        }),
      );

      const memberPrices: MemberPriceDto[] = Object.entries(memberItems).map(([lcvId, items]) => {
        const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);

        // Calculate items price
        const itemsPrice = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
        const personId = items?.find((item) => item.lcvId === lcvId)?.personId || '';

        return {
          lcvId,
          personId: personId,
          totalQuantity,
          totalPrice: itemsPrice,
        };
      });

      // Calculate grand total
      const totalPrice = memberPrices?.reduce((sum, member) => sum + member.totalPrice, 0);

      // Tính phí lưu trữ
      const dosesFee = await this.getStorageFee(dto, carts);
      const tempCart = dosesFee?.tempCart;
      const allCart = dosesFee?.allCart;

      // Prepare parameters for suggestRankingForCart
      const suggestParams: SuggestRankingForCartDto = {
        groupTypeCode: dto.groupTypeCode,
        expectedRank: dto.expectedRank,
        groupCode: dto?.groupCode,
        listPersonAttribute: lcvIds?.flatMap((lcvId) => {
          const cart = cartsByLcvId[lcvId];
          const member = memberPrices.find((memberPrice) => memberPrice.lcvId === lcvId);
          const allCartPricing = allCart?.pricingByLcvId.get(lcvId);

          const sumQuantity =
            cart?.listCartSelected
              ?.filter((e) => e.itemType === ItemType.Product)
              ?.reduce((sum, item) => sum + item?.quantity || 0, 0) || 0;
          return [
            {
              personId: member?.personId || GUID_EMPTY,
              attributeCode: '2',
              attributeValue:
                allCartPricing || (member?.totalPrice || 0) + (cart?.calculatorPriceInfo?.total || 0) || 0,
              lcvId: lcvId,
            },
            {
              personId: member?.personId || GUID_EMPTY,
              attributeCode: '1',
              attributeValue: (member?.totalQuantity || 0) + sumQuantity || 0,
              lcvId: lcvId,
            },
          ];
        }),

        // memberPrices?.flatMap((member) => {
        //   // get cart by lcvId
        //   const cart = cartsByLcvId[member.lcvId];
        //   const sumQuantity =
        //     cart?.listCartSelected
        //       ?.filter((e) => e.itemType === ItemType.Product)
        //       ?.reduce((sum, item) => sum + item?.quantity || 0, 0) || 0;
        //   return [
        //     {
        //       personId: member?.personId,
        //       attributeCode: '2',
        //       attributeValue: member?.totalPrice + (cart?.calculatorPriceInfo?.total || 0) || 0,
        //       lcvId: member?.lcvId,
        //     },
        //     {
        //       personId: member.personId,
        //       attributeCode: '1',
        //       attributeValue: member?.totalQuantity + sumQuantity || 0,
        //       lcvId: member?.lcvId,
        //     },
        //   ];
        // }),
      };

      // Call suggestRankingForCart API
      const suggestRankingResult = suggestParams?.listPersonAttribute?.length
        ? await this.suggestRankingForCart(suggestParams)
        : null;

      return {
        memberPrices: memberPrices.map((member) => {
          return { ...member, totalPrice: tempCart?.pricingByLcvId.get(member?.lcvId) ?? member?.totalPrice };
        }),
        totalPrice: tempCart?.totalPrice ?? totalPrice,
        suggestRankingResult,
      };
    } catch (error) {
      console.error('Error calculating temporary pricing:', error);
      throw error;
    }
  }
}
