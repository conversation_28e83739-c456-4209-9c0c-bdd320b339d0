import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IAMCoreModule } from 'vac-nest-iam';
import { JourneyModule } from 'vac-nest-journey';
import { OMSModule } from 'vac-nest-oms';
import { XuatOffDetailController } from './controllers/xuat-off-detail.controller';
import { XuatOffController } from './controllers/xuat-off.controller';
import { XuatOffStepInfoEntity } from './entities';
import { XuatOffApproveFlowEntity } from './entities/xuat-off-approve-flow.entity';
import { XuatOffDetailEntity } from './entities/xuat-off-detail.entity';
import { XuatOffEntity } from './entities/xuat-off.entity';
import {
  XuatOffApproveFlowRepository,
  XuatOffDetailRepository,
  XuatOffRepository,
  XuatOffStepInfoRepository,
} from './repository';
import { XuatOffDetailService } from './services/xuat-off-detail.service';
import { XuatOffStepInfoService } from './services/xuat-off-step.service';
import { XuatOffService } from './services/xuat-off.service';
import { FilesModule } from '@libs/modules/files/files.module';
import { XuatOffStepController } from './controllers/xuat-off-step.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([XuatOffEntity, XuatOffDetailEntity, XuatOffStepInfoEntity, XuatOffApproveFlowEntity]),
    OMSModule,
    JourneyModule,
    IAMCoreModule,
    FilesModule,
  ],
  controllers: [XuatOffController, XuatOffDetailController, XuatOffStepController],
  providers: [
    XuatOffService,
    XuatOffDetailService,
    XuatOffStepInfoService,

    // Repository
    XuatOffRepository,
    XuatOffStepInfoRepository,
    XuatOffApproveFlowRepository,
    XuatOffDetailRepository,
  ],
  exports: [
    XuatOffService,
    XuatOffDetailService,
    XuatOffStepInfoService,

    // Repository
    XuatOffRepository,
    XuatOffStepInfoRepository,
    XuatOffApproveFlowRepository,
    XuatOffDetailRepository,
  ],
})
export class XuatOffModule {}
