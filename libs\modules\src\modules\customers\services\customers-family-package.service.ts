import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import _ from 'lodash';
import { FamilyService as FamilyCoreService, GetPersonByIdRes, SearchPersonDto } from 'vac-nest-family';
import { IOrder, JourneyService } from 'vac-nest-journey';
import { EnmVerifyMethod, PersonFrom } from '../enum';
import { CheckExistPersonInGroupDto, CheckRuleCreateGroupDto, FamilyRuleService } from 'vac-nest-family-rule';
import { CustomerCoreService } from 'vac-nest-customer-core';
import { plainToInstance } from 'class-transformer';
import {
  AddPersonGroupExtendDto,
  ChangeOwnerDto,
  CheckRuleCreateGroupTransformDto,
  CreateFamilyPackageConvertDto,
  CreateFamilyPackageResponse,
  CreateFamilyPackageTransformDto,
  GetManyPersonDto,
  GroupPersonJunctionResponse,
  InactiveFamilyPackageDto,
  InsertOrUpdateEvidenceExtendDto,
  PersonInfoRes,
  RemovePersonInGroupDto,
  GetAttributeByLcvIdsDto,
} from '../dto';
import { CustomersService } from './customers.service';
import { InjectS3, S3Client } from 'ict-nest-s3';
import { S3_OPERATIONS } from 'ict-nest-s3/dist/s3.constant';
import { PRE_SIGNED_LINK_EXPIRATION } from '@libs/modules/reports/constants';
import {
  concurrentPromise,
  ErrorCode,
  OrderChanel,
  OrderChannels,
  SystemException,
  getExpiredTime,
  RedisService,
} from '@shared';
import { SearchGroupDto } from '../dto/search-group.dto';
import { CheckRuleDeleteMemberDto } from 'vac-nest-family-rule/dist/dto/check-rule-delete-member.dto';
import { CheckRuleChangeOwnerDto } from 'vac-nest-family-rule/dist/dto/check-rule-change-owner.dto';
import { calculateTimeDifference } from 'vac-commons';
import { RegimenService } from 'vac-nest-regimen';
import {
  FamilyPackageService,
  CreateFamilyPackageDto,
  CreateFamilyPackageRes,
  Evidence,
  GetGroupTypeDataByCodeDto,
  InsertOrUpdateEvidenceDto,
  GetFamilyPackageByGroupCodesDto,
  GetFamilyPackageByLcvIdsDto,
  TransactionType,
} from 'vac-nest-family-package';
import { EcomDisplay, OrderStatus, OMSService } from 'vac-nest-oms';
import moment from 'moment';
import { CustomersRuleService } from './customers-rule.service';
import { FamilyPackageApiService } from '@frt/nestjs-api';
import { OrderAttribute } from 'vac-nest-cart-app';
import { JSONPath } from 'jsonpath-plus';
import { OsrService } from 'vac-nest-osr';
import { CalculateTempPriceingByRankDto } from '../dto/calculate-temp-pricing-by-rank.dto';
import { PERMISSION } from '@shared/enum/auth';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { REDIS_OTP_FP_EXPIRED_IN_MINUTES, KEY_REDIS_FAMILY_PACKAGE } from '../constant';

@Injectable()
export class CustomersFamilyPackageService {
  shopCode: string;
  token: string;
  orderChannel: string;

  constructor(
    private readonly familyCoreService: FamilyCoreService,
    private readonly familyPackageService: FamilyPackageService,
    private readonly familyPackageApiService: FamilyPackageApiService,
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly journeyCoreService: JourneyService,
    private readonly customerCoreService: CustomerCoreService,
    private readonly familyRuleService: FamilyRuleService,
    private readonly customersService: CustomersService,
    private readonly regimenCoreService: RegimenService,
    private readonly customerRuleService: CustomersRuleService,
    private readonly osrService: OsrService,
    @InjectS3(process.env.S3_CONNECTION)
    private readonly s3Client: S3Client,
    private readonly journeyService: JourneyService,
    private readonly omsService: OMSService,
    @InjectRedis(process.env.REDIS_CONNECTION)
    private readonly redisService: RedisService,
  ) {
    this.token = (this.req.headers?.authorization as string) || (this.req.headers?.Authorization as string) || '';
  }

  async searchCustomer(query: SearchPersonDto, _employeeCode: string) {
    const newQuery = _.pick(_.cloneDeep(query), [
      'keyword',
      'name',
      'phoneNumber',
      'dateOfBirthFrom',
      'dateOfBirthTo',
      'pageNumber',
      'pageSize',
    ]);

    //#region search theo orderCode
    // check thêm điều kiện để search person theo đơn
    // chỉ search ở RSA
    const isSearchByOrderCode =
      _.isNumber(+newQuery?.keyword) &&
      newQuery?.keyword?.length === 23 &&
      OrderChannels.RSA.includes((this.req.headers?.['order-channel'] as string) || '');

    const orderCode = isSearchByOrderCode ? newQuery?.keyword : null;
    if (isSearchByOrderCode) {
      const { lcvId } = await this.journeyCoreService.getOrderInfoByOrderOrTicket({ orderCode: newQuery?.keyword });
      if (lcvId) newQuery.keyword = lcvId;
    }
    // #endRegion search theo orderCode

    // STEP 1: tìm nhà mình
    const familyData: any = await this.familyCoreService.searchPerson(newQuery);
    if (familyData.items.length) {
      //#region trả thông tin khách hàng kèm orderCode khi search theo orderCode
      // trả thêm thông tin orderCode khi search customer theo orderCode
      if (isSearchByOrderCode) {
        familyData.items = familyData?.items?.map((i) => {
          return {
            ...i,
            familyProfileDetails: i?.familyProfileDetails?.map((f) => {
              return {
                ...f,
                orderCode: (f?.lcvId === newQuery?.keyword && orderCode) || '',
              };
            }),
          };
        });
      }
      //#endregion trả thông tin khách hàng kèm orderCode khi search theo orderCode

      const lcvIds =
        familyData.items?.flatMap((item) => [
          item?.lcvId,
          ...(item.familyProfileDetails?.map((detail) => detail?.lcvId) || []),
        ]) || [];

      const groupFamilyData = await this.familyPackageService.getGroupFamilyPackage({
        lcvIds: lcvIds,
      });

      const itemsConvert = familyData.items?.map((item) => {
        const findGroupFamilyData = groupFamilyData?.find(
          (e) => e.groupPersonJunctions.some((junction) => junction?.lcvId === item.lcvId), // item.lcvId
        );
        return {
          ...item,
          objectGroupFamilyID: findGroupFamilyData?.id || '',
          groupCode: findGroupFamilyData?.code || '',
          familyProfileDetails: item?.familyProfileDetails?.map((itemFamilyProfileDetails) => {
            const findObjectGroupFamily = groupFamilyData?.find((e) =>
              e.groupPersonJunctions.some((junction) => junction?.lcvId === itemFamilyProfileDetails.lcvId),
            );
            return {
              ...itemFamilyProfileDetails,
              objectGroupFamilyID: findObjectGroupFamily?.id || '',
              groupCode: findObjectGroupFamily?.code || '',
            };
          }),
        };
      });

      return {
        totalCount: familyData.totalCount,
        items: itemsConvert,
        source: PersonFrom.LCV,
      };
    }

    return {
      totalCount: 0,
      items: [],
      source: PersonFrom.LCV,
    };
  }

  async getGroupByLcvIds(payload: GetFamilyPackageByLcvIdsDto): Promise<CreateFamilyPackageResponse> {
    const res = await this.familyPackageService.getGroupFamilyPackage({
      lcvIds: payload?.lcvIds,
    });
    let detailGroup = res?.[0];
    if (detailGroup) {
      detailGroup = await this.mapInfoDetailGroup(detailGroup);
    }

    return detailGroup || {};
  }

  async getGroupByGroupCode(payload: GetFamilyPackageByGroupCodesDto): Promise<CreateFamilyPackageResponse> {
    const res = await this.familyPackageService.getGroupFamilyPackageByGroupCode({
      groupCodes: payload?.groupCodes,
    });
    let detailGroup = res?.[0];
    if (detailGroup) {
      detailGroup = await this.mapInfoDetailGroup(detailGroup);
    }
    return detailGroup || {};
  }

  /**
   *  @Modal Chi tiết groupFamily
   *  1. Lấy thông tin danh sách Family
   *  2. Get Person Attribute
   *  3. Get Group Atribute
   *  4. Get thông tin S3
   *  5. Handle isOwner and sort owner
   */

  async mapInfoDetailGroup(detailGroup: CreateFamilyPackageResponse): Promise<CreateFamilyPackageResponse> {
    const result = { ...detailGroup };

    const lcvIds = result?.groupPersonJunctions?.map((e) => e?.lcvId);

    if (lcvIds?.length > 0) {
      const resFamily = await this.familyCoreService.getManyByLcvId({
        lcvId: lcvIds,
      });
      const [resPersonAttribute, resGroupAtrribute, listOrderBylcvId] = await Promise.all([
        this.familyPackageService.getPersonAttribute({
          lcvIds: lcvIds,
        }),
        this.familyPackageService.getGroupAttribute({
          groupCode: result?.code,
        }),
        this.getOrderByLcvidsAttributes(lcvIds),
      ]);

      resFamily?.forEach((e) => (e.familyProfileDetails = undefined));

      result.groupPersonJunctions?.forEach((e) => {
        let listOrderEcom = listOrderBylcvId?.filter(
          (item) =>
            item?.lcvId === e?.lcvId &&
            item?.ecomDisplay === EcomDisplay.AtShop &&
            item?.source === OrderChanel.FromRSAEcom &&
            item?.orderStatus === OrderStatus.Confirmed,
        );

        const listOrderWebApp = listOrderBylcvId.filter(
          (item) =>
            item?.lcvId === e?.lcvId &&
            ([OrderChanel.FromAppEcom, OrderChanel.FromWebEcom] as string[]).includes(item?.source) &&
            [OrderStatus.Confirmed, OrderStatus.FinishDeposit].includes(item?.orderStatus),
        );
        listOrderEcom = [...listOrderWebApp, ...listOrderEcom];

        const listOrderAff = listOrderBylcvId?.filter(
          (item) =>
            item?.lcvId === e?.lcvId &&
            ([OrderChanel.FromVaccineShopAffiliate, OrderChanel.FromMobileAffiliate] as string[])?.includes(
              item?.source,
            ),
        );

        const listPreOrder = listOrderBylcvId?.filter(
          (item) =>
            item?.lcvId === e?.lcvId &&
            item?.orderAttribute === OrderAttribute.PRE_ORDER &&
            item?.orderStatus === OrderStatus.FinishDeposit,
        );
        e.listOrderEcom = listOrderEcom?.map((item) => item?.orderCode);
        e.listOrderAff = listOrderAff?.map((item) => item?.orderCode);
        e.listPreOrder = listPreOrder?.map((item) => item?.orderCode);
        e.isOwner = result.owner === e.lcvId;
        e.personAttribute = resPersonAttribute?.filter((att) => att?.lcvId === e?.lcvId) || [];
        const familyFind = resFamily?.find((person) => person?.lcvId === e?.lcvId);

        e.personInfo = plainToInstance(PersonInfoRes, familyFind, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });
      });
      result.groupPersonJunctions = result.groupPersonJunctions?.sort((a, _b) => (a.isOwner ? -1 : 1));
      result.groupAttribute = resGroupAtrribute;
      const keyImg = result?.evidence?.map((e) => e.evidenceData);
      const arrLinks = await this.s3Client.presignObjects(keyImg, S3_OPERATIONS.GET_OBJECT, PRE_SIGNED_LINK_EXPIRATION);
      arrLinks?.map((url, index) => {
        result.evidence[index].previewUrl = url;
      });
    }

    // Map thêm message
    const groupCode = detailGroup?.code;
    const resSuggestRanking = groupCode
      ? await this.familyRuleService.suggestRankingForCart({
          groupCode,
          groupTypeCode: 1,
          listPersonAttribute: [],
        })
      : null;

    result.groupPersonJunctions?.forEach((item) => {
      item.isAccumulated = !resSuggestRanking?.lcvIdsForNextRank?.some((lcvId) => lcvId === item?.lcvId);
    });

    return {
      ...result,
      message: resSuggestRanking?.message || '',
    };
  }

  private async verifyMethodHandler({
    verifyMethod,
    phoneNumber,
    otp,
    templateType,
    insideCode,
    functionName,
    groupCode,
    isOtpValid,
  }: {
    verifyMethod: number;
    phoneNumber?: string;
    otp?: string;
    templateType?: number;
    insideCode?: string;
    functionName: string;
    groupCode?: string;
    isOtpValid?: boolean | string;
  }) {
    // query pasre sang string nên cover như thế này
    const shouldSkipValidation = isOtpValid === true || isOtpValid === 'true';

    if (shouldSkipValidation) return;
    if (!otp) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_MISSING_OTP,
          message: ErrorCode.getError(ErrorCode.RSA_MISSING_OTP),
          details: ErrorCode.getError(ErrorCode.RSA_MISSING_OTP),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }
    switch (+verifyMethod) {
      case EnmVerifyMethod.OTP:
        await this.customersService.verifyOtp({
          phoneNumber,
          otp,
          type: templateType,
        });
        break;
      case EnmVerifyMethod.Staff:
        await this.customerRuleService.verifyInsideSMByPermission({
          otp,
          token: this.token,
          shopCode: this.req.headers?.['shop-code'] as string,
          functionName,
          insideCode,
          orderChannel: (this.req.headers?.['order-channel'] as string) || '',
          arrPermissionSM: [PERMISSION.FAMILY_PACKAGE],
        });
        break;
      default:
        await this.customersService.verifyOtp({
          phoneNumber,
          otp,
          type: templateType,
        });
        break;
    }
    if (!!groupCode) {
      await this.redisService.set(
        `${KEY_REDIS_FAMILY_PACKAGE}:${groupCode}`,
        otp,
        'EX',
        getExpiredTime('min', REDIS_OTP_FP_EXPIRED_IN_MINUTES),
      );
    }
  }

  /**
   * Step 1: verify OTP
   * Step 2: get group by Id
   * Step 2: suggest ranking
   * Step 3: check rule create // Tạm thời chưa có api
   * Step 4: create object group family
   */
  async createGroupFamilyPackage(body: CreateFamilyPackageConvertDto): Promise<CreateFamilyPackageResponse> {
    const { groupTypeCode, verifyMethod, phoneNumber, otp, templateType, insideCode, isOtpValid = false } = body;

    await this.verifyMethodHandler({
      verifyMethod,
      phoneNumber,
      otp,
      templateType,
      insideCode,
      functionName: 'createGroupFamilyPackage',
      groupCode: '',
      isOtpValid,
    });

    const groupData = await this.familyPackageService.getRankGroupTypeDataByCode({ groupTypeCode });

    const lcvIds = body?.groupPersonJunctions?.map((person) => person?.lcvId);

    const suggestRanking = await this.familyRuleService.suggestByListPerson({
      lcvId: lcvIds,
      groupTypeCode: groupData?.groupTypeCode || 1,
      groupTypeName: groupData?.groupTypeName || '',
    });

    const createFamilyPackageDto: CreateFamilyPackageDto = plainToInstance(
      CreateFamilyPackageTransformDto,
      { ...body, ...groupData, ...suggestRanking, ...{ groupAttributes: suggestRanking?.groupAttribute } },
      {
        excludeExtraneousValues: true,
        exposeUnsetFields: false,
      },
    );

    const checkRuleCreateGroupDto: CheckRuleCreateGroupDto = plainToInstance(CheckRuleCreateGroupTransformDto, body, {
      excludeExtraneousValues: true,
      exposeUnsetFields: false,
    });

    await this.familyRuleService.checkRuleCreateGroup(checkRuleCreateGroupDto);

    const resPonseCreate = await this.familyPackageService.createGroupFamilyPackage(createFamilyPackageDto);

    if (!!resPonseCreate?.code && !!body?.otp) {
      await this.redisService.set(
        `${KEY_REDIS_FAMILY_PACKAGE}:${resPonseCreate?.code}`,
        body?.otp,
        'EX',
        getExpiredTime('min', REDIS_OTP_FP_EXPIRED_IN_MINUTES),
      );
    }

    return this.mapInfoDetailGroup(resPonseCreate);
  }

  async getAllGroupTypeData() {
    return this.familyPackageService.getAllGroupTypeData();
  }

  async getGroupTypeDataByCode(payload: GetGroupTypeDataByCodeDto) {
    return this.familyPackageService.getGroupTypeDataByCode(payload);
  }

  async getRankGroupTypeData() {
    return this.familyPackageService.getRankGroupTypeData();
  }

  async getRankGroupTypeDataByCode(payload: GetGroupTypeDataByCodeDto) {
    return this.familyPackageService.getRankGroupTypeDataByCode(payload);
  }

  async checkExistPersonInGroup(payload: CheckExistPersonInGroupDto) {
    return this.familyRuleService.checkExistPersonInGroup(payload);
  }

  async checkRuleDeleteMember(payload: CheckRuleDeleteMemberDto) {
    const group = await this.familyPackageService.getGroupFamilyPackageByGroupCode({
      groupCodes: [payload?.groupCode],
    });

    if (group?.[0]) {
      const lcvIds = group?.[0]?.groupPersonJunctions?.map((item) => item?.lcvId) || [];

      await this.journeyCoreService.checkPersonEligibility(_.compact(_.uniq([...payload?.lcvId, ...lcvIds])));
    }

    return this.familyRuleService.checkRuleDeleteMember(payload);
  }

  async checkRuleChangeOwner(payload: CheckRuleChangeOwnerDto) {
    return this.familyRuleService.checkRuleChangeOwner(payload);
  }

  async changeGroupOwner(payload: ChangeOwnerDto): Promise<CreateFamilyPackageRes> {
    const { verifyMethod, phoneNumber, otp, templateType, insideCode, isOtpValid, groupCode } = payload;

    await this.verifyMethodHandler({
      verifyMethod,
      phoneNumber,
      otp,
      templateType,
      insideCode,
      functionName: 'changeGroupOwner',
      isOtpValid,
      groupCode,
    });

    await this.familyRuleService.checkRuleChangeOwner({
      groupCode: payload?.groupCode,
      lcvIdOwner: payload?.newOwner,
    });

    const detailGroup = await this.familyPackageService.changeGroupOwner(payload);

    return this.mapInfoDetailGroup(detailGroup);
  }

  /**
   * 1. Verify OTP
   * 2. get group by groupCode
   * 3. Check rule Exist
   * 4. Add person to group
   * 5. Insert or update Evidence nếu có của person khác
   * 6. Calculator rank
   * 7. Flow mapping chi tiết group (mapInfoDetailGroup func)
   */

  async addPersonGroup(payload: AddPersonGroupExtendDto): Promise<CreateFamilyPackageResponse> {
    const {
      verifyMethod,
      phoneNumber,
      otp,
      templateType,
      insideCode,
      addEvidences,
      deleteEvidences,
      groupCode,
      isOtpValid,
    } = payload;

    await this.verifyMethodHandler({
      verifyMethod,
      phoneNumber,
      otp,
      templateType,
      insideCode,
      functionName: 'addPersonGroup',
      isOtpValid,
      groupCode,
    });

    const group = await this.familyPackageService.getGroupFamilyPackageByGroupCode({
      groupCodes: [groupCode],
    });
    const owner = group?.[0]?.owner;
    await this.familyRuleService.checkExistPersonInGroup({
      lcvId: [payload.groupPersonJunctions?.[0]?.lcvId],
      groupCode: groupCode,
      owner: owner,
    });

    let detailGroup: CreateFamilyPackageRes = null;

    // set groupTypeId to payload
    if (group?.at(0)?.groupTypeId && payload?.groupPersonJunctions?.length)
      payload.groupPersonJunctions[0].groupTypeId = group?.at(0)?.groupTypeId;

    const groupData = await this.familyPackageService.addPersonGroup(payload);

    const evidences: Evidence[] = [...addEvidences, ...deleteEvidences];
    if (evidences?.length) {
      detailGroup = await this.familyPackageService.insertOrUpdateEvidence({
        groupCode,
        modifiedBy: payload.groupPersonJunctions?.[0]?.createdBy,
        evidences: evidences,
      });
    } else {
      detailGroup = groupData;
    }

    const resCalculate = await this.familyPackageService.calculateRank({
      groupCode,
      modifiedBy: payload.groupPersonJunctions?.[0]?.createdBy,
      transactionType: TransactionType.ADD_PERSON_FAMILY_PACKAGE,
    });

    return this.mapInfoDetailGroup(resCalculate);
  }

  /**
   * 1. Verify OTP
   * 2. Check rule delete
   * 3. Xóa person khỏi group
   * 4. Caculate rank
   * 5. Flow mapping chi tiết group (mapInfoDetailGroup func)
   */
  async removePersonGroup(payload: RemovePersonInGroupDto): Promise<CreateFamilyPackageRes> {
    const { verifyMethod, phoneNumber, otp, templateType, insideCode, groupCode, lcvId, modifiedBy, isOtpValid } =
      payload;

    await this.verifyMethodHandler({
      verifyMethod,
      phoneNumber,
      otp,
      templateType,
      insideCode,
      functionName: 'removePersonGroup',
      groupCode,
      isOtpValid,
    });

    const group = await this.familyPackageService.getGroupFamilyPackageByGroupCode({
      groupCodes: [groupCode],
    });

    if (group?.[0]) {
      const lcvIds = group?.[0]?.groupPersonJunctions?.map((item) => item?.lcvId) || [];

      await this.journeyCoreService.checkPersonEligibility(_.compact(_.uniq([...payload?.lcvId, ...lcvIds])));
    }

    await this.familyRuleService.checkRuleDeleteMember({
      groupCode,
      lcvId: [lcvId],
    });
    delete payload.lcvId;

    const groupData = await this.familyPackageService.removePersonGroup(payload);

    const resCalculate = await this.familyPackageService.calculateRank({
      groupCode,
      modifiedBy,
      transactionType: TransactionType.REMOVE_PERSON_FAMILY_PACKAGE,
    });

    return this.mapInfoDetailGroup(resCalculate);
  }

  async insertOrUpdateEvidence(payload: InsertOrUpdateEvidenceExtendDto) {
    const payloadInsert: InsertOrUpdateEvidenceDto = {
      groupCode: payload.groupCode,
      modifiedBy: payload.modifiedBy,
      evidences: [...(payload?.addEvidences || []), ...(payload?.deleteEvidences || [])],
    };
    return this.familyPackageService.insertOrUpdateEvidence(payloadInsert);
  }

  async searchGroup(payload: SearchGroupDto): Promise<CreateFamilyPackageResponse[]> {
    const { keyword } = payload;
    const persons = await this.familyCoreService.searchPrimaryPersonByKeyWord(keyword);
    if (!persons.length) {
      return [];
    }
    const lcvIds = persons.map((e) => e?.lcvId);

    /** Find the search info */
    const personExactMatchKeyWord = persons.find((p) => [p?.lcvId, p?.phoneNumber].includes(keyword));
    const lcvIdExactMatchKeyWord = personExactMatchKeyWord?.lcvId;

    const familyGroup = await this.familyPackageService.getGroupFamilyPackage({
      lcvIds,
    });

    const allLcvIds = familyGroup.flatMap((e) =>
      e.groupPersonJunctions.map((groupPersonJunctionItem) => groupPersonJunctionItem.lcvId),
    );

    const [familyDetails, resPersonAttribute] = await concurrentPromise(
      allLcvIds?.length
        ? this.familyCoreService.getManyByLcvId({
            lcvId: allLcvIds,
          })
        : Promise.resolve([]),
      allLcvIds?.length
        ? this.familyPackageService.getPersonAttribute({
            lcvIds: allLcvIds,
          })
        : Promise.resolve([]),
    );

    familyGroup?.forEach((item) => {
      item.groupPersonJunctions?.forEach((e: GroupPersonJunctionResponse) => {
        e.isOwner = item.owner === e.lcvId;
        e.personAttribute = resPersonAttribute?.filter((att) => att?.lcvId === e?.lcvId) || [];
        e.personInfo = _.pick(familyDetails?.find((person) => person?.lcvId === e?.lcvId) || {}, [
          'name',
          'phoneNumber',
          'dateOfBirth',
          'gender',
        ]);

        e.isExactMatchKeyWord = e?.lcvId === lcvIdExactMatchKeyWord;
      });
    });

    return familyGroup;
  }

  async getManyPerson(payload: GetManyPersonDto): Promise<GetPersonByIdRes[]> {
    const persons = await this.familyCoreService.getListPrimaryPerson(payload?.lcvIds);

    const ageRanges = await this.regimenCoreService.getAgeRanges();
    persons?.forEach((person) => {
      person.customerAge = calculateTimeDifference(
        person?.dateOfBirth,
        person?.from,
        person?.to,
        ageRanges,
        person.ageUnitCode,
      );
    });

    return persons;
  }

  /**
   *
   * @docs ../docs/get-order-by-lcvids-attributes.md
   */
  async getOrderByLcvidsAttributes(lcvIds: string[]) {
    const resOrderJourney = await this.journeyService.getOrderByLcvidsAttributes({
      lcvIds,
      source: [
        OrderChanel.FromRSAEcom,
        OrderChanel.FromVaccineShopAffiliate,
        OrderChanel.FromMobileAffiliate,
        OrderChanel.FromRSAOffline,
        OrderChanel.FromWebEcom,
        OrderChanel.FromAppEcom,
      ],
      orderStatus: [OrderStatus.Confirmed, OrderStatus.FinishDeposit],
      pageNumber: 1,
      pageSize: 200,
    });

    // Đơn hàng Affiliate
    const resAffOrderFilter = resOrderJourney?.items?.filter(
      (item) =>
        ([OrderChanel.FromVaccineShopAffiliate, OrderChanel.FromMobileAffiliate] as string[])?.includes(item?.source) &&
        item?.orderStatus === OrderStatus.FinishDeposit &&
        item?.orderAttribute !== 9,
    );

    // Đơn web/app
    const resWebAppOrderFilter = resOrderJourney?.items?.filter(
      (item) =>
        ([OrderChanel.FromWebEcom, OrderChanel.FromAppEcom] as string[])?.includes(item?.source) &&
        (item?.orderStatus === OrderStatus.FinishDeposit || item?.orderStatus === OrderStatus.Confirmed) &&
        item?.orderAttribute !== 9 &&
        item?.orderAttribute !== OrderAttribute.PRE_ORDER,
    );

    // Đơn hàng Ecom
    const resEcomOrderFilter = resOrderJourney?.items?.filter(
      (item) =>
        item?.ecomDisplay === EcomDisplay.AtShop &&
        item?.source === OrderChanel.FromRSAEcom &&
        item?.orderStatus === OrderStatus.Confirmed &&
        item?.orderAttribute !== 9 &&
        item?.orderAttribute !== OrderAttribute.PRE_ORDER,
    );

    // Đơn hàng Pre-order
    const resPreOrderFilter = resOrderJourney?.items?.filter(
      (item) => item?.orderStatus === OrderStatus.FinishDeposit && item?.orderAttribute === OrderAttribute.PRE_ORDER,
    );

    let preOrdersWithPhase = resPreOrderFilter;

    if (resPreOrderFilter?.length) {
      const lstOrder = await this.omsService.getListOrderES({
        orderCode: resPreOrderFilter?.map((e) => e?.orderCode),
      });

      // Lấy tất cả SKU từ các đơn pre-order
      const skuMappingOrder = lstOrder?.orders?.reduce((acc, order) => {
        acc[order?.orderCode] = _.compact(
          _.uniq(
            JSONPath({
              json: order,
              path: '$.details[*].detailAttachments[*].itemCode',
            }),
          ),
        );
        return acc;
      }, {});

      const listSku = Object.values(skuMappingOrder || {}).flat();

      // Lấy thông tin phase cho tất cả SKU
      const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({
        listSku: _.compact(_.uniq(listSku)),
      });

      // Check phase2 cho từng đơn
      preOrdersWithPhase = resPreOrderFilter?.map((order) => {
        const phase2 = osrDepositAmountBySku?.find(
          (e) => e.phaseId === 2 && skuMappingOrder?.[order?.orderCode]?.includes(e?.itemCode),
        );
        const isPhase2 =
          phase2 &&
          moment(moment().format('YYYY-MM-DD')).isBetween(
            moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
            moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
            undefined,
            '[]',
          );

        return {
          ...order,
          isPhase2: isPhase2 ? true : false,
        };
      });
    }

    const returnList: IOrder[] = [];
    lcvIds?.forEach((lcvId) => {
      // Đơn hàng Affiliate
      const listAffOrderOfLcvId = resAffOrderFilter
        ?.filter((item) => item?.lcvId === lcvId)
        ?.sort((a, b) => {
          // Sort by createdDate: newest to oldest
          return moment(a.createdDate).diff(moment(b.createdDate));
        });
      listAffOrderOfLcvId?.length && returnList.push(...listAffOrderOfLcvId);

      // Đơn hàng web/app
      const listWebAppOrderOfLcvId = resWebAppOrderFilter
        ?.filter((item) => item?.lcvId === lcvId)
        ?.sort((a, b) => {
          // Sort by createdDate: newest to oldest
          return moment(a.createdDate).diff(moment(b.createdDate));
        });
      listWebAppOrderOfLcvId?.length && returnList.push(...listWebAppOrderOfLcvId);

      // Đơn hàng Ecom
      const listEcomOrderOfLcvId = resEcomOrderFilter
        ?.filter((item) => item?.lcvId === lcvId)
        ?.sort((a, b) => {
          // Sort by createdDate: newest to oldest
          return moment(a.createdDate).diff(moment(b.createdDate));
        });
      listEcomOrderOfLcvId?.length && returnList.push(...listEcomOrderOfLcvId);

      // Đơn hàng Pre-order
      const listPreOrderOfLcvId = preOrdersWithPhase
        ?.filter((item) => item?.lcvId === lcvId && item?.['isPhase2'])
        ?.sort((a, b) => {
          // Sort by createdDate: newest to oldest
          return moment(a.createdDate).diff(moment(b.createdDate));
        });
      listPreOrderOfLcvId?.length && returnList.push(...listPreOrderOfLcvId);
    });
    return returnList;
  }

  async getAttributeByLcvIds(payload: GetAttributeByLcvIdsDto) {
    return this.familyPackageService.getPersonAttribute({
      lcvIds: payload.lcvIds,
    });
  }

  async getFamilyPromotionFeeByOrderCode(orderCode: string) {
    const resFamily = await this.familyPackageService.getFamilyPromotionFeeByOrderCode(orderCode);
    return resFamily?.find((item) => item?.orderCode === orderCode) || null;
  }

  async inactiveFamilyPackage(payload: InactiveFamilyPackageDto): Promise<boolean> {
    const { verifyMethod, phoneNumber, otp, templateType, insideCode, groupCode, modifiedBy, isOtpValid } = payload;

    await this.verifyMethodHandler({
      verifyMethod,
      phoneNumber,
      otp,
      templateType,
      insideCode,
      functionName: 'inactiveFamilyPackage',
      groupCode,
      isOtpValid,
    });

    // Step 2: Get group info
    const group = await this.familyPackageService.getGroupFamilyPackageByGroupCode({
      groupCodes: [groupCode],
    });

    if (!group?.[0]) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_FAMILY_PACKAGE_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_FAMILY_PACKAGE_NOT_FOUND),
          details: ErrorCode.getError(ErrorCode.RSA_FAMILY_PACKAGE_NOT_FOUND),
        },
        HttpStatus.NOT_FOUND,
      );
    }

    // Step 3: Check eligibility for all members
    const lcvIds = group[0]?.groupPersonJunctions?.map((item) => item?.lcvId) || [];
    if (lcvIds?.length) {
      await this.journeyCoreService.checkPersonEligibility(_.compact(_.uniq(lcvIds)));
    }

    // Step 4: Inactive group
    return this.familyPackageApiService.inactiveGroup({
      groupCode,
      modifiedBy,
    });
  }

  async calCulateTempPricingByRank(body: CalculateTempPriceingByRankDto) {
    const { totalVoucherPrice, totalDiscountAdjustment, totalServiceFee, totalWithoutFee } = body;
    // step 1: call api getGroupRankingDiscountByRankingIds
    const resGetGroupRankingDiscountByRankingIds = await this.familyPackageService.getGroupRankingDiscountByRankingIds(
      body?.listRankId,
    );
    // step 2: Tính toán số tiền sau giảm theo từng hạng
    const resCalculateTempPricingByRank = body?.listRankId?.map((item) => {
      const findItem = resGetGroupRankingDiscountByRankingIds?.find((e) => e?.rankingId === item);
      if (!findItem) return null;
      return {
        rankingId: item,
        tempPricingByRank:
          totalWithoutFee * (1 - findItem?.discountValue / 100) +
          totalServiceFee -
          totalVoucherPrice -
          totalDiscountAdjustment,
      };
    });
    return resCalculateTempPricingByRank?.filter(Boolean) || [];
  }

  async customerCheckExpiredOtp(groupCode: string) {
    const res = await this.redisService.get(`${KEY_REDIS_FAMILY_PACKAGE}:${groupCode}`);
    return !!res;
  }
}
