import * as Excel from 'exceljs';
import moment from 'moment';
import { ItemDataRes } from 'vac-nest-rsa-report';
import { columnToNumber, getColumnName } from '.';
import { BLACK_BORDER, CENTER_ALIGNMENT, LEFT_ALIGNMENT, TEMP_INJECTION_STATISTICS, TEXT_FONT } from '../constants';

const setInjectionStatisticsExcel = (
  worksheet: Excel.Worksheet,
  data: any,
  rowIndex: number,
  offset: number,
  stt: number,
) => {
  TEMP_INJECTION_STATISTICS.header.forEach((subHeader) => {
    // current cell's position
    const colIndex = columnToNumber(subHeader.col);
    // new cell's position
    const newColIndex = colIndex + offset;
    const newColLetter = getColumnName(newColIndex);
    // Get the cell reference for the current column and row
    const cellRef = `${newColLetter}${rowIndex + TEMP_INJECTION_STATISTICS.lineData.dataRowFisrt}`; // Assuming data starts from row 10

    // Get the cell
    const cell = worksheet.getCell(cellRef);

    cell.alignment = subHeader?.isLeftAlignment ? LEFT_ALIGNMENT : CENTER_ALIGNMENT;

    if (data?.isHeader) {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'd9d9d9' },
      };
    }
    // Những field đặt biệt cần format lại khi xuất file excel
    if (subHeader.property === 'stt') {
      cell.value = stt || '';
    }
    if (data.hasOwnProperty(subHeader.property)) {
      // Set the value of the cell to the corresponding value from dataArray
      if (moment(data[subHeader.property]).isValid() && subHeader?.isDate) {
        cell.value = moment(data[subHeader.property]).utcOffset(7).format('DD/MM/YYYY');
      } else {
        cell.value = data?.[subHeader?.property];
      }
      cell.font = { name: TEXT_FONT, bold: data?.isHeader, size: TEMP_INJECTION_STATISTICS.fontSize.normal };
    }
  });
};

export const getExcelInjectionStatistics = async (excelDatas: ItemDataRes[]): Promise<Buffer> => {
  return new Promise((resolve, reject) => {
    const workbook = new Excel.Workbook();

    const data = excelDatas || [];

    const worksheet = workbook.addWorksheet('Báo cáo thống kê số liệu tiêm');

    const limitCellNum = TEMP_INJECTION_STATISTICS.lineData.limitCellNum; //AS col
    const offset = 0;
    const startCol = TEMP_INJECTION_STATISTICS.lineData.titleFisrt;

    // set left header cells value row 8
    TEMP_INJECTION_STATISTICS.header.forEach((item) => {
      const cell = worksheet.getCell(`${item.col}${TEMP_INJECTION_STATISTICS.lineData.titleFisrt}`);
      cell.value = item.value;
      cell.alignment = CENTER_ALIGNMENT;
      cell.font = { name: TEXT_FONT, size: TEMP_INJECTION_STATISTICS.fontSize.title, bold: true };
      worksheet.getColumn(item.col).width = item.width;
    });

    //table border start from col B
    for (
      let row = TEMP_INJECTION_STATISTICS.lineData.titleFisrt;
      row <= TEMP_INJECTION_STATISTICS.lineData.titleFisrt + data?.length;
      row++
    ) {
      for (let col = 1; col <= TEMP_INJECTION_STATISTICS.lineData.limitCellNum + offset; col++) {
        const cell = worksheet.getCell(`${getColumnName(col)}${row}`);
        cell.border = {
          top: BLACK_BORDER,
          left: BLACK_BORDER,
          bottom: BLACK_BORDER,
          right: BLACK_BORDER,
        };
      }
    }

    TEMP_INJECTION_STATISTICS.outLineContent.forEach((item) => {
      const col = item?.col;
      const endCol = item?.endCol;
      const cell = worksheet.getCell(`${col}`);
      if (cell) {
        cell.value = item.value;
        cell.alignment = CENTER_ALIGNMENT;
        cell.font = { size: item?.size, bold: item?.isBold, name: TEXT_FONT };
        worksheet.mergeCells(`${col}:${endCol}`);
      }
    });

    // SHOP rabies report
    let stt = 1;
    data?.forEach?.((item: any, idx) => {
      setInjectionStatisticsExcel(worksheet, item, idx, offset, stt);
      stt++;
    });

    //Style col
    worksheet.getRow(2).height = 35;

    //page setup
    worksheet.pageSetup.printArea = `A1:${getColumnName(limitCellNum + offset)}${data?.length + 5}`;
    worksheet.pageSetup.orientation = 'landscape';
    worksheet.pageSetup.fitToPage = true;
    worksheet.pageSetup.paperSize = startCol + 1;
    worksheet.pageSetup.margins = { top: 0.75, bottom: 0.75, left: 0.25, right: 0.25, header: 0.3, footer: 0.3 };

    workbook.xlsx
      .writeBuffer()
      .then((buffer) => {
        resolve(buffer as any);
      })
      .catch((err) => {
        reject(err);
      });
  });
};
