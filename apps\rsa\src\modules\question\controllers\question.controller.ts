import { Controller, Get, HttpCode, HttpStatus, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, Public, generalSchema } from '@shared';
import { SearchQuestionDto } from 'vac-nest-examination';
import { SearchQuestionResDto } from '../dto';
import { QuestionService } from '../services/question.service';

@Controller({ path: 'question', version: '1' })
@ApiTags('Question')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(ClassResponse, SearchQuestionResDto)
export class QuestionController {
  constructor(private readonly questionService: QuestionService) {}

  @Public()
  @Get('search-question')
  @ApiOperation({
    summary: 'Lấy thông tin khám sáng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Product',
    schema: generalSchema(SearchQuestionResDto, 'object'),
  })
  async searchQuestion(@Query() query: SearchQuestionDto) {
    return this.questionService.searchQuestion(query);
  }
}
