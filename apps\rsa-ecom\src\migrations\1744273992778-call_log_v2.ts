import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CallLogV21744273992778 implements MigrationInterface {
  name = 'CallLogV21744273992778';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const createTableQuery = `
        CREATE TABLE call_log_v2
        (
            id             INT AUTO_INCREMENT PRIMARY KEY,
            inside_code    VARCHAR(255) NULL,
            inside_account VARCHAR(255) NULL,
            inside_name    VARCHAR(255) NULL,
            from_phone     VARCHAR(255) NULL,
            to_phone       VARCHAR(255) NULL,
            note           VARCHAR(255) NULL,
            call_id        VARCHAR(255) NULL,
            session_id     VARCHAR(255) NULL,
            call_type      VARCHAR(255) NULL,
            call_event     VARCHAR(255) NULL,
            dialog_state   VARCHAR(255) NULL,
            start_time     VARCHAR(255) NULL,
            end_time       VARCHAR(255) NULL,
            duration       INT NULL,
            created_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            record_id      VARCHAR(255) NULL
        );`;

    await queryRunner.query(createTableQuery);

    // await queryRunner.query(`CREATE UNIQUE INDEX IDX_SESSION_ID ON call_log_v2 (session_id);`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.query('DROP INDEX IDX_SESSION_ID ON call_log_v2;');
    await queryRunner.query('DROP TABLE call_log_v2;');
  }
}
