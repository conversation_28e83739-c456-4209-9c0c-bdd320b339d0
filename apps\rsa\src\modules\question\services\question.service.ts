import { Injectable } from '@nestjs/common';
import { ExaminationCoreService, SearchQuestionDto } from 'vac-nest-examination';

@Injectable()
export class QuestionService {
  constructor(private readonly examinationCoreService: ExaminationCoreService) {}

  /**
   * @TODO search question
   */
  async searchQuestion(query: SearchQuestionDto) {
    const data = await this.examinationCoreService.searchQuestion(query);

    return {
      items: data,
    };
  }
}
