import { MigrationInterface, QueryRunner } from 'typeorm';

export class ShopHub1721048088572 implements MigrationInterface {
  name = 'ShopHub1721048088572';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "shop_hub" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "shopCode" character varying NOT NULL, "shopHub" character varying NOT NULL, "shopName" character varying NOT NULL, "shopHubName" character varying NOT NULL, "isActive" boolean NOT NULL, CONSTRAINT "PK_bc9404fa44a229d01664eb87cf8" PRIMARY KEY ("id")); COMMENT ON COLUMN "shop_hub"."shopHub" IS 'shop hub'; COMMENT ON COLUMN "shop_hub"."shopName" IS 'shop name'; COMMENT ON COLUMN "shop_hub"."shopHubName" IS 'shopHubName name'; COMMENT ON COLUMN "shop_hub"."isActive" IS 'active name'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "shop_hub"`);
  }
}
