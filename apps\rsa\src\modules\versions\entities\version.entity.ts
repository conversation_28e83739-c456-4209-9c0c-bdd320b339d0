import { Column, CreateDateColumn, Entity } from 'typeorm';
import { BaseEntity } from '@shared';

@Entity({
  name: 'versions',
  orderBy: { created_at: 'DESC' },
})
export class Version extends BaseEntity {
  @Column()
  version: string;

  @Column()
  status: number;

  @Column()
  type: number;

  @Column({
    name: 'version_code',
  })
  versionCode: string;

  @Column()
  note: string;

  @CreateDateColumn({
    name: 'created_at',
  })
  createdAt: Date;
}
