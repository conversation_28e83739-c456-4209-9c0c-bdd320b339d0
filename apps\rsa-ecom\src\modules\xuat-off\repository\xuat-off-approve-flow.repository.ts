import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AbstractRepository } from '@shared/database/typeorm/abstract.repository';
import { EntityManager, Repository } from 'typeorm';
import { XuatOffApproveFlowEntity } from '../entities/xuat-off-approve-flow.entity';

@Injectable()
export class XuatOffApproveFlowRepository extends AbstractRepository<XuatOffApproveFlowEntity> {
  protected readonly logger = new Logger(XuatOffApproveFlowRepository.name);
  constructor(
    @InjectRepository(XuatOffApproveFlowEntity)
    private readonly repository: Repository<XuatOffApproveFlowEntity>,
    private readonly entityManager: EntityManager,
  ) {
    super(repository, entityManager);
  }
}
