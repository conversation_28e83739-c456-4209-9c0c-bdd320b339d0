import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { GetPersonByIdRes, Person, SearchPersonDto, SearchPersonRes } from 'vac-nest-family';

export class CustomSearchCustomerBody extends SearchPersonDto {
  customerDetail: GetPersonByIdRes;
}

export class CreatedCustomer {
  @ApiProperty()
  @Expose()
  nationalVaccineCode: string;

  @ApiProperty()
  @Expose()
  nationalVaccineId: string;

  @ApiProperty()
  @Expose()
  warningMessage?: {
    message?: string;
    lcvId?: string;
    nationalVaccineCode?: string;
    name?: string;
  };
}

export class CustomSearchCustomerResponse extends SearchPersonRes {
  @ApiProperty()
  @Expose()
  message: '';

  @ApiProperty()
  @Expose()
  createdCustomer: CreatedCustomer;

  @ApiProperty()
  @Expose()
  source: string;
}

export class MapTcqgToPersonResponse extends Person {}

export class MapTcqgToPersonBody {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @Expose()
  @IsString()
  nationalVaccineCode: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @Expose()
  @IsString()
  nationalVaccineId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @Expose()
  @IsString()
  lcvId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  frequentlyProvinceCode?: string;

  @ApiProperty()
  @Expose()
  frequentlyProvinceName?: string;

  @ApiProperty()
  @Expose()
  frequentlyDistrictCode?: string;

  @ApiProperty()
  @Expose()
  frequentlyDistrictName?: string;

  @ApiProperty()
  @Expose()
  frequentlyWardCode?: string;

  @ApiProperty()
  @Expose()
  frequentlyWardName?: string;

  @ApiProperty()
  @Expose()
  frequentlyAddress?: string;

  @ApiProperty()
  @Expose()
  temporaryProvinceCode?: string;

  @ApiProperty()
  @Expose()
  temporaryProvinceName?: string;

  @ApiProperty()
  @Expose()
  temporaryDistrictCode?: string;

  @ApiProperty()
  @Expose()
  temporaryDistrictName?: string;

  @ApiProperty()
  @Expose()
  temporaryWardCode?: string;

  @ApiProperty()
  @Expose()
  temporaryWardName?: string;

  @ApiProperty()
  @Expose()
  temporaryAddress?: string;
}

export class GetTcqgDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  phoneNumber?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  lcvId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  dateOfBirth?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  gender?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  frequentlyProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  frequentlyDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  frequentlyWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  frequentlyWardName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  frequentlyDistrictName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  frequentlyProvinceName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  temporaryProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  temporaryDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  temporaryWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isCheckTcqg?: boolean;
}
