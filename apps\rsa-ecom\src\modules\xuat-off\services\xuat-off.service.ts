import { FilesService } from '@libs/modules/files/services/files.service';
import { forwardRef, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { ErrorCode, OrderChanel, SystemException, UserDto } from '@shared';
import { PaginatedResultDto } from '@shared/dto/pagination';
import { PaginationOptionsDto } from '@shared/dto/pagination/pagination-options.dto';
import { APPLICATION, TENANT } from '@shared/enum/auth';
import { plainToInstance } from 'class-transformer';
import { JSONPath } from 'jsonpath-plus';
import _ from 'lodash';
import moment from 'moment';
import { Brackets, DataSource, In, Not } from 'typeorm';
import { IAMCoreService } from 'vac-nest-iam';
import { GetOrdersInfoDto, JourneyService } from 'vac-nest-journey';
import { EcomDisplay, GetOneOrderLibResponse, OMSService, OrderStatus } from 'vac-nest-oms';
import {
  EXOSteps,
  LIST_STATUS_VERIFY_ORDER,
  ORDER_STATUS,
  XUAT_OFF_ACTION,
  XUAT_OFF_STATUS,
  XuatOffStatus,
  XuatOffType,
} from '../constants';
import {
  ApproveMultipleXuatOffDto,
  ApproveXuatOffDto,
  CancelXuatOffDto,
  CreateXuatOffDto,
  GetOneOrderInfoDto,
  GetOrderInfoServiceDto,
  SearchXuatOffDto,
  UpdateXuatOffDto,
  XuatOffResponseDto,
} from '../dto';
import { GetXuatOffDto } from '../dto/get-xuat-off.dto';
import { XuatOffDetailEntity, XuatOffEntity, XuatOffStepInfoEntity } from '../entities';
import { XuatOffApproveFlowEntity } from '../entities/xuat-off-approve-flow.entity';
import { XuatOffApproveFlowRepository, XuatOffRepository } from '../repository';
import { XuatOffActionType } from './../constants/index';
import { XuatOffDetailService } from './xuat-off-detail.service';
import { XuatOffStepInfoService } from './xuat-off-step.service';

@Injectable()
export class XuatOffService {
  constructor(
    // Repository
    private readonly xuatOffApproveFlowRepository: XuatOffApproveFlowRepository,
    @Inject(forwardRef(() => XuatOffDetailService))
    private readonly xuatOffDetailService: XuatOffDetailService,
    private readonly xuatOffRepository: XuatOffRepository,

    // Service
    private readonly omsService: OMSService,
    private readonly journeyCoreService: JourneyService,
    private readonly xuatOffStepInfoService: XuatOffStepInfoService,
    private readonly iamCoreService: IAMCoreService,
    private readonly filesService: FilesService,
    private readonly dataSource: DataSource,
  ) {}

  async create(xuatOffDto: CreateXuatOffDto, user: UserDto): Promise<XuatOffResponseDto> {
    try {
      const orderEcom = await this.verify(xuatOffDto?.orderCodeEcom, true);
      await this._checkExistXuatOffDetails(
        xuatOffDto?.details?.map((detail) => detail.orderCodeShop),
        'CREATE',
      );

      const userRoles = await this.getUserRoleEcom(user?.employee_code);

      const xoCode = `XO${user.employee_code}${moment().valueOf()}`;
      const stepCreated = new XuatOffStepInfoEntity({
        xoCode: xoCode,
        employeeCode: user.employee_code || '',
        employeeName: user.full_name || '',
        step: XuatOffStatus.Pending,
      });

      const payloadCreateXO = new XuatOffEntity({
        ...xuatOffDto,
        xoCode,
        userRoles: userRoles || [],
        details: xuatOffDto.details.map(
          (detail) =>
            new XuatOffDetailEntity({
              ...detail,
              isDelete: false,
              xoCode: xoCode,
              status: XuatOffStatus.Pending,
              createdBy: user.employee_code || '',
              createdByName: user.full_name || '',
              modifiedBy: user.employee_code || '',
              modifiedByName: user.full_name || '',
            }),
        ),
        steps: [stepCreated], // Thay thế steps từ xuatOffData bằng defaultStep
        status: XuatOffStatus.Pending,
        isDelete: false,
        createdBy: user.employee_code || '',
        createdByName: user.full_name || '',
        modifiedBy: user.employee_code || '',
        modifiedByName: user.full_name || '',
        orderEcomCreatedBy: orderEcom?.createdBy || '',
        orderEcomCreatedByName: orderEcom?.createdByName || '',
      });
      const result = await this.xuatOffRepository.create(payloadCreateXO);

      return result;
    } catch (error) {
      throw error;
    }
  }

  async updateXuatOff(id: string, payload: UpdateXuatOffDto, user: UserDto): Promise<GetXuatOffDto> {
    const xuatOff = await this.xuatOffRepository.mustGetXuatOffById(id, { steps: true, details: true });
    // Đã tồn tại details của đơn hàng này trong phiếu xuất off
    await this._checkExistXuatOffDetails(
      payload?.details.filter((detail) => !detail?.id).map((detail) => detail?.orderCodeShop),
      'UPDATE',
      false,
      xuatOff?.xoCode,
    );
    if (xuatOff?.status !== XuatOffStatus.Pending) {
      throw new SystemException(
        {
          code: ErrorCode.XO_UPDATE_ONLY_PENDING,
          message: ErrorCode.getError(ErrorCode.XO_UPDATE_ONLY_PENDING),
          details: ErrorCode.getError(ErrorCode.XO_UPDATE_ONLY_PENDING),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    xuatOff.modifiedBy = user?.employee_code || '';
    xuatOff.modifiedByName = user?.full_name || '';

    this.xuatOffDetailService.updateXuatOffDetail(xuatOff, user, payload?.details);

    delete payload.details; // Xoá trường details khỏi payload để tránh ghi đè
    const xuatOffUpdate = new XuatOffEntity({ ...xuatOff, ..._.pickBy(payload, _.identity) });

    const updateXuatOff = await xuatOffUpdate.save();
    // Bỏ đi các chi tiết đã đánh dấu xóa
    updateXuatOff.details = updateXuatOff.details?.filter((detail) => !detail.isDelete);

    return this.findOne(updateXuatOff.id, updateXuatOff); // Gọi findOne để lấy lại thông tin chi tiết sau khi cập nhật
  }

  async findAll(filter: SearchXuatOffDto, paginate: PaginationOptionsDto): Promise<PaginatedResultDto<GetXuatOffDto>> {
    try {
      const queryBuilder = XuatOffEntity.createQueryBuilder('xuat_off').where('xuat_off.isDelete = :isDelete', {
        isDelete: false,
      });

      if (filter?.xoCode) {
        queryBuilder.andWhere('xuat_off.xo_code = :xoCode', { xoCode: filter.xoCode });
      }

      if (filter?.type?.length) {
        queryBuilder.andWhere('xuat_off.type  IN (:...type)', { type: filter.type });
      }

      if (filter?.status) {
        queryBuilder.andWhere('xuat_off.status IN (:...status)', {
          status: Array.isArray(filter?.status) ? filter?.status : [filter.status],
        });
      }

      if (filter?.reasonCode) {
        queryBuilder.andWhere('xuat_off.reason_code IN (:...reasonCode)', {
          reasonCode: Array.isArray(filter?.reasonCode) ? filter?.reasonCode : [filter.reasonCode],
        });
      }

      if (filter?.fromDate && filter?.toDate) {
        queryBuilder.andWhere('xuat_off.created_date BETWEEN :fromDate AND :toDate', {
          fromDate: moment(filter.fromDate).startOf('day').toDate(),
          toDate: moment(filter.toDate).endOf('day').toDate(),
        });
      }

      if (filter?.createdBy) {
        queryBuilder.andWhere('xuat_off.created_by IN (:...createdBy)', {
          createdBy: Array.isArray(filter.createdBy) ? filter.createdBy : [filter?.createdBy],
        });
      }

      if (filter?.keyword) {
        queryBuilder.innerJoin('xuat_off.details', 'search_detail').andWhere(
          new Brackets((qb) => {
            qb.where('search_detail.order_code_shop ILIKE :keyword', { keyword: `%${filter.keyword}%` })
              .orWhere('xuat_off.customer_phone ILIKE :keyword', { keyword: `%${filter.keyword}%` })
              .orWhere('xuat_off.order_code_ecom ILIKE :keyword', { keyword: `%${filter.keyword}%` });
          }),
        );
      }

      queryBuilder.orderBy('xuat_off.created_date', 'DESC');
      queryBuilder.limit(paginate?.pageSize ?? 10).offset(paginate?.skip ?? 0);

      const [listXuatOff, itemCount] = await queryBuilder.getManyAndCount();

      let sortedListXuatOff: XuatOffEntity[] = [];
      if (listXuatOff.length) {
        const xuatOffIds = listXuatOff.map((item) => item.id);
        const fullQueryBuilder = XuatOffEntity.createQueryBuilder('xuat_off')
          .leftJoinAndSelect('xuat_off.details', 'xuat_off_detail')
          .leftJoinAndSelect('xuat_off.steps', 'xuat_off_step_info')
          .where('xuat_off.id IN (:...ids)', { ids: xuatOffIds })
          .orderBy('xuat_off.created_date', 'DESC');

        sortedListXuatOff = await fullQueryBuilder.getMany();

        const idToXuatOffMap = new Map(sortedListXuatOff.map((item) => [item.id, item]));
        sortedListXuatOff = listXuatOff.map((item) => idToXuatOffMap.get(item.id)).filter((item) => item);
      }

      await this.mapOrderDetail(sortedListXuatOff);

      return plainToInstance(
        PaginatedResultDto,
        {
          items: plainToInstance(GetXuatOffDto, sortedListXuatOff, {
            excludeExtraneousValues: true,
          }),
          totalCount: itemCount,
        },
        { excludeExtraneousValues: true },
      ) as PaginatedResultDto<GetXuatOffDto>;
    } catch (error) {
      throw error;
    }
  }

  async mapOrderDetail(xuatOffs: XuatOffEntity[], orderCodeEcom?: string) {
    let arrOrderCode: string[] = _.uniqBy(
      _.compact(
        JSONPath({
          path: '$..orderCodeShop',
          json: xuatOffs,
        }),
      ),
    );
    const arrOrderCodeShop = arrOrderCode;
    if (orderCodeEcom) arrOrderCode = arrOrderCode.concat(orderCodeEcom);

    if (!arrOrderCode?.length) return;

    const [arrOrder, arrJourney] = await Promise.all([
      this.omsService.getListOrderES({ orderCode: arrOrderCode }),
      this.journeyCoreService.getStatusGiftByOrderCodes({ orderCodes: arrOrderCodeShop }),
    ]);
    const { orders } = arrOrder;

    xuatOffs.forEach((xuatOff) => {
      if (orderCodeEcom) {
        const order = orders.find((o) => o.orderCode === xuatOff?.orderCodeEcom);
        xuatOff['shopCode'] = order?.shopCode;
        xuatOff['shopName'] = order?.shopName;
      }

      xuatOff?.details.forEach((detail) => {
        const order = orders.find((o) => o.orderCode === detail.orderCodeShop);
        const journey = arrJourney.find((o) => o.orderCode === detail.orderCodeShop);
        detail['shopCode'] = order?.shopCode;
        detail['shopName'] = order?.shopName;
        detail['journeyId'] = order?.journeyId;
        detail['orderStatus'] = order?.orderStatus;
        detail['ticketCode'] = journey?.ticketCode || '';
      });
    });
  }

  async findOne(id: string, xuatOffDetails?: XuatOffEntity): Promise<GetXuatOffDto> {
    let xuatOff = xuatOffDetails || null;
    if (!xuatOffDetails) {
      const getXuatOff = await this.xuatOffRepository.findOne(
        { id },
        {
          details: true,
          steps: true,
        },
      );
      xuatOff = getXuatOff || null;
    }

    const [images, files] = await Promise.all([
      this.filesService.getS3PresignWithKeys({ urls: xuatOff?.images || [] }),
      this.filesService.getS3PresignWithKeys({ urls: xuatOff?.files || [] }),
    ]);
    xuatOff['images'] = images as any;
    xuatOff['files'] = files as any;

    await this.mapOrderDetail([xuatOff], xuatOff?.orderCodeEcom);
    return plainToInstance(GetXuatOffDto, xuatOff, { excludeExtraneousValues: true });
  }

  // async update(id: string, xuatOffDto: UpdateXuatOffDto) {
  //   return this.xuatOffRepositoryService.update(id, xuatOffDto);
  // }

  async remove(id: string) {
    return this.xuatOffRepository.softDelete({ id });
  }

  async verify(orderCode: string, isOnlyVerify = false): Promise<GetOneOrderLibResponse> {
    const order = await this.omsService.getOneOrder(orderCode);
    if (order?.orderChanel !== OrderChanel.FromRSAEcom) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_XUAT_OFF_VERIFY_ECOM,
        },
        HttpStatus.FORBIDDEN,
      );
    }
    if (order?.ecomDisplay !== EcomDisplay.AtShop) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_XUAT_OFF_VERIFY_AT_SHOP,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    // Kiểm tra orderStatus - chỉ cho phép những đơn khác 4,5,6
    const invalidOrderStatuses = [...LIST_STATUS_VERIFY_ORDER, OrderStatus.FinishDeposit];
    if (invalidOrderStatuses.includes(order?.orderStatus)) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_XUAT_OFF_VERIFY_ORDER_STATUS,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_XUAT_OFF_VERIFY_ORDER_STATUS).replace(
            '{orderStatus}',
            ORDER_STATUS[order?.orderStatus],
          ),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_XUAT_OFF_VERIFY_ORDER_STATUS).replace(
            '{orderStatus}',
            ORDER_STATUS[order?.orderStatus],
          ),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    // Đơn không thuộc bất ký phiếu xuất off ở trạng thái (Chở xử lý, Đã duyệt lần 1, Đã duyệt lần 2) → Báo lỗi nếu vi phạm: Đơn hàng ecom đã được tạo tại phiếu xuất off số <Mã XO>.
    await this._checkExistXuatOff(orderCode);
    if (isOnlyVerify) return order;
    const journey = await this.journeyCoreService.getStatusGiftByOrderCodes({ orderCodes: [orderCode] });
    return { ...order, ticketCode: journey?.at(0)?.ticketCode, orderStatus: journey?.at(0)?.orderStatus } as any;
  }

  /**
   * @description lấy danh sách đơn gộp nhóm
   */
  async getOrderInfoService(body: GetOrderInfoServiceDto): Promise<GetOneOrderLibResponse[]> {
    const payload: GetOrdersInfoDto = {
      ...body,
      keyWord: body?.phoneNumber,
      pageNumber: 1,
      pageSize: 20,
      sources: ['14', '15', '16', '1', '6'],
      orderStatus: LIST_STATUS_VERIFY_ORDER,
      orderAttributes: [],
    };
    const arrJourney = await this.journeyCoreService.getOrderInfoEcom(payload);

    if (!arrJourney?.items?.length) {
      return [];
    }

    // Lọc arrJourney.items theo fromDate và toDate (bao gồm cả giờ)
    let filteredJourneyItems = arrJourney.items;

    if (body?.fromDate || body?.toDate) {
      filteredJourneyItems = arrJourney.items.filter((item) => {
        const itemDate = moment(item.orderDate).utcOffset(7);

        // Kiểm tra fromDate (bao gồm giờ)
        if (body?.fromDate) {
          const fromDate = moment(body.fromDate).utcOffset(7);
          if (itemDate.isBefore(fromDate, 'milliseconds')) {
            return false;
          }
        }

        // Kiểm tra toDate (bao gồm giờ)
        if (body?.toDate) {
          const toDate = moment(body.toDate).utcOffset(7);
          if (itemDate.isAfter(toDate, 'milliseconds')) {
            return false;
          }
        }

        return true;
      });
    }

    const listOrderCodes = _.uniq(
      _.compact(
        JSONPath({
          path: '$[*].orderCode',
          json: filteredJourneyItems,
        }),
      ),
    );

    if (!listOrderCodes.length) {
      return [];
    }

    const arrOrder = await this.omsService.getListOrderES({ orderCode: listOrderCodes });

    const xuatOffDetails = await this.xuatOffDetailService.findByOrderCodeShops(
      arrOrder?.orders?.map((order) => order.orderCode) || [],
      body?.excludeXOCode,
    );

    const existingShopOrders = xuatOffDetails?.map((detail) => detail?.orderCodeShop) || [];

    return arrOrder?.orders?.filter((order) => !existingShopOrders.includes(order?.orderCode));
  }

  async getOneOrder(orderCode: string, payload: GetOneOrderInfoDto): Promise<GetOneOrderLibResponse> {
    const order = await this.omsService.getOneOrder(orderCode);

    // Thêm validation check date range
    if (payload?.fromDate && payload?.toDate) {
      if (moment(order?.createdDate).utcOffset(7).isBefore(moment(payload.fromDate).utcOffset(7), 'milliseconds')) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_XUAT_OFF_INVALID_DATE,
          },
          HttpStatus.FORBIDDEN,
        );
      }

      const isInRange = moment(order.createdDate)
        .utcOffset(7)
        .isBetween(moment(payload.fromDate).utcOffset(7), moment(payload.toDate).utcOffset(7), 'milliseconds', '[]');

      if (!isInRange) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_XUAT_OFF_DATE_RANGE,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    if (order?.orderChanel === OrderChanel.FromRSAEcom) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_XUAT_OFF_IS_ECOM,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    if (!LIST_STATUS_VERIFY_ORDER.includes(order?.orderStatus)) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_XO_INVALID_ORDER,
          message: ErrorCode.getError(ErrorCode.RSA_XO_INVALID_ORDER)
            .replace('{orderStatus}', ORDER_STATUS[order?.orderStatus])
            .replace('{action}', XUAT_OFF_ACTION['CREATE']),
          details: ErrorCode.getError(ErrorCode.RSA_XO_INVALID_ORDER)
            .replace('{orderStatus}', ORDER_STATUS[order?.orderStatus])
            .replace('{action}', XUAT_OFF_ACTION['CREATE']),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    // Đã tồn tại details của đơn hàng này trong phiếu xuất off
    await this._checkExistXuatOffDetails([orderCode], 'CREATE', false, payload?.excludeXOCode);

    return order;
  }

  async cancelXuatOff(id: string, payload: CancelXuatOffDto, user: UserDto): Promise<boolean> {
    const xuatOff = await this.xuatOffRepository.mustGetXuatOffById(id, { steps: true, details: true });
    if (xuatOff?.status !== XuatOffStatus.Pending) {
      // Only one cancel when in pending status
      throw new SystemException(
        {
          code: ErrorCode.XO_CAN_CANCEL_ONLY_PENDING,
          message: ErrorCode.getError(ErrorCode.XO_CAN_CANCEL_ONLY_PENDING),
          details: ErrorCode.getError(ErrorCode.XO_CAN_CANCEL_ONLY_PENDING),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }
    xuatOff.status = XuatOffStatus.Cancelled;
    xuatOff.modifiedBy = user?.employee_code || '';
    xuatOff.modifiedByName = user?.full_name || '';

    this.xuatOffStepInfoService.assignXuatOffStep(xuatOff, user, XuatOffStatus.Cancelled, payload?.notes);

    xuatOff?.details?.forEach((detail) => {
      detail.status = XuatOffStatus.Cancelled;
      detail.modifiedBy = user?.employee_code || '';
      detail.modifiedByName = user?.full_name || '';
    });

    await xuatOff.save();

    return true;
  }

  async processXuatOff(user: UserDto, payload: ApproveMultipleXuatOffDto, targetStatus: XuatOffStatus) {
    const [xuatOffs, approveFlow] = await Promise.all([
      this.xuatOffRepository.find(
        {},
        { id: In(payload?.items?.map((item) => item.xoId)) },
        { details: true, steps: true },
      ),
      this.xuatOffApproveFlowRepository.find({}, {}),
    ]);

    const approverRole = await this.getUserRoleEcom(user?.employee_code);

    this._checkApproveRuleMulti(
      xuatOffs,
      approveFlow,
      approverRole || [],
      payload,
      targetStatus !== XuatOffStatus.Rejected,
    );

    const listPayloadDetailId = payload?.items?.flatMap((item) => item.detailIds);
    const xuatOffClones = _.cloneDeep(xuatOffs).map((item) => ({
      ...item,
      details: item.details.filter((detail) => listPayloadDetailId.includes(detail.id)),
    }));
    const orderCodeShops = _.uniqBy(
      _.compact(
        JSONPath({
          path: '$..orderCodeShop',
          json: xuatOffClones,
        }),
      ),
    );

    if (targetStatus !== XuatOffStatus.Rejected)
      await this._checkExistXuatOffDetails(orderCodeShops, 'APPROVE', true, undefined, xuatOffClones);

    xuatOffs?.forEach((xuatOff) => {
      const payloadDetail = payload?.items?.find((item) => item.xoId === xuatOff?.id);
      this.handleProcessXuatOffDetail(xuatOff, user, targetStatus, payloadDetail, payload?.notes);
    });

    this.xuatOffRepository.save(xuatOffs);

    return true;
  }

  handleProcessXuatOffDetail(
    xuatOff: XuatOffEntity,
    user: UserDto,
    targetStatus: XuatOffStatus,
    payload: ApproveXuatOffDto,
    notes?: string,
  ) {
    xuatOff.modifiedBy = user.employee_code;
    xuatOff.modifiedByName = user.full_name;

    const currentDetails = xuatOff?.details.filter((detail) => payload.detailIds.includes(detail.id));
    if (!currentDetails?.length) return;

    currentDetails.forEach((detail) => {
      this._checkValidStatusTransition(detail.status, targetStatus);

      detail.modifiedBy = user.employee_code;
      detail.modifiedByName = user.full_name;
      detail.status = targetStatus;
    });

    // Nếu trường hợp duyệt phiếu, Từ chối các phiếu còn lại
    if ([XuatOffStatus.ApprovedByLeaderOrManager, XuatOffStatus.ApprovedByNVVH].includes(targetStatus)) {
      let rejectDetails: XuatOffDetailEntity[];

      switch (targetStatus) {
        // Duyệt lần 1, chỉ huỷ các phiếu ở trạng thái pending
        case XuatOffStatus.ApprovedByLeaderOrManager:
          rejectDetails = xuatOff?.details.filter(
            (detail) => !payload.detailIds.includes(detail.id) && detail?.status === XuatOffStatus.Pending,
          );
          break;

        // Duyệt lần 2, chỉ huỷ các phiếu ở trạng thái đã duyệt lần 1 (ApprovedByLeaderOrManager)
        case XuatOffStatus.ApprovedByNVVH:
          rejectDetails = xuatOff?.details.filter(
            (detail) =>
              !payload.detailIds.includes(detail.id) && detail?.status === XuatOffStatus.ApprovedByLeaderOrManager,
          );
          break;

        default:
          break;
      }

      currentDetails.forEach((detail) => {
        detail.modifiedBy = user.employee_code;
        detail.modifiedByName = user.full_name;
        detail.status = targetStatus;
      });

      rejectDetails?.forEach((detail) => {
        this._checkValidStatusTransition(detail.status, XuatOffStatus.Rejected);

        detail.modifiedBy = user.employee_code;
        detail.modifiedByName = user.full_name;
        detail.status = XuatOffStatus.Rejected;
      });
    }

    this.xuatOffStepInfoService.assignXuatOffStep(xuatOff, user, targetStatus, notes);
    this.updateFinalXuatOffStatus(xuatOff, targetStatus === XuatOffStatus.Rejected ? XuatOffStatus.Rejected : null);
  }

  async rejectXuatOff(user: UserDto, payload: ApproveMultipleXuatOffDto) {
    if (!payload?.items?.length) return false;
    return this.processXuatOff(user, payload, XuatOffStatus.Rejected);
  }

  async approveXuatOff(user: UserDto, payload: ApproveMultipleXuatOffDto) {
    if (!payload?.items?.length) return false;

    return this.processXuatOff(user, payload, payload?.approveStep);
  }

  public updateFinalXuatOffStatus(xuatOff: XuatOffEntity, targetStatus: XuatOffStatus) {
    const detailStatuses = xuatOff.details.map((detail) => detail.status);

    // 1. Ưu tiên: Nếu có ít nhất 1 đơn Pending
    if (detailStatuses.includes(XuatOffStatus.Pending)) {
      xuatOff.status = XuatOffStatus.Pending;
      return;
    }

    // 2. Ưu tiên: Nếu tất cả đơn đều Rejected
    if (detailStatuses.every((status) => status === XuatOffStatus.Rejected)) {
      xuatOff.status = XuatOffStatus.Rejected;
      return;
    }

    // 3. Ưu tiên: Nếu tất cả đơn đều cancel
    if (detailStatuses.every((status) => status === XuatOffStatus.Cancelled)) {
      xuatOff.status = XuatOffStatus.Cancelled;
      return;
    }

    // 4. Ưu tiên: Nếu có ít nhất 1 đơn duyệt lần 1
    if (detailStatuses.includes(XuatOffStatus.ApprovedByNVVH)) {
      xuatOff.status = XuatOffStatus.ApprovedByNVVH;
      return;
    }

    // 5. Ưu tiên: Nếu có ít nhất 1 đơn duyệt lần 2
    if (detailStatuses.includes(XuatOffStatus.ApprovedByLeaderOrManager)) {
      xuatOff.status = XuatOffStatus.ApprovedByLeaderOrManager;
      return;
    }

    // 6. Nếu trường hợp trả đơn, ưu tiên huỷ XO khi các đơn còn lại đều là huỷ hoặc từ chối
    if (detailStatuses.every((status) => status === XuatOffStatus.Cancelled || status === XuatOffStatus.Rejected)) {
      xuatOff.status = targetStatus || XuatOffStatus.Cancelled;
      return;
    }
  }

  async getUserRoleEcom(employeeCode: string): Promise<string[]> {
    return await this.iamCoreService.getUserRPFByUserName({
      applicationId: APPLICATION.RSA_ECOM,
      tenantId: TENANT.VACCINE,
      userName: employeeCode,
      type: '3',
    });
  }

  private _getActualApproveStep(detailStatus: XuatOffStatus): EXOSteps | null {
    switch (detailStatus) {
      case XuatOffStatus.Pending:
        return EXOSteps.APPROVE_1ST;

      case XuatOffStatus.ApprovedByLeaderOrManager:
        return EXOSteps.APPROVE_2ST;

      default:
        return null;
    }
  }

  private _checkApproveRuleMulti(
    xuatOffs: XuatOffEntity[],
    approveFlow: XuatOffApproveFlowEntity[],
    approverRoles: string[],
    payload: ApproveMultipleXuatOffDto,
    isApprove: boolean = true,
  ) {
    for (const xuatOff of xuatOffs) {
      const creatorRoles = xuatOff.userRoles;
      const userRole = xuatOff?.userRoles?.join(', ') || '';

      const payloadItem = payload.items.find((item) => item.xoId === xuatOff.id);
      if (!payloadItem) continue;

      const { detailIds = [] } = payloadItem;

      for (const detailId of detailIds) {
        const detail = xuatOff.details.find((d) => d.id === detailId);
        if (!detail) continue;

        const actualStep = this._getActualApproveStep(detail.status);

        if (!actualStep) continue;

        const validFlows = approveFlow.filter(
          (flow) => creatorRoles?.includes(flow.creatorRole) && flow.statusXO === actualStep,
        );

        if (!validFlows?.length) {
          this._throwApproveException(userRole, isApprove);
        }

        const isValid = validFlows.some((flow) => approverRoles.includes(flow.approverRole));
        if (!isValid) {
          this._throwApproveException(userRole, isApprove);
        }
      }
    }
  }

  private _throwApproveException(creatorName: string, isApprove: boolean) {
    const action = isApprove ? 'duyệt' : 'từ chối';
    const errorMessage = ErrorCode.getError(ErrorCode.XO_APPROVE_ROLE_NOT_ALLOWED)
      .replace('{role}', creatorName)
      .replace('{action}', action);

    throw new SystemException(
      {
        code: ErrorCode.XO_APPROVE_ROLE_NOT_ALLOWED,
        message: errorMessage,
        details: errorMessage,
        validationErrors: null,
      },
      HttpStatus.FORBIDDEN,
    );
  }

  private _checkValidStatusTransition(currentStatus: XuatOffStatus, nextStatus: XuatOffStatus) {
    const validStatusTransitions: Record<XuatOffStatus, XuatOffStatus[]> = {
      [XuatOffStatus.Pending]: [
        XuatOffStatus.ApprovedByLeaderOrManager,
        XuatOffStatus.Rejected,
        XuatOffStatus.Cancelled,
      ],
      [XuatOffStatus.ApprovedByLeaderOrManager]: [
        XuatOffStatus.ApprovedByNVVH,
        XuatOffStatus.Rejected,
        XuatOffStatus.Cancelled,
      ],
      [XuatOffStatus.ApprovedByNVVH]: [XuatOffStatus.Cancelled],
      [XuatOffStatus.Rejected]: [XuatOffStatus.Cancelled],
      [XuatOffStatus.Cancelled]: [],
    };

    /**
     * --> Từ chối lần 1 : pending(1)
     */

    const allowedStatuses = validStatusTransitions[currentStatus];

    if (!allowedStatuses.includes(nextStatus)) {
      const currentName = XUAT_OFF_STATUS[currentStatus];
      const nextName = XUAT_OFF_STATUS[nextStatus];

      throw new SystemException(
        {
          code: ErrorCode.XO_INVALID_STATUS_TRANSITION,
          message: ErrorCode.getError(ErrorCode.XO_INVALID_STATUS_TRANSITION)
            .replace('{current}', currentName)
            .replace('{next}', nextName),
          details: ErrorCode.getError(ErrorCode.XO_INVALID_STATUS_TRANSITION)
            .replace('{current}', currentName)
            .replace('{next}', nextName),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async _checkExistXuatOff(orderCode: string) {
    const xuatOff = await this.xuatOffRepository.findOne({
      orderCodeEcom: orderCode,
      status: Not(In([XuatOffStatus.Cancelled, XuatOffStatus.Rejected])),
    });

    if (xuatOff)
      throw new SystemException(
        {
          code: ErrorCode.XO_EXIST,
          message: ErrorCode.getError(ErrorCode.XO_EXIST).replace('{xoCode}', xuatOff?.xoCode),
          details: ErrorCode.getError(ErrorCode.XO_EXIST).replace('{xoCode}', xuatOff?.xoCode),
          validationErrors: null,
        },
        HttpStatus.CONFLICT,
      );
  }

  async _checkExistXuatOffDetails(
    orderCodes: string[],
    action: XuatOffActionType,
    isApproveAction: boolean = false,
    excludeXOCode?: string,
    xuatOffs?: XuatOffEntity[],
  ) {
    if (!orderCodes?.length) return;

    const [orders, xuatOffDetails] = await Promise.all([
      this.omsService.getListOrderES({
        orderCode: orderCodes,
      }),
      this.xuatOffDetailService.findByOrderCodeShops(orderCodes),
    ]);

    // Xác định valid orderStatus dựa trên action và type
    if (action === 'APPROVE' && xuatOffs?.length) {
      // Mapping orderCodeShops theo type để xác định valid statuses
      const autoSatelliteOrderCodes: string[] = [];

      xuatOffs.forEach((xuatOff) => {
        if (xuatOff.type === XuatOffType.AUTO_SATELLITE) {
          // Type = 2 (AUTO_SATELLITE)
          xuatOff.details?.forEach((detail) => {
            if (orderCodes.includes(detail.orderCodeShop)) {
              autoSatelliteOrderCodes.push(detail.orderCodeShop);
            }
          });
        }
      });

      // Check orderStatus với logic khác nhau cho từng type
      const invalidOrders = orders?.orders?.filter((order) => {
        if (autoSatelliteOrderCodes.includes(order?.orderCode)) {
          // Với type AUTO_SATELLITE, cho phép thêm orderStatus = 6 (FinishDeposit)
          return ![...LIST_STATUS_VERIFY_ORDER, OrderStatus.FinishDeposit].includes(order?.orderStatus);
        } else {
          // Với type MANUAL, chỉ check LIST_STATUS_VERIFY_ORDER như cũ
          return !LIST_STATUS_VERIFY_ORDER.includes(order?.orderStatus);
        }
      });

      this._throwInvalidOrderException(invalidOrders, action);
    } else {
      // Logic cho các action khác hoặc khi không có xuatOffs
      const invalidOrders = orders?.orders?.filter((order) => !LIST_STATUS_VERIFY_ORDER.includes(order?.orderStatus));
      this._throwInvalidOrderException(invalidOrders, action);
    }

    if (isApproveAction) return;

    const filteredXuatOffDetails = xuatOffDetails?.filter((detail) => detail?.xoCode !== excludeXOCode);

    if (filteredXuatOffDetails?.length) {
      throw new SystemException(
        {
          code: ErrorCode.XO_DETAIL_EXIST,
          message: ErrorCode.getError(ErrorCode.XO_DETAIL_EXIST)
            .replace('{xoCode}', filteredXuatOffDetails?.at(0)?.xoCode)
            .replace('{orderCode}', filteredXuatOffDetails?.at(0)?.orderCodeShop),
          details: ErrorCode.getError(ErrorCode.XO_DETAIL_EXIST)
            .replace('{xoCode}', filteredXuatOffDetails?.at(0)?.xoCode)
            .replace('{orderCode}', filteredXuatOffDetails?.at(0)?.orderCodeShop),
          validationErrors: null,
        },
        HttpStatus.CONFLICT,
      );
    }
  }

  private _throwInvalidOrderException(invalidOrders: any[], action: XuatOffActionType) {
    if (invalidOrders?.length) {
      const invalidOrder = invalidOrders[0];
      throw new SystemException(
        {
          code: ErrorCode.RSA_XO_INVALID_ORDER,
          message: ErrorCode.getError(ErrorCode.RSA_XO_INVALID_ORDER)
            .replace('{orderStatus}', ORDER_STATUS[invalidOrder?.orderStatus])
            .replace('{action}', XUAT_OFF_ACTION[action]),
          details: ErrorCode.getError(ErrorCode.RSA_XO_INVALID_ORDER)
            .replace('{orderStatus}', ORDER_STATUS[invalidOrder?.orderStatus])
            .replace('{action}', XUAT_OFF_ACTION[action]),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  /**
   * Gọi stored procedure sp_insert_xuatoff0d
   * @returns Kết quả từ stored procedure
   */
  async insertXuatOff0d(): Promise<boolean> {
    try {
      this.dataSource.query('SELECT * FROM sp_insert_xuatoff0d()');
      return true;
    } catch (error) {
      throw new SystemException(
        {
          code: ErrorCode.INTERNAL_SERVER,
          message: 'Lỗi khi thực thi stored procedure function insert data xuat off 0d',
          details: error.message,
          validationErrors: null,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
