# Tài liệu API createConsultantForFamilyPackage

## 1. Tổng quan

### 1.1 <PERSON><PERSON> tả

API `createConsultantForFamilyPackage` được sử dụng để tạo tư vấn cho gói gia đình (Family Package). Đ<PERSON><PERSON> là API chính để khởi tạo quy trình tư vấn và đặt hàng cho nhiều thành viên trong gia đình cùng lúc.

### 1.2 Thông tin API

- **Endpoint**: `POST /api/v1/consultants-family-package`
- **Controller**: `ConsultantsFamilyPackageController`
- **Service**: `ConsultantsFamilyPackageService.createConsultantForFamilyPackage`
- **Authentication**: Public (không cần token)
- **Tags**: Consultant-family-package

## 2. Request

### 2.1 Request Body (CreateConsultantFamilyPackageDto)

```typescript
{
  // Thông tin cơ bản
  "shopCode": "string",           // Mã shop (mặc định: RSA_ECOM_HARD_DEFAULT_SHOP_CODE)
  "shopName": "string",           // Tên shop (optional)
  "phoneNumber": "string",        // Số điện thoại (required)
  "custIdMain": "string",         // ID khách hàng chính
  "groupCode": "string",          // Mã nhóm gia đình (optional)

  // Thông tin người tạo
  "createdBy": "string",          // Người tạo (optional)
  "createdByName": "string",      // Tên người tạo (optional)

  // Danh sách thành viên
  "subList": [                    // Danh sách người tiêm
    {
      "personIdSub": "string",    // ID người tiêm
      "custIdSub": "string",      // ID khách hàng phụ
      // ... các thông tin khác
    }
  ],

  // Thông tin giỏ hàng
  "arrCartItem": [                // Danh sách sản phẩm trong giỏ (optional)
    {
      "productId": "string",
      "quantity": number,
      // ... các thông tin sản phẩm
    }
  ],

  // Danh sách số điện thoại
  "phones": [                     // Danh sách số điện thoại
    {
      "phoneNumber": "string",
      "lcvId": "string"
    }
  ],
  "listPhoneNumber": [            // Danh sách số điện thoại khác
    {
      "phoneNumber": "string",
      "lcvId": "string"
    }
  ],

  // Nguồn
  "fromSource": "rsa-affiliate"   // Nguồn đơn hàng (optional)
}
```

### 2.2 Headers

```
Content-Type: application/json
order-channel: string (optional)
cart-type: FAMILY_PACKAGE
lcv-id: string (ID của thành viên đầu tiên)
```

## 3. Response

### 3.1 Success Response (CreateConsultantRes)

```typescript
{
  "sessionId": "string",          // ID session giỏ hàng
  "journey": [                    // Danh sách journey được tạo
    {
      "id": "string",             // ID journey
      "personIdSub": "string",    // ID người tiêm
      "custIdSub": "string",      // ID khách hàng phụ
      "step": number,             // Bước hiện tại
      "stepIdCurrent": "string",  // ID bước hiện tại
      // ... các thông tin journey khác
    }
  ],
  "isAddCartSuccess": boolean     // Trạng thái thêm giỏ hàng thành công
}
```

### 3.2 Error Response

```typescript
{
  "statusCode": number,
  "message": "string",
  "error": "string"
}
```

## 4. Quy trình xử lý

### 4.1 Sequence Diagram

```mermaid
sequenceDiagram
    participant FE
    participant Service as BE
    participant OrdersFPService as OrdersFamilyPackageService
    participant FamilyService as FamilyService
    participant JourneyService as JourneyService
    participant CartAppService as CartAppService
    participant Redis as Redis

    FE->>Service: POST /api/v1/consultants-family-package
    Note over FE,Service: CreateConsultantFamilyPackageDto

    Note over Service: Extract personSubIds from payload.subList
    Service->>Service: personSubIds = payload.subList.map(item => item.personIdSub)

    Note over Service: Step 1: Save incentive order redis
    Service->>OrdersFPService: saveIncentiveOrderRedis(personSubIds)
    OrdersFPService->>Redis: Save incentive data
    Redis-->>OrdersFPService: Success
    OrdersFPService-->>Service: Success

    Note over Service: Step 2: Get DOB information
    Service->>FamilyService: getManyByLcvId({ lcvId: personSubIds })
    FamilyService-->>Service: personSub (family member info)

    Note over Service: Step 3: Prepare journey payload
    Service->>Service: Build payloadCreatedJourney from personSub

    Note over Service: Step 4: Validation (Parallel)
    par Validate Hoi Chan
        Service->>Service: validateHoiChanEvaluationRequests(personSubIds, personSub)
    and Validate Person Info
        Service->>Service: validatePersonInfo(personSub)
    end

    Note over Service: Step 5: Create Journey & Session (Parallel)
    par Create Multiple Journey
        Service->>JourneyService: postCreatedMultipleJourneyForFamilyPackage(payloadCreatedJourney)
        JourneyService-->>Service: dataJourneyList
    and Create Generic Session
        Service->>CartAppService: genericSession({ shopCode, lcvIds: personSubIds })
        Note over CartAppService: Headers: cart-type=FAMILY_PACKAGE, lcv-id=personSubIds[0]
        CartAppService-->>Service: genericSession (sessionId)
    end

    Note over Service: Step 6: Merge Cart for each member (Parallel)
    loop For each journey in dataJourneyList
        Service->>CartAppService: mergeCartToCustomer(journey details)
        Note over CartAppService: customerId=journey.custIdSub, sessionId, journeyId=journey.id
        CartAppService-->>Service: Success
    end

    Note over Service: Step 7: Merge Cart for main customer
    Service->>CartAppService: mergeCartToCustomer(main customer)
    Note over CartAppService: customerId=payload.custIdMain, sessionId, journeyList
    CartAppService-->>Service: Success

    Note over Service: Step 8: Build response
    Service->>Service: Build CreateConsultantRes
    Note over Service: { sessionId, journey: dataJourneyList, isAddCartSuccess }

    Service-->>FE: HTTP 200 OK
    Note over Service,FE: { sessionId, journey[], isAddCartSuccess }
```

### 4.2 Các bước chính

1. **Lấy thông tin người tiêm**: Trích xuất danh sách `personIdSub` từ `subList`
2. **Lưu incentive order redis**: Lưu thông tin khuyến mãi cho từng lcvId
3. **Lấy thông tin DOB**: Lấy ngày sinh của các thành viên để merge cart
4. **Tạo payload journey**: Chuẩn bị dữ liệu để tạo journey
5. **Validate**: Kiểm tra thông tin hội chẩn và thông tin cá nhân
6. **Tạo journey và session**: Tạo đồng thời journey và session giỏ hàng
7. **Merge cart**: Merge cart cho từng thành viên và khách hàng chính

### 4.3 Chi tiết quy trình

#### Bước 1: Chuẩn bị dữ liệu

```typescript
const personSubIds = payload?.subList?.map((item) => item?.personIdSub);
const orderChannel = this.req.headers?.['order-channel'] || '';
const source = orderChannel;
```

#### Bước 2: Lưu incentive order redis

```typescript
await this.ordersFamilyPackageService.saveIncentiveOrderRedis(personSubIds);
```

#### Bước 3: Lấy thông tin DOB

```typescript
const personSub = await this.familyService.getManyByLcvId({
  lcvId: personSubIds,
});
```

#### Bước 4: Validation

```typescript
await Promise.all([
  this.validateHoiChanEvaluationRequests(personSubIds, personSub),
  this.validatePersonInfo(personSub),
]);
```

#### Bước 5: Tạo journey và session

```typescript
const [dataJourneyList, genericSession] = await Promise.all([
  this.journeyService.postCreatedMultipleJourneyForFamilyPackage(payloadCreatedJourney),
  this.cartAppService.genericSession(
    {
      shopCode: payload?.shopCode,
      lcvIds: personSubIds,
    },
    {
      headers: {
        ...this.req.headers,
        'cart-type': EnmCartType.FAMILY_PACKAGE,
        'lcv-id': personSubIds?.at(0),
      },
    },
  ),
]);
```

#### Bước 6: Merge cart

```typescript
// Merge cart cho từng thành viên
const arrPromiseMerge = dataJourneyList.map((journey) =>
  this.cartAppService.mergeCartToCustomer({
    customerId: journey.custIdSub,
    sessionId: genericSession?.sessionId,
    // ... các thông tin khác
  }),
);

// Merge cart cho khách hàng chính
await concurrentPromiseThrowError(
  ...arrPromiseMerge,
  this.cartAppService.mergeCartToCustomer({
    customerId: payload?.custIdMain,
    sessionId: genericSession?.sessionId,
    // ... các thông tin khác
  }),
);
```

## 5. Validation Rules

### 5.1 Validation hội chẩn

- Kiểm tra trạng thái yêu cầu đánh giá hội chẩn
- Đảm bảo không có yêu cầu hội chẩn đang pending

### 5.2 Validation thông tin cá nhân

- Kiểm tra tính hợp lệ của thông tin các thành viên
- Validate dữ liệu đầu vào

## 6. Dependencies

### 6.1 Services sử dụng

- `JourneyService`: Tạo và quản lý journey
- `CartAppService`: Quản lý giỏ hàng và session
- `FamilyService`: Lấy thông tin thành viên gia đình
- `OrdersFamilyPackageService`: Xử lý đơn hàng gói gia đình
- `CustomersRuleService`: Validate rules khách hàng

### 6.2 External APIs

- Family Core Service: `getManyByLcvId`
- Cart Service: `genericSession`, `mergeCartToCustomer`
- Journey Service: `postCreatedMultipleJourneyForFamilyPackage`

## 7. Error Handling

### 7.1 Các lỗi thường gặp

- **400 Bad Request**: Dữ liệu đầu vào không hợp lệ
- **404 Not Found**: Không tìm thấy thông tin thành viên
- **500 Internal Server Error**: Lỗi hệ thống

### 7.2 Validation Errors

- Thiếu thông tin bắt buộc (phoneNumber)
- Thông tin thành viên không hợp lệ
- Lỗi validation hội chẩn

## 8. Notes

### 8.1 Đặc điểm quan trọng

- API này chỉ dành cho RSA (không dành cho RSA_ECOM)
- Hỗ trợ tạo đồng thời nhiều journey cho nhiều thành viên
- Tự động merge cart cho tất cả thành viên và khách hàng chính
- Lưu thông tin khuyến mãi vào Redis

### 8.2 Performance

- Sử dụng `Promise.all` để xử lý đồng thời
- Sử dụng `concurrentPromiseThrowError` để merge cart song song
- Cache thông tin incentive trong Redis

### 8.3 Security

- API public nhưng có validation nghiêm ngặt
- Kiểm tra quyền thông qua order-channel header
- Validate tất cả dữ liệu đầu vào
