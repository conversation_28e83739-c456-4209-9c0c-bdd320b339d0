import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from '@shared';
import { Controller, Get, HttpCode, HttpStatus } from '@nestjs/common';
import { RSAIntegrationService } from 'vac-nest-rsa-integration';

@Controller({ path: 'surveys', version: '1' })
@ApiTags('Survey')
export class SurveysController {
  constructor(private readonly rsaIntegrationService: RSAIntegrationService) {}

  @Get('master-data/hour-range')
  @Public()
  @ApiOperation({
    summary: '<PERSON><PERSON><PERSON> danh sách filter theo khoảng giờ.',
  })
  @HttpCode(HttpStatus.OK)
  getSurveyHourRange() {
    return this.rsaIntegrationService.getSurveyHourRange();
  }
}
