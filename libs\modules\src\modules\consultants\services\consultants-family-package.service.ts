import { FLOW_JOURNEY_ENUM, ORDER_ATTRIBUTE } from '@libs/modules/orders/constants';
import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { concurrentPromiseThrowError, EnmCartType, ErrorCode, IError, OrderChannels, SystemException } from '@shared';
import { Request } from 'express';
import { CartAppService } from 'vac-nest-cart-app';
import { ExaminationCoreService } from 'vac-nest-examination';
import { FamilyService, GetPersonByIdRes } from 'vac-nest-family';
import {
  CreatedJourneyDto,
  CreatedMultipleJourneyDto,
  JourneyService,
  STEP_NAME,
  TRANSACTION_TYPE,
  TypeStepId,
} from 'vac-nest-journey';
import { CustomersRuleService } from '../../customers/services/customers-rule.service';
import { RegimensService } from '../../regimens/services/regimens.service';
import { CreateConsultantFamilyPackageDto } from '../dto';
import { EvaluationRequestStatus } from '../enums/evaluation-status.enum';
import { ReturnHomeService } from 'vac-nest-return-home';
import { OrderRedisService } from '@libs/modules/orders/services/order-redis.service';
import { OrdersFamilyPackageService } from '@libs/modules/orders/services/orders-family-package.service';
import moment from 'moment';
import { WEEKDAY } from '../../cashback/constants';
import { OsrService } from 'vac-nest-osr';
import { CashbackOrderStatus } from '../../cashback/constants';
import { IncentiveOrderRedisResultDto } from '../../orders/dto/redis-incentive-order.dto';
import { PhoneList } from '../dto/create-consultant.dto';

@Injectable()
export class ConsultantsFamilyPackageService {
  constructor(
    private readonly journeyService: JourneyService,
    private readonly cartAppService: CartAppService,
    private readonly customersRuleService: CustomersRuleService,
    private readonly familyCoreService: FamilyService,
    private readonly regimensService: RegimensService,
    private readonly examinationService: ExaminationCoreService,
    private readonly returnHomeService: ReturnHomeService,
    private readonly orderRedisService: OrderRedisService,
    private readonly ordersFamilyPackageService: OrdersFamilyPackageService,
    private readonly osrService: OsrService,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  // Family package: only for RSA
  async createConsultantForFamilyPackage(payload: CreateConsultantFamilyPackageDto) {
    /**
     * @TODO
     *   - Tạo Journey
     *   - Tạo session
     *   - Merge cart customer
     *   - Tạo Journey Step
     */
    const orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    const source = orderChannel;

    // Lấy dob của personSub (Người tiêm) gắn vào merge cart
    // get DOB
    const personSubIds = payload?.subList?.map((item) => item?.personIdSub);

    // Thực hiện save incentive order redis cho từng lcvId
    await this.ordersFamilyPackageService.saveIncentiveOrderRedis(personSubIds);
    // End save incentive order redis cho từng lcvId

    //Start check rule kiểm tra xem khách có đơn trả hàng chưa xử lý không để thông báo
    const [result, listGetIncentiveOrder] = await Promise.all([
      this.journeyService.getOrderInfoFamilyPackage({
        lcvIds: personSubIds,
        orderStatus: [4, 5],
        pageSize: 1000,
      }),
      this.orderRedisService.getIncentiveOrderRedis(personSubIds),
    ]);

    // check weekday
    const today = moment().utcOffset(7).day();
    const isDayInWeek = WEEKDAY?.includes(today);
    // check don affiliate
    const countTicketAffiliate = this.checkShopAffiliate(listGetIncentiveOrder);

    if (
      !OrderChannels.RSA_AFFILIATE.includes(this.req.headers['order-channel'] as string) &&
      countTicketAffiliate &&
      isDayInWeek
    ) {
      const mobileCarrierSelected = await this.checkMobileCarrierSelection(payload?.phoneNumber);
      if (!mobileCarrierSelected) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_SAVE_MOBILE_CARRIER_RULE,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    const orderCodes = result?.items?.map((order) => order?.orderCode)?.filter((order) => order);
    if (orderCodes?.length) {
      const orderReturns = await this.returnHomeService.searchOrderReturn({ orderCodes, status: [1] });
      const orderReturnCodes = orderReturns?.filter((x) => x?.orderReturnCode)?.map((x) => x?.orderReturnCode);

      if (orderReturnCodes?.length) {
        const exception: IError = {
          code: ErrorCode.RSA_ERROR_CONSULTANTS_ORDER_RULE,
          message: ErrorCode.getError(ErrorCode.RSA_ERROR_CONSULTANTS_ORDER_RULE).replace(
            '{orderCode}',
            orderReturnCodes?.[0],
          ),

          details: ErrorCode.getError(ErrorCode.RSA_ERROR_CONSULTANTS_ORDER_RULE).replace(
            '{orderCode}',
            orderReturnCodes?.[0],
          ),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }
    }

    const payloadCreatedJourney: CreatedMultipleJourneyDto = {
      items: payload?.subList?.map((item) => {
        const findDataIncentiveOrder = listGetIncentiveOrder?.find((x) => x?.lcvId === item?.personIdSub)?.data;
        const isOrderAff = findDataIncentiveOrder?.orderAttribute === ORDER_ATTRIBUTE.DON_HANG_SHOP_AFFILIATE;
        const shopCodeCreateIncentive = isOrderAff
          ? findDataIncentiveOrder?.shopCodeCreate
          : findDataIncentiveOrder?.shopCode;
        const shopNameCreateIncentive = isOrderAff
          ? findDataIncentiveOrder?.shopNameCreate
          : findDataIncentiveOrder?.shopName;
        return {
          ...payload,
          createdBy: findDataIncentiveOrder?.createdBy || payload?.createdBy || '',
          createdByName: findDataIncentiveOrder?.createdByName || payload?.createdByName || '',
          personIdSub: item?.personIdSub || '',
          custIdSub: item?.custIdSub || '',
          shopCodeCreate: shopCodeCreateIncentive || payload?.shopCode || undefined,
          shopNameCreate: shopNameCreateIncentive || payload?.shopName || undefined,
          stepIdCurrent: TypeStepId.TIEP_NHAN_THONG_TIN,
          flow: FLOW_JOURNEY_ENUM.COMMON,
          step: [
            {
              stepId: TypeStepId.TIEP_NHAN_THONG_TIN,
              stepName: STEP_NAME[TypeStepId.TIEP_NHAN_THONG_TIN] ?? '',
              transactionNums: '',
              transactionType: TRANSACTION_TYPE['NONE'], // tạo tư vấn transaction type = 0
            },
          ],
          source,
          custIdSubList: undefined,
          personIdSubList: undefined,
        };
      }),
    };

    const [personSub, getDOB] = await Promise.all([
      this.familyCoreService.getListPrimaryPerson(personSubIds),
      this.regimensService.getAgeRanges(),
    ]);

    await Promise.all([
      this.validateHoiChanEvaluationRequests(personSubIds, personSub),
      this.validatePersonInfo(personSub),
    ]);
    // const listLcvId = payload?.phones?.map((i) => i?.lcvId) || payload?.listPhoneNumber?.map((i) => i?.lcvId) || [];

    const [dataJourneyList, genericSession] = await Promise.all([
      this.journeyService.postCreatedMultipleJourneyForFamilyPackage(payloadCreatedJourney),
      this.cartAppService.genericSession(
        { shopCode: payload?.shopCode, lcvIds: personSubIds },
        {
          headers: {
            ...this.req.headers,
            'cart-type': EnmCartType.FAMILY_PACKAGE,
            'lcv-id': personSubIds?.at(0),
          } as any,
        },
      ),
      // this.customersRuleService._verifyEmailFpter(listLcvId),
    ]);
    // const { phoneRegister, email } = resVerifyEmailFpter;
    const arrPromiseMerge = [];
    const arrPromiseDOB = [];
    for (const journey of dataJourneyList) {
      const person = personSub.find((e) => e.lcvId === journey.personIdSub);
      const mappingUniKey = [person?.from, person?.to, person?.ageUnitCode].join('_');
      const isHaveDateOfBirth = mappingUniKey == '0_0_0';
      const dob = !isHaveDateOfBirth
        ? new Date(getDOB.find((e) => e.uniqkey == mappingUniKey)?.dob)
        : person?.dateOfBirth;
      arrPromiseMerge.push(
        this.cartAppService.mergeCartToCustomer(
          {
            customerId: payload?.custIdMain,
            sessionId: genericSession?.sessionId,
            phoneNumber: payload?.phoneNumber,
            phoneRegister: '',
            email: '',
            dob, //Xem lại, đang lấy dob của thằng đầu tiên
            vaccinationCode: person?.lcvId || '', //Xem lại, đang lấy dob của thằng đầu tiên
            createdBy: payload?.createdBy,
            createdByName: payload?.createdByName,
            journeyId: journey?.id,
            groupCode: payload?.groupCode,
          },
          {
            headers: {
              ...this.req.headers,
              'cart-type': EnmCartType.FAMILY_PACKAGE,
              'lcv-id': journey.personIdSub,
            } as any,
          },
        ),
      );
    }

    await concurrentPromiseThrowError(
      ...arrPromiseMerge,
      this.cartAppService.mergeCartToCustomer({
        customerId: payload?.custIdMain,
        sessionId: genericSession?.sessionId,
        phoneNumber: payload?.phoneNumber,
        createdBy: payload?.createdBy,
        createdByName: payload?.createdByName,
        journeyId: '',
        journeyList: dataJourneyList?.map((item) => ({
          journeyId: item?.id,
          lcvId: item?.personIdSub,
        })),
        groupCode: payload?.groupCode,
      }),
    );
    for (const journey of dataJourneyList) {
      arrPromiseDOB.push(
        this.cartAppService.updateDob(
          genericSession?.sessionId,
          {
            orderAttribute: 9,
          },
          {
            headers: {
              ...this.req.headers,
              'cart-type': EnmCartType.FAMILY_PACKAGE,
              'lcv-id': journey.personIdSub,
            } as any,
          },
        ),
      );
    }
    await concurrentPromiseThrowError(
      ...arrPromiseDOB,
      this.cartAppService.updateDob(genericSession?.sessionId, { orderAttribute: 9 }),
    );

    const cart = await this.cartAppService.getCart(
      {
        sessionId: genericSession?.sessionId,
        phoneNumber: payload?.phoneNumber,
      },
      {
        headers: {
          ...this.req.headers,
          'cart-type': EnmCartType.FAMILY_PACKAGE,
          'lcv-id': personSubIds?.at(0),
        } as any,
      },
    );

    const listDataStepJourney = await Promise.all(
      dataJourneyList?.map((item) =>
        this.journeyService.postCreatedJourneyStep({
          journeyId: item?.id,
          stepId: TypeStepId.TU_VAN,
          stepName: STEP_NAME[TypeStepId.TU_VAN] ?? '',
          createdBy: payload?.createdBy,
          transactionNums: genericSession?.sessionId,
          transactionType: TRANSACTION_TYPE['CART_SESSION'], // tạo tư vấn với session cart transaction type = 1
        }),
      ),
    );
    return {
      ...genericSession,
      journeys: listDataStepJourney,
      cart: cart || null,
      isAddCartSuccess: true,
    };
  }

  private readonly validateHoiChanEvaluationRequests = async (lcvIds: string[], persons: GetPersonByIdRes[]) => {
    // validate hoi chan evaluation requests
    const evaluationRequest = await this.examinationService.getEvaluationRequestByLcvIds(lcvIds);
    const choHoiChanEvaluationRequests = evaluationRequest?.filter(
      (e) => e?.status === EvaluationRequestStatus.Cho_hoi_chan,
    );
    if (choHoiChanEvaluationRequests?.length > 0) {
      const choHoiChanEvaluationRequestLcvIds = choHoiChanEvaluationRequests.map((e) => e.lcvId);
      const choHoiChanPersonSubs = persons.filter((e) => choHoiChanEvaluationRequestLcvIds.includes(e.lcvId));
      const errorMessage = ErrorCode.getError(ErrorCode.CHO_HOI_CHAN_EVALUATION_REQUEST).replace(
        '<Name>',
        choHoiChanPersonSubs?.map((e) => e?.name).join(', '),
      );
      throw new SystemException(
        {
          code: ErrorCode.CHO_HOI_CHAN_EVALUATION_REQUEST,
          details: errorMessage,
          message: errorMessage,
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  };

  private readonly validatePersonInfo = async (people: GetPersonByIdRes[]) => {
    // validate customer has enough information
    const validateKeys = [
      'name',
      'dateOfBirth',
      'frequentlyProvinceCode',
      'frequentlyProvinceName',
      'frequentlyDistrictCode',
      'frequentlyDistrictName',
      'frequentlyAddress',
      'temporaryProvinceCode',
      'temporaryProvinceName',
      'temporaryDistrictCode',
      'temporaryDistrictName',
      'temporaryAddress',
      'ethnicCode',
    ];
    if (people.length) {
      const invalidPeople = people.filter((_person) =>
        Object.keys(_person).find((key) => validateKeys.includes(key) && !_person[key]),
      );
      if (invalidPeople.length) {
        const errorMessage = ErrorCode.getError(ErrorCode.CUSTOMER_HAS_NOT_ENOUGH_INFO).replace(
          '<Name>',
          invalidPeople.map((e) => e?.name).join(', '),
        );
        throw new SystemException(
          {
            code: ErrorCode.CUSTOMER_HAS_NOT_ENOUGH_INFO,
            details: errorMessage,
            message: errorMessage,
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  };

  private async checkMobileCarrierSelection(phoneNumber: string) {
    const receipt = await this.osrService.getListCashBackByPhone([phoneNumber]);
    if (receipt?.length) return true;
    const cashbackSuggestOrders = await this.osrService.searchCashbackSuggestByPhoneNumber([phoneNumber]);
    const order = cashbackSuggestOrders?.find(
      (item) => item?.status === CashbackOrderStatus.DON_HANG_DANG_XU_LY && item?.isCashBack,
    );
    if (!order) return true;
    if (!order?.brandUniqueId) return false;
    return true;
  }

  private checkShopAffiliate(listOrder: IncentiveOrderRedisResultDto[]) {
    const listAffiliate = listOrder?.map(
      (item) => item?.data?.orderAttribute === ORDER_ATTRIBUTE.DON_HANG_SHOP_AFFILIATE,
    );
    return listAffiliate.length;
  }
}
