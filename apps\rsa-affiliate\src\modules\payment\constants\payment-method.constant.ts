import { FilterPaymentMethodDto } from '../dto/filter-payment-method.dto';
import { PaymentMethod } from 'vac-nest-payment-gateway';
import { VoucherVendorsEnum } from '../enum';

export const ALLOWED_PAYMENT_METHOD: FilterPaymentMethodDto[] = [
  {
    allowedPaymentMethodId: PaymentMethod.PAY_AT_SHOP,
    allowedVendors: 'ALL',
  },
  {
    allowedPaymentMethodId: PaymentMethod.CASH,
    allowedVendors: 'ALL',
  },
  {
    allowedPaymentMethodId: PaymentMethod.TRANSFER,
    allowedVendors: 'ALL',
  },
  {
    allowedPaymentMethodId: PaymentMethod.CARD,
    allowedVendors: 'ALL',
  },
  {
    allowedPaymentMethodId: PaymentMethod.VOUCHER,
    allowedVendors: [VoucherVendorsEnum.URBOX, VoucherVendorsEnum.GOTIT],
  },
  {
    allowedPaymentMethodId: PaymentMethod.WALLET,
    allowedVendors: 'ALL',
  },
];
