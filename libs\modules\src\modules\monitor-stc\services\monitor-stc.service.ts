import { Inject, Injectable, Logger } from '@nestjs/common';
import { GetMonitorStcDto } from '../dto/get-monitor-stc.dto';
import { DisTanceType, EPaymentStatus, VaccineRecordStatus } from '../enum/vaccination-book.enum';
import { VaccinationBookRes } from '../dto/response.vaccination-book.dto';
import { VaccineSortingStrategy } from './vaccine-sorting.strategy';
import { VaccinationRecordProcessor } from './vaccination-record-processor.service';
import { VaccinationDataProvider } from './vaccination-data.provider';
import { CalculateTempPricingDto } from '../dto/calculate-temp-pricing.dto';
import { CalculateTempPricingResponseDto } from '../dto/calculate-temp-pricing-response.dto';
import * as _ from 'lodash';
import { Request } from 'express';
import { REQUEST } from '@nestjs/core';
import { EnmCartType } from '@shared';
import { OsrService } from 'vac-nest-osr';

@Injectable()
export class MonitorStcService {
  private readonly logger = new Logger(MonitorStcService.name);

  constructor(
    private readonly dataProvider: VaccinationDataProvider,
    private readonly sortingStrategy: VaccineSortingStrategy,
    private readonly recordProcessor: VaccinationRecordProcessor,
    private readonly osrService: OsrService,

    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  /**
   * Convert distance type to moment format
   */
  selectTypeDate(typeDate: DisTanceType): 'days' | 'months' | 'years' {
    switch (typeDate) {
      case DisTanceType.DAY:
        return 'days';
      case DisTanceType.MONTH:
        return 'months';
      case DisTanceType.YEAR:
        return 'years';
      default:
        return 'days';
    }
  }

  /**
   * Calculate payment status
   */
  handlePaymentStatus(waittingPaid: number, isPaid: boolean): EPaymentStatus {
    if (waittingPaid === 5) {
      return EPaymentStatus.PARTLY_PAID;
    }
    return isPaid ? EPaymentStatus.PAID : EPaymentStatus.NOT_PAID;
  }

  /**
   * Main method to load the vaccination book
   */
  async loadVaccinationBook(query: GetMonitorStcDto): Promise<VaccinationBookRes> {
    try {
      const { lcvId, birthday } = query;
      const person = await this.dataProvider.getPersonByLcvId(lcvId, birthday);
      const personId = person?.id || '';

      // Initialize response object
      const vacBookRes: VaccinationBookRes = {
        personId,
        lcvId,
        // person: {
        //   ...person,
        // },
        items: [],
      };

      // Short-circuit if no personId found
      if (!personId) {
        this.logger.warn(`No person found with LCV ID: ${lcvId}`);
        return vacBookRes;
      }

      // Fetch all required data
      const {
        diseaseGroups,
        histories,
        schedulesData,
        // listPreOrder,
        cart,
        // cartDataSum,
      } = await this.dataProvider.fetchAllData(personId, lcvId, query);

      // Call api suggest ranking
      const listLcv: string[] = _.uniq(
        cart?.ranking?.listPersonAttribute?.filter((item) => item?.attributeCode === '1')?.map((item) => item?.lcvId),
      );

      const cartType = (this.req.headers?.['cart-type'] as string) || '';
      const rankingRes = await this.dataProvider.calculateTempPricing({
        arrSku: [],
        expectedRank: query.expectedRank,
        sessionId: query.sessionId,
        groupCode: query.groupCode,
        groupTypeCode: 1, //BA bảo luôn là 1
        lcvIds: +cartType === EnmCartType.FAMILY_PACKAGE ? listLcv : [lcvId],
        shopCode: query?.shopCode,
      });
      // Không lấy pre-order
      const listPreOrder = [];

      // Fetch SKU information
      const { listSkuInfo, resListSkuHistory } = await this.dataProvider.fetchSkuInformation(histories, listPreOrder);

      // Process disease groups
      const getByAge = await this.osrService.getAgeSuggestDiseaseByAge(person?.customerAge?.year);
      vacBookRes.items = this.recordProcessor.processDiseaseGroups(
        diseaseGroups,
        histories,
        schedulesData,
        listPreOrder,
        listSkuInfo,
        resListSkuHistory,
        cart,
        getByAge,
      );

      // Enrich with shop and product information
      const enrichedItems = await this.enrichWithProductInfo(
        vacBookRes.items,
        query?.shopCode || (this.req.headers['shop-code'] as string),
      );

      // Filter by rule
      vacBookRes.items = this.recordProcessor.processFilterDiseaseGroupsByRule(enrichedItems);

      // filter những sku không có trong osr
      vacBookRes.items = this.recordProcessor.filterVaccinationRecordsByAge(vacBookRes.items, getByAge);

      // Sort items
      const needTotalExpenseNextRank =
        rankingRes?.suggestRankingResult?.needTotalExpenseNextRank || cart?.ranking?.needTotalExpenseNextRank || 0;
      vacBookRes.items = this.sortingStrategy.applyRuleSort(
        vacBookRes.items,
        needTotalExpenseNextRank,
        1, // BA NguyeneNT81 confirm là không chia nữa ==> Tức là luôn là 1
        // listLcv?.length || cartDataSum?.headerData?.journeyList?.length || 1, //Đôi khi cart sum nó không trả ra journeyList nên bắt buộc phải viết vầy
      );

      return vacBookRes;
    } catch (error) {
      this.logger.error(`Error loading vaccination book: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Enrich items with shop and product information
   */
  private async enrichWithProductInfo(
    items: VaccinationBookRes['items'],
    shopCode?: string,
  ): Promise<VaccinationBookRes['items']> {
    try {
      // Extract shop codes and SKUs
      const { listSkuNeedToEnrichData } = this.dataProvider.extractShopCodesAndSkus(items);

      // Get product information
      const productsInfo = await this.dataProvider.getProductInfoBySku(listSkuNeedToEnrichData, shopCode);

      // Enrich with product info
      const enrichedWithProducts = items.map((item) => ({
        ...item,
        vaccinationRecords: (item.vaccinationRecords || []).map((record) => {
          const isEnrichData =
            ([VaccineRecordStatus.Da_hen, VaccineRecordStatus.Cart_suggest_da_hen]?.includes(record?.status) &&
              !record?.price) ||
            record?.status === VaccineRecordStatus.Chua_tiem;

          const product = productsInfo.find((p) => p.sku === record.sku);
          const measure = record?.unitCodeSale
            ? product?.measures?.find((m) => m.measureUnitId === record.unitCodeSale)
            : product?.measures?.find((m) => m.isSellDefault);

          if (!isEnrichData) {
            return { ...record, price: measure?.price };
          }

          const productAttributeOptionId = product?.attributeShop?.attributeOptionId || null;
          return {
            ...record,
            unitCodeSale: record?.unitCodeSale || measure?.measureUnitId || null,
            unitNameSale: record?.unitNameSale || measure?.measureUnitName || null,
            isMultiDose: record?.isMultiDose || product?.isMultiDose || null,
            price: measure?.price || null,
            //Loại trừ dòng có SKU có tag "TẠM HẾT HÀNG", "KHÁCH ĐẶT MỚI LẤY", "PRE-ORDER"
            isResTricted:
              (!!productAttributeOptionId &&
                [
                  9665, //TẠM HẾT HÀNG
                  9666, // KHÁCH ĐẶT MỚI LẤY
                  9668, //TẠM HẾT HÀNG
                ]?.includes(productAttributeOptionId)) ||
              product?.isPreOrder,
          };
        }),
      }));

      return enrichedWithProducts;
    } catch (error) {
      this.logger.error(`Error enriching with shop and product info: ${error.message}`, error.stack);
      return items; // Return original items on error
    }
  }

  /**
   * Calculate temporary pricing for consultation needs
   * @param dto The calculation request parameters
   * @returns Calculation results including member pricing and total
   */
  async calculateTempPricing(dto: CalculateTempPricingDto): Promise<CalculateTempPricingResponseDto> {
    try {
      this.logger.log(`Calculating temporary pricing for ${dto.arrSku.length} items`);
      return await this.dataProvider.calculateTempPricing(dto);
    } catch (error) {
      this.logger.error(`Error calculating temporary pricing: ${error.message}`, error.stack);
      throw error;
    }
  }
}
