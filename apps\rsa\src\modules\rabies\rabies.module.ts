import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { <PERSON>biesController } from './controllers/rabies.controller';
import { RabiesService } from './services/rabies.service';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { MonitorCoreModule } from 'vac-nest-monitor';
import { ReportCoreModule } from 'vac-nest-report';

@Module({
  imports: [ExaminationCoreModule, MonitorCoreModule, ReportCoreModule],
  controllers: [RabiesController],
  providers: [RabiesService],
})
export class RabiesModule {}
