import { Body, Controller, Get, Headers, HttpCode, HttpStatus, Put, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, Public, generalSchema } from '@shared';
import {
  AssignRoomDto,
  AssignRoomDtoV2,
  ExaminationCoreService,
  GetRoom,
  GetRoomVaccineDto,
  GetRoomVaccineRes,
  TicketDetailRes,
} from 'vac-nest-examination';
import _ from 'lodash';

@Controller({ path: 'rooms', version: '1' })
@ApiTags('Room')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(ClassResponse, GetRoomVaccineRes, GetRoom)
export class RoomsController {
  constructor(private readonly examinationCoreService: ExaminationCoreService) {}

  @Public()
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách phòng khám/tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin danh sách phác đồ',
    schema: generalSchema(GetRoomVaccineRes, 'object'),
  })
  getRooms(@Query() getRoomVaccineDto: GetRoomVaccineDto) {
    return this.examinationCoreService.getRoomVaccineV2(getRoomVaccineDto);
  }

  @Public()
  @Get('get-by-shop-code')
  @ApiOperation({
    summary: 'Lấy thông tin phòng khám/tiêm theo shopcode',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin danh sách phác đồ',
    schema: generalSchema(GetRoom, 'object'),
  })
  async getRoomByShopCode(@Query() getRoomVaccineDto: GetRoomVaccineDto, @Headers('shop-code') shopCode: string) {
    getRoomVaccineDto.shopCode = shopCode;
    const rooms = await this.examinationCoreService.getRoomVaccineV2(getRoomVaccineDto);
    return rooms?.items?.at(0) || null;
  }

  @Public()
  @Put('assign-room')
  @ApiOperation({
    summary: 'Assign phòng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin phiếu khám',
    schema: generalSchema(TicketDetailRes, 'object'),
  })
  async assignRoom(@Body() assignRoomDto: AssignRoomDto) {
    return await this.examinationCoreService.assignRoom(assignRoomDto);
  }

  @Public()
  @Put('assign-room-group')
  @ApiOperation({
    summary: 'Assign phòng đơn nhóm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin phiếu khám',
    schema: generalSchema(TicketDetailRes, 'array'),
  })
  async assignRoomMultiTicket(@Body() assignRoomDto: AssignRoomDtoV2) {
    const assignRooms = await this.examinationCoreService.assignRoomV2(assignRoomDto);
    const assignRoomsConvert = assignRooms?.map((item) =>
      _.pick(item, [
        'ticketCode',
        'roomType',
        'clinicId',
        'clinicName',
        'injectionClinicId',
        'injectionClinicName',
        'tableId',
        'modifiedBy',
      ]),
    );
    const res = await this.examinationCoreService.adjustTicketMany(assignRoomsConvert);

    return res;
  }
}
