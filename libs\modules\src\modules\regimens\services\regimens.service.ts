import { Inject, Injectable, Logger } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { OrderChannels, concurrentPromise, getStartDate, isSameDate } from '@shared';
import { RSA_ECOM_HARD_DEFAULT_SHOP_CODE } from '@shared/common/hard-code';
import { Request } from 'express';
import * as _ from 'lodash';
import moment from 'moment';
import { VacHistoryService } from 'vac-nest-history';
import { DetailRegimenCloseRes, UpdateRegimenClose, VacOrderInjectionService } from 'vac-nest-order-injection';
import { PIMAppService } from 'vac-nest-pim-app';
import {
  DetailRegimenDto,
  OrderInjectionStatus,
  RegimenComboRes,
  RegimenItem,
  RegimenService as RegimenServiceCore,
  SearchRegimenByAgeAndGenderDto,
  SearchRegimenDto,
  SuggestRegimenDto,
} from 'vac-nest-regimen';
import { ItemScheduleByPerson, ScheduleCoreService } from 'vac-nest-schedule';
import { CurrentV2, ScheduleEngineAppService, SuggestScheduleDto } from 'vac-nest-schedule-engine-app';
import { RegimensAdapter } from '../adapters/regimens.abstract';
import {
  GetPreviouUpdateDto,
  InjectionDto,
  ItemRegimenOrderInjectionPrevious,
  RegimenIncludeInventory,
  RegimenVaccineItem,
  SearchRegimenByInjectionAgeAndGenderDto,
  SearchRegimenOutput,
  SuggestFullRegimenOutput,
  SuggestRegimenOutput,
} from '../dto';
import {
  GetListStockAllShopRes,
  GetListStockMedicByShopRes,
  IMSService,
  WHS_CODE_NORMAL,
  whsCodeWithShopCodeWhsType,
} from 'vac-nest-ims';
import { calculateDOBV2 } from '../utils/regimen-util';
import { JSONPath } from 'jsonpath-plus';
import { CartAppService, ItemUpdateDto } from 'vac-nest-cart-app';
import { ExaminationCoreService } from 'vac-nest-examination';
import { GetAffSkuByShopsRes, OsrService } from 'vac-nest-osr';
import {
  GetRewardRuleBySkuAndUnitCodeResponseDto,
  RewardApiService,
  RewardTypeEnum,
} from '@frt/nestjs-api/dist/reward-api';

@Injectable()
export class RegimensService extends RegimensAdapter {
  shopCode: string;
  orderChannel: string;

  constructor(
    protected readonly regimenCoreService: RegimenServiceCore,
    protected readonly pimAppService: PIMAppService,
    protected readonly imsService: IMSService,
    protected readonly vacOrderInjectionService: VacOrderInjectionService,
    protected readonly scheduleEngineAppService: ScheduleEngineAppService,
    protected readonly scheduleCoreService: ScheduleCoreService,
    protected readonly historyCoreService: VacHistoryService,
    private readonly cartAppService: CartAppService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly osrService: OsrService,
    private readonly rewardApiService: RewardApiService,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {
    super(historyCoreService, vacOrderInjectionService, pimAppService, regimenCoreService);
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
    this.orderChannel = (this.req.headers?.['order-channel'] as string) || '';
  }

  /**
   * Tìm kiếm phác đồ
   * @param payload Từ khóa
   * @returns Danh sách phù hợp
   */
  async search(payload: SearchRegimenDto): Promise<SearchRegimenOutput> {
    const { metadata: metadata, data: regimens } = await this.regimenCoreService.searchRegimen(payload);

    if (_.isEmpty(regimens) || metadata.total == 0) return { metadata: metadata, data: [] };

    // Vinhdq6: Trả thêm tồn cho Regimen
    const regimenRes = await this.getInventoryForRegimen(regimens);
    return {
      metadata: metadata,
      data: regimenRes,
    };
  }

  async suggestRegimen(payload: SuggestRegimenDto): Promise<SuggestRegimenOutput> {
    /**
     * @TODO
     * call api search từ regimen app để lấy danh sách phác đồ vaccine
     */

    const { data: regimens } = await this.regimenCoreService.suggestRegimen(payload);

    if (_.isEmpty(regimens)) return { regimens: [] };

    return {
      regimens: regimens.sort((a, b) => b.matchingScore - a.matchingScore || a.priority - b.priority),
    };
  }

  /**
   * @TODO lấy chi tiết phác đồ theo id
   */
  async regimenDetail(regimenId: string): Promise<RegimenItem> {
    return await this.regimenCoreService.getById(regimenId);
  }

  /**
   * @TODO lấy danh sách đường dùng với liều lượng bởi list regimen id
   */
  async getUsageByListRegimenIds(regimenIds: string[]) {
    if (!regimenIds?.length)
      return {
        items: [],
      };

    const data = await this.regimenCoreService.getDosagesByRegimenIds({ regimenIds: regimenIds });

    if (!data) {
      return [];
    }

    const arrDataRegimen = data?.map((itemRegiment) => {
      return {
        ...itemRegiment,
        injectionRoutes: itemRegiment?.injectionRoutes?.map((injection) => {
          return injection['usage'];
        }),
      };
    });

    return {
      items: arrDataRegimen,
    };
  }

  async getListRegimenIds(regimenIds: string[]) {
    let data = await this.regimenCoreService.getRegimenByIds({
      regimenIds: regimenIds,
    });

    // call pim lấy thuộc tính đa liều
    if (data?.length) {
      const skus = data?.map((i) => i?.vaccine?.sku);
      const { listProduct } = await this.pimAppService.getListProductBySku(skus);
      data = data.map((i) => {
        return {
          ...i,
          vaccine: {
            ...i?.vaccine,
            isMultiDose: listProduct?.find((p) => p?.sku === i?.vaccine?.sku)?.isMultiDose || false,
          },
        };
      });
    }

    return { items: data };
  }

  async getListRegimenByIds(regimenIds: string[]) {
    return this.regimenCoreService.getRegimenByIds({ regimenIds: regimenIds });
  }

  async getRegimensByGroupId(groupId: string): Promise<RegimenComboRes> {
    const comboRegimen = await this.regimenCoreService.getRegimensByGroupId({ objectGroupId: groupId });
    if (!_.isEmpty(comboRegimen)) {
      const regimenRes = await this.getInventoryForRegimen(comboRegimen.regimens);
      return {
        objectGroupId: comboRegimen.objectGroupId,
        objectGroupName: comboRegimen.objectGroupName,
        skuCombo: comboRegimen.skuCombo,
        unitCode: comboRegimen.unitCode,
        unitName: comboRegimen.unitName,
        comboType: comboRegimen.comboType,
        regimens: regimenRes,
      };
    }
    return {} as any;
  }

  async suggestRegimenV2(payload: SuggestRegimenDto): Promise<SuggestFullRegimenOutput> {
    const returnData: SuggestFullRegimenOutput = {
      singleRegimens: [],
      comboRegimens: [],
    };
    /**
     * @TODO
     * call api suggest phác đồ vaccine: phác đồ lẻ + phác đồ combo
     */

    const { singleRegimens: singleRegimens, comboRegimens: comboRegimens } =
      await this.regimenCoreService.suggestRegimenV2(payload);

    if (!_.isEmpty(singleRegimens)) {
      returnData.singleRegimens = await this.getInventoryForRegimen(_.compact(singleRegimens));
      if (payload?.personId) {
        // Loại trừ phác đã tiêm đủ mũi khỏi gợi ý phác đồ lẻ
        const vaccineDatas = await this.historyCoreService.getByPerson({ personId: payload.personId });
        const filteredData = Object.values(
          vaccineDatas.reduce((acc, item) => {
            if (!acc[item.regimenId] || acc[item.regimenId].injection < item.injection) {
              acc[item.regimenId] = item;
            }
            return acc;
          }, {}),
        ) as any;
        returnData.singleRegimens = returnData.singleRegimens?.filter(
          (regimen) =>
            !filteredData.some(
              (vaccine) => regimen.id === vaccine?.regimenId && vaccine?.injection >= regimen?.requiredInjections,
            ),
        );
      }
    }

    if (!_.isEmpty(comboRegimens)) {
      returnData.comboRegimens = comboRegimens.map((x) => {
        return {
          objectGroupId: x.objectGroupId,
          objectGroupName: x.objectGroupName,
          skuCombo: x.skuCombo,
          unitCode: x.unitCode,
          unitName: x.unitName,
          comboType: x.comboType,
        };
      });
    }
    return returnData;
  }

  /**
   * @description Lấy thông tin phắc đồ trước đó  đã thanh toán chưa tiêm | đã tiêm
   */
  async getRegimenVaccineOrderV2(lcvIds: string[]) {
    const { dataReturn, singleRegimens, arrFutureV2 } = await this.preProcessDataRegimen(lcvIds, false);
    let { items: scheduleRes } = await this.scheduleCoreService.getScheduleByPersonCodes({
      personCodes: lcvIds,
      status: [0],
      isPaid: false,
    });
    // sort scheduleRes
    scheduleRes = _.orderBy(scheduleRes, ['appointmentDate'], ['asc']);

    for (const item of dataReturn) {
      Logger.log(`==================== START SCHEDULE ====================`);
      const regimentInfo = singleRegimens.find((regimentEntry) => regimentEntry.id === item.regimenId);

      // k có phác đồ  -> bypass
      Logger.log(`regimenInfo: ${JSON.stringify(regimentInfo)}`);
      if (!regimentInfo) continue;

      // lịch hen theo người
      const scheduleFilterRes = scheduleRes.filter((e) => e.lcvId === item.lcvId);

      _.forEach(scheduleFilterRes, (schedule: ItemScheduleByPerson) => {
        Logger.log(`==================== START SCHEDULE LOOP ====================`);
        Logger.log(`schedule: ${JSON.stringify(schedule)}`);
        if (schedule.isPaid) return;
        if (
          schedule.regimenId === item.regimenId &&
          schedule.lcvId === item.lcvId &&
          schedule.status === 0 &&
          schedule?.waittingPaid !== 5 &&
          !schedule.isPaid &&
          schedule.sku === item?.listVaccine?.at(0)?.sku
        ) {
          const dataPush: RegimenVaccineItem = {
            ...item.listVaccine.at(0),
            date: getStartDate(new Date(schedule?.appointmentDate), '+07:00'),
            scheduleId: schedule?.id,
            status: OrderInjectionStatus.Cho_thanh_toan,
            orderInjections: schedule.injections,
            orderInjectionId: null,
            orderDetailAttachmentId: null,
            vacOrderCode: null,
            orderDetailAttachmentCode: null,
            orderCode: null,
            ticketCode: null,
            unitCodeSale: schedule?.unitCodeSale,
            unitNameSale: schedule?.unitNameSale,
          };
          item.listVaccine.push(dataPush);
          const isBefore = moment(moment(schedule.appointmentDate).utcOffset(7).format('YYYY-MM-DD')).isBefore(
            moment(moment().format('YYYY-MM-DD')),
            'D',
          );
          if (!isBefore) {
            arrFutureV2.push({
              sku: item?.listVaccine.at(0)?.sku,
              regimenId: item?.regimenId,
              date: moment(schedule.appointmentDate).format(),
              status: OrderInjectionStatus.Cho_thanh_toan,
              diseaseGroupId: item?.regimenInfo?.diseaseGroupId,
              lcvId: item?.lcvId,
            });
          }
        }

        Logger.log(`==================== END SCHEDULE LOOP ====================`);
      });
      Logger.log(`==================== END SCHEDULE ====================`);
      Logger.log(`==================== START VALIDATE DATE EMPTY ==============`);
      Logger.log(`item.listVaccine: ${JSON.stringify(item.listVaccine)}`);
      const checkDateTreHen = item.listVaccine.find(
        (e) =>
          moment(moment(e.date).utcOffset(7).format('YYYY-MM-DD')).isBefore(
            moment(moment().format('YYYY-MM-DD')),
            'D',
          ) && e.status !== OrderInjectionStatus.Da_tiem,
      );
      if (!checkDateTreHen) continue;
      Logger.log(`checkDateEmpty: ${JSON.stringify(checkDateTreHen)}`);
      const suggestScheduleDto: SuggestScheduleDto = {
        arrFuture: arrFutureV2.filter((e) => e.lcvId === item.lcvId),
        arrCurrent: [],
      };
      let isChangeDate = false;
      _.forEach(item?.listVaccine, (vaccineEntry: RegimenVaccineItem) => {
        const oldDate = vaccineEntry.date;
        if (
          moment(moment(vaccineEntry.date).utcOffset(7).format('YYYY-MM-DD')).isBefore(
            moment(moment().format('YYYY-MM-DD')),
            'D',
          )
        )
          isChangeDate = true;

        if (isChangeDate && vaccineEntry.status !== OrderInjectionStatus.Da_tiem) {
          // remove same date  và nhóm bệnh
          _.remove(
            suggestScheduleDto.arrFuture,
            (e) =>
              isSameDate(new Date(e.date), new Date(vaccineEntry.date)) &&
              e['diseaseGroupId'] === item?.regimenInfo?.diseaseGroupId &&
              e['sku'] === vaccineEntry.sku,
          );

          vaccineEntry.date = '';
        }
        suggestScheduleDto.arrCurrent.push({
          date: vaccineEntry?.date as any,
          diseaseGroupId: item?.regimenInfo?.diseaseGroupId,
          regimenId: item?.regimenId,
          isChangeDate: false,
          lcvId: item?.lcvId,
          sku: vaccineEntry?.sku,
          orderInjections: vaccineEntry?.orderInjections,
          priority: 1,
          status: vaccineEntry?.status,
          vaccinatedNow: 'LATER',
          oldDate: oldDate,
        } as any);
      });
      const { arrCurrent: currentSchedule } = await this.scheduleEngineAppService.suggestSchedule(suggestScheduleDto);
      const arrDateDate = currentSchedule?.map((e) => ({ date: e.date }));
      item.listVaccine = item?.listVaccine?.map((obj, i) => {
        return { ...obj, ...arrDateDate[i] };
      });
      // push vào arrFuture
      currentSchedule.forEach((e) => {
        const futureFind = suggestScheduleDto.arrFuture.find(
          (futureItem) =>
            futureItem.diseaseGroupId === e.diseaseGroupId && isSameDate(new Date(futureItem.date), new Date(e.date)),
        );
        if (!futureFind) {
          const newFutureItem = {
            sku: e.sku,
            regimenId: e.regimenId,
            date: moment(e.date).format(),
            status: e.status,
            diseaseGroupId: e.diseaseGroupId,
            lcvId: item.lcvId,
          };
          suggestScheduleDto.arrFuture.push(newFutureItem);
          // Giữ lại phần tử vừa push vào arrFuture
          arrFutureV2.push(newFutureItem);
        }
      });

      Logger.log(`==================== END VALIDATE DATE EMPTY ==============`);
    }

    // Lấy thông tin phác đồ đã đóng những mũi đã hẹn
    const regimenDetailClose = await this.vacOrderInjectionService.searchRegimenDetailClose({
      lcvId: lcvIds,
    });

    dataReturn.forEach((e) => {
      const regimenDetailCloseFindLcvId = regimenDetailClose?.items?.find((item) => item.lcvId === e.lcvId);
      const closeFindDetail = regimenDetailCloseFindLcvId?.details.filter(
        (item) => item?.regimenId === e?.listVaccine?.at(0)?.regimenId && item?.sku === e.listVaccine.at(0)?.sku,
      );
      if (!closeFindDetail?.length) return;
      // filter ra chờ thanh toán
      for (const close of closeFindDetail) {
        // _.remove schedule === appointmentDate
        _.remove(
          e.listVaccine,
          (item) => item.date === close.appointmentDate && item.status === OrderInjectionStatus.Cho_thanh_toan,
        );
      }
    });

    /**
     * @TODO tính lại mũi thứ.
     */
    this.convertOrderInjection(dataReturn);
    /**
     * @TODO add mũi nhắc lại cho trường hợp chưa có thông tin
     */
    this.addScheduleAppointmentReminder(dataReturn);

    /**
     * @TODO Thêm cờ đa liều
     */
    await this.addFieldProduct(dataReturn);
    return { items: dataReturn };
  }

  /**
   * @description Lấy thông tin phắc đồ trước đó đã thanh toán chưa tiêm | đã tiêm sau khi update lịch hẹn
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=158249322
   */
  async getRegimenVaccineOrderUpdate(getPreviouUpdateDto: GetPreviouUpdateDto) {
    // const previousData = await this.getRegimenVaccineOrderV2([getPreviouUpdateDto?.lcvId]);

    const { lcvId, sessionId, shopCode, ticketCode, previousData, changedSchedules } = getPreviouUpdateDto;

    const getCart = await this.cartAppService.getCart({
      sessionId: sessionId,
      shopCode: shopCode || this.shopCode,
    });
    const ticketData = await this.examinationCoreService.getTicket({ ticketCode });

    const listRegimenPrevious: string[] = _.uniq(
      _.compact(
        JSONPath({
          path: '$.items[*].listVaccine[*].regimenId',
          json: previousData,
        }),
      ),
    );

    const dataRegimen = await this.regimenCoreService.getRegimenByIds({
      regimenIds: _.uniq(
        _.compact(listRegimenPrevious?.concat(changedSchedules?.map((schedule) => schedule?.regimenId))),
      ),
    });

    // Lấy thông tin cart để updateList và thông tin scheduleUpdate để update ticket
    const scheduleUpdate = [];
    const arrItemCart: ItemUpdateDto[] = [];
    if (getCart?.listCartSelected?.length) {
      getCart?.listCartSelected?.forEach((cartItem) => {
        const scheduleFind = changedSchedules?.find((item) => item?.regimenOldId === cartItem?.regimenId);
        if (scheduleFind) {
          arrItemCart.push({
            id: cartItem?.id,
            regimenId: scheduleFind?.regimenId,
            vaccinatedNow: cartItem?.vaccinatedNow,
            orderInjections: cartItem?.orderInjections,
            startDateInjection: cartItem?.startDateInjection,
            injectedVaccineCount: cartItem?.injectedVaccineCount,
          });

          scheduleUpdate?.push({
            regimenId: scheduleFind?.regimenId,
            sku: cartItem?.itemCart,
            diseaseGroupId: cartItem?.diseaseGroupId,
          });
        }
      });
    }

    // Update list cart
    let updateListCartItem = getCart;
    if (arrItemCart?.length) {
      await this.cartAppService.updateListCartItemRedis({
        arrItem: arrItemCart,
        sessionId: getPreviouUpdateDto?.sessionId,
      });
      updateListCartItem = await this.cartAppService.getCart({
        sessionId: getPreviouUpdateDto?.sessionId,
        shopCode: getPreviouUpdateDto?.shopCode || this.shopCode,
      });
    }

    // Gắn lại regimenId vào schedule và indication trong phiếu để update
    ticketData?.schedules?.forEach((scheduleTicket) => {
      const dataUpdateFind = scheduleUpdate?.find(
        (entry) => entry?.sku === scheduleTicket?.sku && entry?.diseaseGroupId === scheduleTicket?.diseaseGroupId,
      );
      if (dataUpdateFind) {
        scheduleTicket.regimenId = dataUpdateFind?.regimenId;
      }
    });
    ticketData?.indications?.forEach((indication) => {
      const dataUpdateFind = scheduleUpdate?.find(
        (entry) => entry?.sku === indication?.sku && indication?.status === 0,
      );
      if (dataUpdateFind) {
        indication.regimenId = dataUpdateFind?.regimenId;
      }
    });

    const adjustTicket = await this.examinationCoreService.adjustIndicationAndSchedule({
      ticketCode: getPreviouUpdateDto?.ticketCode,
      modifiedBy: ticketData?.modifiedBy,
      indications: ticketData?.indications,
      schedules: ticketData?.schedules,
    });

    const previousNew: ItemRegimenOrderInjectionPrevious[] = [];
    previousData?.items?.forEach((previous) => {
      // Tìm đúng vaccine đã update lịch hẹn nếu có lịch sử thì giữ lịch sử đấy những mũi hẹn sang previousNew
      const changedScheduleOldFind = changedSchedules?.find(
        (schedule) => schedule?.regimenOldId === previous.regimenId,
      );

      if (changedScheduleOldFind) {
        const dataHistory = previous?.listVaccine?.filter((vac) => vac?.status === 2);
        if (dataHistory?.length) {
          const dataSchedule = previous?.listVaccine
            ?.filter((vac) => vac?.status !== 2)
            .map((vac) => ({ ...vac, regimenId: changedScheduleOldFind?.regimenId }));
          // Nếu đổi phác đồ những mũi hẹn
          if (dataSchedule?.length) {
            previousNew.push({
              ...previous,
              listVaccine: dataSchedule,
              regimenId: changedScheduleOldFind?.regimenId,
            });
            previous.listVaccine = dataHistory;
          }
        } else {
          previous?.listVaccine.forEach((vac) => {
            vac.regimenId = changedScheduleOldFind?.regimenId;
          });
          previous.regimenId = changedScheduleOldFind?.regimenId;
        }
      }
    });

    previousData.items = previousData.items.concat(previousNew);

    previousData?.items?.forEach((previous) => {
      const regimenInfoFind = dataRegimen?.find((entry) => entry?.id === previous?.regimenId);
      previous.regimenInfo = regimenInfoFind;

      // Update dosage field in each listVaccine item
      previous.listVaccine?.forEach((vaccine) => {
        if (regimenInfoFind?.details?.length > 0) {
          const dosage = regimenInfoFind?.details?.at(0)?.dosage || '';
          const unit = regimenInfoFind?.details?.at(0)?.unit || '';
          vaccine.dosage = dosage && unit ? `${dosage} ${unit}` : dosage || '';
          vaccine.injectionRoute = regimenInfoFind?.injectionRoutes?.at(0)?.usage;
        } else {
          vaccine.dosage = '';
        }
      });
    });

    /**
     * @TODO tính lại mũi thứ.
     */
    this.convertOrderInjection(previousData.items, true);

    return {
      previousData,
      cartData: updateListCartItem,
      ticketData: adjustTicket,
    };
  }

  /**
   * @description Lấy thông tin phắc đồ dóng toàn phần
   */
  async getRegimentCloseFull(lcvIds: string[]) {
    const { dataReturn } = await this.preProcessDataRegimen(lcvIds, true);
    const search = await this.vacOrderInjectionService.searchRegimenClose({ lcvId: lcvIds });

    // Mở lại những phác đồ đóng
    const listRegimenClose: UpdateRegimenClose[] =
      _.uniqBy(
        JSONPath({
          path: '$.items[*].details[*]',
          json: search,
        }),
        'id',
      )?.filter((e) => !e?.vaccinatedDate) || [];

    // Những phác đồ đóng đã có ngày hẹn/tiêm
    const dataReCloseRegimenClose =
      _.uniqBy(
        JSONPath({
          path: '$.items[*].details[*]',
          json: search,
        }),
        'id',
      )?.filter((e) => e?.vaccinatedDate) || [];

    if (listRegimenClose?.length) {
      await this.vacOrderInjectionService.updateManyRegimenClose({
        arrRegimenClose: listRegimenClose?.map((e) => ({ ...e, modifiedBy: e?.createdBy, status: 0 })),
      });

      // uniq phác đồ
      const listRegimenCloseFilter: UpdateRegimenClose[] = _.uniqBy(listRegimenClose, (e) =>
        [e['regimenId'], e['sku'], e['lcvId']].join(),
      );

      const historyOfUser = await this.historyCoreService.getManyByLcvIds(lcvIds);

      const addRegimenCodeData = [];
      for (const itemRegimenClose of listRegimenCloseFilter) {
        const dataHistoryRegimenClose = historyOfUser
          ?.find((e) => e?.lcvId === itemRegimenClose?.lcvId)
          ?.history?.filter((e) => e?.regimenId === itemRegimenClose?.regimenId && e?.isRegimenClose);
        dataHistoryRegimenClose?.forEach((vaccine) => {
          // Lấy vaccinatedDate từ lịch sử
          addRegimenCodeData.push({ ...itemRegimenClose, status: 1, vaccinatedDate: vaccine?.vaccinatedDate });
        });
      }

      // Tạo nhiều phác đồ đóng
      const dataReCloseRegimen = await this.vacOrderInjectionService.createRegimenClose({
        arrRegimenClose: addRegimenCodeData,
      });
      dataReCloseRegimenClose.push(...dataReCloseRegimen);
    }

    const arrReturn: ItemRegimenOrderInjectionPrevious[] = [];

    _.forEach(dataReturn, (item: ItemRegimenOrderInjectionPrevious) => {
      const searchItemByLcvId = search?.items?.find((e) => e?.lcvId === item?.lcvId);
      if (!searchItemByLcvId) return;

      const itemFilterInjected = item?.listVaccine?.filter((e) => e?.status === OrderInjectionStatus.Da_tiem);

      itemFilterInjected?.forEach((injected) => {
        const regimenCloseFind = dataReCloseRegimenClose?.find(
          (e) =>
            e.regimenId === injected?.regimenId &&
            e.sku === injected?.sku &&
            injected?.appointmentDate === e?.vaccinatedDate &&
            item?.lcvId === e?.lcvId,
        );
        if (regimenCloseFind) injected['injectionCloseId'] = regimenCloseFind?.id;
      });

      arrReturn.push({ ...item, listVaccine: itemFilterInjected });
    });

    /**
     * @TODO tính lại mũi thứ.
     */
    this.convertOrderInjection(arrReturn);
    this.addFieldProduct(dataReturn);

    return { items: arrReturn };
  }

  /**
   * @TODO Lấy schedule chưa mua
   */
  async getScheduleNotBuy(lcvId: string) {
    let { items } = await this.scheduleCoreService.getScheduleByPersonCode({ personCode: lcvId });
    items = items.filter((e) => e.status === 0);
    const arrRegimenId = _.uniq(_.compact(items.map((e) => e.regimenId)));
    const arrSku = _.uniq(_.compact(items.map((e) => e.sku)));
    if (!arrRegimenId?.length || !arrSku?.length) {
      return { items: [] };
    }
    const regimens = await this.regimenCoreService.getRegimenByIds({ regimenIds: arrRegimenId });
    const products = await this.pimAppService.getListProductBySkuNoRule(arrSku);
    const dataReturn: ItemRegimenOrderInjectionPrevious[] = [];

    /**
     * @TODO ChiTNB confirm
     *  - Phác đồ nào đã thanh toán k trả ra
     */
    const itemIsPaid = items.filter((e) => e.isPaid);
    // remove những mũi đã thanh toán mà có lịch hẹn
    items?.forEach((e) => {
      const itemFind = itemIsPaid.find((item) => item.sku === e.sku && item.regimenId === e.regimenId);
      if (itemFind) {
        items = items.filter((item) => item.sku !== e.sku && item.regimenId !== e.regimenId);
      }
    });

    for (const item of items) {
      if (item.isPaid === true) continue;
      const regimentInfo = regimens.find((e) => e.id === item.regimenId);
      const product = products.listProduct.find((e) => e.sku === item.sku);
      const measure = product?.measures?.find((e) => e.isSellDefault);

      const dataPush: RegimenVaccineItem = {
        sku: item.sku,
        date: new Date(getStartDate(new Date(item.appointmentDate), '+00:00')),
        appointmentDate: item?.appointmentDate,
        status: item.status,
        orderInjections: item.injections,
        itemCart: item.sku,
        vaccineName: regimentInfo?.vaccine?.name,
        unitCode: measure.measureUnitId,
        unitName: measure.measureUnitName,
        regimenId: item.regimenId,
        manufactor: product?.manufactor || '',
        indicationNotes: regimentInfo?.vaccine?.indicationNotes,
        injectionRoutes: regimentInfo?.vaccine?.injectionRoutes,
        scheduleId: item.id,
        orderInjectionId: null,
        orderDetailAttachmentId: null,
        vacOrderCode: null,
        orderDetailAttachmentCode: null,
        orderCode: null,
        ticketCode: null,
        sourceId: null,
        doctorRegimen: true,
        diseaseGroupId: regimentInfo?.diseaseGroupId,
        lcvId: lcvId,
      };

      const dataFindIndex = dataReturn.findIndex(
        (dataEntry) => dataEntry.lcvId === lcvId && dataEntry.regimenId === item.regimenId,
      );
      if (dataFindIndex === -1) {
        dataReturn.push({
          listVaccine: [dataPush],
          regimenId: item?.regimenId,
          lcvId: lcvId,
          regimenInfo: regimentInfo,
        });
      } else {
        dataReturn[dataFindIndex].listVaccine.push(dataPush);
        dataReturn[dataFindIndex].listVaccine = _.orderBy(dataReturn[dataFindIndex].listVaccine, [
          'orderInjections',
          'date',
        ]);
      }
    }

    return { items: dataReturn };
  }

  async getScheduleNotBuyManyPer(lcvIds: string[]) {
    let { items } = await this.scheduleCoreService.getScheduleByPersonCodes({ personCodes: lcvIds, status: [0] });
    /**
     * @TODO ChiTNB confirm
     *  - Phác đồ nào đã thanh toán k trả ra
     */
    const itemIsPaid = items.filter((e) => e.isPaid);
    // remove những mũi đã thanh toán mà có lịch hẹn
    items?.forEach((e) => {
      const itemFind = itemIsPaid.find(
        (item) => item.sku === e.sku && item.regimenId === e.regimenId && e.lcvId === item.lcvId,
      );
      // Loại tất cả vaccine mà có 1 mũi đã thanh toán
      // VD: skuA có 2 mũi, 1 mũi đã paid và 1 mũi chưa => loại hết 2 mũi
      if (itemFind) {
        const listIdRemove = items
          .filter((item) => item.sku === e.sku && item.regimenId === e.regimenId && e.lcvId === item.lcvId)
          ?.map((item) => item?.id);
        items = items.filter((item) => !listIdRemove?.includes(item?.id));
      }
    });

    const arrRegimenId = _.uniq(_.compact(items.map((e) => e.regimenId)));
    const arrSku = _.uniq(_.compact(items.map((e) => e.sku)));
    if (!arrRegimenId?.length || !arrSku?.length) {
      return { items: [] };
    }
    const regimens = await this.regimenCoreService.getRegimenByIds({ regimenIds: arrRegimenId });
    const products = await this.pimAppService.getListProductBySkuNoRule(arrSku);
    const dataReturn: ItemRegimenOrderInjectionPrevious[] = [];

    for (const item of items) {
      if (item.isPaid === true) continue;
      const regimentInfo = regimens.find((e) => e.id === item.regimenId);
      const product = products.listProduct.find((e) => e.sku === item.sku);
      const measure = product?.measures?.find((e) => e.isSellDefault);

      const dataPush: RegimenVaccineItem = {
        sku: item.sku,
        date: new Date(getStartDate(new Date(item.appointmentDate), '+00:00')),
        appointmentDate: item?.appointmentDate,
        status: item.status,
        orderInjections: item.injections,
        itemCart: item.sku,
        vaccineName: regimentInfo?.vaccine?.name,
        unitCode: measure.measureUnitId,
        unitName: measure.measureUnitName,
        regimenId: item.regimenId,
        manufactor: product?.manufactor || '',
        indicationNotes: regimentInfo?.vaccine?.indicationNotes,
        injectionRoutes: regimentInfo?.vaccine?.injectionRoutes,
        scheduleId: item.id,
        orderInjectionId: null,
        orderDetailAttachmentId: null,
        vacOrderCode: null,
        orderDetailAttachmentCode: null,
        orderCode: null,
        ticketCode: null,
        sourceId: null,
        doctorRegimen: true,
        diseaseGroupId: regimentInfo?.diseaseGroupId,
        lcvId: item.lcvId,
      };

      const dataFindIndex = dataReturn.findIndex(
        (dataEntry) => dataEntry.lcvId === item.lcvId && dataEntry.regimenId === item.regimenId,
      );
      if (dataFindIndex === -1) {
        dataReturn.push({
          listVaccine: [dataPush],
          regimenId: item?.regimenId,
          lcvId: item.lcvId,
          regimenInfo: regimentInfo,
        });
      } else {
        dataReturn[dataFindIndex].listVaccine.push(dataPush);
        dataReturn[dataFindIndex].listVaccine = _.orderBy(dataReturn[dataFindIndex].listVaccine, [
          'orderInjections',
          'date',
        ]);
      }
    }

    return { items: dataReturn };
  }

  private async getInventoryForRegimen(regimens: RegimenItem[], isFilterActiveFromPim = true) {
    const regimenRes = [...regimens] as RegimenIncludeInventory[];
    let lstSku = regimenRes.flatMap((x) => x.vaccine).flatMap((x) => x.sku);
    lstSku = [...new Set(lstSku)];

    if (!lstSku?.length) return regimenRes;

    // if orderchannel = rsa ecom, shopcode = null => shopcode = RSA_ECOM_HARD_DEFAULT_SHOP_CODE
    let shopCode = this.shopCode;
    if (
      !shopCode &&
      this.orderChannel &&
      [...OrderChannels.RSA_ECOM, ...OrderChannels.RSA_AFFILIATE].includes(this.orderChannel)
    ) {
      shopCode = RSA_ECOM_HARD_DEFAULT_SHOP_CODE;
    }

    const { listProduct } = await this.pimAppService.getListProductBySku(lstSku, shopCode);

    let lstInventory: GetListStockAllShopRes | GetListStockMedicByShopRes;

    if (shopCode === RSA_ECOM_HARD_DEFAULT_SHOP_CODE) {
      lstInventory = await this.imsService.getListStockAllShop({
        skuCodes: lstSku,
      });
    } else {
      lstInventory = await this.imsService.getListStockMedicAtShop({
        skuCodes: lstSku,
        shopCodes: [this.shopCode],
        whsCodes: [whsCodeWithShopCodeWhsType(this.shopCode, WHS_CODE_NORMAL)],
      });
    }

    regimenRes
      .sort((a, b) => b.matchingScore - a.matchingScore || a.priority - b.priority)
      .forEach((regimen) => {
        regimen.inventory = null;
        const unitDefault = listProduct
          ?.find((e) => e.sku === regimen.vaccine?.sku)
          ?.measures?.find((e) => e.isSellDefault)?.measureUnitId;
        const inventoryInfo = lstInventory?.inventories?.find(
          (item) => item.sku == regimen.vaccine?.sku && item.unitCode == unitDefault,
        );

        if (inventoryInfo != null) {
          regimen.inventory = {
            ...inventoryInfo,
            itemCode: inventoryInfo.sku,
            itemName: '',
            whsCode: inventoryInfo.whsCode,
            whsName: inventoryInfo.whsName,
            shopCode: '',
            shopName: '',
            sl: inventoryInfo.quantity,
            dvt: inventoryInfo.unitCode,
            nameDvt: inventoryInfo.unitName,
            isLevel: inventoryInfo.unitLevel,
            isRemain: OrderChannels.RSA_AFFILIATE.includes(this.orderChannel)
              ? 1
              : inventoryInfo?.quantityAvailable > 0
              ? 1
              : 0,
          };
        }

        if (!regimen.inventory && OrderChannels.RSA_AFFILIATE.includes(this.orderChannel)) {
          regimen.inventory = {
            isRemain: 1,
          } as any;
        }

        regimen['isActive'] = false;
        const productFind = listProduct.find((e) => e.sku === regimen?.vaccine?.sku);
        if (!productFind) return;
        regimen.vaccine.measures = productFind.measures as any;
        regimen.vaccine.prices = productFind.prices as any;
        regimen.vaccine.name = productFind?.name as any;
        regimen['isActive'] = true;
        const tmp: any = regimen.vaccine; // skip type check
        tmp.attributeShop = productFind.attributeShop;
        tmp.sellPriority = productFind.sellPriority;
        tmp['isPreOrder'] = productFind['isPreOrder'];
        regimen.vaccine.isMultiDose = listProduct?.find((p) => p?.sku === regimen?.vaccine?.sku)?.isMultiDose || false;
      });

    if (isFilterActiveFromPim) {
      return regimenRes?.filter((e) => e?.['isActive']);
    }
    return regimenRes;
  }

  async getWarningVaccineInteraction(skus: string[]) {
    const data = await this.regimenCoreService.getWaringVaccineInteraction({ skus });
    return {
      items: data,
    };
  }

  async searchByAgeAndGender(payload: SearchRegimenByAgeAndGenderDto) {
    const response = { metadata: { ...payload } as any, data: [] };
    try {
      const data = await this.regimenCoreService.searchRegimenByAgeAndGender(payload);
      const regimenRes = await this.getInventoryForRegimen(data.data as any);
      response.metadata = data.metadata;
      response.data = regimenRes;
    } catch (error) {}
    return response;
  }

  async searchByInjectionsAgeAndGender(payload: SearchRegimenByInjectionAgeAndGenderDto) {
    // Handle call history
    let getHistories = [];

    try {
      getHistories = payload?.personId
        ? await this.historyCoreService.getByPerson({
            personId: payload?.personId,
          })
        : [];
    } catch (err) {}

    const historiesConvert = getHistories
      ?.filter((item) => item?.isQualify && !item?.isRegimenClose) // Chỉ lấy "isQualify": true và "isRegimenClose": false
      ?.map((item) => ({
        date: item?.vaccinatedDate || '',
        diseaseGroupId: item?.diseaseGroupId || '',
        regimenId: item?.regimenId || '',
        sku: item?.sku || '',
        status: 2, //Map status các lịch sử call từ api ES = 2
        sourceId: item?.sourceId || '',
      }));
    // End handle
    const injectionListPayload = payload?.injectionList?.filter((item) => !(item?.status === 2)) || []; // ListFE truyền xuống fillter bỏ status = 2
    const injectionListTotal = [...historiesConvert, ...injectionListPayload];

    // group by sku
    const injectionListGroupBySku = _.groupBy(injectionListTotal?.filter((item) => !!item?.date) || [], 'sku');

    let injectionListGroupBySkuNewest: InjectionDto[] = [];
    for (const [_key, value] of Object.entries(injectionListGroupBySku)) {
      const listInjection = value as InjectionDto[];
      const sortInjectionByDate = _.sortBy(listInjection, ['date']);
      const lastItemSortInjectionByDate = sortInjectionByDate.at(-1);
      const lastRegimenId = lastItemSortInjectionByDate?.regimenId || '';
      const filterListByLastRegimenId = sortInjectionByDate?.filter(
        (item) => item?.regimenId && lastRegimenId && item?.regimenId === lastRegimenId && item?.status === 2, //Chỉ lấy lịch sử
      );
      injectionListGroupBySkuNewest = [
        ...injectionListGroupBySkuNewest,
        {
          ...sortInjectionByDate.at(-1),
          amountInjection: filterListByLastRegimenId?.length || 0,
        },
      ];
    }
    // let sonResFind: GetAffSkuByShopsRes = null;
    // if (OrderChannels.RSA_AFFILIATE.includes(String(this.req?.headers?.['order-channel']))) {
    //   const shopCode: string = payload?.shopCode || String(this.req?.headers?.['shop-code']) || '';
    //
    //   // call data chỗ sơn
    //   const sonRes = await this.osrService.getAffSkuByShops({
    //     shopCode: [shopCode],
    //   });
    //
    //   sonResFind = sonRes.find((e) => e.shopCode === shopCode);
    // }
    // const arrDiseaseGroupId: string[] = !sonResFind?.isAllow
    //   ? _.uniq(sonResFind?.details.map((e) => e?.diseaseGroupId))
    //   : [];
    //
    // end group by
    let additional = {};
    if (
      OrderChannels.RSA_AFFILIATE.includes(String(this.req?.headers?.['order-channel'])) &&
      process.env.IS_BLOCK_PRODUCT === 'true'
    ) {
      additional = {
        isBlockProduct: process.env.IS_BLOCK_PRODUCT === 'true' ? true : false,
        blockProductText: process.env.BLOCK_PRODUCT_TEXT || '',
      };
    }
    const response = { metadata: { ...payload } as any, data: [], ...additional };
    if (payload?.injectionList?.length) {
      delete payload.injectionList;
    }
    try {
      let data;
      // Affiliate thì truyền order channel để lấy vaccine chỉ cho affiliate
      if (OrderChannels.RSA_AFFILIATE.includes(this.orderChannel)) {
        // lấy những mũi lịch sử có status = 2
        const listHistory = [];
        if (getHistories?.length) {
          for (const itemHistory of getHistories) {
            if (itemHistory?.isQualify) {
              listHistory.push({
                date: itemHistory?.vaccinatedDate || '',
                diseaseGroupId: itemHistory?.diseaseGroupId || '',
                regimenId: itemHistory?.regimenId || '',
                sku: itemHistory?.sku || '',
              });
            }
          }
        }

        data = await this.regimenCoreService.searchRegimenByAgeAndGenderIncludeOrderChannel({
          ...payload,
          injectionList: injectionListGroupBySkuNewest,
          channel: +this.orderChannel,
          listHistory,
        });
      } else {
        data = await this.regimenCoreService.searchRegimenByAgeAndGenderPost({
          ...payload,
          injectionList: injectionListGroupBySkuNewest,
        });
      }
      const regimenRes = await this.getInventoryForRegimen(data.data as any);
      response.metadata = data.metadata;

      response.data = regimenRes;

      if (OrderChannels.RSA.includes(String(this.orderChannel))) {
        try {
          const validDate = moment().utcOffset(7).format('YYYY-MM-DD');
          const rewards = await this.rewardApiService.getRewardRuleBySkuAndUnitCode(
            regimenRes.map((item) => ({
              sku: item?.vaccine?.sku,
              unitCode: item?.vaccine?.measures?.find((e) => e.isSellDefault)?.measureUnitId,
              validDate,
            })),
          );
          response.data = this.mapRewardToRegimens(regimenRes, rewards);
        } catch (error) {
          response.data = regimenRes;
        }
      }
      // if (
      //   OrderChannels.RSA_AFFILIATE.includes(String(this.req?.headers?.['order-channel'])) &&
      //   arrDiseaseGroupId?.length
      // ) {
      //   _.remove(response.data, (item: RegimenIncludeInventory) => arrDiseaseGroupId.includes(item?.diseaseGroupId));
      // }
    } catch (error) {}
    return response;
  }

  private mapRewardToRegimens(
    regimens: RegimenIncludeInventory[],
    rewards: GetRewardRuleBySkuAndUnitCodeResponseDto,
  ): RegimenIncludeInventory[] {
    if (!rewards?.length) return regimens;

    const regimenMap = new Map<string, RegimenIncludeInventory>();
    regimens.forEach((regimen) => {
      if (regimen?.inventory?.sku) regimenMap.set(regimen?.inventory?.sku, regimen);
    });

    rewards.forEach((reward) => {
      const regimen = regimenMap.get(reward.sku);
      if (regimen) {
        regimen.displayRewardPointByType = regimen.displayRewardPointByType || [];
        reward?.rewardRules.forEach((rule) => {
          if (!rule?.rewardType || !rule?.rewardValue) return;
          regimen.displayRewardPointByType.push({
            rewardType: rule?.rewardType,
            rewardValue: rule?.rewardValue,
            displayValue:
              rule?.rewardType === RewardTypeEnum.MONEY
                ? `H${((rule?.rewardValue || 0) / 1000).toLocaleString()}`
                : `D${rule?.rewardValue || 0}`,
          });
        });
      }
    });

    return regimens;
  }

  async getAgeRanges() {
    const ageRanges = await this.regimenCoreService.getAgeRanges();
    return ageRanges.map((item) => ({
      ...item,
      dob: calculateDOBV2(item.ageUnitCode, item.from),
      uniqkey: `${item.from}_${item.to}_${item.ageUnitCode}`,
    }));
  }

  async searchByAgeAndGenderIncludeInActive(payload: SearchRegimenByAgeAndGenderDto) {
    const response = { metadata: { ...payload } as any, data: [] };
    try {
      const data = await this.regimenCoreService.searchRegimenByAgeAndGenderIncludeInactive(payload);
      const regimenRes = await this.getInventoryForRegimen(data.data as any, false);
      response.metadata = data.metadata;
      response.data = regimenRes;
    } catch (error) {}
    return response;
  }

  async getListVaccineByDisease(diseaseGroupId: string) {
    const result = await this.regimenCoreService.getListVaccineGroupByDisease({ diseaseGroupIds: [diseaseGroupId] });
    // call pim để lấy thuộc tính đa liều
    const skus: string[] = JSONPath({
      path: '$[*].regimens.[*].sku',
      json: result,
    });
    const lstProduct = await this.pimAppService.getListProductBySku(skus);

    result?.at(0)?.regimens?.forEach((regimen) => {
      const productFind = lstProduct?.listProduct?.find((product) => product?.sku === regimen?.sku);

      regimen['attributeShop'] = productFind?.attributeShop;
      regimen['sellPriority'] = productFind?.sellPriority;
      regimen['isPreOrder'] = productFind?.isPreOrder;
      regimen['isMultiDose'] = productFind?.isMultiDose || false;
    });

    return {
      regimens: { ...result?.at(0) },
    };
  }

  /**
   * @description Lấy regimen theo skus
   */
  async getRegimentBySku(payload: DetailRegimenDto) {
    const { data } = await this.regimenCoreService.getRegimenByListSkuIncludeInactive(payload);

    // call pim để lấy thuộc tính đa liều
    if (data?.length) {
      const skus = data?.map((i) => i?.vaccine?.sku);

      const { listProduct } = await this.pimAppService.getListProductBySku(skus);
      data.forEach((item) => {
        item.vaccine.isMultiDose = listProduct?.find((p) => p?.sku === item?.vaccine?.sku)?.isMultiDose || false;
      });
    }

    return {
      data: data || [],
    };
  }
}
