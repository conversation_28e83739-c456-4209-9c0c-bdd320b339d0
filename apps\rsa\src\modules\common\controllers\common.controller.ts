import { Body, Controller, Get, HttpCode, HttpStatus, Post, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, Public, generalSchema } from '@shared';
import { GetManyMonitorDto } from 'vac-nest-monitor';
import { CreateHealthMonitorRes, CreateMonitorRes, GetManyHealthMonitorRes, GetMonitorRes } from '../dto';
import { CommonService } from '../services/common.service';

@Controller({ path: 'common', version: '1' })
@ApiTags('Common')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(ClassResponse, GetMonitorRes, CreateHealthMonitorRes, GetManyHealthMonitorRes)
export class CommonController {
  constructor(private readonly commonService: CommonService) {}

  /**
   * @TODO lấy danh sách phản ứng sau tiêm
   * @returns
   */
  @Public()
  @Get('get-monitor')
  @ApiOperation({
    summary: 'Lấy danh sách phản ứng sau tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách phản ứng sau tiêm',
    schema: generalSchema(GetMonitorRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  async getMonitor() {
    return this.commonService.getMonitor();
  }

  @Public()
  @Post('create-health-monitor')
  @ApiOperation({
    summary: 'Tạo phản ứng sau tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Tạo phản ứng sau tiêm',
    schema: generalSchema(CreateHealthMonitorRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  async createMonitor(@Body() body: CreateMonitorRes) {
    return this.commonService.createHealthMonitor(body);
  }

  /**
   * @TODO lấy kết quả phản ứng sau tiêm
   * @param body
   * @returns
   */
  @Public()
  @Get('health-monitor/get-many')
  @ApiOperation({
    summary: 'Lấy kết quả phản ứng sau tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả phản ứng sau tiêm',
    schema: generalSchema(GetManyHealthMonitorRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  async getManyHealthMonitor(@Query() query: GetManyMonitorDto) {
    return this.commonService.healthMonitorGetManyService(query);
  }
}
