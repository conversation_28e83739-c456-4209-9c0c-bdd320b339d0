import { Module } from '@nestjs/common';
import { AutoGenerateService } from './auto-generate.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SurveyIndex } from './entities/survey-index.entity';
import { SurveyInsideIndex } from './entities/survey-inside-index.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SurveyIndex, SurveyInsideIndex])],
  providers: [AutoGenerateService],
  exports: [AutoGenerateService],
  controllers: [],
})
export class AutoGenerateModule {}
