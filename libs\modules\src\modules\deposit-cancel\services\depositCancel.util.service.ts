import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import CryptoJS from 'crypto-js';
import AES from 'crypto-js/aes';
import moment from 'moment';
import { NotificationService, ShortLinkService } from 'vac-nest-notification';
import { SendSMSTransferDto } from '../dto/send-sms-transfer';
import { SendSMSSMDto } from '../dto/send-sms-sm.dto';
import { formatCurrency, translateVietnameseAccents } from '@shared';
import { RegimenService } from 'vac-nest-regimen';
import { OMSService } from 'vac-nest-oms';
import { VacStorefrontService } from 'vac-nest-storefront';
import * as _ from 'lodash';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { DepositCancel } from '../entities/depositCancel.entity';

@Injectable()
export class DepositCancelUtilService {
  constructor(
    private readonly configService: ConfigService,
    private readonly shortLinkService: ShortLinkService,
    private readonly notificationService: NotificationService,
    private readonly regimenService: RegimenService,
    private readonly omsService: OMSService,
    private readonly storeFontService: VacStorefrontService,
    @InjectRepository(DepositCancel)
    private readonly depositCancelRepository: Repository<DepositCancel>,
  ) {}

  async sendSMSLinkTransfer(payload: SendSMSTransferDto) {
    const { orderCode } = payload;
    const smsPayload = await this.collectInforSendSMS(orderCode);
    const password = this.configService.get('DEPOSIT_SMS_PASSWORD');
    const finalPayload = Object.assign(
      smsPayload,
      _.pickBy(payload, (value: any) => value !== undefined),
    );

    // The key length is dependent on the algorithm.
    const text = CryptoJS.enc.Utf8.parse(orderCode);
    const key = password;
    const encrypted = AES.encrypt(text, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.ZeroPadding,
    });
    const encryptedText = Buffer.from(encrypted.toString()).toString('hex');

    const response = {
      originalLink: `${this.configService.get('TIEM_CHUNG_WEB_URL')}huy-coc/vaccine/${encryptedText}`,
      shortLink: null,
      message: '',
      notificationData: null,
    };

    if (response.originalLink) {
      const expiredInMinutes = this.configService.get('DEPOSIT_CANCEL_SMS_TRANSFER_LINK_EXPIRED_IN_MINUTES');
      const shortLinkResponse = await this.shortLinkService.createShortLink(
        {
          originalUrl: response.originalLink,
          expires: moment()
            .utcOffset('+07:00')
            .add(Number(expiredInMinutes) || 15, 'minutes')
            .format(),
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: process.env.SMS_AUTHEN_BASIC,
          },
        },
      );
      response.shortLink = shortLinkResponse?.shortLink;
    }

    /**
     * @TODO: send sms
     */

    try {
      response.notificationData = await this.notificationService.sendNotification({
        FromSys: this.configService.get('APP_NAME'),
        Sender: this.configService.get('APP_NAME'),
        Messages: [
          {
            TemplateId: this.configService.get('DEPOSIT_CANCEL_SMS_TEMPLATE_ID'),
            To: finalPayload.phoneNumbers,
            Bcc: [],
            Cc: [],
            Param: {
              Title: {},
              Content: {
                orderCode: payload.orderCode?.slice(-7) || '',
                paymentUrl: response.shortLink || '',
                diseaseName: translateVietnameseAccents(finalPayload?.diseaseName) || '',
                customerName: translateVietnameseAccents(finalPayload?.customerName) || '',
                shopAddress: translateVietnameseAccents(finalPayload?.shopAddress) || '',
              },
              ContentFailOver: {},
              ExtraProperties: {},
            },
          },
        ],
      });
      // send sms hẻe
    } catch (error) {
      response.message = typeof error.message === 'string' ? error.message : 'Send SMS failed';
      if (this.configService.get('NODE_ENV') !== 'CI') {
        // by pass error in CI for test.
        throw error;
      }
    }

    return response;
  }

  async sendSMSSM(payload: SendSMSSMDto) {
    const smsPayload = await this.collectInforSendSMS(payload.orderCode);
    return this.notificationService.sendNotification({
      FromSys: this.configService.get('APP_NAME'),
      Sender: this.configService.get('APP_NAME'),
      Messages: [
        {
          TemplateId: this.configService.get('DEPOSIT_CANCEL_SMS_SM_TEMPLATE_ID'),
          To: smsPayload.phoneNumbers,
          Bcc: [],
          Cc: [],
          Param: {
            Title: {},
            Content: {},
            ContentFailOver: {
              diseaseName: translateVietnameseAccents(smsPayload?.diseaseName),
              customerName: translateVietnameseAccents(smsPayload?.customerName),
              shopAddress: translateVietnameseAccents(smsPayload?.shopAddress),
            },
            ExtraProperties: {
              customerName: smsPayload?.customerName,
              orderCode: smsPayload?.orderCode?.slice(-7) || '',
              diseaseName: smsPayload?.diseaseName,
              canceledDate: smsPayload?.canceledDate,
            },
          },
        },
      ],
    });
  }

  async collectInforSendSMS(orderCode: string) {
    const orderInfo = await this.omsService.getOneOrder(orderCode);
    const depositCancelByOrderCode = await this.depositCancelRepository.findOneBy({ orderCode });
    const sku = _.uniq(
      _.compact(orderInfo?.details?.flatMap((item) => item?.detailAttachments?.map((itemJ) => itemJ?.itemCode))),
    );
    if (!sku?.length) {
      return null;
    }
    const regimens = await this.regimenService.getListDiseaseGroupBySku(sku);
    const cancelDate = orderInfo?.employees.find((item) => item.step === 4)?.createdDate;
    const shop = await this.storeFontService.getShop(orderInfo.shopCode);

    // xóa ký tự thừa
    const redundantTextIndex =
      Math.max(shop?.shopNameDisplay?.toLowerCase()?.indexOf('(mở cửa'), 0) || shop?.shopNameDisplay?.length;
    const shopAddress = shop?.shopNameDisplay?.substring(0, redundantTextIndex)?.trim() || '';

    return {
      phoneNumbers: [orderInfo.phone],
      customerName: orderInfo.custName,
      diseaseName: regimens?.at(0)?.diseaseGroupName || '',
      orderCode: orderCode,
      canceledDate: cancelDate ? moment(`${cancelDate}+07:00`).utcOffset('+07:00').format('DD/MM/YYYY') : '',
      shopAddress: shopAddress,
      refundAmount: depositCancelByOrderCode.totalRefund
        ? formatCurrency(Number(depositCancelByOrderCode.totalRefund))
        : null,
    };
  }
}
