import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import { Request } from 'express';
import _ from 'lodash';
import moment from 'moment';
import { DSMSService } from 'vac-nest-dsms';
import { ExaminationCoreService, ItemTicket } from 'vac-nest-examination';
import { FamilyService } from 'vac-nest-family';
import { ChildCarer, TCQGIntegrationService, UpdateTCGQForJobDto } from 'vac-nest-tcqg-integration';
import { ValidVoucher } from 'vac-nest-voucher-core';
import { CustomersService } from '../../customers/services/customers.service';
import { PersonTCQGMappingDto } from '../dto/persion-tcgq-mapping.dto';
import { UpdateInfoToTCQGByLcvDto } from '../dto/update-info-to-tcqg-by-lcv.dto';
import { concurrentPromise } from '../../../../../shared/src';

export abstract class PublicsAbstract {
  shopCode: string;
  private readonly logger = new Logger(PublicsAbstract.name);
  constructor(
    protected readonly examinationCoreService: ExaminationCoreService,
    protected readonly familyService: FamilyService,
    protected readonly tcqgIntegrationService: TCQGIntegrationService,
    protected readonly configService: ConfigService,
    protected readonly dmsService: DSMSService,
    protected readonly customersService: CustomersService,
    protected readonly req: Request,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
    if (this.shopCode === '50001') {
      this.shopCode = '';
    }
  }

  async updateInfoToTCQG(): Promise<true> {
    // lay danh sach shop de chay data
    const shops = await this.dmsService.getAllShopVaccine('VACCiNE');
    const arrShop = _.compact(_.uniq(shops?.items?.map((item) => item.code)));

    for (const shop of arrShop) {
      const getPayloadUpdateInfoToTCQG = await this.getPayloadUpdateInfoToTCQG(shop);
      // Call API to update data to TCQG
      // filter ra không có nationalVaccineCode
      const data = getPayloadUpdateInfoToTCQG.filter((item) => item.nationalVaccineCode);
      const data1 = getPayloadUpdateInfoToTCQG.filter((item) => !item.nationalVaccineCode);
      const arr = {
        Succeeded: [],
        Failed: [],
      };
      Logger.log(`[updateInfoToTCQG] Total data to update: ${JSON.stringify(data)}`);
      Logger.log(`[updateInfoToTCQG] Total data to not nationalVaccineCode: ${JSON.stringify(data1)}`);
      arr.Failed.push(...data1);

      const chuckArray: Array<
        Array<{
          nationalVaccineCode: string;
          nationalVaccineId: string;
          name: string;
          dateOfBirth: string;
          lcvId: string;
          shopCode: string;
        }>
      > = _.chunk(data, 100);
      for (const chuck of chuckArray) {
        try {
          const arrUpdateDto: UpdateTCGQForJobDto[] = chuck.map((e) => {
            const doiTuongId = e.nationalVaccineId;
            const tenDoiTuong = e.name;
            const ngaySinh = new Date(e.dateOfBirth);

            delete e['nationalVaccineCode'];
            delete e['nationalVaccineId'];
            delete e['name'];
            delete e['dateOfBirth'];
            delete e['lcvId'];
            return {
              ...e,
              DoiTuongId: doiTuongId,
              NgaySinh: ngaySinh,
              shopCode: e.shopCode,
              TenDoiTuong: tenDoiTuong,
            };
          });

          await this.tcqgIntegrationService.updateTCQGForJob(arrUpdateDto);
          arr.Succeeded.push(...chuck);
        } catch (error) {
          Logger.log(`[updateInfoToTCQG] Error: ${error}`);
          arr.Failed.push(...chuck);
        }
      }

      Logger.log(`====================== END UPDATE ========================`);

      Logger.log(`Array Succeeded ${shop}: ${JSON.stringify(arr.Succeeded)}`);
      Logger.log(`Array Failed ${shop}: ${JSON.stringify(arr.Failed)}`);
    }

    return true;
  }

  async getPayloadUpdateInfoToTCQG(shopCode: string): Promise<
    Array<{
      nationalVaccineCode: string;
      nationalVaccineId: string;
      name: string;
      dateOfBirth: string;
      lcvId: string;
      shopCode: string;
    }>
  > {
    const arrReturn: Array<{
      nationalVaccineCode: string;
      nationalVaccineId: string;
      name: string;
      dateOfBirth: string;
      lcvId: string;
      shopCode: string;
      [key: string]: any;
    }> = [];

    const arrTicketInjection: ItemTicket[] = [];

    let allDataFetched = false;
    let skipCount = 0;
    const maxResultCount = 100;

    while (!allDataFetched) {
      try {
        const { ticketDetails: batchData } = await this.examinationCoreService.searchTicketAlwaysIndication({
          fromDate: moment().utcOffset(7).startOf('D').format() as any,
          toDate: moment().utcOffset(7).endOf('D').format() as any,
          statuses: [8],
          skipCount: skipCount,
          maxResultCount: maxResultCount,
          shopCode: shopCode || this.shopCode || this.configService.get('JOB_TCQG_SHOP_CODE', ''),
        });
        // Assuming batchData is an array of the data fetched
        if (batchData.length > 0) {
          // Process and add the batch data to arrReturn
          // For example, if you need to transform or directly add the batchData
          arrTicketInjection.push(...batchData); // Adjust this line based on the actual structure of batchData
          if (batchData.length < maxResultCount) {
            // If the number of items in the batch is less than maxResultCount, it means this is the last batch
            allDataFetched = true;
          } else {
            // Prepare for the next iteration
            skipCount += maxResultCount;
          }
        } else {
          // No more data to fetch
          allDataFetched = true;
        }
      } catch (error) {
        allDataFetched = true;
        Logger.log({
          fields: {
            info: '[searchTicketAlwaysIndication] Error fetching data',
            error: error,
          },
        });
      }
    }
    const arrLcvId: Array<string> = _.uniq(arrTicketInjection.map((item) => item.lcvId));
    const chuckArrLcvId: Array<Array<string>> = _.chunk(arrLcvId, 100);
    for (const item of chuckArrLcvId) {
      try {
        const data = await this.familyService.getManyByLcvId({
          lcvId: item,
        });
        for (const itemData of data) {
          const isHoster = itemData?.familyProfileDetails?.find((itemEntry) => itemEntry?.isHost);
          // chủ hộ chỉ đẩy bản thân lên thoy
          if (itemData?.lcvId === isHoster?.lcvId) {
            arrReturn.push({
              nationalVaccineCode: itemData.nationalVaccineCode,
              nationalVaccineId: itemData.nationalVaccineId,
              name: itemData.name,
              dateOfBirth: new Date(itemData.dateOfBirth).toISOString(),
              lcvId: itemData.lcvId,
              shopCode: arrTicketInjection?.find((x) => x.lcvId === itemData.lcvId)?.shopCode,
            });
          } else {
            arrReturn.push({
              nationalVaccineCode: itemData.nationalVaccineCode,
              nationalVaccineId: itemData.nationalVaccineId,
              name: itemData.name,
              dateOfBirth: new Date(itemData.dateOfBirth).toISOString(),
              lcvId: itemData.lcvId,
              shopCode: arrTicketInjection?.find((x) => x.lcvId === itemData.lcvId)?.shopCode,
              ...(await this.customersService._getGuardianTcqgInfo({
                familyProfileDetails: itemData?.familyProfileDetails,
              })),
            });
          }
        }
      } catch (error) {
        Logger.log({
          fields: {
            info: '[getListPrimaryPerson] Error fetching data',
            error: error,
          },
        });
      }
    }

    return arrReturn;
  }

  async getPayloadInfoToTCQGByLcvId(
    updateInfoToTCQGByLcvDto: UpdateInfoToTCQGByLcvDto,
  ): Promise<Array<UpdateTCGQForJobDto>> {
    try {
      const { lcvIds } = updateInfoToTCQGByLcvDto;
      const [{ wards }, { districts }, { provinces }, arrPerson, ticketsByLcvId] = await concurrentPromise(
        this.dmsService.getWardAll(),
        this.dmsService.getDistrictAll(),
        this.dmsService.getProvince(),
        this.familyService.getManyByLcvId({
          lcvId: lcvIds,
        }),
        this.examinationCoreService.searchByLcsIds({
          lCVIds: lcvIds,
        }),
      );
      const arrUpdateDto: UpdateTCGQForJobDto[] = [];
      for (const person of arrPerson) {
        //#region Thường trú
        const wardFind = wards.find((item) => item.code === person?.frequentlyWardCode);
        const districtFind = districts.find((item) => item.code === person?.frequentlyDistrictCode);
        const provinceFind = provinces.find((item) => item.code === person?.frequentlyProvinceCode);
        person['frequentlyTCQGProvinceCode'] = provinceFind?.tcqgProvince;
        person['frequentlyTCQGDistrictCode'] = districtFind?.tcqgDistrict;
        person['frequentlyTCQGWardCode'] = wardFind?.tcqgWard;
        //#endregion

        //#region Tạm trú
        const wardTempFind = wards.find((item) => item.code === person?.temporaryWardCode);
        const districtTempFind = districts.find((item) => item.code === person?.temporaryDistrictCode);
        const provinceTempFind = provinces.find((item) => item.code === person?.temporaryProvinceCode);
        person['temporaryTCQGProvinceCode'] = provinceTempFind?.tcqgProvince;
        person['temporaryTCQGDistrictCode'] = districtTempFind?.tcqgDistrict;
        person['temporaryTCQGWardCode'] = wardTempFind?.tcqgWard;
        //#endregion

        //#region Lấy Shop Code
        const shopCode = ticketsByLcvId?.find((item) => item?.lcvId === person?.lcvId)?.shopCode;
        person['shopCode'] = shopCode;
        person['employeeCode'] = updateInfoToTCQGByLcvDto.employeeCode;
        person['nationalVaccineCode'] = person.nationalVaccineCode;
        //#endregion

        //#region  push Dto
        arrUpdateDto.push(
          plainToInstance(
            PersonTCQGMappingDto,
            {
              ...person,
              ...(await this.customersService._getGuardianTcqgInfo({
                familyProfileDetails: person?.familyProfileDetails,
              })),
            },
            {
              exposeUnsetFields: false,
              excludeExtraneousValues: true,
            },
          ),
        );
        // #endregion
      }
      this.logger.log(
        `[${arrUpdateDto?.at(0)?.lcvId}] [getPayloadInfoToTCQGByLcvId] arrUpdateDto: ${JSON.stringify(arrUpdateDto)}`,
      );
      const arrNew = arrUpdateDto?.filter((e) => e?.DoiTuongId && e?.shopCode);
      const arrFilter = arrUpdateDto?.filter((e) => !e?.DoiTuongId || !e?.shopCode);
      this.logger.log(
        `[${arrUpdateDto?.at(0)?.lcvId}] [getPayloadInfoToTCQGByLcvId] arrFilterError: ${JSON.stringify(arrFilter)}`,
      );

      return arrNew;
    } catch (error) {
      Logger.error({
        message: 'updateInfoToTCQGByLcvId',
        error,
      });
      return [];
    }
  }

  abstract getVoucherByOrderCode(orderCode: string): Promise<ValidVoucher[]>;
  abstract updateInfoToTCQGByLcvId(updateInfoToTCQGByLcvDto: UpdateInfoToTCQGByLcvDto): Promise<true>;
}
