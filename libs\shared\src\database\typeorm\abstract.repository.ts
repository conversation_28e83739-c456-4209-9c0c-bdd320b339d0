import { Logger, NotFoundException } from '@nestjs/common';
import * as _ from 'lodash';
import {
  DeepPartial,
  EntityManager,
  FindManyOptions,
  FindOptionsRelations,
  FindOptionsWhere,
  ObjectLiteral,
  Repository,
  SaveOptions,
  UpdateResult,
} from 'typeorm';
import { FindOptionsOrder } from 'typeorm/find-options/FindOptionsOrder';
import { FindOptionsSelect } from 'typeorm/find-options/FindOptionsSelect';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { AbstractEntity } from './abtract.entity';

export interface IRepository<TEntity extends ObjectLiteral> {
  findById(id: number): Promise<TEntity | null>;

  findAll(where: FindOptionsWhere<TEntity>): Promise<Array<TEntity>>;

  create(entity: TEntity): Promise<TEntity>;

  save(entity: TEntity | TEntity[]): Promise<TEntity | TEntity[]>;

  findOne(where: FindOptionsWhere<TEntity>, relations?: FindOptionsRelations<TEntity>): Promise<TEntity | null>;

  findOneAndUpdate(
    where: FindOptionsWhere<TEntity>,
    partialEntity: QueryDeepPartialEntity<TEntity>,
  ): Promise<TEntity | null>;

  findOneAndDelete(where: FindOptionsWhere<TEntity>): Promise<any | null>;

  count(options: FindManyOptions<TEntity>): Promise<number>;

  softRemove(entitiesOrEntity: TEntity | TEntity[], options?: SaveOptions): Promise<TEntity[] | TEntity>;

  remove(entitiesOrEntity: TEntity | TEntity[], options?: SaveOptions): Promise<TEntity[] | TEntity>;

  softDelete(where: FindOptionsWhere<TEntity>): Promise<UpdateResult>;

  findAndCount(options?: FindManyOptions<TEntity>): Promise<[TEntity[], number]>;

  findAndCountBy(where: FindOptionsWhere<TEntity> | FindOptionsWhere<TEntity>[]): Promise<[TEntity[], number]>;

  updateById(id: number, partialEntity: DeepPartial<TEntity>): Promise<TEntity>;

  recover(entitiesOrEntity: TEntity | TEntity[], options?: SaveOptions): Promise<TEntity[] | TEntity>;
}

export abstract class AbstractRepository<TEntity extends AbstractEntity<TEntity>> implements IRepository<TEntity> {
  protected abstract readonly logger: Logger;

  protected readonly _repository: Repository<TEntity>;
  protected readonly _entityManager: EntityManager;

  constructor(repository: Repository<TEntity>, entityManager: EntityManager) {
    this._repository = repository;
    this._entityManager = entityManager;
  }

  async findById(id: any): Promise<TEntity | null> {
    const entity = await this._repository.findOneBy({ id });
    if (!entity) {
      this.logger.warn('Entity not found with', id);
      throw new NotFoundException('Entity not found.');
    }

    return entity;
  }

  async findOne(
    where: FindOptionsWhere<TEntity>[] | FindOptionsWhere<TEntity>,
    relations?: FindOptionsRelations<TEntity>,
    order?: FindOptionsOrder<TEntity>,
    withDeleted: boolean = false,
  ): Promise<TEntity> {
    return this._repository.findOne({ where, relations, order, withDeleted });
  }

  async find(
    select: FindOptionsSelect<TEntity>,
    where: FindOptionsWhere<TEntity>[] | FindOptionsWhere<TEntity>,
    relations?: FindOptionsRelations<TEntity>,
    order?: FindOptionsOrder<TEntity>,
    take?: number,
    skip?: number,
  ): Promise<Array<TEntity>> {
    return this._repository.find({
      select,
      where,
      relations,
      order,
      take,
      skip,
    });
  }

  async findAll(where: FindOptionsWhere<TEntity>[] | FindOptionsWhere<TEntity>): Promise<Array<TEntity>> {
    return this._repository.findBy(where);
  }

  async create(entity: TEntity): Promise<TEntity> {
    return this._entityManager.save(entity);
  }

  async save(entity: TEntity | TEntity[]): Promise<TEntity | TEntity[]> {
    return this._entityManager.save(entity);
  }

  async findOneAndUpdate(
    where: FindOptionsWhere<TEntity>,
    partialEntity: QueryDeepPartialEntity<TEntity>,
    relations?: FindOptionsRelations<TEntity>,
  ): Promise<TEntity> {
    const updateResult = await this._repository.update(where, partialEntity);

    if (!updateResult.affected) {
      this.logger.warn('Entity not found with where', where);
      throw new NotFoundException('Entity not found.');
    }

    return this.findOne(where, relations);
  }

  async findAndUpdate(
    where: FindOptionsWhere<TEntity>,
    partialEntity: QueryDeepPartialEntity<TEntity>,
    relations?: FindOptionsRelations<TEntity>,
  ): Promise<TEntity[]> {
    const updateResult = await this._repository.update(where, partialEntity);

    if (!updateResult.affected) {
      this.logger.warn('Entity not found with where', where);
      throw new NotFoundException('Entity not found.');
    }

    return this.find({}, where, relations);
  }

  async findOneAndDelete(where: FindOptionsWhere<TEntity>): Promise<any | null> {
    await this._repository.delete(where);
  }

  async count(options: FindManyOptions<TEntity>): Promise<number> {
    return this._repository.count(options);
  }

  async softRemove(entitiesOrEntity: TEntity | TEntity[], options?: SaveOptions): Promise<TEntity | TEntity[]> {
    return this._entityManager.softRemove(entitiesOrEntity, options);
  }

  async softDelete(where: FindOptionsWhere<TEntity>): Promise<UpdateResult> {
    return this._repository.softDelete(where);
  }

  async findAndCount(options?: FindManyOptions<TEntity>): Promise<[TEntity[], number]> {
    return this._repository.findAndCount(options);
  }

  async exist(options?: FindManyOptions<TEntity>): Promise<boolean> {
    return this._repository.exist(options);
  }

  async findAndCountBy(where: FindOptionsWhere<TEntity> | FindOptionsWhere<TEntity>[]): Promise<[TEntity[], number]> {
    return this._repository.findAndCountBy(where);
  }

  async updateById(id: number, partialEntity: DeepPartial<TEntity>): Promise<TEntity> {
    const foundInstance = await this.findById(id);
    _.keys(partialEntity).forEach((key) => {
      _.set(foundInstance, key, partialEntity[key]);
    });

    return this._entityManager.save(foundInstance);
  }

  async recover(entitiesOrEntity: TEntity | TEntity[], options?: SaveOptions): Promise<TEntity | TEntity[]> {
    return this._entityManager.recover(entitiesOrEntity, options);
  }

  async remove(entitiesOrEntity: TEntity | TEntity[], options?: SaveOptions): Promise<TEntity | TEntity[]> {
    return this._entityManager.remove(entitiesOrEntity, options);
  }
}
