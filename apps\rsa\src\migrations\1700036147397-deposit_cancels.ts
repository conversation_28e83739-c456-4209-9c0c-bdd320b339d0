import { MigrationInterface, QueryRunner } from 'typeorm';

export class depositCancels1700036147397 implements MigrationInterface {
  name = 'depositCancels1700036147397';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "deposit_cancels" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "reasonName" character varying NOT NULL, "reasonNote" character varying NOT NULL, "orderCode" character varying NOT NULL, "orderStatus" integer NOT NULL, "orderType" character varying NOT NULL, "depositCancelType" integer NOT NULL, "createdBy" character varying NOT NULL, "createdByName" character varying NOT NULL, "updatedBy" character varying NOT NULL, "updatedByName" character varying NOT NULL, "totalRefund" integer NOT NULL, "inventory" jsonb NOT NULL, "paymentHistory" jsonb NOT NULL, "paymentRequestTransferInfo" jsonb NOT NULL, "orderChanel" integer NOT NULL, "phone" character varying NOT NULL, CONSTRAINT "PK_f05defea3722bbd854a3708ab7e" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "deposit_cancels"`);
  }
}
