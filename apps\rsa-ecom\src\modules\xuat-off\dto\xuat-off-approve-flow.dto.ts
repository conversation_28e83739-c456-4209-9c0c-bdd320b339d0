import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateXuatOffApproveFlowDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  creatorRole: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  approverRole: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  statusXO: number;

  @ApiProperty({ default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateXuatOffApproveFlowDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  creatorRole?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  approverRole?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  statusXO?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class XuatOffApproveFlowRes {
  @ApiProperty()
  id: string;

  @ApiProperty()
  creatorRole: string;

  @ApiProperty()
  approverRole: string;

  @ApiProperty()
  statusXO: number;

  @ApiProperty()
  isActive: boolean;
}
