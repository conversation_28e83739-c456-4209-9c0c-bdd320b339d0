import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { XuatOffDetailResponseDto, XuatOffResponseDto } from './common.dto';

export class GetXuatOffDetailDto extends XuatOffDetailResponseDto {
  @ApiProperty({ nullable: true })
  @Expose()
  shopCode?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  orderStatus?: number;

  @ApiProperty({ nullable: true })
  @Expose()
  shopName?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  journeyId?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  ticketCode?: string;
}

export class FileItemDto {
  @ApiProperty()
  @Expose()
  key: string;

  @ApiProperty()
  @Expose()
  url: string;
}

export class GetXuatOffDto extends OmitType(XuatOffResponseDto, ['details', 'images', 'files'] as const) {
  @ApiProperty({ type: [GetXuatOffDetailDto] })
  @Expose()
  @IsOptional()
  @Type(() => GetXuatOffDetailDto)
  details?: GetXuatOffDetailDto[];

  @ApiProperty({ nullable: true })
  @Expose()
  shopCode?: string;

  @ApiProperty({ nullable: true })
  @Expose()
  shopName?: string;

  @ApiProperty({ type: [FileItemDto], nullable: true })
  @Expose()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FileItemDto)
  images?: FileItemDto[];

  @ApiProperty({ type: [FileItemDto], nullable: true })
  @Expose()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FileItemDto)
  files?: FileItemDto[];
}
