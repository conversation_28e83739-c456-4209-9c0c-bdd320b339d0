# API In Phiếu Trả Hàng - Print Order Return

## 📋 Tổng quan

### Mô tả
API mới được tạo trên module printCenter để in phiếu trả hàng. API này tích hợp với nhiều services để lấy đầy đủ thông tin cần thiết cho việc in phiếu.

### Thông tin API
- **Endpoint**: `POST /api/v1/print-center/order-return`
- **Controller**: `PrintCenterController.printOrderReturn`
- **Service**: `PrintCenterService.printOrderReturn`
- **Authentication**: Public (không cần token)

## 🔄 Quy trình xử lý

### Sequence Diagram
```mermaid
sequenceDiagram
    participant FE
    participant PrintCenterService
    participant ReturnHomeService
    participant OMSService
    participant InsideService
    participant PaymentGatewayService

    FE->>PrintCenterService: POST /order-return
    Note over FE,PrintCenterService: { printerInfo, orderReturnId }

    Note over PrintCenterService: B1: <PERSON><PERSON><PERSON> chi tiết đơn trả hàng
    PrintCenterService->>ReturnHomeService: getOrderReturn(orderReturnId)
    ReturnHomeService-->>PrintCenterService: orderReturnDetail

    Note over PrintCenterService: B2: Lấy chi tiết đơn hàng gốc
    PrintCenterService->>OMSService: getOneOrder(orderCode)
    OMSService-->>PrintCenterService: orderDetail

    Note over PrintCenterService: B3: Lấy thông tin job title
    PrintCenterService->>InsideService: getJobTitleByEmployee(employeeCode)
    InsideService-->>PrintCenterService: jobTitles (find isMaster=true)

    Note over PrintCenterService: B4: Lấy thông tin transfer
    PrintCenterService->>PaymentGatewayService: transferInfoByPaymentCode(paymentCode)
    PaymentGatewayService-->>PrintCenterService: transferInfo

    Note over PrintCenterService: Tạo printData và return
    PrintCenterService-->>FE: PrintCenterRes
```

## 📝 Request & Response

### Request Body (PrintOrderReturnDto)
```typescript
{
  "printerInfo": {
    "printerName": "string",      // Tên máy in
    "printerType": "string",      // Loại máy in
    "ignorePrint": boolean        // Bỏ qua in (chỉ lấy data)
  },
  "orderReturnId": "string"       // ID đơn trả hàng (required)
}
```

### Response (PrintCenterRes)
```typescript
{
  "url": "string",               // URL file (placeholder)
  "imageUrl": "string",          // URL image (placeholder)
  "pdfUrl": "string",           // URL PDF (placeholder)
  "printData": {                // Dữ liệu để in
    "paymentCode": "string",
    "totalFeeCompensationForBreachOfContract": number,
    "typePayment": "string",
    "completedDate": "string",
    "masterEmployeeName": "string",
    "masterEmployeeCode": "string",
    "accountNum": "string",
    "accountName": "string",
    "bankName": "string",
    "orderCode": "string",
    "orderReturnCode": "string"
  }
}
```

## 🔧 Chi tiết implementation

### Bước 1: Lấy chi tiết đơn trả hàng
```typescript
const orderReturnDetail = await this.returnHomeService.getOrderReturn(orderReturnId);
```
**Lấy được:**
- `paymentCode` → Dùng cho B4
- `totalFeeCompensationForBreachOfContract` → Vào printData
- `typePayment` → Vào printData
- `orderCode` → Dùng cho B2

### Bước 2: Lấy chi tiết đơn hàng gốc
```typescript
const orderDetail = await this.omsService.getOneOrder(orderReturnDetail.orderCode);
```
**Lấy được:**
- `employees` → Tìm employeeStep 2 để lấy `modifiedDate`
- `createdBy` → Dùng cho B3

### Bước 3: Lấy thông tin job title
```typescript
const jobTitles = await this.insideService.getJobTitleByEmployee({
  EmployeeCode: orderDetail.createdBy,
});
const masterJob = jobTitles?.find(job => job.isMaster === true);
```
**Lấy được:**
- `employeeName` → `masterEmployeeName`
- `employeeCode` → `masterEmployeeCode`

### Bước 4: Lấy thông tin transfer
```typescript
const transferInfo = await this.paymentGatewayService.transferInfoByPaymentCode({
  PaymentCode: orderReturnDetail.paymentCode,
});
```
**Lấy được:**
- `transferAll.accountNum` → `accountNum`
- `transferAll.accountName` → `accountName`
- `transferAll.bankName` → `bankName`

## 📊 Data Mapping

### Nguồn dữ liệu cho PrintData

| **Field** | **Nguồn** | **Bước** | **Mô tả** |
|-----------|-----------|----------|-----------|
| `paymentCode` | `orderReturnDetail.paymentCode` | B1 | Mã thanh toán |
| `totalFeeCompensationForBreachOfContract` | `orderReturnDetail.totalFeeCompensationForBreachOfContract` | B1 | Phí bồi thường |
| `typePayment` | `orderReturnDetail.typePayment` | B1 | Loại thanh toán |
| `completedDate` | `orderDetail.employees[step=2].modifiedDate` | B2 | Ngày hoàn tất |
| `masterEmployeeName` | `jobTitles[isMaster=true].employeeName` | B3 | Tên NV master |
| `masterEmployeeCode` | `jobTitles[isMaster=true].employeeCode` | B3 | Mã NV master |
| `accountNum` | `transferInfo.transferAll.accountNum` | B4 | Số tài khoản |
| `accountName` | `transferInfo.transferAll.accountName` | B4 | Tên tài khoản |
| `bankName` | `transferInfo.transferAll.bankName` | B4 | Tên ngân hàng |
| `orderCode` | `orderReturnDetail.orderCode` | B1 | Mã đơn gốc |
| `orderReturnCode` | `orderReturnDetail.orderReturnCode` | B1 | Mã đơn trả |

## ⚠️ Error Handling

### Các lỗi có thể xảy ra
1. **Không tìm thấy đơn trả hàng**: `orderReturnId` không tồn tại
2. **Không tìm thấy đơn hàng gốc**: `orderCode` không tồn tại
3. **Lỗi khi gọi InsideService**: Không lấy được job title
4. **Lỗi khi gọi PaymentGatewayService**: Không lấy được transfer info

### Error Response
```typescript
{
  "statusCode": 500,
  "message": "Lỗi khi in phiếu trả hàng: [error details]",
  "error": "Internal Server Error"
}
```

## 🔗 Dependencies

### Services được sử dụng
- **ReturnHomeService**: `getOrderReturn(orderReturnId)`
- **OMSService**: `getOneOrder(orderCode)`
- **InsideService**: `getJobTitleByEmployee({ EmployeeCode })`
- **PaymentGatewayService**: `transferInfoByPaymentCode({ PaymentCode })`

### DTOs được tạo
- **PrintOrderReturnDto**: Input DTO
- **PrintOrderReturnDataDto**: Output data structure

## 🚀 Usage Example

### Request
```bash
curl -X POST "http://localhost:3000/api/v1/print-center/order-return" \
  -H "Content-Type: application/json" \
  -d '{
    "printerInfo": {
      "printerName": "HP LaserJet",
      "printerType": "LASER",
      "ignorePrint": false
    },
    "orderReturnId": "RET123456789"
  }'
```

### Response
```json
{
  "url": "",
  "imageUrl": "",
  "pdfUrl": "",
  "printData": {
    "paymentCode": "PAY123456",
    "totalFeeCompensationForBreachOfContract": 50000,
    "typePayment": "BANK_TRANSFER",
    "completedDate": "2024-01-15T10:30:00Z",
    "masterEmployeeName": "Nguyễn Văn A",
    "masterEmployeeCode": "EMP001",
    "accountNum": "**********",
    "accountName": "NGUYEN VAN A",
    "bankName": "Vietcombank",
    "orderCode": "ORD123456789",
    "orderReturnCode": "RET123456789"
  }
}
```

## 📝 Notes

### Tính năng hiện tại
- ✅ Lấy đầy đủ dữ liệu từ 4 services
- ✅ Error handling cho từng bước
- ✅ Type safety với DTOs
- ⚠️ Chưa tích hợp với print service thực tế

### Cần phát triển thêm
- 🔄 Tích hợp với print service để tạo PDF/Image thực tế
- 🔄 Thêm validation cho business rules
- 🔄 Thêm logging và monitoring
- 🔄 Thêm unit tests

### Business Logic
- **EmployeeStep 2**: Là bước hoàn tất đơn hàng
- **isMaster = true**: Nhân viên có quyền cao nhất
- **transferAll**: Thông tin chuyển khoản đầy đủ
