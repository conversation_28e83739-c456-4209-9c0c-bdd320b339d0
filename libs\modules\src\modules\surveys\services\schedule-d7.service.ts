import { HttpStatus, Inject, Injectable, LoggerService } from '@nestjs/common';
import { ErrorCode, SystemException } from '@shared';
import * as _ from 'lodash';
import moment from 'moment';
import { POTENTIAL_CUSTOMER_TICKET_STATUS, SurveyConsultantResult } from 'vac-nest-rsa-integration';
import { formatNotificationPayload, generateAddressId } from '@libs/modules/notification/notification.utils';
import { NOTI_REGISTER_CHANEL_NAME } from '@libs/modules/notification/notification.enum';
import {
  AssignScheduleD7RemindTemplate,
  AssignScheduleD7Template,
} from '@libs/modules/notification/templates/assign-survey.template';
import { NotificationService as LocalNotificationService } from 'modules/modules/modules/notification';
import { VacCoreAssignJobService } from 'vac-nest-assign-job';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import {
  AssignSurveysDto,
  OsrService,
  SearchSurveyCustomerD7ByQueryDto,
  SurveyCustomerD7,
  SurveyCustomerD7DetailGroup,
} from 'vac-nest-osr';
import { SMSSurveysService } from './sms.service';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import Redis from 'ioredis';
import {
  REDIS_CACHE_ASSIGN_SCHEDULE_D7_DATA,
  SCHEDULE_D7_TICKET_STATUS,
  ScheduleD7ConsultantResult,
} from '../constants';
import { ScheduleRequestD7AssignTypes } from 'vac-nest-assign-job/dist/assign-job.enum';
import { AutoAssignScheduleD7Dto, HandAssignScheduleD7Dto, UpdateSurveyCustomerD7 } from '../dto/schedule-d7.dto';
import { InsideService } from 'vac-nest-inside';
import { JSONPath } from 'jsonpath-plus';
import { EmployeeService } from 'apps/rsa-ecom/src/modules/employee/employee.service';
import { ExaminationCoreService } from 'vac-nest-examination';
import { JourneyService } from 'vac-nest-journey';
import { NotifyOsrDto } from '../dto/survey-ocr.dto';
import { GetAgeRangeResponseDto, RegimenService } from 'vac-nest-regimen';
import { calculateTimeDifference } from 'vac-commons';

@Injectable()
export class ScheduleD7Service {
  constructor(
    private readonly localNotificationService: LocalNotificationService,
    private readonly vacCoreAssignJobService: VacCoreAssignJobService,
    private readonly osrService: OsrService,
    private readonly sMSSurveysService: SMSSurveysService,
    private readonly insideService: InsideService,
    private readonly employeeService: EmployeeService,
    private readonly examinationCoreApiService: ExaminationCoreService,
    private readonly journeyService: JourneyService,
    private readonly regimenService: RegimenService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    @InjectRedis(process.env.REDIS_CONNECTION)
    private readonly redis: Redis,
  ) {}

  private async validateScheduleD7(schedule: SurveyCustomerD7) {
    if (!schedule) {
      throw new SystemException(
        {
          code: 'SCHEDULE_D7_REQUEST_NOT_FOUND:000404',
          message: 'Không tìm thấy yêu cầu lịch hẹn D7',
          details: 'Không tìm thấy yêu cầu lịch hẹn D7',
          validationErrors: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }

    if (
      !(
        +schedule?.status === SCHEDULE_D7_TICKET_STATUS.NEW &&
        +schedule?.consultantResult === ScheduleD7ConsultantResult.PENDING
      )
    ) {
      throw new SystemException(
        {
          code: 'SCHEDULE_D7_REQUEST_ALREADY_PROCESSED:000403',
          message: 'Yêu cầu đã xử lí',
          details: 'Yêu cầu đã xử lí',
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!!schedule?.assigneeId) {
      throw new SystemException(
        {
          code: 'SCHEDULE_D7_REQUEST_ALREADY_ASSIGNED:000403',
          message: 'Lịch hẹn D7 đã được phân công cho nhân viên khác',
          details: 'Lịch hẹn D7 đã được phân công cho nhân viên khác',
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    return true;
  }

  private async getScheduleD7DetailById(id: string) {
    const surveyRes = await this.osrService.getSurveyCustomerD7ByListId(id);
    const scheduleDetail = surveyRes?.[0];
    return scheduleDetail;
  }

  async getListScheduleD7(body: SearchSurveyCustomerD7ByQueryDto) {
    const [res, ageRanges] = await Promise.all([
      this.osrService.searchSurveyCustomerD7ByQueryHasGrouped(body),
      this.regimenService.getAgeRanges(),
    ]);
    return {
      totalCount: res?.totalCount || 0,
      items: res?.items?.map((item) => ({
        ...item,
        details: item.details?.map((detail) => ({
          ...detail,
          detailsGroupByInjectedLCV: detail.detailsGroupByInjectedLCV?.map((group) => ({
            ...this.enRichIsInProcess(group),
            ageGroup: this.handleAgeGroup(group?.injectedPersonDOB, ageRanges),
          })),
        })),
      })),
    };
  }

  private processListVaccineConsultant(listVaccineConsultant: any, surveyTransaction: any) {
    const consultants = listVaccineConsultant || [];
    if (consultants?.length === 0) return [];

    let theLastTransaction;
    if (surveyTransaction?.length > 0) {
      const transactions = surveyTransaction.sort(
        (a, b) => moment(a.createdDate).valueOf() - moment(b.createdDate).valueOf(),
      );
      // Lấy transaction cuối cùng
      theLastTransaction = transactions[transactions.length - 1];
    }

    if (!theLastTransaction) return [];

    // Tìm createdAt lớn nhất bằng moment
    // const maxCreatedDate = consultants.reduce((max, t) => {
    //   const current = moment(t.createdDate);
    //   return current.isAfter(max) ? current : max;
    // }, moment(consultants[0].createdDate));
    const maxCreatedDate = moment(theLastTransaction?.createdDate);

    // Filter những transaction có cùng thời gian chính xác
    return consultants.filter(
      (t) => moment(t?.createdDate).format('YYYY-MM-DD HH:mm') === maxCreatedDate.format('YYYY-MM-DD HH:mm'),
    );
  }

  async getDetailScheduleD7(id: string) {
    const [scheduleDetail, ageRanges] = await Promise.all([
      this.getScheduleD7DetailById(id),
      this.regimenService.getAgeRanges(),
    ]);
    try {
      const [journey, { items: examinations }] = await Promise.all([
        this.journeyService.getJourneyByOrderCode({ orderCode: scheduleDetail?.orderCode }),
        this.examinationCoreApiService.getTicketByOrderCode({
          orderCodes: [scheduleDetail?.orderCode],
        }),
      ]);
      scheduleDetail['journeyId'] = journey?.id || '';
      scheduleDetail['ticketCode'] = examinations[0]?.ticketCode || '';
    } catch (_error) {
      scheduleDetail['ticketCode'] = '';
      scheduleDetail['journeyId'] = '';
    }

    return {
      ...this.enRichIsInProcess(scheduleDetail),
      listVaccineConsultant: this.processListVaccineConsultant(
        scheduleDetail?.listVaccineConsultant,
        scheduleDetail?.surveyTransactions,
      ),
      ageGroup: this.handleAgeGroup(scheduleDetail?.injectedPersonDOB, ageRanges),
    };
  }

  async updateScheduleD7(body: UpdateSurveyCustomerD7) {
    const phoneNumberSendSMS = body?.phoneNumberSendSMS || body?.phoneNumber || '';
    const customerSendSMS = body?.customerSendSMS || body?.customerName || '';
    body?.phoneNumberSendSMS && delete body?.phoneNumberSendSMS;
    body?.customerSendSMS && delete body?.customerSendSMS;
    // Các rule theo BA:
    //1. Nếu consultantResult = 2,3,4,5,10 thì status chuyển về Done = 3
    //2. Có ghi chú sẽ lưu ghi chú
    //3. Nếu consultantResult = 3,5,10 thì lưu thêm list vaccine tư vấn (listVaccineConsultant) và lưu thêm 2 feild phoneNumberSendSMS customerSendSMS --> FE lam
    //4. Bỏ ([SurveyConsultantResult.ORDER_CANCELLED].includes(Number(currentSurvey.consultantResult)))
    //5. Vẫn keep rule isInProcess
    //6. Bỏ rule khung giờ
    // Step 1: Cập nhật kết quả schedule
    // Step 2: Gửi tin nhắn sms cho khách hàng
    const [scheduleDetail, ageRanges] = await Promise.all([
      this.getScheduleD7DetailById(body.id),
      this.regimenService.getAgeRanges(),
    ]);
    if (!scheduleDetail) {
      throw new SystemException(
        {
          code: 'SCHEDULE_D7_REQUEST_NOT_FOUND:000404',
          message: 'Không tìm thấy yêu cầu lịch hẹn D7',
          details: 'Không tìm thấy yêu cầu lịch hẹn D7',
          validationErrors: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }

    if (
      [
        ScheduleD7ConsultantResult.APPOINTMENT_SUCCESS,
        ScheduleD7ConsultantResult.REFERENCE_1,
        ScheduleD7ConsultantResult.NO_NEED,
        ScheduleD7ConsultantResult.CANT_CONTACT,
        ScheduleD7ConsultantResult.DO_NOT_ANSWER_THE_PHONE,
        ScheduleD7ConsultantResult.OTHER,
      ].includes(body.consultantResult)
    ) {
      body.status = SCHEDULE_D7_TICKET_STATUS.CONTACTED;
    }

    if (!this.enRichIsInProcess(scheduleDetail)?.isInProcess) {
      throw new SystemException(
        {
          code: 'SCHEDULE_D7_REQUEST_NOT_IN_PROCESS:000403',
          message: 'Yêu cầu không nằm trong quá trình xử lí',
          details: 'Yêu cầu không nằm trong quá trình xử lí',
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    const survey = await this.osrService.updateSurveyCustomerD7(body.id, {
      ...body,
      crCurrentStep: body?.step,
    });

    // Add user to queue
    await this.vacCoreAssignJobService.addQueueScheduleRequestD7Assignment({
      type: ScheduleRequestD7AssignTypes.User,
      sub: {
        listInsideId: [body.assigneeId],
      },
    });

    // Send sms to customer
    try {
      const vaccineCategorizations = await this.osrService.getListVaccineCategorizationByCodes(
        body?.listVaccineConsultant?.map((e) => `${e?.code}`),
      );
      await this.sMSSurveysService.sendSmsSurveyForScheduleD7({
        customerName: customerSendSMS || scheduleDetail?.customerName,
        phoneCustomerSendSMS: phoneNumberSendSMS || scheduleDetail?.phoneNumber,
        vaccineCategorizations,
        consultantResult: body?.consultantResult,
        modifiedByName: body?.modifiedByName,
      });
    } catch (err) {
      await this.logger.log(
        {
          message: `send sms fail`,
          fields: {
            info: `send sms fail`,
            method: `Post`,
            url: `send sms fail`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify(err || []),
          },
        },
        false,
      );
    }

    return {
      survey: {
        ...this.enRichIsInProcess(survey),
        listVaccineConsultant: this.processListVaccineConsultant(
          survey?.listVaccineConsultant,
          survey?.surveyTransactions,
        ),
        ageGroup: this.handleAgeGroup(survey?.injectedPersonDOB, ageRanges),
      },
      errorMessage: '',
    };
  }

  async updateScheduleD7Assign(body: HandAssignScheduleD7Dto) {
    // Hàm assign tay
    // Step 1: Lấy chi tiết schedule d7
    // Step 2: kiểm tra xem schedule có notInProcess hay không ==> có thì chặn

    const scheduleDetail = await this.getScheduleD7DetailById(body.id);

    const resGetListScheduleSamePhone = await this.getListScheduleD7WithFlatItems({
      skipCount: 0,
      maxResultCount: 10,
      query: scheduleDetail.phoneNumber,
      status: [SCHEDULE_D7_TICKET_STATUS.NEW],
      consultantResult: [ScheduleD7ConsultantResult.PENDING],
    });

    if (!this.enRichIsInProcess(scheduleDetail)?.isInProcess) {
      throw new SystemException(
        {
          code: 'SCHEDULE_D7_REQUEST_NOT_IN_PROCESS:000403',
          message: 'Yêu cầu không nằm trong quá trình xử lí',
          details: 'Yêu cầu không nằm trong quá trình xử lí',
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    await this.osrService.updateAssigneeSurveyCustomerD7(
      body?.isReAssign
        ? [
            {
              id: body.id,
              assigneeId: body.assigneeId,
              assigneeName: body.assigneeName,
              reAssigneeDate: moment().utcOffset(7).format(),
              modifiedBy: body.modifiedBy,
            },
          ]
        : [
            {
              id: body.id,
              assigneeId: body.assigneeId,
              assigneeName: body.assigneeName,
              assigneeDate: moment().utcOffset(7).format(),
              modifiedBy: body.modifiedBy,
            },
          ],
    );
    // Remove user, request from assign job queue
    await Promise.all([
      // remove user được phân công
      this.vacCoreAssignJobService.removeQueueScheduleRequestD7Assignment({
        type: ScheduleRequestD7AssignTypes.User,
        sub: {
          listInsideId: [body.assigneeId],
        },
      }),
      // remove schedule request d7
      this.vacCoreAssignJobService.removeQueueScheduleRequestD7Assignment({
        type: ScheduleRequestD7AssignTypes.ScheduleRequestD7,
        sub: {
          listScheduleRequestCode: [body.id],
        },
      }),
      // Add lại nhân viên bị đá ra nếu có
      scheduleDetail?.assigneeId &&
        this.vacCoreAssignJobService.addQueueScheduleRequestD7Assignment({
          type: ScheduleRequestD7AssignTypes.User,
          sub: {
            listInsideId: [scheduleDetail.assigneeId],
          },
        }),
      // Noti cho nhân viên
      this.sendNotificationToEmployee(
        body.assigneeId,
        body.id,
        scheduleDetail?.phoneNumber,
        scheduleDetail?.createdDate,
        scheduleDetail?.injectedPersonLCV || '',
      ),
    ]);
    // End remove
    // Xử lý cho list còn lại

    const listScheduleNeedAssign = resGetListScheduleSamePhone?.filter((item) => item.id !== body.id);
    await this.handleScheduleD7ListNotHaveAssigneeId(listScheduleNeedAssign, body.assigneeId, body.assigneeName);
    return true;
  }

  async updateAutoScheduleD7Assign(body: AutoAssignScheduleD7Dto) {
    // step 1: Lấy chi tiết schedule d7
    const [scheduleDetail, empData, logCheckIn] = await Promise.all([
      this.getScheduleD7DetailById(body.scheduleId),
      this.insideService.getEmployeeByCode({
        listEmployeeCode: [body.insideId],
      }),
      this.employeeService.getEmployee(body.insideId),
    ]);

    if (!empData?.length) {
      throw new SystemException(
        {
          code: 'SCHEDULE_D7_USER_NOT_ONLINE:000400',
          message: 'Nhân viên không tồn tại ở ecom',
          details: 'Nhân viên không tồn tại ở ecom',
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    if (logCheckIn?.onlineUnPaidSurvey !== 1) {
      throw new SystemException(
        {
          code: 'SCHEDULE_D7_USER_NOT_ONLINE:000400',
          message: ErrorCode.getError(ErrorCode.SCHEDULE_D7_USER_NOT_ONLINE),
          details: ErrorCode.getError(ErrorCode.SCHEDULE_D7_USER_NOT_ONLINE),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // Step 2: Kiểm tra cache redis
    const keyRedis = `${REDIS_CACHE_ASSIGN_SCHEDULE_D7_DATA}:${body?.scheduleId}`;

    try {
      const cachedData = await this.redis.get(keyRedis);
      // Nếu mà có cache Data và chưa có assigneeId thì gán assigneeId từ cache
      if (cachedData && !scheduleDetail?.assigneeId) {
        scheduleDetail.assigneeId = cachedData;
      }
    } catch (error) {
      this.logger.error('info', `Get Cache redis zero order error: ${error?.message}`);
    }

    // Step 3: Validate survey assignment
    await this.validateScheduleD7(scheduleDetail);

    // Handle auto-assign logic with phone number matching
    // Step 4: Tìm những schedule d7 có cùng số điện thoại với trạng thái new và consultantResult là pending
    const resGetListScheduleSamePhone = await this.getListScheduleD7WithFlatItems({
      skipCount: 0,
      maxResultCount: 10,
      query: scheduleDetail.phoneNumber,
      status: [SCHEDULE_D7_TICKET_STATUS.NEW],
      consultantResult: [ScheduleD7ConsultantResult.PENDING],
    });

    let isAlreadyHandleListNotHaveAssigneeId = false;
    // Find surveys that are currently being processed by other assignees
    const currentlyProcessedSchedule: SurveyCustomerD7DetailGroup[] = resGetListScheduleSamePhone?.filter(
      (s) => s.assigneeId && s.id !== scheduleDetail.id,
    );
    // If someone is already processing this phone number, assign to that person
    const payloadUpdate = {
      id: body.scheduleId,
      assigneeId: body.insideId,
      assigneeName: empData?.[0]?.employeeName || '',
      assigneeDate: moment().utcOffset(7).format(),
    };
    if (currentlyProcessedSchedule?.length) {
      const existingAssignee = currentlyProcessedSchedule[0];
      // Update payload to use existing assignee
      payloadUpdate.assigneeId = existingAssignee.assigneeId;
      payloadUpdate.assigneeName = existingAssignee.assigneeName;

      if (existingAssignee.assigneeId !== body.insideId) {
        // Phân công
        await this.osrService.updateAssigneeSurveyCustomerD7([payloadUpdate]);
        // Noti cho nhân viên
        await this.sendNotificationToEmployee(
          existingAssignee.assigneeId,
          existingAssignee.id,
          scheduleDetail?.phoneNumber,
          scheduleDetail?.createdDate,
          scheduleDetail?.injectedPersonLCV,
        );
      }

      const listScheduleNeedToAssign = resGetListScheduleSamePhone?.filter(
        (s) => !s.assigneeId && s.id !== scheduleDetail.id,
      );

      await this.handleScheduleD7ListNotHaveAssigneeId(
        listScheduleNeedToAssign,
        payloadUpdate.assigneeId,
        payloadUpdate.assigneeName,
      );
      isAlreadyHandleListNotHaveAssigneeId = true;

      if (existingAssignee.assigneeId !== body.insideId) {
        // Không thể trả 200 mà phải trả lỗi remove request ra khỏi queue
        throw new SystemException(
          {
            code: 'SCHEDULE_D7_REQUEST_ALREADY_PROCESSED:000403',
            message: 'Yêu cầu đã xử lí',
            details: 'Yêu cầu đã xử lí',
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    } else {
      // Check if the assignee is already processing other phone numbers
      // const dateNow = moment().utcOffset(7).format('YYYY-MM-DD');
      const today = moment().utcOffset(7).format('YYYY-MM-DD');
      // const dayBefore4Days = moment().utcOffset(7).subtract(4, 'days').format('YYYY-MM-DD');
      const scheduleActiveSurveys = await this.getListScheduleD7WithFlatItems({
        consultantResult: [SurveyConsultantResult.PENDING],
        status: [POTENTIAL_CUSTOMER_TICKET_STATUS.NEW],
        assigneeIds: [body.insideId],
        maxResultCount: 100,
        skipCount: 0,
        assigneeDate: today,
        // fromDate: dayBefore4Days,
        // toDate: today,
      });

      // Find surveys that assignee is processing with different phone numbers
      const otherPhoneSurveys = scheduleActiveSurveys?.filter(
        (s) =>
          s.id !== body.scheduleId &&
          s.phoneNumber !== scheduleDetail?.phoneNumber &&
          ((s?.assigneeDate && moment(s?.assigneeDate).utcOffset(7).format('YYYY-MM-DD') === today) ||
            (s?.reAssigneeDate && moment(s?.reAssigneeDate).utcOffset(7).format('YYYY-MM-DD') === today)),
      );

      if (otherPhoneSurveys?.length) {
        throw new SystemException(
          {
            code: 'SCHEDULE_D7_USER_ALREADY_ASSIGN:000403',
            message: `Nhân viên đã có lịch hẹn với số điện thoại khác trong ngày hôm nay: ${otherPhoneSurveys?.[0]?.phoneNumber} ${otherPhoneSurveys?.[0]?.id}`,
            details: `Nhân viên đã có lịch hẹn với số điện thoại khác trong ngày hôm nay: ${otherPhoneSurveys?.[0]?.phoneNumber} ${otherPhoneSurveys?.[0]?.id}`,
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    await this.osrService.updateAssigneeSurveyCustomerD7([payloadUpdate]);
    // Noty cho nhân viên
    await this.sendNotificationToEmployee(
      payloadUpdate.assigneeId,
      payloadUpdate.id,
      scheduleDetail?.phoneNumber,
      scheduleDetail?.createdDate,
      scheduleDetail?.injectedPersonLCV,
    );
    const listScheduleNeedToAssign = resGetListScheduleSamePhone?.filter(
      (s) => !s.assigneeId && s.id !== scheduleDetail.id,
    );
    !isAlreadyHandleListNotHaveAssigneeId &&
      (await this.handleScheduleD7ListNotHaveAssigneeId(
        listScheduleNeedToAssign,
        payloadUpdate.assigneeId,
        payloadUpdate.assigneeName,
      ));
    // Save redis cache
    try {
      await this.redis.set(keyRedis, payloadUpdate.assigneeId, 'EX', 5);
    } catch (error) {
      this.logger.error('info', `Set Cache redis zero order error ${error?.message}`);
    }
    return true;
  }

  async getKQTVByIdAndSource(id: string) {
    return await this.osrService.getConsultantResultForEcomD7(id);
  }

  async notifyOsr(body: NotifyOsrDto) {
    // Gửi noti cho nhân viên
    const listIdRq = body?.requestIds;
    const listDetailRq = listIdRq?.length ? await this.osrService.getSurveyCustomerD7ByIds(listIdRq) : [];
    for (const detailRq of listDetailRq) {
      const info = {
        idSurvey: detailRq?.id,
        phoneNumberSurvey: detailRq?.phoneNumber,
        assignTimeSurvey: moment(detailRq?.assigneeDate).utcOffset('+07:00').format('HH:mm DD/MM/YYYY'),
        createdTimeSurvey: moment(detailRq?.createdDate).utcOffset('+07:00').format('HH:mm DD/MM/YYYY'),
        lcvId: detailRq?.injectedPersonLCV || '',
      };
      await this.localNotificationService.sendNotifications([
        formatNotificationPayload({
          To: [generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, detailRq?.assigneeId)],
          Cc: [],
          Bcc: [],
          template: AssignScheduleD7RemindTemplate(info),
          replaceParams: {
            title: info,
            content: info,
            messageLink: info,
          },
        }),
      ]);
    }
    // End noti
    return true;
  }

  private async handleScheduleD7ListNotHaveAssigneeId(
    listScheduleD7Assign: SurveyCustomerD7[],
    assigneeId: string,
    assigneeName: string,
  ) {
    if (!listScheduleD7Assign?.length) {
      return;
    }
    // step 1: Assign listScheduleD7Assign cho assigneeId
    await this.osrService.updateAssigneeSurveyCustomerD7([
      ...listScheduleD7Assign?.map((item) => {
        const isReAssign = !!item?.assigneeId;
        return isReAssign
          ? {
              id: item.id,
              assigneeId: assigneeId,
              assigneeName: assigneeName,
              reAssigneeDate: moment().utcOffset(7).format(),
            }
          : {
              id: item.id,
              assigneeId: assigneeId,
              assigneeName: assigneeName,
              assigneeDate: moment().utcOffset(7).format(),
            };
      }),
    ]);
    // step 2: Send noti cho assigneeId với những ticket đã assign
    // step 3: Add lại những nhân viên đã bị đá ra schedule d7 vào lại queue
    // Step 4: remove assigneeId ra khỏi queue
    // step 5: remove listScheduleD7Assign ra khỏi queue
    const promiseSendNoti = [];
    for (const item of listScheduleD7Assign) {
      promiseSendNoti.push(
        this.sendNotificationToEmployee(
          assigneeId,
          item?.id,
          item?.phoneNumber,
          item?.createdDate,
          item?.injectedPersonLCV,
        ),
      );
    }
    // Send noti cho nhân viên
    await Promise.all(promiseSendNoti);

    const listAssigneeAddAgain = listScheduleD7Assign?.map((item) => item?.assigneeId).filter((item) => !!item) || [];
    const listScheduleRemove = listScheduleD7Assign?.map((item) => item?.id) || [];
    await Promise.all([
      // Add lại những user bị đá ra
      this.vacCoreAssignJobService.addQueueScheduleRequestD7Assignment({
        type: ScheduleRequestD7AssignTypes.User,
        sub: {
          listInsideId: _.uniq(listAssigneeAddAgain),
        },
      }),
      // remove user được phân công
      this.vacCoreAssignJobService.removeQueueScheduleRequestD7Assignment({
        type: ScheduleRequestD7AssignTypes.User,
        sub: {
          listInsideId: [assigneeId],
        },
      }),
      // remove các schedule request d7
      this.vacCoreAssignJobService.removeQueueScheduleRequestD7Assignment({
        type: ScheduleRequestD7AssignTypes.ScheduleRequestD7,
        sub: {
          listScheduleRequestCode: _.uniq(listScheduleRemove),
        },
      }),
    ]);
  }

  private enRichIsInProcess(survey: SurveyCustomerD7) {
    const reference = +survey.consultantResult === ScheduleD7ConsultantResult.REFERENCE_1 && survey.crCurrentStep === 1;
    const notAnswer =
      +survey.consultantResult === ScheduleD7ConsultantResult.DO_NOT_ANSWER_THE_PHONE && survey.crCurrentStep === 1;
    const pending = +survey.consultantResult === ScheduleD7ConsultantResult.PENDING;

    const isInProcess =
      [SCHEDULE_D7_TICKET_STATUS.NEW, SCHEDULE_D7_TICKET_STATUS.CONTACT_AGAIN].includes(survey.status) &&
      [reference, notAnswer, pending].includes(true);

    return {
      ...survey,
      isInProcess,
    };
  }

  private async sendNotificationToEmployee(
    assigneeId: string,
    scheduleId: string,
    phoneNumber: string,
    createdDate: string,
    lcvId: string,
  ) {
    try {
      const info = {
        idSurvey: scheduleId,
        phoneNumberSurvey: phoneNumber,
        assignTimeSurvey: moment().utcOffset('+07:00').format('HH:mm DD/MM/YYYY'),
        createdTimeSurvey: moment(createdDate).utcOffset('+07:00').format('HH:mm DD/MM/YYYY'),
        lcvId,
      };
      await this.localNotificationService.sendNotifications([
        formatNotificationPayload({
          To: [generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, assigneeId)],
          Cc: [],
          Bcc: [],
          template: AssignScheduleD7Template(info),
          replaceParams: {
            title: info,
            content: info,
            messageLink: info,
          },
        }),
      ]);
    } catch (err) {
      await this.logger.log(
        {
          message: `Noti fail`,
          fields: {
            info: `Noti fail`,
            method: `Post`,
            url: `Noti fail`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify(err || []),
          },
        },
        false,
      );
    }
  }

  private async getListScheduleD7WithFlatItems(body: SearchSurveyCustomerD7ByQueryDto) {
    const res = await this.osrService.searchSurveyCustomerD7ByQueryHasGrouped(body);
    // Sử dụng JSONPath để lấy tất cả details từ items
    const allDetails = JSONPath({ path: '$.items[*].details[*].detailsGroupByInjectedLCV[*]', json: res });

    return allDetails as SurveyCustomerD7DetailGroup[];
  }

  private handleAgeGroup(ageGroup: string, ageRanges: GetAgeRangeResponseDto[]) {
    const ageRes = ageGroup ? calculateTimeDifference(ageGroup, 0, 0, ageRanges, 0)?.textDisplay : ageGroup;
    return ageRes;
  }
}
