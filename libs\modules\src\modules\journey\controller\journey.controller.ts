import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, Public, generalSchema } from '@shared';
import {
  BaseJourneyRes,
  CreatedJourneyStepDto,
  GetJourneyByOrderCodeDto,
  JourneyService as JourneyCoreService,
  OrdersInfo,
  SearchJourneyDto,
  UpdateGiftDeliveryDto,
  UpdateMultipleJourneyDto,
} from 'vac-nest-journey';
import { JourneyLocalService } from '../services/journey.service';
import {
  ResGetJourneyByOrderCode,
  ResGetJourneyId,
  UpdateManyJourneyDto,
  UpdateMultipleDetailJourneyRes,
} from '../dto';
@Controller({ path: 'journey', version: '1' })
@ApiTags('Journey')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(
  ClassResponse,
  BaseJourneyRes,
  ResGetJourneyId,
  UpdateMultipleDetailJourneyRes,
  ResGetJourneyByOrderCode,
)
export class JourneyController {
  constructor(
    private readonly journeyCoreService: JourneyCoreService,
    private readonly journeyLocalService: JourneyLocalService,
  ) {}

  @Public()
  @Get('get-journey-by-id/:journeyId')
  @ApiOperation({
    summary: 'Chi tiết journey',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Journey',
    schema: generalSchema(BaseJourneyRes, 'object'),
  })
  async show(@Param('journeyId') journeyId: string) {
    return await this.journeyCoreService.getJourneyDetail(journeyId);
  }

  @Public()
  @Post('search')
  @ApiOperation({
    summary: 'Search journey',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Journey',
    schema: generalSchema(BaseJourneyRes, 'object'),
  })
  async search(@Body() payload: SearchJourneyDto) {
    return await this.journeyCoreService.postJourneySearch(payload);
  }

  @Public()
  @Post('journey-step')
  @ApiOperation({
    summary: 'Tạo journey step',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Journey',
    schema: generalSchema(BaseJourneyRes, 'object'),
  })
  async journeyStep(@Body() payload: CreatedJourneyStepDto) {
    return await this.journeyCoreService.postCreatedJourneyStep(payload);
  }

  /**
   * @TODO lấy journeyID bởi ticketCode
   */
  @Public()
  @Get('/get-journey-by-ticketCode')
  @ApiOperation({
    summary: 'Lấy journeyID',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Journey Id',
    schema: generalSchema(ResGetJourneyId, 'object'),
  })
  async getJourneyByTicketCode(@Query('ticketCode') ticketCode: string) {
    return this.journeyLocalService.getJourneyIdByTicketCode(ticketCode);
  }

  /**
   * @TODO lấy journeyID bởi orderCode
   */
  @Public()
  @Get('/get-journey-by-orderCode')
  @ApiOperation({
    summary: 'Lấy thông tin Journey bởi OrderCode',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin journey',
    schema: generalSchema(ResGetJourneyByOrderCode, 'object'),
  })
  async getJourneyByOrderCode(@Query('orderCode') orderCode: string) {
    return this.journeyLocalService.getJourneyByOrderCode(orderCode);
  }

  @Public()
  @Put('/update-many')
  @ApiOperation({
    summary: 'Update many journey',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Journey Id',
    schema: generalSchema(UpdateMultipleDetailJourneyRes, 'object'),
  })
  async updateDetailMultipleJourney(@Body() updateManyJourneyDto: UpdateManyJourneyDto) {
    const arrData = await this.journeyCoreService.updateDetailMultipleJourney(updateManyJourneyDto);
    await this.journeyCoreService.updateCustomerInfo({
      journeyId: updateManyJourneyDto?.details?.at(0)?.id,
      customerName: updateManyJourneyDto?.customerName || '',
      phone: updateManyJourneyDto?.phone || '',
      modifiedBy: updateManyJourneyDto?.modifiedBy || '',
    });
    return { items: arrData };
  }

  @Public()
  @Put('/update-many-family-package')
  @ApiOperation({
    summary: 'Update many journey của đơn gia đình',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Journey Id',
    schema: generalSchema(UpdateMultipleDetailJourneyRes, 'object'),
  })
  async updateDetailMultipleJourneyForFamilyPackage(@Body() updateManyJourneyDto: UpdateManyJourneyDto) {
    const arrData = await this.journeyCoreService.updateDetailMultipleJourney(updateManyJourneyDto);
    const payloadUpdate = updateManyJourneyDto?.details?.map((detail) => ({
      journeyId: detail?.id,
      customerName: updateManyJourneyDto?.customerName || '',
      phone: updateManyJourneyDto?.phone || '',
      modifiedBy: updateManyJourneyDto?.modifiedBy || '',
    }));
    await this.journeyCoreService.updateCustomerInfoForFamilyPackage(payloadUpdate);
    return { items: arrData };
  }

  /**
   * @description update gift delivery
   */
  @Public()
  @Put('/update-gift-delivery')
  @ApiOperation({
    summary: 'Update gift delivery',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trạng thái cập nhật trạng thái quà',
    schema: generalSchema(OrdersInfo, 'object'),
  })
  async updateGiftDelivery(@Body() body: UpdateGiftDeliveryDto) {
    return this.journeyLocalService.updateGiftDelivery(body);
  }
}
