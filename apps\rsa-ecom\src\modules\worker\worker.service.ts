import { Inject, Injectable, LoggerService } from '@nestjs/common';
import { storePaymentCodeDto } from './dto/store-payment-code.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { PaymentCodeStored } from './entities/payment-code-stored.entity';
import { Repository } from 'typeorm';
import { plainToInstance } from 'class-transformer';
import Redis from 'ioredis';
import { REDIS_PAYMENT_CODE_STORED, STATUS_STORED } from './constants';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { ScreenLevel2sService } from '../screen-level-2s/screen-level-2s.service';
import moment from 'moment';
import { ScheduleRequestsUtilService } from '../schedule-requests/services/schedule-requests-util.service';
import { EmployeeJobTitleCodes } from '@shared/constants/employee-title-code.constant';
import _ from 'lodash';
import { formatNotificationPayload, generateAddressId } from 'modules/modules/modules/notification/notification.utils';
import { NOTI_REGISTER_CHANEL_NAME } from 'modules/modules/modules/notification/notification.enum';
import { NotificationService as LocalNotificationService } from 'modules/modules/modules/notification';
import { PaymentNotSuccessTemplate } from 'modules/modules/modules/notification/templates';
import { getExpiredTime, parseDateTimeZone } from '@shared';
import { GetOneOrderLibResponse } from 'vac-nest-oms';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { JourneyService } from 'vac-nest-journey';

@Injectable()
export class WorkerService {
  constructor(
    @InjectRepository(PaymentCodeStored)
    private readonly paymentCodeStoredRepository: Repository<PaymentCodeStored>,
    @InjectRedis(process.env.REDIS_CONNECTION)
    private readonly redis: Redis,
    private readonly screenLevel2sService: ScreenLevel2sService,
    private readonly scheduleRequestsUtilService: ScheduleRequestsUtilService,
    private readonly localNotificationService: LocalNotificationService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    private readonly journeyService: JourneyService,
  ) {}

  async storePaymentCode(dataBody: storePaymentCodeDto) {
    try {
      const checkExist = await this.redis.get(REDIS_PAYMENT_CODE_STORED.replace('{key}', dataBody.paymentCode));
      if (checkExist) {
        await this.logger.log(
          {
            message: 'HttpException',
            fields: {
              info: `paymentCode: ${dataBody?.paymentCode} - already exist`,
              method: `POST`,
              url: `paymentCode: ${dataBody?.paymentCode} - already exist`,
              bodyReq: '{}',
              queryReq: '{}',
              paramsReq: '{}',
              headers: '{}',
              dataRes: checkExist,
            },
          },
          'HttpException',
          false,
        );
        return;
      }

      const current = new Date();
      const now = moment(current).utcOffset('+07:00');
      const journeyRes = await this.journeyService.getJourneyByOrderCode({ orderCode: dataBody.orderCode });
      const dataSave = await this.paymentCodeStoredRepository.save(
        plainToInstance(PaymentCodeStored, {
          ...dataBody,
          journeyId: journeyRes.id,
          createdAt: now.format(),
        }),
      );
      const pipeline = this.redis.pipeline();
      pipeline.zadd(
        REDIS_PAYMENT_CODE_STORED.replace('{key}', this.keyRedisStorePaymentCode(now)),
        1,
        dataBody.paymentCode,
      );
      pipeline.expire(
        REDIS_PAYMENT_CODE_STORED.replace('{key}', this.keyRedisStorePaymentCode(now)),
        getExpiredTime('min', 30),
      );
      pipeline.set(
        REDIS_PAYMENT_CODE_STORED.replace('{key}', dataBody.paymentCode),
        JSON.stringify({ ...dataSave, key: this.keyRedisStorePaymentCode(now) }),
        'EX',
        getExpiredTime('min', 30),
      );
      await pipeline.exec();

      await this.logger.log(
        {
          message: 'HttpException',
          fields: {
            info: `paymentCode: ${dataSave?.paymentCode} - stored`,
            method: `POST`,
            url: `paymentCode: ${dataSave?.paymentCode} - stored`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify({
              data: dataSave || {},
              key: this.keyRedisStorePaymentCode(now),
            }),
          },
        },
        'HttpException',
        false,
      );
      return dataSave;
    } catch (error) {
      let errorContent = '{}';
      try {
        errorContent = JSON.stringify(error);
      } catch (e) {}
      await this.logger.log(
        {
          message: 'HttpException',
          fields: {
            info: `orderCode: ${dataBody?.orderCode} - cannot store payment code`,
            method: `PUT`,
            url: `orderCode: ${dataBody?.orderCode} - cannot store payment code`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: errorContent,
          },
        },
        'HttpException',
        false,
      );
    }
  }

  async removePaymentCodeStored(order: GetOneOrderLibResponse) {
    const resDataPaymentStored = await this.redis.get(
      REDIS_PAYMENT_CODE_STORED.replace('{key}', order.paymentRequestCode),
    );
    await this.logger.log(
      {
        message: 'HttpException',
        fields: {
          info: `remove from list`,
          method: `DELETE`,
          url: `remove from list`,
          bodyReq: '{}',
          queryReq: '{}',
          paramsReq: '{}',
          headers: '{}',
          dataRes:
            resDataPaymentStored ||
            JSON.stringify({
              paymentCode: order.paymentRequestCode,
              key: this.keyRedisStorePaymentCode(order.createdDate),
            }),
        },
      },
      'HttpException',
      false,
    );
    const dataPaymentStored = JSON.parse(resDataPaymentStored || '{}');
    if (!dataPaymentStored?.key) return;

    await this.redis.zrem(REDIS_PAYMENT_CODE_STORED.replace('{key}', dataPaymentStored?.key), order.paymentRequestCode);
    await this.paymentCodeStoredRepository.update(
      { paymentCode: order.paymentRequestCode },
      {
        status: STATUS_STORED.SUCCESS,
      },
    );
  }

  async jobPaymentCode(keyRunJobManually: string = null) {
    const now = new Date();
    // Lấy thời gian của 15 phút trước
    const fifteenMinutesBefore = moment(now).utcOffset('+07:00').subtract(15, 'minutes');
    const keyRunJob = keyRunJobManually ? keyRunJobManually : this.keyRedisStorePaymentCode(fifteenMinutesBefore);

    const resPaymentCodeStored = await this.redis.zrange(REDIS_PAYMENT_CODE_STORED.replace('{key}', keyRunJob), 0, -1);

    await this.logger.log(
      {
        message: 'HttpException',
        fields: {
          info: `run job`,
          method: `GET`,
          url: `run job`,
          bodyReq: '{}',
          queryReq: '{}',
          paramsReq: '{}',
          headers: '{}',
          dataRes: JSON.stringify({
            data: resPaymentCodeStored || [],
            key: keyRunJob,
          }),
        },
      },
      'HttpException',
      false,
    );

    if (!resPaymentCodeStored || !resPaymentCodeStored.length) {
      return true;
    }

    const pipeline = this.redis.pipeline();
    for (const paymentCode of resPaymentCodeStored) {
      pipeline.get(REDIS_PAYMENT_CODE_STORED.replace('{key}', paymentCode));
    }
    const resPaymentCodesPipeline = await pipeline.exec();
    const resPaymentCodesData = resPaymentCodesPipeline.map((item) => item[1]).filter((item) => item);

    const paymentCodeMap = {};
    const insideIds = [];
    for (const resPaymentCodeData of resPaymentCodesData) {
      if (typeof resPaymentCodeData === 'string') {
        const paymentCodeData: storePaymentCodeDto = JSON.parse(resPaymentCodeData);
        paymentCodeMap[paymentCodeData.paymentCode] = paymentCodeData;
        insideIds.push(paymentCodeData.insideId);
      }
    }
    const groupsAssignedHash = await this.screenLevel2sService.findAllGroupByEmployees(insideIds);
    const allGroup = await this.screenLevel2sService.getAllGroup();

    const requestLevel2DTO = [];
    for (const resPaymentCodeData of resPaymentCodesData) {
      if (typeof resPaymentCodeData === 'string') {
        const paymentCodeData: PaymentCodeStored = JSON.parse(resPaymentCodeData);
        requestLevel2DTO.push({
          name: paymentCodeData.name,
          solution: '',
          phone: paymentCodeData.phone,
          description: `Đơn hàng thanh toán thất bại: ${paymentCodeData.orderCode}`,
          cityCode: paymentCodeData.cityCode,
          districtCode: paymentCodeData.districtCode,
          wardCode: paymentCodeData.wardCode,
          address: paymentCodeData.address,
          groupAssigned: groupsAssignedHash[paymentCodeData?.insideId]?.id || allGroup?.[0]?.id || '',
          isNoGroup: groupsAssignedHash[paymentCodeData?.insideId]?.id ? false : true,
          employeeCode: paymentCodeData?.insideId,
          orderCode: paymentCodeData.orderCode,
          metaData: {
            journeyId: paymentCodeData.journeyId,
            orderCode: paymentCodeData.orderCode,
          },
          files: null,
        });
      }
    }
    const screenLevel2sReturn = await this.screenLevel2sService.createMany(requestLevel2DTO);

    const sendNotiList = [];
    for (const screenLevel2Return of screenLevel2sReturn) {
      const requestLevel2DTOItem = requestLevel2DTO.find((item) => item.description === screenLevel2Return.description);
      sendNotiList.push({
        ...screenLevel2Return,
        requestLevel2Id: screenLevel2Return?.uuid,
        orderCode: requestLevel2DTOItem.orderCode,
      });
    }

    try {
      this.logger.log(
        {
          message: 'HttpException',
          fields: {
            info: `Job payment remind`,
            method: `GET`,
            url: `Data request lv2 job payment remind`,
            bodyReq: JSON.stringify({
              group: allGroup,
              data: requestLevel2DTO,
            }),
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify(sendNotiList),
          },
        },
        'HttpException',
        false,
      );
    } catch (error) {}

    await this.sendNoti(sendNotiList);
    return true;
  }

  async sendNoti(requestLv2s: any[]) {
    if (!requestLv2s || requestLv2s?.length <= 0) return;
    const notificationAddresses = [];
    const employeesSendTo = await this.scheduleRequestsUtilService.getEmployeeListSalesWithInsideId(
      [...EmployeeJobTitleCodes.Manager, ...EmployeeJobTitleCodes.EcomSaleLeader],
      ..._.uniq(requestLv2s?.map((e) => e.createdBy)),
    );

    notificationAddresses.push(
      ...employeesSendTo.map((item) => generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, item.employeeCode)),
    );

    await this.localNotificationService.sendNotifications(
      requestLv2s.map((e) => {
        return formatNotificationPayload({
          To: _.uniq(notificationAddresses),
          Cc: [],
          Bcc: [],
          template: PaymentNotSuccessTemplate,
          replaceParams: {
            content: {
              orderCode: e.orderCode,
              timezone: parseDateTimeZone(new Date(), '+07:00', 'hh:mm dd/mm/yyyy'),
            },
            messageLink: { uuid: (e as any).requestLevel2Id || '' },
            extraProperties: {
              code: e.orderCode,
            },
          },
        });
      }),
      true,
    );

    return true;
  }

  private keyRedisStorePaymentCode(inputDate) {
    const date = moment(inputDate);
    return `${date.year()}_${date.month() + 1}_${date.date()}_${date.hours()}_${date.minutes()}`;
  }
}
