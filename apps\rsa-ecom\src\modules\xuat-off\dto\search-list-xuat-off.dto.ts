import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsOptional, IsString } from 'class-validator';
import { XuatOffType } from '../constants';
import { Transform, Type } from 'class-transformer';

export class SearchXuatOffDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  xoCode?: string;

  @ApiPropertyOptional({ type: Number, isArray: true })
  @IsOptional()
  status?: number[];

  @ApiPropertyOptional({ type: String, isArray: true })
  @IsString({ each: true })
  @IsOptional()
  reasonCode: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  toDate?: string;

  @ApiPropertyOptional({ type: String, isArray: true })
  @IsEnum(XuatOffType, { each: true })
  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return [];
    if (Array.isArray(value)) return value.map((v) => Number(v));
    return [Number(value)];
  })
  type?: XuatOffType[];

  @ApiPropertyOptional({ type: [String] })
  @IsOptional()
  @IsString({ each: true })
  createdBy?: string[] | string;
}
