import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import { concurrentPromise, OrderChannels, translateVietnameseAccents } from '@shared';
import { Request } from 'express';
import moment from 'moment';
import { InsideService } from 'vac-nest-inside';
import { NotificationService, ShortLinkService } from 'vac-nest-notification';
import { ListVaccineConsultant } from 'vac-nest-osr';
import { VaccineCategorizationRes } from 'vac-nest-osr/dist/dto/vaccine-categorization.dto';
import { SurveyConsultantResult } from 'vac-nest-rsa-integration';
import { VacStorefrontService } from 'vac-nest-storefront';
import { TypeTemplateSMS } from './../enum/tenant-code.enum';
import { plainToInstance } from 'class-transformer';
import {
  ContentFailOverCase1Dto,
  ContentFailOverCase2Dto,
  ContentFailOverCase3Dto,
  ContentFailOverCase4Dto,
  ContentFailOverDto,
  ExtraPropertiesCase1Dto,
  ExtraPropertiesCase2Dto,
  ExtraPropertiesCase3Dto,
  ExtraPropertiesCase4Dto,
  ExtraPropertiesDto,
} from '../dto/survey-ocr.dto';

@Injectable()
export class SMSSurveysService {
  constructor(
    private readonly shortLinkService: ShortLinkService,
    private readonly configService: ConfigService,
    private readonly storeFontService: VacStorefrontService,
    private readonly notificationService: NotificationService,
    private readonly insideService: InsideService,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  async sendSMSSurvey(payload: {
    modifiedBy?: string;
    shopCode?: string;
    vaccineCategorizations?: VaccineCategorizationRes[];
    consultantResult?: number;
    typeSMS?: TypeTemplateSMS;
    listVaccineConsultant?: ListVaccineConsultant[];
    phoneCustomerSendSMS?: string;
    customerName?: string;
  }) {
    const {
      shopCode,
      vaccineCategorizations = [],
      consultantResult = 0,
      listVaccineConsultant = [],
      modifiedBy,
      phoneCustomerSendSMS,
      customerName,
    } = payload;
    // const isEcom = OrderChannels.RSA_ECOM.includes((this.req.headers['order-channel'] as string) || '');
    const isRsa = OrderChannels.RSA.includes((this.req.headers['order-channel'] as string) || '');

    let templateSMSIdDefault = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_1A_2');
    // Template Tắt máy/Không nghe máy
    const { isTemplate1, isTemplate2 } = this.getTemplateName(consultantResult);

    const rsVaccineCategorizations = await this.genShortLink(vaccineCategorizations);
    const { shopNameDisplay, rsShop } = await this.getInfoShop(shopCode);
    const employeeInfo = await this.insideService.getEmployeeByCode({
      listEmployeeCode: [modifiedBy],
    });

    let errorMessage = '';
    if (isRsa) {
      // 2 vaccine
      if (listVaccineConsultant?.length === 2) {
        if (isTemplate1) {
          templateSMSIdDefault = this.configService.get('TEMPLATE_SEND_SMS_FOR_VACCINE_CARE_1_2');
        }
        if (isTemplate2) {
          templateSMSIdDefault = this.configService.get('TEMPLATE_SEND_SMS_FOR_VACCINE_CARE_2_2');
        }
      }
      // 1 vaccine
      if (listVaccineConsultant?.length === 1) {
        if (isTemplate1) {
          templateSMSIdDefault = this.configService.get('TEMPLATE_SEND_SMS_FOR_VACCINE_CARE_1_1');
        }
        if (isTemplate2) {
          templateSMSIdDefault = this.configService.get('TEMPLATE_SEND_SMS_FOR_VACCINE_CARE_2_1');
        }
      }
    }

    const fromSystem = isRsa ? 'be-rsa-v2' : 'be-rsa-ecom';
    // send sms 2 vaccine
    if (listVaccineConsultant?.length === 2) {
      errorMessage = await this.sendSMSTemplateTwoVaccine({
        fromSystem,
        templateId: templateSMSIdDefault,
        phoneSendSMS: phoneCustomerSendSMS,
        customerName,
        employeeName: employeeInfo?.at(0)?.employeeName,
        phoneNumberEmployee: employeeInfo?.at(0)?.phoneNumber,
        vaccineCategorizations: rsVaccineCategorizations,
        shopNameDisplay,
        consultantResult,
      });
    }

    // send sms 1 vaccine
    if (listVaccineConsultant?.length === 1) {
      errorMessage = await this.sendSMSTemplateOneVaccine({
        fromSystem,
        templateId: templateSMSIdDefault,
        phoneSendSMS: phoneCustomerSendSMS,
        customerName,
        employeeName: employeeInfo?.at(0)?.employeeName,
        phoneNumberEmployee: employeeInfo?.at(0)?.phoneNumber,
        vaccineCategorizations: rsVaccineCategorizations,
        shopNameDisplay,
        consultantResult,
      });
    }

    return {
      errorMessage,
    };
  }

  /**
   * @description template sms send 1 vaccine
   * @param payload
   */
  private async sendSMSTemplateOneVaccine(payload?: {
    fromSystem?: string;
    templateId?: string;
    phoneSendSMS?: string;
    customerName?: string;
    employeeName?: string;
    phoneNumberEmployee?: string;
    vaccineCategorizations?: VaccineCategorizationRes[];
    shopNameDisplay?: string;
    consultantResult?: number;
  }) {
    let errorMessage = '';
    try {
      const {
        fromSystem,
        templateId,
        phoneSendSMS,
        employeeName,
        phoneNumberEmployee,
        vaccineCategorizations,
        customerName,
        shopNameDisplay,
        consultantResult,
      } = payload;

      const extraProperties = {
        customerName: customerName || '',
        customerId: phoneSendSMS ? `*******${phoneSendSMS?.slice(-3)}` : '',
        advisorName: employeeName || '',
        firstDiseaseName: vaccineCategorizations?.at(0)?.name || '',
        firstToken: vaccineCategorizations?.at(0)?.['token'] || '',
      };
      const { isTemplate1 } = this.getTemplateName(consultantResult);

      if (isTemplate1) {
        delete extraProperties.advisorName;
      }

      await this.notificationService.sendNotification({
        FromSys: fromSystem,
        Sender: fromSystem,
        Messages: [
          {
            TemplateId: templateId,
            To: [phoneSendSMS],
            Param: {
              Title: {},
              Content: {},
              ContentFailOver: {
                advisorName: translateVietnameseAccents(employeeName) || '',
                firstDiseaseName: translateVietnameseAccents(vaccineCategorizations?.at(0)?.name) || '',
                firstDiseaseLink: vaccineCategorizations?.at(0)?.link || '',
                advisorPhoneNumber: phoneNumberEmployee || '',
              },
              ExtraProperties: extraProperties,
            },
          },
        ],
      });
    } catch (error) {
      errorMessage = error.message;
    }

    return errorMessage;
  }

  /**
   * @description template sms send 2 vaccine
   * @param payload
   */
  private async sendSMSTemplateTwoVaccine(payload?: {
    fromSystem?: string;
    templateId?: string;
    phoneSendSMS?: string;
    customerName?: string;
    employeeName?: string;
    phoneNumberEmployee?: string;
    vaccineCategorizations?: VaccineCategorizationRes[];
    shopNameDisplay?: string;
    consultantResult?: number;
  }) {
    let errorMessage = '';
    try {
      const {
        fromSystem,
        templateId,
        phoneSendSMS,
        employeeName,
        phoneNumberEmployee,
        vaccineCategorizations,
        customerName,
        shopNameDisplay,
        consultantResult,
      } = payload;
      const extraProperties = {
        customerName: customerName || '',
        customerId: phoneSendSMS ? `*******${phoneSendSMS?.slice(-3)}` : '',
        firstDiseaseName: vaccineCategorizations?.at(0)?.name || '',
        secondDiseaseName: vaccineCategorizations?.at(1)?.name || '',
        advisorName: employeeName || '',
        shopAddress: shopNameDisplay || '',
        firstToken: vaccineCategorizations?.at(0)?.['token'] || '',
        secondToken: vaccineCategorizations?.at(1)?.['token'] || '',
      };

      const { isTemplate1 } = this.getTemplateName(consultantResult);
      if (isTemplate1) {
        delete extraProperties.advisorName;
      }

      await this.notificationService.sendNotification({
        FromSys: fromSystem,
        Sender: fromSystem,
        Messages: [
          {
            TemplateId: templateId,
            To: [phoneSendSMS],
            Param: {
              Title: {},
              Content: {},
              ContentFailOver: {
                advisorName: translateVietnameseAccents(employeeName) || '',
                firstDiseaseName: translateVietnameseAccents(vaccineCategorizations?.at(0)?.name) || '',
                secondDiseaseName: translateVietnameseAccents(vaccineCategorizations?.at(1)?.name) || '',
                firstDiseaseLink: vaccineCategorizations?.at(0)?.link || '',
                secondDiseaseLink: vaccineCategorizations?.at(1)?.link || '',
                advisorPhoneNumber: phoneNumberEmployee || '',
              },
              ExtraProperties: extraProperties,
            },
          },
        ],
      });
    } catch (error) {
      errorMessage = error.message;
    }
    return errorMessage;
  }

  /**
   * @description gen short link
   * @param vaccineCategorizations
   * @returns
   */
  private async genShortLink(vaccineCategorizations?: VaccineCategorizationRes[]) {
    if (!vaccineCategorizations?.length) return vaccineCategorizations;

    const arrShortLinkPromise = [];
    vaccineCategorizations?.forEach((vaccineCategorization) => {
      if (vaccineCategorization?.link) {
        const shortLinkPromise = this.shortLinkService.createShortLink(
          {
            originalUrl: vaccineCategorization?.link,
            expires: moment().utcOffset('+07:00').add(30, 'days').format(),
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: this.configService.get('SMS_AUTHEN_BASIC'),
            },
          },
        );
        arrShortLinkPromise.push(shortLinkPromise);
      }
    });

    if (!arrShortLinkPromise?.length) return arrShortLinkPromise;
    const arrShortLinkRes = (await concurrentPromise(...arrShortLinkPromise)) || [];

    if (!arrShortLinkRes?.length) return vaccineCategorizations;

    vaccineCategorizations?.forEach((vaccineCategorization, index) => {
      vaccineCategorization.link = arrShortLinkRes?.[index]?.shortLink;
      vaccineCategorization['token'] = arrShortLinkRes?.[index]?.token;
    });

    return vaccineCategorizations;
  }

  /**
   * @description Lấy thông tin nhân viên thực hiện
   * @param shopCode
   * @returns
   */
  private async getInfoShop(shopCode: string) {
    const rsShop = await this.storeFontService.getShop(shopCode);
    const shopNameDisplay = rsShop?.shopNameDisplay?.split('(Mở cửa')?.at(0);
    return {
      shopNameDisplay,
      rsShop,
    };
  }

  private getTemplateName(consultantResult: number) {
    return {
      isTemplate1: [SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE, SurveyConsultantResult.CANT_CONTACT].includes(
        consultantResult,
      ),
      isTemplate2: [SurveyConsultantResult.REFERENCE_1].includes(consultantResult),
    };
  }

  // OCR zone
  private chooseCaseOfTemplateOcr(consultantResult: number, amountOfVaccine: number) {
    const listAllCase = [
      {
        consultantResult: [SurveyConsultantResult.CANT_CONTACT, SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE],
        amountOfVaccine: 1,
        templateOtp: process.env.TEMPLATE_SEND_SMS_UPDATE_SURVEY_OCR_CASE_1,
        case: 1,
      },
      {
        consultantResult: [SurveyConsultantResult.CANT_CONTACT, SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE],
        amountOfVaccine: 2,
        templateOtp: process.env.TEMPLATE_SEND_SMS_UPDATE_SURVEY_OCR_CASE_2,
        case: 2,
      },
      {
        consultantResult: [SurveyConsultantResult.REFERENCE_1],
        amountOfVaccine: 1,
        templateOtp: process.env.TEMPLATE_SEND_SMS_UPDATE_SURVEY_OCR_CASE_3,
        case: 3,
      },
      {
        consultantResult: [SurveyConsultantResult.REFERENCE_1],
        amountOfVaccine: 2,
        templateOtp: process.env.TEMPLATE_SEND_SMS_UPDATE_SURVEY_OCR_CASE_4,
        case: 4,
      },
    ];
    return listAllCase?.find(
      (item) => item?.consultantResult?.includes(consultantResult) && amountOfVaccine === item?.amountOfVaccine,
    );
  }

  async sendSmsSurveyForOcr(body: {
    customerName?: string;
    phoneCustomerSendSMS?: string;
    vaccineCategorizations?: VaccineCategorizationRes[];
    consultantResult?: number;
    modifiedByName?: string;
  }) {
    const { customerName, phoneCustomerSendSMS, vaccineCategorizations, consultantResult, modifiedByName } = body;
    const dataChooseCaseTemplate = this.chooseCaseOfTemplateOcr(consultantResult, vaccineCategorizations?.length);
    if (!dataChooseCaseTemplate) {
      return;
    }
    const vaccineCategorizationsAddToken = await this.genShortLink(vaccineCategorizations);
    const contentFailOverFull = {
      customerName,
      advisorName: modifiedByName || '',
      firstDiseaseName: vaccineCategorizations?.[0]?.name || '',
      firstDiseaseLink: vaccineCategorizations?.[0]?.link || '',
      secondDiseaseName: vaccineCategorizations?.[1]?.name || '',
      secondDiseaseLink: vaccineCategorizations?.[1]?.link || '',
    };

    const extraPropertiesFull = {
      customerName,
      customerId: phoneCustomerSendSMS ? `*******${phoneCustomerSendSMS?.slice(-3)}` : '',
      advisorName: modifiedByName || '',
      firstDiseaseName: vaccineCategorizations?.[0]?.name || '',
      secondDiseaseName: vaccineCategorizations?.[1]?.name || '',
      firstToken: vaccineCategorizationsAddToken?.[0]?.token || '',
      secondToken: vaccineCategorizationsAddToken?.[1]?.token || '',
    };
    const caseOfTemplate = dataChooseCaseTemplate?.case;
    let contentFailOver;
    let extraProperties;
    switch (caseOfTemplate) {
      case 1:
        contentFailOver = plainToInstance(ContentFailOverCase1Dto, contentFailOverFull, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });
        extraProperties = plainToInstance(ExtraPropertiesCase1Dto, extraPropertiesFull, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });
        break;
      case 2:
        contentFailOver = plainToInstance(ContentFailOverCase2Dto, contentFailOverFull, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });
        extraProperties = plainToInstance(ExtraPropertiesCase2Dto, extraPropertiesFull, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });
        break;
      case 3:
        contentFailOver = plainToInstance(ContentFailOverCase3Dto, contentFailOverFull, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });
        extraProperties = plainToInstance(ExtraPropertiesCase3Dto, extraPropertiesFull, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });
        break;
      case 4:
        contentFailOver = plainToInstance(ContentFailOverCase4Dto, contentFailOverFull, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });
        extraProperties = plainToInstance(ExtraPropertiesCase4Dto, extraPropertiesFull, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });
        break;
      default:
        break;
    }

    const bodySendOtp = {
      TemplateId: dataChooseCaseTemplate?.templateOtp,
      To: [`${phoneCustomerSendSMS}`],
      Bcc: [''],
      Cc: [''],
      Param: {
        Title: {},
        Content: {},
        ContentFailOver: contentFailOver,
        ExtraProperties: extraProperties,
      },
    };

    await this.notificationService.sendNotification({
      FromSys: 'be-rsa-ecom',
      Sender: 'be-rsa-ecom',
      Messages: [bodySendOtp],
    });
  }
  // End ocr zone
}
