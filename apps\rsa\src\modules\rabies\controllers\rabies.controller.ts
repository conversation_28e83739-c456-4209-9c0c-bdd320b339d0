import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { GetRabiesDeclarationRes, PostRabiesDeclarationDto, UpdateRabiesByTicketDto } from 'vac-nest-examination';
import {
  CommonReportDto,
  ItemUsedVaccineReportRes,
  RabiesVaccineMonitoringReportRes,
  RabiesVaccineReportRes,
  UsedVaccineReportDto,
  UsedVaccineReportRes,
} from 'vac-nest-report';
import { ClassErrorResponse, ClassResponse, Public, generalSchema } from '../../../../../../libs/shared/src';
import { CreateRabiesRes, GetHistoryRabiesRes } from '../dto';
import { RabiesService } from '../services/rabies.service';

@Controller({ path: 'rabies', version: '1' })
@ApiTags('Rabies')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(
  ClassResponse,
  GetHistoryRabiesRes,
  CreateRabiesRes,
  UsedVaccineReportRes,
  RabiesVaccineReportRes,
  RabiesVaccineMonitoringReportRes,
  GetRabiesDeclarationRes,
)
export class RabiesController {
  constructor(private readonly rabiesService: RabiesService) {}

  /**
   * @TODO api check lịch sử khai báo tiêm dại theo lcvId
   */
  @Public()
  @Get('history-rabies')
  @ApiOperation({
    summary: 'check lịch sử khai báo tiêm dại theo lcvId',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin lịch sử khai báo tiêm dại theo lcvId',
    schema: generalSchema(GetHistoryRabiesRes, 'object'),
  })
  getHistoryRabies(@Query('lcvId') lcvId: string) {
    return this.rabiesService.getHistoryRabies(lcvId);
  }

  /**
   * @TODO lưu khai báo tiêm dại
   */
  @Public()
  @Post('create-rabies')
  @ApiOperation({
    summary: 'Lấy khai báo tiêm dại',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin tiêm dại',
    schema: generalSchema(CreateRabiesRes, 'object'),
  })
  createRabies(@Body() createRabiesDto: PostRabiesDeclarationDto) {
    return this.rabiesService.createRabies(createRabiesDto);
  }

  /**
   * @TODO khai báo sử dụng dại
   */
  @Public()
  @Post('report/used-vaccine')
  @ApiOperation({
    summary: 'Báo cáo sử dụng dại',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin báo cáo sử dụng dại',
    schema: generalSchema(ItemUsedVaccineReportRes, 'array'),
  })
  reportUsedVaccine(@Body() body: UsedVaccineReportDto) {
    return this.rabiesService.getUsedVaccine(body);
  }

  /**
   * @TODO thông tin dại tổng hợp
   */
  @Public()
  @Post('report/rabies-vaccine')
  @ApiOperation({
    summary: 'Báo cáo dại tổng hợp',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin dại tổng hợp',
    schema: generalSchema(RabiesVaccineReportRes, 'object'),
  })
  reportRabiesVaccine(@Body() body: CommonReportDto) {
    return this.rabiesService.getRabiesVaccine(body);
  }

  /**
   * @TODO thông tin dại tổng hợp
   */
  @Public()
  @Post('report/rabies-vaccine-monitoring')
  @ApiOperation({
    summary: 'Báo cáo dại theo khách hàng tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin báo cáo dại theo khách hàng tiêm',
    schema: generalSchema(RabiesVaccineMonitoringReportRes, 'object'),
  })
  reportRabiesMonitoringVaccine(@Body() body: CommonReportDto) {
    return this.rabiesService.getRabiesMonitoringVaccine(body);
  }

  /**
   * @TODO lấy thông tin báo cáo dại theo ticket
   */
  @Public()
  @Get('/get-rabies-by-ticket/:ticketCode')
  @ApiOperation({
    summary: 'Thông tin báo cáo dại theo ticket',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin báo cáo dại theo ticket',
    schema: generalSchema(GetRabiesDeclarationRes, 'object'),
  })
  getRabiesByTicket(@Param('ticketCode') ticketCode: string) {
    return this.rabiesService.getRabiesByTicketCode(ticketCode);
  }

  /**
   * @TODO cập nhật rabies by ticket
   */
  @Public()
  @Put('/update-rabies-details/:id')
  @ApiOperation({
    summary: 'Thông tin cập nhật báo cáo dại theo ticket',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin cập nhật báo cáo dại theo ticket',
    schema: generalSchema(GetRabiesDeclarationRes, 'object'),
  })
  updateRabiesByTicket(@Param('id') id: string, @Body() body: UpdateRabiesByTicketDto) {
    return this.rabiesService.updateRabiesByTicket(id, body);
  }
}
