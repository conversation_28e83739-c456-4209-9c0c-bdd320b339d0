import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from '@shared';
import { ScheduleD7Service } from '../services/schedule-d7.service';
import { AssignSurveysDto, SearchSurveyCustomerD7ByQueryDto, SurveyCustomerD7 } from 'vac-nest-osr';
import {
  AutoAssignScheduleD7Dto,
  ConsultantResultTypeDto,
  HandAssignScheduleD7Dto,
  UpdateSurveyCustomerD7,
} from '../dto/schedule-d7.dto';
import { NotifyOsrDto } from '../dto/survey-ocr.dto';

@Controller({ path: 'schedule-d7', version: '1' })
@ApiTags('schedule d7')
export class ScheduleD7Controller {
  constructor(private readonly scheduleD7Service: ScheduleD7Service) {}

  @Post('es/get-by-query')
  @Public()
  @ApiOperation({
    summary: 'Filter danh sách schedule d7',
  })
  @HttpCode(HttpStatus.OK)
  async getListScheduleD7(@Body() body: SearchSurveyCustomerD7ByQueryDto) {
    return this.scheduleD7Service.getListScheduleD7(body);
  }

  @Get('es/get-by-id/:id')
  @Public()
  @ApiOperation({
    summary: 'Chi tiết schedule d7',
  })
  @HttpCode(HttpStatus.OK)
  async getDetailScheduleD7(@Param('id') id: string) {
    return this.scheduleD7Service.getDetailScheduleD7(id);
  }

  @Put('')
  @Public()
  @ApiOperation({
    summary: 'Update schedule d7',
  })
  @HttpCode(HttpStatus.OK)
  async updateScheduleD7(@Body() body: UpdateSurveyCustomerD7) {
    return this.scheduleD7Service.updateScheduleD7(body);
  }

  @Post('assign-hand')
  @Public()
  @ApiOperation({
    summary: 'Phân công tay schedule d7',
  })
  @HttpCode(HttpStatus.OK)
  async updateScheduleD7Assign(@Body() body: HandAssignScheduleD7Dto) {
    return this.scheduleD7Service.updateScheduleD7Assign(body);
  }

  @Get('consultant-result-type')
  @Public()
  @ApiOperation({
    summary: 'get consultant result ocr by survey id and source',
  })
  @HttpCode(HttpStatus.OK)
  async getKQTVBySurveyIdAndSource(@Query() query: ConsultantResultTypeDto) {
    return await this.scheduleD7Service.getKQTVByIdAndSource(query?.surveyId);
  }

  @Post('assign-auto')
  @Public()
  @ApiOperation({
    summary: 'Phân công tự động schedule d7',
  })
  @HttpCode(HttpStatus.OK)
  async updateAutoScheduleD7Assign(@Body() body: AutoAssignScheduleD7Dto) {
    return this.scheduleD7Service.updateAutoScheduleD7Assign(body);
  }

  @Post('notify-recall')
  @Public()
  @ApiOperation({
    summary: 'Thông báo cho nhân viên. TypeDefault:1',
  })
  @HttpCode(HttpStatus.OK)
  async notifyOsr(@Body() body: NotifyOsrDto) {
    return this.scheduleD7Service.notifyOsr(body);
  }
}
