import { PickType } from '@nestjs/swagger';
import { CancelQRMbankDto, CreatedQRMBankDto } from 'vac-nest-payment-gateway';

export class PayloadCreatedQRMBankDto extends PickType(CreatedQRMBankDto, [
  'paymentCode',
  'amount',
  'orderCode',
  'shopCode',
  'typePaymentRequest',
  'referenceId',
]) {}

export class PayloadCancelQRMBankDto extends PickType(CancelQRMbankDto, ['paymentCode', 'paymentRequestCode']) {}
