import { Module } from '@nestjs/common';
import { CartAppModule } from 'vac-nest-cart-app';
import { FamilyModule } from 'vac-nest-family';
import { JourneyModule } from 'vac-nest-journey';
import { PIMAppModule } from 'vac-nest-pim-app';
import { RegimenModule } from 'vac-nest-regimen';
import { CustomersModule } from '../customers/customers.module';
import { ConsultantsController } from './controllers/consultants.controller';
import { ConsultantsService } from './services/consultants.service';
import { RegimensModule } from '../regimens/regimens.module';
import { OrderRuleEngineModule } from 'vac-nest-order-rule-engine';
import { VacOrderInjectionModule } from 'vac-nest-order-injection';
import { ReturnHomeModule } from 'vac-nest-return-home';
import { DSMSModule } from 'vac-nest-dsms';
import { ScheduleCoreModule } from 'vac-nest-schedule';
import { ConsultantsFamilyPackageController } from './controllers/consultants-family-package.controller';
import { ConsultantsFamilyPackageService } from './services/consultants-family-package.service';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { InsideModule } from 'vac-nest-inside';
import { OrdersModule } from '../orders/orders.module';
import { OsrModule } from 'vac-nest-osr';

@Module({
  imports: [
    CartAppModule,
    JourneyModule,
    PIMAppModule,
    RegimenModule,
    FamilyModule,
    CustomersModule,
    RegimensModule,
    OrderRuleEngineModule,
    VacOrderInjectionModule,
    ReturnHomeModule,
    DSMSModule,
    ScheduleCoreModule,
    ExaminationCoreModule,
    InsideModule,
    OrdersModule,
    OsrModule,
  ],
  providers: [ConsultantsService, ConsultantsFamilyPackageService],
  controllers: [ConsultantsController, ConsultantsFamilyPackageController],
  exports: [ConsultantsService],
})
export class ConsultantsModule {}
