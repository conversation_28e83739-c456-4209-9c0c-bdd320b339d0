import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AbstractRepository } from '@shared/database/typeorm/abstract.repository';
import { EntityManager, Repository } from 'typeorm';
import { XuatOffDetailEntity } from '../entities';

@Injectable()
export class XuatOffDetailRepository extends AbstractRepository<XuatOffDetailEntity> {
  protected readonly logger = new Logger(XuatOffDetailRepository.name);
  constructor(
    @InjectRepository(XuatOffDetailEntity)
    private readonly repository: Repository<XuatOffDetailEntity>,
    private readonly entityManager: EntityManager,
  ) {
    super(repository, entityManager);
  }
}
