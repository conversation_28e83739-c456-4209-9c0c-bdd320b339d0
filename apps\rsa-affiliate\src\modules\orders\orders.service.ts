import { Request } from 'express';
import { JSONPath } from 'jsonpath-plus';
import _ from 'lodash';
import { ContinueBuyingRes, UpdateStatusOrderPartialPaymentDto } from 'modules/modules/modules/orders/dto';
import { GetDetailsByTicketCodeDTO } from 'modules/modules/modules/orders/dto/get-details-by-ticket-code.dto';
import { PushsOrderPayloadDto } from 'modules/modules/modules/orders/dto/push-order.dto';
import { OrdersService as OrdersRsaService } from 'modules/modules/modules/orders/services/orders.service';
import { OrderUtilsService as OrderUtilsRsaService } from 'modules/modules/modules/orders/services/order-utils.services';

import { calculateTimeDifference } from 'vac-commons';
import { FamilyService } from 'vac-nest-family';
import {
  DetailAttachment,
  EcomDisplay,
  EmployeeStep,
  OMSService,
  OrderStatus,
  UpdateOrderLibResponse,
} from 'vac-nest-oms';
import { OsrService } from 'vac-nest-osr';
import { PaymentGatewayService, PaymentOnlineStatus } from 'vac-nest-payment-gateway';
import { RegimenService } from 'vac-nest-regimen';
import { RSAIntegrationService } from 'vac-nest-rsa-integration';

import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import {
  CASHBACK_KEY,
  concurrentPromise,
  ErrorCode,
  getExpiredTime,
  IError,
  OrderChannels,
  RedisService,
  SystemException,
} from '@shared';

import { CreateSurveyAfterPushingDto } from '../dto/create-survey-after-push-order.dto';
import { CartAppService } from 'vac-nest-cart-app';

@Injectable()
export class OrdersService {
  constructor(
    private readonly ordersRsaService: OrdersRsaService,
    private readonly orderUtilsRsaService: OrderUtilsRsaService,
    private readonly omsService: OMSService,
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly rsaIntegration: RSAIntegrationService,
    private readonly regimenService: RegimenService,
    private readonly familyService: FamilyService,
    private readonly redisService: RedisService,
    private readonly osrService: OsrService,
    private readonly cartAppService: CartAppService,
  ) {}

  /**
   * @TODO Thực hiện việc tiếp tục mua hàng
   */
  async continueBuying(orderCode: string): Promise<ContinueBuyingRes> {
    const orderOMS = await this.omsService.getOneOrder(orderCode);
    if (!orderOMS || !OrderChannels.RSA_AFFILIATE.includes(orderOMS.orderChanel)) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_ORDER_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND),
          validationErrors: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const dataContinueBuying = await this.ordersRsaService.continueBuying(orderCode, true);
    return dataContinueBuying;
  }

  async pushOrder(body: PushsOrderPayloadDto) {
    const { orderCode, modifiedBy, surveysInfo, modifiedByName } = body;
    let survey = null;
    const order = await this.omsService.getOneOrder(orderCode);

    const arrSku: string[] = _.uniqBy(
      _.compact(
        JSONPath({
          path: '$..itemCode',
          json: order,
        }),
      ),
    );

    const arrSkuRestriction: string[] = String(process.env.SKU_RESTRICTION || '').split(',');

    const isSubset: boolean = _.some(arrSkuRestriction, (i: string) => _.includes(arrSku, i));

    if (arrSkuRestriction?.length && isSubset) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_AFF_RESTRICTION_SKU,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    if (order.orderStatus === OrderStatus.Cancel) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_CANCELED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
    if (order.ecomDisplay === EcomDisplay.AtShop) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_PUSHED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    const employeeStep5 = _.orderBy(
      order?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const orderInfo = await this.omsService.updateStatusOrderDeposit({
      orderCode: orderCode,
      modifiedBy: employeeStep5?.employeeCode || modifiedBy,
      modifiedByName: employeeStep5?.employeeName || modifiedByName,
      orderStatus: OrderStatus.FinishDeposit,
      ecomDisplay: EcomDisplay.AtShop,
      orderType: order.orderType,
      shopCode: order.shopCode,
    });
    /**
    await this.omsService.updateEcomDisplay({
      orderCode: orderCode,
      modifiedBy: modifiedBy,
      modifiedByName: modifiedByName,
      ecomDisplay: EcomDisplay.AtShop,
      shopCode: order.shopCode,
    } as any);
     */

    // Lưu thông tin cashback cho in bill affiliate
    await this.addCashbackRedis(orderInfo);

    if (surveysInfo) {
      survey = await this.createSurveyAfterPushing({
        order,
        modifiedBy,
        modifiedByName,
        surveysInfo,
      });
    }

    return { isSuccess: true, ticketInfor: [], survey };
  }

  async getDetailsByTicketCodeV2(ticketCode: string, getDetailsByTicketCodeDTO: GetDetailsByTicketCodeDTO) {
    const dataReturn = (await this.ordersRsaService.getDetailsByTicketCodeV2(
      ticketCode,
      getDetailsByTicketCodeDTO,
    )) as any;
    const groupItemVaccine = _.groupBy(dataReturn.appointmentSchedulesOrder, (a) => a.regimenId);
    dataReturn.orderDataDetails = [];
    for (const [key, value] of Object.entries(groupItemVaccine)) {
      let totalPrice = 0;
      for (const item of value as any) {
        totalPrice += Number(item.price);
      }
      dataReturn.orderDataDetails.push({
        ...value[0],
        totalPrice,
        itemNumber: (value as any)?.length,
      });
    }

    return dataReturn;
  }

  async updateStatusOmsOrderDeposit(payload: UpdateStatusOrderPartialPaymentDto) {
    const order = await this.omsService.getOneOrder(payload.orderCode);

    const payment = await this.paymentGatewayService.getPaymentHistoryES({
      paymentCode: order.paymentRequestCode,
    });

    if (payment.isPayment === true) {
      await this.omsService.updateStatusPayment(
        payload.orderCode,
        PaymentOnlineStatus.Complete,
        order.paymentRequestCode,
      );
      //FV-16188 confirm promotion ở đây nè
      const confirmPromotion = await this.cartAppService.getCartConfirmByOrderCode(payload.orderCode);
      await this.orderUtilsRsaService.confirmPromotion(payload.orderCode, confirmPromotion);
    }

    const updateStatusOms = await this.ordersRsaService.updateStatusOmsOrderDeposit(
      {
        orderStatus: OrderStatus.FinishDeposit,
        ecomDisplay: EcomDisplay.AtShop,
        modifiedBy: payload.modifiedBy,
        modifiedByName: payload.modifiedByName,
        orderCode: payload.orderCode,
      },
      order,
    );

    // Lưu thông tin cashback cho in bill affiliate
    await this.addCashbackRedis(order);

    return updateStatusOms;
  }

  /**
   *
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=161273758
   * FV-10609
   */
  async addCashbackRedis(orderInfo: UpdateOrderLibResponse) {
    // Lấy thông tin cash back
    const arrOrderDetailAttachmentCode = _.compact(
      _.uniq(
        JSONPath({
          path: '$.details[*].detailAttachments[*].orderDetailAttachmentCode',
          json: orderInfo,
        }),
      ),
    );

    const getCashBack = await this.osrService.cashbackSuggest({
      orderCode: orderInfo?.orderCode,
      orderAttachmentCodes: arrOrderDetailAttachmentCode,
      phoneNumber: orderInfo?.phone,
      createdBy: orderInfo?.createdBy || '',
      totalOrderVac: orderInfo?.totalBill || 0,
    });

    await this.redisService.set(
      `${CASHBACK_KEY}:${orderInfo?.orderCode}`,
      JSON.stringify(getCashBack),
      'EX',
      getExpiredTime('day', 10),
    );
  }

  private async createSurveyAfterPushing({
    order,
    surveysInfo,
    modifiedBy,
    modifiedByName,
  }: CreateSurveyAfterPushingDto) {
    const allDetailsAttachments: DetailAttachment[] = JSONPath({
      path: `$.details[*].detailAttachments[*]`,
      json: order,
    });

    const [diseaseGroups, person, ageRanges] = await concurrentPromise(
      this.regimenService.getListDiseaseGroupBySku(_.uniq(_.map(allDetailsAttachments, 'itemCode'))),
      this.familyService.getPersonByLcvId(allDetailsAttachments?.at(0)?.personIdSub),
      this.regimenService.getAgeRanges(),
    );

    if (person) {
      person.customerAge = calculateTimeDifference(
        person?.dateOfBirth,
        person?.from,
        person?.to,
        ageRanges,
        person.ageUnitCode,
      );
    }

    const listVaccine = _.uniqBy(
      allDetailsAttachments?.map((item) => {
        return {
          sku: item.itemCode,
          skuName: item.itemName,
          deseaseGroupName: diseaseGroups?.find((diseaseGroupItem) => diseaseGroupItem.sku === item.itemCode)
            ?.diseaseGroupName,
        };
      }),
      (vaccineItem: any) => vaccineItem.sku,
    );

    return this.rsaIntegration.createSurvey({
      createdBy: modifiedBy,
      createdByName: modifiedByName,
      customerName: order.custName,
      phoneNumber: order.phone,
      shopCodeVaccine: order.shopCode,
      shopNameVaccine: order.shopName,
      doctorInside: '',
      doctorName: '',
      consultantResult: 1,
      isContactNow: false,
      contactChannel: 'Gọi điện thoại', // BA kêu hard.
      ageGroup: person?.customerAge?.textDisplay,
      diseaseType: diseaseGroups?.map((item) => item?.diseaseGroupName || '').join(', '),
      ...surveysInfo,
      orderCode: order.orderCode,
      injectedPersonName: person?.name,
      injectedPersonDOB: person?.dateOfBirth as any,
      injectedPersonLCV: person?.lcvId,
      nationalVaccineCode: person?.nationalVaccineCode,
      injectedPersonGender: person?.gender,
      injectedPersonPhoneNumber: person.phoneNumber,
      // https://reqs.fptshop.com.vn/browse/FV-11000 - docs keu hard
      status: 1,
      sources: 1,
      listVaccine,
    });
  }
}
