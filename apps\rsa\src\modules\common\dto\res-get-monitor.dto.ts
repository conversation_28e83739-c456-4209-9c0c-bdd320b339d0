import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class ChildrenGetMonitor {
  @ApiProperty()
  @Expose()
  @IsOptional()
  id?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  label?: string;
}

export class ItemGetMonitor {
  @ApiProperty()
  @Expose()
  @IsOptional()
  id?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  monitorType?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  monitorTypeName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isRequireNote?: boolean;

  @ApiProperty({ isArray: true, type: ChildrenGetMonitor })
  @Expose()
  @IsOptional()
  children?: ChildrenGetMonitor[];
}

export class GetMonitorRes {
  @ApiProperty({ isArray: true, type: ItemGetMonitor })
  @Expose()
  items?: ItemGetMonitor[];
}
