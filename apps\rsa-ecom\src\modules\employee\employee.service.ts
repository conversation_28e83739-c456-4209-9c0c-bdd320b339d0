import { HttpStatus, Inject, Injectable, LoggerService } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { Employee } from './entities/employee.entity';
import { omit } from 'lodash';
import { ErrorCode, SystemException, UserDto, parseDateTimeZone } from '@shared';
import { AssignSurveyTypes, UserCheckInStatus } from './enum';
import { AssignmentRemoteService, AssignTypesEnum } from 'ict-nest-assign-job';
import { AssignSurveyTypeEnum, VacCoreAssignJobService } from 'vac-nest-assign-job';
import { EmployeeScheduling } from '../employee-scheduling/entities/employee-scheduling.entity';
import {
  EMPLOYEE_SCHEDULING_TYPE,
  ONLINE_ENUM,
  SHIFT,
} from '../employee-scheduling/constants/employee-scheduling.constants';
import { getCurrentStartDateTimeZoneV2 } from '@shared/utilities/function-time-zone';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { ElasticSearchService } from '@shared/modules/elastic-search/elastic-search.service';
import { STATUS_SCHEDULE_REQUEST } from '@shared/enum';
import { getCurrentShift } from '../schedule-requests/utils';
import { AutoGenerateService } from '../auto-generate/auto-generate.service';
import { Sale } from '../employee-scheduling/entities/sale.entity';
import { ASSIGN_ROLE } from '../employee-scheduling/enum/group-sale.enum';
import { LogEntry, TYPE_LOG_ENTRY_ENUM } from './entities/log-entry.entity';
import { CHECK_IN_OUT_LOG } from './constants/route.constants';
import { ScheduleRequestType } from '../schedule-requests/enums/schedule-request-type.enum';
import { CheckShiftAndScheduleRequestsDto } from './dto/check-shift-and-schedule.dto';
import { Shift } from 'vac-nest-assign-job/dist/assign-job.enum';

@Injectable()
export class EmployeeService {
  constructor(
    @InjectRepository(Employee)
    private readonly employeeRepo: Repository<Employee>,
    @InjectRepository(EmployeeScheduling)
    private readonly employeeScheduleRepo: Repository<EmployeeScheduling>,
    private readonly assignService: AssignmentRemoteService,
    private readonly vacAssignJobService: VacCoreAssignJobService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    private readonly elasticSearchService: ElasticSearchService,
    private readonly autoGenerateService: AutoGenerateService,
    @InjectRepository(Sale)
    private readonly saleRepository: Repository<Sale>,
    @InjectRepository(LogEntry)
    private readonly logEntryService: Repository<LogEntry>,
  ) {}

  async getByInside(inside: string) {
    try {
      return await this.employeeRepo.findOne({
        where: {
          employeeCode: inside,
        },
      });
    } catch (error) {
      throw error;
    }
  }

  async getEmployee(inside: string) {
    const resEmployee = await this.findOne(inside);
    return {
      ...resEmployee,
      onlineSurvey: resEmployee?.onlineSurvey ? resEmployee?.onlineSurvey : -1,
      onlineUnPaidSurvey: resEmployee?.onlineUnPaidSurvey ? resEmployee?.onlineUnPaidSurvey : -1,
    } as Employee;
  }

  async findOne(inside: string) {
    try {
      return await this.employeeRepo
        .createQueryBuilder('employee')
        .where('employee.employeeCode = :inside', { inside })
        .getOne();
    } catch (error) {
      throw error;
    }
  }

  async findMany(insides: string[]) {
    try {
      return await this.employeeRepo.findBy({
        employeeCode: In(insides),
      });
    } catch (error) {
      throw error;
    }
  }

  async updateOrCreateByInside(employeeCode: string, updateEmployeeDto: UpdateEmployeeDto) {
    try {
      const property = await this.findOne(employeeCode);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const saveData = omit(property, 'assignments');

      return await this.employeeRepo.save({
        ...saveData, // existing fields
        ...updateEmployeeDto, // updated fields
      });
    } catch (error) {
      throw error;
    }
  }

  async checkInOutSurvey(user: UserDto, isCheckIn: boolean) {
    try {
      switch (isCheckIn) {
        case true:
          try {
            await this.logEntryService.save({
              inside: user.employee_code || '',
              employeeName: user.full_name || '',
              status: CHECK_IN_OUT_LOG.CHECK_IN,
              updatedAt: new Date().toISOString(),
              createdAt: new Date().toISOString(),
            });
            await this.logger.log(
              {
                message: 'check-in-survey',
                fields: {
                  info: `check-in-survey-log`,
                  method: `GET`,
                  url: `check-in-survey for user ${user.employee_code}`,
                  dataRes: JSON.stringify(user),
                  bodyReq: '{}',
                  queryReq: '{}',
                  paramsReq: '{}',
                  headers: '{}',
                },
              },
              false,
            );
          } catch (error) {}
          const sale = await this.saleRepository.findOneBy({
            inside: user?.employee_code,
          });
          if (sale && sale?.assignRole !== ASSIGN_ROLE.SURVEY) {
            throw new SystemException(
              {
                code: ErrorCode.RSA_ECOM_ASSIGN_ROLE_SURVEY,
                message: ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGN_ROLE_SURVEY),
                details: ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGN_ROLE_SURVEY),
                validationErrors: null,
              },
              HttpStatus.BAD_REQUEST,
            );
          }

          // push vao insideId vao queues.
          const startDate = parseDateTimeZone(new Date(), '+07:00', 'YYYY-MM-DD');
          const regions = await this.employeeScheduleRepo.find({
            where: {
              insideCode: user.employee_code,
              startDate,
              type: 2,
            },
          });
          if (!regions.length) {
            throw new SystemException(
              {
                code: ErrorCode.RSA_ECOM_SHOP_VE_TINH_REGION_NOT_FOUND,
                message: ErrorCode.getError(ErrorCode.RSA_ECOM_SHOP_VE_TINH_REGION_NOT_FOUND),
                details: ErrorCode.getError(ErrorCode.RSA_ECOM_SHOP_VE_TINH_REGION_NOT_FOUND),
                validationErrors: null,
              },
              HttpStatus.NOT_FOUND,
            );
          }
          const sortIndex = await this.autoGenerateService.createSurveyIndex();
          await this.vacAssignJobService.createOneAssignment({
            insideId: user.employee_code,
            type: AssignSurveyTypes.User,
            regionCode: regions.map((region) => parseInt(String(region.region))),
            sortIndex: parseInt(sortIndex),
            metaData: {
              assigneeName: user.full_name,
            },
          });

          await this.updateOrCreateByInside(user.employee_code, {
            employeeCode: user.employee_code,
            onlineSurvey: isCheckIn ? UserCheckInStatus.Online : UserCheckInStatus.Offline,
            employeeName: user.full_name,
          });

          const payload = {
            isSuccess: true,
          };

          return payload;
        default:
          try {
            await this.logEntryService.save({
              inside: user.employee_code || '',
              employeeName: user.full_name || '',
              status: CHECK_IN_OUT_LOG.CHECK_OUT,
              updatedAt: new Date().toISOString(),
              createdAt: new Date().toISOString(),
            });
            await this.logger.log(
              {
                message: 'check-out-survey',
                fields: {
                  url: `check-out-survey for user ${user.employee_code}`,
                  dataRes: JSON.stringify(user),
                },
              },
              false,
            );
          } catch (error) {}
          // logic shop ve tinh.
          let checkOutShopVeTinhStatus = false;
          if (!user?.employee_code) {
            throw new SystemException(
              {
                code: ErrorCode.RSA_ECOM_EMPLOYEE_NOT_FOUND,
                message: ErrorCode.getError(ErrorCode.RSA_ECOM_EMPLOYEE_NOT_FOUND),
                details: ErrorCode.getError(ErrorCode.RSA_ECOM_EMPLOYEE_NOT_FOUND),
                validationErrors: null,
              },
              HttpStatus.NOT_FOUND,
            );
          }
          const employeeCheckout = await this.employeeRepo.findOne({
            where: {
              employeeCode: user.employee_code,
            },
          });
          if (employeeCheckout) {
            checkOutShopVeTinhStatus = true;
            await Promise.all([
              this.vacAssignJobService.removeOneAssignment({
                type: AssignSurveyTypes.User,
                insideId: employeeCheckout.employeeCode, //0đ
              }),
              this.vacAssignJobService.removeOneAssignment({
                type: AssignSurveyTypes.User,
                insideId: employeeCheckout.employeeCode,
                typeOrder: 2, //ocr
              }),
            ]);
          }

          await this.updateOrCreateByInside(user.employee_code, {
            employeeCode: user.employee_code,
            onlineSurvey: isCheckIn ? UserCheckInStatus.Online : UserCheckInStatus.Offline,
            employeeName: user.full_name,
          });

          return {
            isSuccess: checkOutShopVeTinhStatus,
          };
      }
    } catch (error) {
      throw error;
    }
  }

  async checkInOutUnPaidSurvey(user: UserDto, isCheckIn: boolean) {
    try {
      switch (isCheckIn) {
        case true:
          try {
            await this.logEntryService.save({
              inside: user.employee_code || '',
              employeeName: user.full_name || '',
              status: CHECK_IN_OUT_LOG.CHECK_IN,
              updatedAt: new Date().toISOString(),
              createdAt: new Date().toISOString(),
              logType: TYPE_LOG_ENTRY_ENUM.LOG_CHECK_IN_UN_PAID_SURVEY,
            });
            await this.logger.log(
              {
                message: 'check-in-un-paid-survey',
                fields: {
                  info: `check-in-survey-log`,
                  method: `GET`,
                  url: `check-in-survey for user ${user.employee_code}`,
                  dataRes: JSON.stringify(user),
                  bodyReq: '{}',
                  queryReq: '{}',
                  paramsReq: '{}',
                  headers: '{}',
                },
              },
              false,
            );
          } catch (error) {}
          await this.vacAssignJobService.unpaidSurveyAddToQueue({
            type: AssignSurveyTypeEnum.User,
            id: user.employee_code,
          });

          await this.vacAssignJobService.unpaidSurveySaveUserActions({
            userId: user.employee_code,
            action: '[RSA ECOM]: check-in - add to queue',
          });

          await this.updateOrCreateByInside(user.employee_code, {
            employeeCode: user.employee_code,
            onlineUnPaidSurvey: isCheckIn ? UserCheckInStatus.Online : UserCheckInStatus.Offline,
            employeeName: user.full_name,
          });

          const payload = {
            isSuccess: true,
          };

          return payload;
        default:
          try {
            await this.logEntryService.save({
              inside: user.employee_code || '',
              employeeName: user.full_name || '',
              status: CHECK_IN_OUT_LOG.CHECK_OUT,
              updatedAt: new Date().toISOString(),
              createdAt: new Date().toISOString(),
              logType: TYPE_LOG_ENTRY_ENUM.LOG_CHECK_IN_UN_PAID_SURVEY,
            });
            await this.logger.log(
              {
                message: 'check-out-unpaid-survey',
                fields: {
                  url: `check-out-unpaid-survey for user ${user.employee_code}`,
                  dataRes: JSON.stringify(user),
                },
              },
              false,
            );
          } catch (error) {}
          // logic shop ve tinh.
          let checkOutShopVeTinhStatus = false;
          if (!user?.employee_code) {
            throw new SystemException(
              {
                code: ErrorCode.RSA_ECOM_EMPLOYEE_NOT_FOUND,
                message: ErrorCode.getError(ErrorCode.RSA_ECOM_EMPLOYEE_NOT_FOUND),
                details: ErrorCode.getError(ErrorCode.RSA_ECOM_EMPLOYEE_NOT_FOUND),
                validationErrors: null,
              },
              HttpStatus.NOT_FOUND,
            );
          }
          const employeeCheckout = await this.employeeRepo.findOne({
            where: {
              employeeCode: user.employee_code,
            },
          });
          if (employeeCheckout) {
            checkOutShopVeTinhStatus = true;
            await this.vacAssignJobService.unpaidSurveyRemoveFromQueue({
              id: employeeCheckout.employeeCode,
              type: AssignSurveyTypeEnum.User,
            });
            await this.vacAssignJobService.unpaidSurveySaveUserActions({
              userId: user.employee_code,
              action: '[RSA ECOM]: check-out - remove from queue',
            });
          }

          await this.updateOrCreateByInside(user.employee_code, {
            employeeCode: user.employee_code,
            onlineUnPaidSurvey: isCheckIn ? UserCheckInStatus.Online : UserCheckInStatus.Offline,
            employeeName: user.full_name,
          });

          return {
            isSuccess: checkOutShopVeTinhStatus,
          };
      }
    } catch (error) {
      throw error;
    }
  }

  async checkInOut(user: UserDto, isCheckIn: boolean) {
    try {
      switch (isCheckIn) {
        case true:
          await this.logger.log(
            {
              message: 'check-in sale ecom',
              fields: {
                url: `check-in for user ${user.employee_code}`,
                dataRes: JSON.stringify(user),
              },
            },
            false,
          );

          await this.popRedis({
            insideId: user.employee_code,
          });

          const sale = await this.saleRepository.findOneBy({
            inside: user?.employee_code,
          });
          if (sale && sale?.assignRole !== ASSIGN_ROLE.SCHEDULE) {
            throw new SystemException(
              {
                code: ErrorCode.RSA_ECOM_ASSIGN_ROLE,
                message: ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGN_ROLE),
                details: ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGN_ROLE),
                validationErrors: null,
              },
              HttpStatus.BAD_REQUEST,
            );
          }

          // if employees has not been scheduled, throw error at this line
          const { shifts, shiftRaw } = await this.getShiftAndCheckPossibleAssign(
            user.employee_code,
            false,
            ScheduleRequestType.Default,
          );

          const updateCheckIn = await Promise.all([
            this.updateOrCreateByInside(user.employee_code, {
              employeeCode: user.employee_code,
              online: isCheckIn ? UserCheckInStatus.Online : UserCheckInStatus.Offline,
              employeeName: user.full_name,
            }),
            shifts &&
              shifts.length &&
              this.pushRedis({
                insideId: user.employee_code,
                shifts,
              }),
            shiftRaw?.includes(Shift.Day) &&
              this.vacAssignJobService.addQueueOnlineOrderAssignment({
                type: AssignTypesEnum.user,
                sub: {
                  listInsideId: [user.employee_code],
                },
              }),
            shiftRaw?.includes(Shift.Night) &&
              this.vacAssignJobService.addQueueOnlineOrderAssignment({
                type: AssignTypesEnum.user,
                sub: {
                  listInsideId: [user.employee_code],
                },
                isNightShift: true,
              }),
          ]);
          return updateCheckIn?.at(0);

        default:
          await this.logger.log(
            {
              message: 'check-out sale ecom',
              fields: {
                url: `check-out for user ${user.employee_code}`,
                dataRes: JSON.stringify(user),
              },
            },
            false,
          );
          const updateCheckOut = await Promise.all([
            this.updateOrCreateByInside(user.employee_code, {
              employeeCode: user.employee_code,
              online: isCheckIn ? UserCheckInStatus.Online : UserCheckInStatus.Offline,
              employeeName: user.full_name,
            }),
            this.popRedis({
              insideId: user.employee_code,
            }),
            this.vacAssignJobService.removeQueueOnlineOrderAssignment({
              type: AssignTypesEnum.user,
              sub: {
                listInsideId: [user.employee_code],
              },
            }),
            this.vacAssignJobService.removeQueueOnlineOrderAssignment({
              type: AssignTypesEnum.user,
              sub: {
                listInsideId: [user.employee_code],
              },
              isNightShift: true,
            }),
          ]);
          return updateCheckOut?.at(0);
      }
    } catch (error) {
      throw error;
    }
  }

  async checkAndScheduleRequests(payload: CheckShiftAndScheduleRequestsDto) {
    const startDate = getCurrentStartDateTimeZoneV2('YYYY-MM-DD');
    let scheduleRequestType = ScheduleRequestType.All;
    let type = undefined;
    switch (Number(payload.type)) {
      case 1:
        scheduleRequestType = ScheduleRequestType.Default;
        type = 1;
        break;
      case 2:
        scheduleRequestType = ScheduleRequestType.PreOrder;
        type = 3;
        break;
    }
    const employeeSchedules = await this.employeeScheduleRepo.find({
      where: {
        insideCode: String(payload.employeeCode),
        startDate,
        type,
      },
    });

    const scheduleRequest = await this.elasticSearchService.searchScheduleRequest(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      {
        insideId: [String(payload.employeeCode)],
        status: [STATUS_SCHEDULE_REQUEST.NEW.toString()],
      },
      scheduleRequestType,
    );
    return { employeeSchedules, scheduleRequest };
  }

  async checkInOutPreOrder(user: UserDto, isCheckIn: boolean) {
    try {
      switch (isCheckIn) {
        case true:
          await this.logger.log(
            {
              message: 'check-in pre order',
              fields: {
                url: `check-in pre order ${user.employee_code}`,
                dataRes: JSON.stringify(user),
              },
            },
            false,
          );
          await this.popPreOrderRedis({
            insideId: user.employee_code,
          });

          const sale = await this.saleRepository.findOneBy({
            inside: user?.employee_code,
          });
          if (sale && sale?.assignRole !== ASSIGN_ROLE.PREORDER) {
            throw new SystemException(
              {
                code: ErrorCode.RSA_ECOM_ASSIGN_ROLE_PRE_ORDER,
                message: ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGN_ROLE_PRE_ORDER),
                details: ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGN_ROLE_PRE_ORDER),
                validationErrors: null,
              },
              HttpStatus.BAD_REQUEST,
            );
          }

          // if employees has not been scheduled, throw error at this line
          const { shifts } = await this.getShiftAndCheckPossibleAssign(
            user.employee_code,
            false,
            ScheduleRequestType.PreOrder,
            ErrorCode.RSA_ECOM_CHECK_IN_PRE_ORDER,
          );

          const updateCheckIn = await Promise.all([
            this.updateOrCreateByInside(user.employee_code, {
              employeeCode: user.employee_code,
              onlinePreOrder: isCheckIn ? UserCheckInStatus.Online : UserCheckInStatus.Offline,
              employeeName: user.full_name,
            }),
            shifts &&
              shifts.length &&
              this.pushPreOrderRedis({
                insideId: user.employee_code,
                shifts,
              }),
          ]);
          return updateCheckIn?.at(0);

        default:
          await this.logger.log(
            {
              message: 'check-out pre order',
              fields: {
                url: `check-out pre order ${user.employee_code}`,
                dataRes: JSON.stringify(user),
              },
            },
            false,
          );
          const updateCheckOut = await Promise.all([
            this.updateOrCreateByInside(user.employee_code, {
              employeeCode: user.employee_code,
              onlinePreOrder: isCheckIn ? UserCheckInStatus.Online : UserCheckInStatus.Offline,
              employeeName: user.full_name,
            }),
            this.popPreOrderRedis({
              insideId: user.employee_code,
            }),
          ]);
          return updateCheckOut?.at(0);
      }
    } catch (error) {
      throw error;
    }
  }

  async pushPreOrderRedis(sub: { shifts?: number[]; insideId?: string; orderCode?: string }): Promise<any> {
    try {
      return await this.assignService.pushRedisPreOrder({
        sub: { ...sub },
        type: AssignTypesEnum.user,
      });
    } catch (error) {
      throw error;
    }
  }

  async popPreOrderRedis(sub: { insideId?: string; orderCode?: string }): Promise<any> {
    try {
      return await this.assignService.popRedisPreOrder({
        sub: { ...sub, ...{ shifts: [SHIFT.DAY, SHIFT.NIGHT] } },
        type: AssignTypesEnum.user,
      });
    } catch (error) {
      throw error;
    }
  }

  async pushRedis(sub: { shifts?: number[]; insideId?: string; orderCode?: string }): Promise<any> {
    try {
      return await this.assignService.pushRedis({
        sub: { ...sub },
        type: AssignTypesEnum.user,
      });
    } catch (error) {
      throw error;
    }
  }

  async popRedis(sub: { insideId?: string; orderCode?: string }): Promise<any> {
    try {
      return await this.assignService.popRedis({
        sub: { ...sub, ...{ shifts: [SHIFT.DAY, SHIFT.NIGHT] } },
        type: AssignTypesEnum.user,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * @description Check if the employee is possible to be assigned, since the employee has no schedules in the current date, throw error
   * @param employee_code
   * @return employee shifts
   */
  async getShiftAndCheckPossibleAssign(
    employee_code: string,
    isManualAssign: boolean,
    scheduleRequestType: ScheduleRequestType,
    errorCode?: ErrorCode,
    isOnlineOrder?: boolean,
  ) {
    let type;
    switch (scheduleRequestType) {
      case ScheduleRequestType.Default:
        type = EMPLOYEE_SCHEDULING_TYPE.SALE_ECOM;
        break;
      case ScheduleRequestType.PreOrder:
        type = EMPLOYEE_SCHEDULING_TYPE.PRE_ORDER;
        break;
    }

    const startDate = getCurrentStartDateTimeZoneV2('YYYY-MM-DD');
    const employeeSchedules = await this.employeeScheduleRepo.find({
      where: {
        insideCode: employee_code,
        startDate,
        type: type,
      },
    });
    if (!employeeSchedules.length) {
      if (errorCode) {
        throw new SystemException(
          {
            code: errorCode as any,
            message: ErrorCode.getError(errorCode as any),
            details: ErrorCode.getError(errorCode as any),
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_ASSIGN,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGN),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGN),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    const shifts = employeeSchedules.map((item) => item.shift);

    if (isManualAssign) {
      return {
        shifts,
        err: null,
      };
    }

    const scheduleRequestRaw = await this.elasticSearchService.searchScheduleRequest(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      {
        insideId: [employee_code],
        status: [STATUS_SCHEDULE_REQUEST.NEW.toString()],
      },
      scheduleRequestType,
    );
    const items =
      scheduleRequestRaw?.items?.filter((item: any) => (isOnlineOrder ? item.type === 5 : item.type !== 5)) || [];
    const scheduleRequest = {
      totalCount: items?.length || 0,
      items,
    };

    let returnErr = null;
    const shiftsAlreadyAssigned = scheduleRequest.items.map((item) => {
      return getCurrentShift(new Date(item['createdDate']));
    });

    if (scheduleRequest.totalCount) {
      const shiftsAfterCheck = shifts.filter((shift) => !shiftsAlreadyAssigned.includes(shift));
      if (shiftsAfterCheck?.length < shifts?.length) {
        const message = ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING).replace(
          '{id}',
          String((scheduleRequest?.items?.[0] as any)?.id),
        );
        returnErr = {
          code: ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING,
          message: message,
          details: message,
          validationErrors: null,
        };
      }
      return { shifts: shiftsAfterCheck, err: returnErr, shiftRaw: shifts };
    }

    return { shifts: shifts, err: returnErr, shiftRaw: shifts };
  }

  async addEmployeeToShopVeTinhQueue(employeeCode: string) {
    const employee = await this.employeeRepo.findOne({
      where: {
        employeeCode: employeeCode,
      },
    });
    if (!employee || employee?.onlineSurvey !== 1) {
      return {
        isSuccess: false,
        employee,
        error: {
          code: ErrorCode.RSA_ECOM_SHOP_VE_TINH_EMPLOYEE_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_SHOP_VE_TINH_EMPLOYEE_NOT_FOUND),
        },
      };
    }

    const sortIndex = await this.autoGenerateService.createSurveyIndex();
    const startDate = parseDateTimeZone(new Date(), '+07:00', 'YYYY-MM-DD');
    const regions = await this.employeeScheduleRepo.find({
      where: {
        insideCode: employeeCode,
        startDate,
        type: 2,
      },
    });

    if (!regions.length) {
      return {
        isSuccess: false,
        error: {
          code: ErrorCode.RSA_ECOM_SHOP_VE_TINH_REGION_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_SHOP_VE_TINH_REGION_NOT_FOUND),
        },
      };
    }

    await this.vacAssignJobService.createOneAssignment({
      insideId: employee.employeeCode,
      type: AssignSurveyTypes.User,
      regionCode: regions.map((region) => parseInt(String(region.region))),
      sortIndex: parseInt(sortIndex),
      metaData: {
        assigneeName: employee.employeeName,
      },
    });

    return {
      isSuccess: true,
    };
  }

  async addEmployeeToUnpaidSurveyQueue(employeeCode: string) {
    const employee = await this.employeeRepo.findOne({
      where: {
        employeeCode: employeeCode,
      },
    });
    if (!employee || employee?.onlineUnPaidSurvey !== 1) {
      await this.vacAssignJobService.unpaidSurveySaveUserActions({
        userId: employee.employeeCode,
        action: '[RSA ECOM]: add user to queue failed - user not online',
      });
      return {
        isSuccess: false,
        employee,
        error: {
          code: ErrorCode.RSA_ECOM_SHOP_VE_TINH_EMPLOYEE_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_SHOP_VE_TINH_EMPLOYEE_NOT_FOUND),
        },
      };
    }

    await this.vacAssignJobService.unpaidSurveySaveUserActions({
      userId: employee.employeeCode,
      action: '[RSA ECOM]: add user to queue success',
    });

    await this.vacAssignJobService.unpaidSurveyAddToQueue({
      id: employee.employeeCode,
      type: AssignSurveyTypeEnum.User,
    });

    return {
      isSuccess: true,
    };
  }

  async checkoutEndOfDay() {
    const usersOnline = await this.employeeRepo
      .createQueryBuilder()
      .where('online = 1 OR online_survey = 1 OR online_pre_order = 1 OR online_unpaid_survey = 1')
      .getMany();

    if (usersOnline?.length > 0) {
      const users = usersOnline?.map((e) => [e.employeeCode, e.employeeName].join('-'));
      await this.employeeRepo
        .createQueryBuilder()
        .update()
        .set({
          online: () => 'CASE WHEN online = 1 THEN -1 ELSE online END',
          onlineSurvey: () => 'CASE WHEN online_survey = 1 THEN -1 ELSE online_survey END',
          onlinePreOrder: () => 'CASE WHEN online_pre_order = 1 THEN -1 ELSE online_pre_order END',
          onlineUnPaidSurvey: () => 'CASE WHEN online_unpaid_survey = 1 THEN -1 ELSE online_unpaid_survey END',
        })
        .where('online = 1 OR online_survey = 1 OR online_pre_order = 1 OR online_unpaid_survey = 1')
        .execute();

      try {
        await this.logger.log(
          {
            message: 'Checkout all user end of day',
            fields: {
              info: `check-out-users-end-of-day`,
              url: `Users for checkout end of day`,
              dataRes: JSON.stringify(users),
              bodyReq: JSON.stringify(users),
            },
          },
          false,
        );
        await this.vacAssignJobService.checkoutUsersEndOfDay();
      } catch (error) {}
      return {
        status: true,
        data: users,
      };
    }

    return { status: true, data: 'Tất cả nhân viên đã checkout' };
  }

  async getShiftRaw(employee_code: string, scheduleRequestType: ScheduleRequestType = ScheduleRequestType.Default) {
    let type = EMPLOYEE_SCHEDULING_TYPE.SALE_ECOM;
    switch (scheduleRequestType) {
      case ScheduleRequestType.Default:
        type = EMPLOYEE_SCHEDULING_TYPE.SALE_ECOM;
        break;
      case ScheduleRequestType.PreOrder:
        type = EMPLOYEE_SCHEDULING_TYPE.PRE_ORDER;
        break;
    }

    const startDate = getCurrentStartDateTimeZoneV2('YYYY-MM-DD');
    const employeeSchedules = await this.employeeScheduleRepo.find({
      where: {
        insideCode: employee_code,
        startDate,
        type: type,
      },
    });

    const shifts = employeeSchedules.map((item) => item.shift);

    return { shiftRaw: shifts };
  }
}
