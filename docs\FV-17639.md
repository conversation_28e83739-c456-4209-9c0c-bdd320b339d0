# Tài liệu <PERSON> thuật: <PERSON><PERSON>i bỏ Luồng Lưu <PERSON>ơn Theo Lịch Hẹn

## <PERSON><PERSON><PERSON> lụ<PERSON>
1. [Tổng quan](#tổng-quan)
2. [Hiện trạng](#hiện-trạng)
3. [<PERSON><PERSON><PERSON> cầu thay đổi](#yêu-cầu-thay-đổi)
4. [<PERSON><PERSON><PERSON><PERSON> pháp kỹ thuật](#gi<PERSON>i-ph<PERSON>p-kỹ-thuật)
5. [Luồng dữ liệu](#luồng-dữ-liệu)
6. [<PERSON><PERSON> đồ tuần tự](#sơ-đồ-tuần-tự)
7. [Chi tiết triển khai](#chi-tiết-triển-khai)
8. [Checklist kiểm tra](#checklist-kiểm-tra)

## Tổng quan

Tài liệu này mô tả việc loại bỏ luồng lưu đơn dựa trên lịch hẹn trong hệ thống RSA ECOM. Thay đổi này nhằm đơn giản hóa quy trình xử lý đơn hàng bằng cách loại bỏ logic kiểm tra ngày hẹn và các giao diện liên quan.

**Ticket:** FV-17639

## Hiện trạng

### Mô tả luồng hiện tại
Đối với đơn hàng có lịch hẹn > 3 ngày:
- Sale chọn phương thức thanh toán COD
- Hệ thống sẽ vào luồng lưu đơn
- Hiển thị thông tin ở tab "Lịch hẹn sớm"
- Hiển thị màn hình nhắc hẹn đơn chờ (D-2)

### Flowchart luồng hiện tại

```mermaid
flowchart TD
    A[Đơn hàng mới] --> B{Kiểm tra lịch hẹn}
    B -->|Lịch hẹn > 3 ngày| C{PTTT = COD?}
    B -->|Lịch hẹn ≤ 3 ngày| D[Push đơn hàng]
    C -->|Có| E[Lưu đơn]
    C -->|Không| D
    E --> F[Hiển thị tab Lịch hẹn sớm]
    E --> G[Màn hình nhắc hẹn D-2]
    D --> H[Hoàn thành]
    F --> H
    G --> H
```

## Yêu cầu thay đổi

### Mục tiêu
Loại bỏ hoàn toàn luồng lưu đơn dựa trên lịch hẹn:

1. **Backend**: Không check ngày hẹn để lưu đơn
2. **Frontend**: Ẩn tab "Lịch hẹn sớm" ở màn hình Tiếp nhận
3. **Frontend**: Ẩn tab "Lịch hẹn sớm" ở menu Danh sách nhắc hẹn
4. **Job**: Tắt job sync ES VacScheduleV2AppService.SyncScheduleEcomInDay

### Flowchart luồng mới

```mermaid
flowchart TD
    A[Đơn hàng mới] --> B[Push đơn hàng]
    B --> C[Hoàn thành]
    
    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
```

## Giải pháp kỹ thuật

### Backend Changes (RSA ECOM)

#### 1. Cập nhật `checkNextActionForOrder`
- Luôn trả về `PUSH_ORDER`
- Vô hiệu hóa logic kiểm tra lịch hẹn

#### 2. Cập nhật `isPossibleContinueBuyingByScheduleD2`
- Luôn trả về `isPossible: true`
- Vô hiệu hóa logic kiểm tra D-2

### Frontend Changes (RSA ECOM)

#### 1. Ẩn nút "Lưu đơn"
- Không hiển thị option lưu đơn trong giao diện

#### 2. Ẩn tab "Lịch hẹn sớm"
- Màn hình Tiếp nhận
- Menu Danh sách nhắc hẹn

### Job Changes

#### 1. Tắt Sync Job
- VacScheduleV2AppService.SyncScheduleEcomInDay

## Luồng dữ liệu

### Data Flow Diagram

```mermaid
graph TB
    subgraph "Frontend"
        A[Tạo đơn hàng]
        B[Giao diện đơn giản hóa]
    end
    
    subgraph "Backend API"
        C[checkNextActionForOrder]
        D[isPossibleContinueBuyingByScheduleD2]
        E[Order Processing]
    end
    
    subgraph "Database"
        F[Orders Table]
        G[Schedule Data]
    end
    
    subgraph "External Services"
        H[Elasticsearch]
        I[OMS Service]
    end
    
    A --> C
    C --> E
    E --> F
    E --> I
    
    D --> E
    
    B -.->|Không sử dụng| G
    
    style C fill:#ffcdd2
    style D fill:#ffcdd2
    style G fill:#f5f5f5,stroke-dasharray: 5 5
```

## Sơ đồ tuần tự

### Sequence Diagram - Luồng xử lý đơn hàng mới

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant BE as Backend API
    participant DB as Database
    participant OMS as OMS Service
    
    FE->>BE: Tạo đơn hàng
    BE->>BE: checkNextActionForOrder()
    Note over BE: Luôn trả về PUSH_ORDER
    
    BE->>BE: isPossibleContinueBuyingByScheduleD2()
    Note over BE: Luôn trả về isPossible: true
    
    BE->>OMS: Push đơn hàng
    OMS-->>BE: Xác nhận
    
    BE->>DB: Lưu trạng thái đơn hàng
    DB-->>BE: Thành công
    
    BE-->>FE: Phản hồi thành công
    
    Note over FE: Không hiển thị tab Lịch hẹn sớm
    Note over FE: Không có nút Lưu đơn
```

### Sequence Diagram - So sánh trước và sau

```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant BE as Backend
    participant ES as Elasticsearch
    
    Note over U,ES: TRƯỚC THAY ĐỔI
    U->>FE: Tạo đơn có lịch hẹn > 3 ngày
    FE->>BE: Kiểm tra lịch hẹn
    BE->>BE: isNearestScheduleNotWithinRangeDays()
    BE-->>FE: Kết quả: SAVE_ORDER
    FE->>U: Hiển thị tab "Lịch hẹn sớm"
    FE->>ES: Sync dữ liệu lịch hẹn
    
    Note over U,ES: SAU THAY ĐỔI
    U->>FE: Tạo đơn hàng
    FE->>BE: Xử lý đơn hàng
    BE->>BE: checkNextActionForOrder()
    Note over BE: Bỏ qua kiểm tra lịch hẹn
    BE-->>FE: Kết quả: PUSH_ORDER
    FE->>U: Giao diện đơn giản
    Note over ES: Không sync lịch hẹn
```

## Chi tiết triển khai

### 1. Backend Implementation

#### File: `next-action.util.ts`

**Hiện tại:**
```typescript
export const checkNextActionForOrder = (
  orderDetail: GetOrderEsRes,
  loggerService: LoggerService,
): EnumNextActionOrder => {
  const isSaveOrder = isNearestScheduleNotWithinRangeDays(orderDetail, loggerService, 3).result;
  return isSaveOrder && orderDetail?.ecomDisplay !== EcomDisplay.Save
    ? EnumNextActionOrder.SAVE_ORDER
    : EnumNextActionOrder.PUSH_ORDER;
};
```

**Sau thay đổi:**
```typescript
export const checkNextActionForOrder = (
  _orderDetail: GetOrderEsRes,
  _loggerService: LoggerService,
): EnumNextActionOrder => {
  return EnumNextActionOrder.PUSH_ORDER;
  // FV-17639: Tắt luồng lưu đơn khi lịch hẹn gần nhất lớn hơn 3 ngày
};
```

### 2. Frontend Implementation

#### Các component cần ẩn:
1. Tab "Lịch hẹn sớm" trong màn hình Tiếp nhận
2. Tab "Lịch hẹn sớm" trong menu Danh sách nhắc hẹn
3. Nút "Lưu đơn" trong form tạo đơn hàng

### 3. Job Configuration

#### Tắt job sync:
```typescript
// VacScheduleV2AppService.SyncScheduleEcomInDay
// Cần disable hoặc comment out job này
```

## Checklist kiểm tra

### Backend Testing
- [ ] `checkNextActionForOrder` luôn trả về `PUSH_ORDER`
- [ ] `isPossibleContinueBuyingByScheduleD2` luôn trả về `isPossible: true`
- [ ] Không có logic kiểm tra lịch hẹn được thực thi
- [ ] API response không chứa thông tin về save order

### Frontend Testing
- [ ] Tab "Lịch hẹn sớm" không hiển thị ở màn hình Tiếp nhận
- [ ] Tab "Lịch hẹn sớm" không hiển thị ở menu Danh sách nhắc hẹn
- [ ] Nút "Lưu đơn" không hiển thị
- [ ] Luồng tạo đơn hàng hoạt động bình thường

### Job Testing
- [ ] Job `VacScheduleV2AppService.SyncScheduleEcomInDay` đã bị tắt
- [ ] Không có sync dữ liệu lịch hẹn lên Elasticsearch

### Integration Testing
- [ ] Đơn hàng được tạo và push thành công
- [ ] Không có dữ liệu orphan trong database
- [ ] Performance cải thiện do bỏ logic phức tạp

## Ghi chú

- Thay đổi này là một phần của việc đơn giản hóa quy trình xử lý đơn hàng
- Cần đảm bảo backward compatibility với các đơn hàng đã tồn tại
- Monitor performance sau khi deploy để đảm bảo cải thiện như mong đợi
- Cần update documentation cho team support về thay đổi này
