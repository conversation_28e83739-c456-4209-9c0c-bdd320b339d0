# Tài liệu API updateStatusOrderMany

## 1. Tổng quan

### 1.1 Mô tả

API `updateStatusOrderMany` được sử dụng để cập nhật trạng thái nhiều đơn hàng gói gia đình cùng lúc. API này xử lý việc hoàn tất cọc và tạo phiếu khám cho tất cả đơn hàng trong nhóm.

### 1.2 Thông tin API

- **Endpoint**: `POST /api/v1/orders-family-package/update-status-order-deposit-many`
- **Controller**: `OrdersFamilyPackageController`
- **Service**: `OrdersFamilyPackageService.updateStatusOrderMany`
- **Authentication**: Bearer Token required
- **Tags**: Orders-family-package

## 2. Request

### 2.1 Request Body (UpdateStatusOrderManyDto)

```typescript
{
  // Danh sách mã đơn hàng
  "orderCodes": ["string[]"],     // Danh sách mã đơn hàng cần cập nhật (required)

  // Thông tin người thực hiện
  "modifiedBy": "string",         // Mã nhân viên thực hiện
  "modifiedByName": "string",     // Tên nhân viên thực hiện

  // Thông tin phiếu khám
  "ticketInfor": [                // Thông tin phiếu khám (optional - có thể lấy từ Redis)
    {
      "orderCode": "string",      // Mã đơn hàng
      "lcvId": "string",          // ID người tiêm
      "indications": [            // Danh sách chỉ định
        {
          "productId": "string",
          "quantity": number,
          // ... thông tin chỉ định khác
        }
      ],
      "schedules": [              // Danh sách lịch hẹn
        {
          "scheduleDate": "string",
          "timeSlot": "string",
          // ... thông tin lịch hẹn khác
        }
      ]
      // ... thông tin phiếu khám khác
    }
  ],

  // Journey ID (optional)
  "journeyId": "string"           // ID journey liên quan
}
```

### 2.2 Headers

```
Content-Type: application/json
Authorization: Bearer <token>
shop-code: string (required)
```

## 3. Response

### 3.1 Success Response (UpdateStatusOrderResponse)

```typescript
{
  "isSuccess": boolean,           // Trạng thái thành công
  "ticketInfor": [               // Danh sách phiếu khám đã tạo
    {
      "ticketCode": "string",     // Mã phiếu khám
      "orderCode": "string",      // Mã đơn hàng
      "lcvId": "string",          // ID người tiêm
      "status": "string",         // Trạng thái phiếu khám
      // ... thông tin phiếu khám chi tiết
    }
  ]
}
```

### 3.2 Error Response

```typescript
{
  "statusCode": number,
  "message": "string",
  "error": "string"
}
```

## 4. Quy trình xử lý

### 4.1 Sequence Diagram

```mermaid
sequenceDiagram
    participant FE
    participant Service as BE
    participant Redis as Redis
    participant OMSService as OMSService
    participant CartAppService as CartAppService
    participant OrderUtilsService as OrderUtilsService
    participant CamundaApiService as CamundaApiService
    participant OrderRedisService as OrderRedisService

    FE->>Service: POST /api/v1/orders-family-package/update-status-order-deposit-many
    Note over FE,Service: UpdateStatusOrderManyDto

    Note over Service: Step 1: Get ticket info from Redis
    Service->>Redis: pipelineGet(TICKET_CREATE_KEY:orderCode)
    Redis-->>Service: ticketCreateCache

    Note over Service: Step 2: Get orders and cart confirm (Parallel)
    par Get Orders
        Service->>OMSService: getListOrderES({ orderCode: orderCodes })
        OMSService-->>Service: arrOrderInfo
    and Get Cart Confirm for each order
        loop For each orderCode
            Service->>CartAppService: getCartConfirmByOrderCode(orderCode)
            CartAppService-->>Service: cartConfirmData
        end
    end

    Note over Service: Step 3: Confirm promotion for child carts (Parallel)
    loop For each cartConfirm
        Service->>OrderUtilsService: confirmPromotion(orderCode, cartConfirm, orderData)
        OrderUtilsService-->>Service: Success
    end

    Note over Service: Step 4: Get incentive data
    Service->>OrderRedisService: getIncentiveOrderRedis(arrLCVId)
    OrderRedisService-->>Service: incentiveOrderRedis

    Note over Service: Step 5: Confirm promotion for summary cart
    Service->>CartAppService: getPromotionCartTotal(sessionIdBase)
    CartAppService-->>Service: resPromotionOfCartSum
    Service->>OrderUtilsService: confirmPromotionForFamilyPackage(orderCodes, promotion, orderInfo)
    OrderUtilsService-->>Service: Success

    Note over Service: Step 6: Update order status (Parallel)
    loop For each order
        Service->>OMSService: updateStatusOrderDepositFP(payload)
        OMSService-->>Service: Success
    end

    Note over Service: Step 7: Create closed examination tickets
    Service->>Service: createManyTicket(ticketInfor, orderCodes, arrOrderInfo)
    Service-->>Service: arrTicket (closed tickets)

    Note over Service: 🆕 Step 8: Check channel and early return for RSA_ECOM
    alt RSA_ECOM Channel (order-channel = '7')
        Service-->>FE: HTTP 200 OK (Early Return)
        Note over Service,FE: { isSuccess, ticketInfor: arrTicket } - Only closed tickets
    else RSA Channels (order-channel = '14', '2')
        Note over Service: Step 9: Create open examination tickets
        Service->>Service: handleLogicUpdateStatusOrder(body, arrOrderInfo)
        Service-->>Service: response with open tickets

        Note over Service: Step 10: Debit promotion for open tickets
        loop For each open ticket
            Service->>OrderUtilsService: debitPromotion(orderCode, ticketCode)
            OrderUtilsService-->>Service: Success
        end

        Note over Service: Step 11: Cancel deposits (Background)
        loop For each cancelDepositDto
            Service->>CamundaApiService: cancelDeposit(cancelDepositDto)
        end

        Note over Service: Step 12: Clean up Redis
        Service->>OrderRedisService: deleteIncentiveOrderRedis(arrLCVId)
        OrderRedisService-->>Service: Success

        Service-->>FE: HTTP 200 OK
        Note over Service,FE: { isSuccess, ticketInfor: [...] } - Both closed and open tickets
    end
```

### 4.2 Các bước chính

1. **Lấy thông tin phiếu khám từ Redis**: Lấy ticket info từ cache hoặc sử dụng từ request
2. **Lấy thông tin đơn hàng và cart**: Parallel call để lấy order info và cart confirm
3. **Xác nhận khuyến mãi cho cart con**: Confirm promotion cho từng cart riêng lẻ
4. **Lấy thông tin incentive**: Lấy dữ liệu khuyến mãi từ Redis
5. **Xác nhận khuyến mãi tổng**: Confirm promotion cho cart tổng hợp
6. **Cập nhật trạng thái đơn hàng**: Update status thành FinishDeposit
7. **Tạo phiếu khám đóng + 🆕 Debit promotion**: Tạo closed examination tickets và xử lý debit promotion (hỗ trợ cả RSA và RSA_ECOM)
8. **🆕 Kiểm tra channel và return sớm**: **RSA_ECOM chỉ cần phiếu khám đóng**
9. **Tạo phiếu khám mở**: Chỉ cho RSA channels (không phải RSA_ECOM)
10. **Hủy cọc cũ**: Cancel deposits của các đơn hàng cũ (background)
11. **Dọn dẹp Redis**: Xóa incentive data khỏi Redis

### 4.3 Chi tiết quy trình

#### Bước 1: Lấy thông tin phiếu khám

```typescript
const ticketCreateCache = await this.redisService.pipelineGet<CreateTicketDto>(
  orderCodes?.map((orderCode) => `${TICKET_CREATE_KEY}:${orderCode}`),
);
const ticketInfor = ticketCreateCache?.length ? ticketCreateCache : body?.ticketInfor || [];
```

#### Bước 2: Lấy thông tin đơn hàng và cart

```typescript
const [{ orders: arrOrderInfo }, ...cartConfirmListOrder] = await Promise.all([
  this.omsService.getListOrderES({ orderCode: orderCodes }),
  ...orderCodes?.map((orderCode) => this.cartAppService.getCartConfirmByOrderCode(orderCode)),
]);
```

#### Bước 3: Xác nhận khuyến mãi

```typescript
// Confirm promotion cho từng cart
await Promise.all(
  cartConfirmListOrder?.map((itemCartConfirm) => {
    const findOrderData = arrOrderInfo?.find(
      (itemOrderInfo) => itemOrderInfo?.orderCode === itemCartConfirm?.orderCode,
    );
    return this.orderUtilsService.confirmPromotion(itemCartConfirm?.orderCode, itemCartConfirm, findOrderData || null);
  }),
);
```

#### Bước 4: Cập nhật trạng thái đơn hàng

```typescript
const payload: UpdateOrderStatusFamilyPackageDto = {
  orderCode: order?.orderCode,
  modifiedBy: employeeStep5?.employeeCode || modifiedBy,
  modifiedByName: employeeStep5?.employeeName || modifiedByName,
  orderStatus: OrderStatus.FinishDeposit,
  ecomDisplay: EcomDisplay.AtShop,
  orderType: order.orderType,
  shopCode: this.req.headers?.['shop-code'] as string,
  paymentRequestId: order.paymentRequestCode,
  paymentStatus: PaymentOnlineStatus.Complete,
};
```

#### 🆕 Bước 5: Kiểm tra channel và xử lý khác biệt

```typescript
// Tạo phiếu khám đóng cho tất cả channels
const arrTicket: CreateTicketRes[] = await this.createManyTicket(ticketInfor, orderCodes, arrOrderInfo);

// 🆕 RSA_ECOM: Chỉ trả về phiếu khám đóng, không tạo phiếu khám mở
if (OrderChannels.RSA_ECOM.includes((this.req.headers?.['order-channel'] as string) || '')) {
  return { isSuccess, ticketInfor: arrTicket };
}

// RSA: Tiếp tục tạo phiếu khám mở
const arrTicketHaveIndications = _.cloneDeep(arrTicket)?.filter((ticket) => ticket?.indications?.length);
if (!arrTicketHaveIndications?.length) return { isSuccess, ticketInfor: arrTicket };

// Tạo phiếu khám mở và xử lý debit promotion...
```

#### 🆕 Bước 6: Debit promotion cho phiếu khám (hỗ trợ cả RSA và RSA_ECOM)

```typescript
// Trong createManyTicket function - áp dụng cho cả RSA và RSA_ECOM
const ticketInforPayload = await this.createAdjustTicketDtoV2(createTicketDto, orders);
for (const entry of ticketInforPayload) {
  await this.orderUtilsService.debitPromotion(entry?.orderCode, entry?.ticketCode);
}
return this.examinationCoreService.createTicket(ticketInforPayload);

// 🆕 Logic debitPromotion đã được cập nhật để hỗ trợ RSA_ECOM:
// - RSA: Sử dụng shopCode từ header
// - RSA_ECOM: Sử dụng shopCode từ order data khi header không có hoặc là RSA_ECOM channel
```

## 5. Validation Rules

### 5.1 Validation đầu vào

- `orderCodes` phải là array không rỗng
- Tất cả orderCode phải tồn tại trong hệ thống
- Đơn hàng phải ở trạng thái có thể cập nhật

### 5.2 Validation phiếu khám

- Mỗi phiếu khám phải có ít nhất 1 indication hoặc schedule
- Thông tin chỉ định phải hợp lệ
- Lịch hẹn không được trùng lặp

### 5.3 Business Rules

- Chỉ cập nhật đơn hàng gói gia đình (orderType = 8)
- Đơn hàng phải đã thanh toán cọc
- Không được cập nhật đơn hàng đã hủy

## 6. Dependencies

### 6.1 Services sử dụng

- `OMSService`: Quản lý đơn hàng
- `CartAppService`: Quản lý giỏ hàng và khuyến mãi
- `OrderUtilsService`: Utilities cho đơn hàng
- `CamundaApiService`: Workflow management
- `OrderRedisService`: Quản lý Redis cho đơn hàng
- `RedisService`: Cache service

### 6.2 External APIs

- OMS Service: `getListOrderES`, `updateStatusOrderDepositFP`
- Cart Service: `getCartConfirmByOrderCode`, `getPromotionCartTotal`
- Camunda API: `cancelDeposit`

## 7. Error Handling

### 7.1 Các lỗi thường gặp

- **400 Bad Request**: Dữ liệu đầu vào không hợp lệ
- **403 Forbidden**: Phiếu khám thiếu thông tin chỉ định
- **404 Not Found**: Không tìm thấy đơn hàng
- **500 Internal Server Error**: Lỗi hệ thống

### 7.2 Specific Errors

- `RSA_EXAMINATION_TICKET_INDICATION_EMPTY`: Phiếu khám thiếu chỉ định
- Order not found: Không tìm thấy đơn hàng
- Invalid order status: Trạng thái đơn hàng không hợp lệ

## 8. Notes

### 8.1 Đặc điểm quan trọng

- Xử lý đồng thời nhiều đơn hàng trong cùng 1 transaction
- Tự động confirm promotion cho cả cart riêng lẻ và cart tổng
- Hỗ trợ lấy ticket info từ Redis cache
- Background processing cho cancel deposit

### 8.2 Performance

- Sử dụng `Promise.all` để xử lý đồng thời
- Pipeline Redis operations để tối ưu performance
- Concurrent promise execution cho update operations

### 8.3 Security

- Require authentication token
- Validate shop-code header
- Check employee permissions through modifiedBy

### 8.4 Business Logic

- Chỉ áp dụng cho đơn hàng gói gia đình (Family Package)
- Tự động xử lý incentive và cashback
- Workflow integration với Camunda

## 9. 🆕 Thay đổi mới - Hỗ trợ RSA_ECOM

### 9.1 🎯 Mục đích thay đổi

- **Trước đây**: API chỉ hỗ trợ RSA channels, tạo cả phiếu khám đóng và mở
- **Bây giờ**: Hỗ trợ thêm RSA_ECOM channel với logic đơn giản hóa
- **Lý do**: RSA_ECOM chỉ cần phiếu khám đóng để hoàn tất đơn hàng

### 9.2 🔄 Logic thay đổi chính

#### **Channel Detection & Early Return**

```typescript
// 🆕 Kiểm tra channel và return sớm cho RSA_ECOM
if (OrderChannels.RSA_ECOM.includes((this.req.headers?.['order-channel'] as string) || '')) {
  return { isSuccess, ticketInfor: arrTicket }; // Chỉ trả về phiếu khám đóng
}
```

#### **Debit Promotion Enhancement**

```typescript
// 🆕 Logic trong debitPromotion đã được cập nhật để hỗ trợ RSA_ECOM
let shopCode = this.req.headers?.['shop-code'] as string;
if (!shopCode || OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string)) {
  shopCode = orderData.shopCode; // Lấy shopCode từ order data cho RSA_ECOM
}
```

### 9.3 📊 So sánh luồng xử lý

| **Aspect**           | **RSA Channels ('14', '2')** | **🆕 RSA_ECOM Channel ('7')** |
| -------------------- | ---------------------------- | ----------------------------- |
| **Phiếu khám đóng**  | ✅ Tạo                       | ✅ Tạo                        |
| **Phiếu khám mở**    | ✅ Tạo                       | ❌ Không tạo                  |
| **Debit promotion**  | ✅ Xử lý (cho phiếu khám mở) | ✅ Hỗ trợ (shopCode từ order) |
| **Journey creation** | ✅ Tạo journey mới           | ❌ Không cần                  |
| **Cart merging**     | ✅ Merge cart                | ❌ Không cần                  |
| **Performance**      | Chậm hơn (nhiều bước)        | ⚡ Nhanh hơn (ít bước)        |
| **Response time**    | ~3-5s                        | ~1-2s                         |

### 9.4 🎯 Lợi ích của thay đổi

#### **Cho RSA_ECOM:**

- **Tốc độ nhanh hơn**: Bỏ qua các bước không cần thiết
- **Đơn giản hóa**: Chỉ tạo phiếu khám đóng
- **Ổn định hơn**: Ít dependency, ít lỗi

#### **Cho RSA:**

- **Giữ nguyên logic**: Không ảnh hưởng đến luồng hiện tại
- **Backward compatible**: Hoàn toàn tương thích ngược

### 9.5 🔧 Chi tiết thay đổi `debitPromotion`

#### **Trước đây:**

```typescript
// Chỉ hỗ trợ RSA channels với shopCode từ header
let shopCode = this.req.headers?.['shop-code'] as string;
```

#### **🆕 Bây giờ:**

```typescript
// Hỗ trợ cả RSA và RSA_ECOM với logic shopCode linh hoạt
let shopCode = this.req.headers?.['shop-code'] as string;
if (!shopCode || OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string)) {
  shopCode = orderData.shopCode; // Fallback cho RSA_ECOM
}
```

#### **Logic mới:**

1. **RSA Channels**: Sử dụng `shopCode` từ header như trước
2. **RSA_ECOM Channel**:
   - Nếu không có `shopCode` trong header → Lấy từ `orderData.shopCode`
   - Nếu là RSA_ECOM channel → Luôn lấy từ `orderData.shopCode`

#### **Lý do thay đổi:**

- **RSA_ECOM** thường không có `shop-code` header hoặc có giá trị khác
- **Order data** luôn chứa `shopCode` chính xác từ khi tạo đơn
- **Đảm bảo tính nhất quán** trong việc xử lý inventory và promotion
