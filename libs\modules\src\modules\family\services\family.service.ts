import { Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import { DSMSService } from 'vac-nest-dsms';
import { FamilyService as FamilyCoreService, FamilyProfileDetails, GetListTitleDto } from 'vac-nest-family';
import { Gender, TCQGIntegrationService } from 'vac-nest-tcqg-integration';
import { ReqSearchDto, ReqSearchFamilyDto, ReqSearchKeywordDto } from '../dto';
import { validateVietnamesePhoneNumber } from '../functions';
import { FamilyUtilsService } from './family-utils.services';

@Injectable()
export class FamilyService {
  constructor(
    private readonly familyCoreService: FamilyCoreService,
    private readonly tcqgIntegrationService: TCQGIntegrationService,
    private readonly dmsService: DSMSService,
    private readonly familyUtilsService: FamilyUtilsService,
  ) {}

  /**
   * @TODO tìm kiếm theo family và tiêm chủng quốc gia
   * @returns
   */
  async searchFamily(query: ReqSearchKeywordDto) {
    /**
     * @TODO gọi api family
     * group data family theo profileID
     */
    const { keyword, ...payload } = query;

    // case: chỉ nhập keyword => sdt, mã tiêm chủng, họ tên
    // call api family
    const { items } = await this.familyCoreService.searchPersonFamily({ keyword });

    /**
     * Nếu không có trong family thì gọi lên tiêm chủng quốc gia
     */
    if (items.length <= 0) {
      // kiểm tra keyword nhập vào
      // nếu valid phone thì truyền vào phone
      // nếu invalid phone thì truyền vào mã tiêm chủng quốc gia
      // chưa cover search theo name ==> khi search name đẩy vào tiem chung quoc gia
      // check case: search basic

      if (keyword) {
        if (validateVietnamesePhoneNumber(keyword)) {
          payload.phone = keyword;
        } else {
          payload.nationalVaccineCode = keyword;
        }
      }

      // gọi api tiêm chủng quốc gia để lấy thông tin
      // const payloadReqTCQG: ReqSearchTCQG = {
      //   ...payload,
      // };

      // gọi api tiêm chủng quốc gia để lấy thông tin
      let data = null;
      try {
        data = await this.tcqgIntegrationService.searchCustomer({ ...payload, source: 1 });
      } catch (error) {}

      // vinhdq6: Update check nếu tìm thấy 0 hoặc > 1 kết quả thì đều trả về 0
      if (data?.items.length == 1) {
        const personMapTcqg = await this.mapPersonFromTCQG(data?.items);

        return {
          totalCount: personMapTcqg.length,
          items: personMapTcqg,
        };
      }

      return {
        totalCount: 0,
        items: [],
      };
    }

    return {
      totalCount: items.length || 0,
      items,
    };
  }

  /**
   * @TODO tìm nâng cao tìm kiếm theo family và tiêm chủng quốc gia
   * @returns
   */
  async searchFamilyAdvance(payload: ReqSearchDto) {
    // case: chỉ nhập keyword => sdt, mã tiêm chủng, họ tên
    const payloadReqFamily: ReqSearchFamilyDto = {};

    // case: search nâng cao khi nhập fullname
    if (payload.fullName) {
      payloadReqFamily.Name = payload.fullName;
    }

    // call api family
    const { items } = await this.familyCoreService.searchPerson(payloadReqFamily);

    /**
     * Nếu không có trong family thì gọi lên tiêm chủng quốc gia
     */
    if (items.length <= 0) {
      // case: search nâng cao khi có tỉnh thành/ quận huyện/ phường xã
      if (payload?.provinceCode && !payload?.districtCode && !payload?.wardCode) {
        const { provinceCode } = await this.getNationalVaccinationAreas(payload);
        payload.provinceCode = provinceCode;
      }
      if (payload?.provinceCode && payload?.districtCode && !payload?.wardCode) {
        const { provinceCode, districtCode } = await this.getNationalVaccinationAreas(payload);
        payload.provinceCode = provinceCode;
        payload.districtCode = districtCode;
      }
      if (payload?.provinceCode && payload?.districtCode && payload?.wardCode) {
        const { provinceCode, districtCode, wardCode } = await this.getNationalVaccinationAreas(payload);
        payload.provinceCode = provinceCode;
        payload.districtCode = districtCode;
        payload.wardCode = wardCode;
      }

      // gọi api tiêm chủng quốc gia để lấy thông tin
      // const payloadReqTCQG: ReqSearchAdvanceTCQG = {
      //   ...payload,
      // };

      let data = null;
      try {
        data = await this.tcqgIntegrationService.searchCustomer({ ...payload, source: 1 });
      } catch (error) {}

      if (data?.items.length == 1) {
        const personMapTcqg = await this.mapPersonFromTCQG(data?.items);

        return {
          totalCount: personMapTcqg.length,
          items: personMapTcqg,
        };
      }

      return {
        totalCount: 0,
        items: [],
      };
    }

    return {
      totalCount: items.length || 0,
      items: items,
    };
  }

  /**
   * @TODO lấy mã vùng của tiêm chủng quốc gia
   * @param params
   * @returns
   */
  private async getNationalVaccinationAreas(params: ReqSearchDto) {
    const { provinceCode = '', districtCode = '', wardCode = '' } = params;
    /**
     * get district to province
     */
    let infortcqg = {
      provinceCode: 0,
      districtCode: 0,
      wardCode: 0,
    };

    // case 1: chỉ truyền vào provinceCode từ family FE
    // gọi tiêm chủng quốc gia lấy tcqgProvince
    if (provinceCode && districtCode === '' && wardCode === '') {
      const { provinces } = await this.dmsService.getProvince();
      const { tcqgProvince } = provinces.find(({ code }) => parseInt(code) === provinceCode);

      infortcqg = {
        ...infortcqg,
        provinceCode: parseInt(tcqgProvince),
      };
    }

    // case 2: chỉ truyền vào provinceCode và districtCode từ family FE
    // gọi tiêm chủng quốc gia lấy tcqgProvince và tcqgDistrict
    if (provinceCode && districtCode && wardCode === '') {
      const { districts } = await this.dmsService.getDistrict({ province: provinceCode.toString() });
      const { tcqgDistrict, tcqgProvince } = districts?.find(({ code }) => parseInt(code) === districtCode);

      infortcqg = {
        ...infortcqg,
        districtCode: parseInt(tcqgDistrict) || 0,
        provinceCode: parseInt(tcqgProvince) || 0,
      };
    }

    // case 3: chỉ truyền vào provinceCode và districtCode, wardCode từ family FE
    // gọi tiêm chủng quốc gia lấy tcqgProvince và tcqgDistrict, tcqgWard
    if (provinceCode && districtCode && wardCode) {
      const { wards } = await this.dmsService.getWard({ district: districtCode.toString() });
      const { tcqgDistrict, tcqgProvince, tcqgWard } = wards.find(({ code }) => parseInt(code) === wardCode);
      infortcqg = {
        districtCode: parseInt(tcqgDistrict) || 0,
        provinceCode: parseInt(tcqgProvince) || 0,
        wardCode: parseInt(tcqgWard) || 0,
      };
    }

    return infortcqg;
  }

  /**
   * @TODO map data từ tiêm chủng quốc gia về cho FE
   * @param items
   * @returns
   */
  async mapPersonFromTCQG(items: any[]) {
    let mapPersonTCQG = [];
    if (_.isEmpty(items)) return mapPersonTCQG;

    // const titles = await this.familyCoreService.getListTitle();

    items.map((tcqgPerson) => {
      // const familyDetails = this.getFamilyDetailInfos(tcqgPerson, titles);

      const personTCQG = {
        id: null,
        lcvId: null,
        nationalVaccineCode: tcqgPerson?.maDoiTuong,
        nationalVaccineId: tcqgPerson?.doiTuong2Id,
        name: tcqgPerson?.hoTen,
        gender: tcqgPerson?.gioiTinh ?? 2,
        dateOfBirth: tcqgPerson?.ngaySinh,
        phoneNumber: tcqgPerson?.soDienThoai,
        isSearchHeader: true,
        personContacts: [],
        // familyProfileDetails: [...familyDetails],
        IsSearchTcqg: true,
      };

      mapPersonTCQG = [...mapPersonTCQG, personTCQG];
    });
    return mapPersonTCQG;
  }

  getFamilyDetailInfos(tcqgPerson: any, titles: GetListTitleDto[]) {
    const guardians = this.getInfoGuardian(tcqgPerson, titles);
    let familyDetails: FamilyProfileDetails[] = [];
    if (guardians) {
      familyDetails = guardians.map((item) => {
        const familyDetail: FamilyProfileDetails = {
          name: item.name,
          phoneNumber: item.phoneNumber,
          gender: item.gender,
          titleId: item.titleId,
          titleName: item.titleName,
          personId: null,
          familyProfileId: null,
          lcvId: null,
          dateOfBirth: item.dateOfBirth,
        };
        return familyDetail;
      });
    }
    return familyDetails;
  }

  getInfoGuardian(tcqgPerson: any, titles: GetListTitleDto[]) {
    const listGuardian = [];
    let isHaveMomContact = false;
    let isHaveDadContact = false;
    const tenMe = this.removeVietnameseTones(tcqgPerson.tenMe);
    const tenBo = this.removeVietnameseTones(tcqgPerson.tenBo);
    const tenNguoiBaoHo = this.removeVietnameseTones(tcqgPerson.tenNguoiBaoHo);

    if (tcqgPerson.tenMe || tcqgPerson.dienThoaiMe) {
      const { id, name } = titles.find((x) => x.name == 'Mẹ');
      const namSinhMe = tcqgPerson?.namSinhMe && tcqgPerson?.namSinhMe != '0' ? `01-01-${tcqgPerson?.namSinhMe}` : null;
      const item = {
        gender: Gender.Female,
        titleId: id,
        titleName: name,
        dateOfBirth: namSinhMe,
        name: tcqgPerson?.tenMe ?? null,
        phoneNumber: tcqgPerson?.dienThoaiMe ?? null,
      };
      listGuardian.push(item);
      isHaveMomContact = true;
    }
    if (tcqgPerson.tenBo || tcqgPerson.dienThoaiBo) {
      const { id, name } = titles.find((x) => x.name == 'Bố');
      const namSinhBo = tcqgPerson?.namSinhBo && tcqgPerson?.namSinhBo != '0' ? `01-01-${tcqgPerson?.namSinhBo}` : null;
      const item = {
        gender: Gender.Male,
        titleId: id,
        titleName: name,
        dateOfBirth: namSinhBo,
        name: tcqgPerson?.tenBo ?? null,
        phoneNumber: tcqgPerson?.dienThoaiBo ?? null,
      };
      listGuardian.push(item);
      isHaveDadContact = true;
    }
    if (tcqgPerson.tenNguoiBaoHo || tcqgPerson.dienThoaiNguoiBaoHo) {
      if (tcqgPerson.tenMe) {
        if (isHaveMomContact && tenNguoiBaoHo == tenMe) {
          if (!listGuardian.find((x) => x.name == tcqgPerson.tenMe).phoneNumber)
            listGuardian.find((x) => x.name == tcqgPerson.tenMe).phoneNumber = tcqgPerson.dienThoaiNguoiBaoHo;
        }
      }
      if (tcqgPerson.tenBo) {
        if (isHaveDadContact && tenNguoiBaoHo == tenBo) {
          if (!listGuardian.find((x) => x.name == tcqgPerson.tenBo).phoneNumber)
            listGuardian.find((x) => x.name == tcqgPerson.tenBo).phoneNumber = tcqgPerson.dienThoaiNguoiBaoHo;
        }
      }
      if (
        (tenNguoiBaoHo != tenMe && tenNguoiBaoHo != tenBo) ||
        (tcqgPerson.dienThoaiMe != tcqgPerson.dienThoaiNguoiBaoHo &&
          tcqgPerson.dienthoaiBo != tcqgPerson.dienThoaiNguoiBaoHo)
      ) {
        const { id, name } = titles.find((x) => x.name == 'Khác');
        const namSinhNguoiBaoHo =
          tcqgPerson?.namSinhNguoiBaoHo && tcqgPerson?.namSinhNguoiBaoHo != '0'
            ? `01-01-${tcqgPerson?.namSinhNguoiBaoHo}`
            : null;
        const item = {
          gender: Gender.Other,
          titleId: id,
          titleName: name,
          dateOfBirth: namSinhNguoiBaoHo,
          name: tcqgPerson?.tenNguoiBaoHo ?? null,
          phoneNumber: tcqgPerson?.dienThoaiNguoiBaoHo ?? null,
        };
        listGuardian.push(item);
      }
    }
    return listGuardian;
  }

  removeVietnameseTones(str) {
    if (_.isEmpty(str) || str == null) {
      return '';
    }
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/đ/g, 'd');
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A');
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E');
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O');
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U');
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y');
    str = str.replace(/Đ/g, 'D');
    // Some system encode vietnamese combining accent as individual utf-8 characters
    // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, ' ');
    str = str.trim();
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g, ' ');
    return str.toLowerCase();
  }
}
