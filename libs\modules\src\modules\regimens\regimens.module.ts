import { Module } from '@nestjs/common';
import { RegimensController } from './controllers/regimens.controller';
import { RegimensService } from './services/regimens.service';
import { RegimenModule as RegimenModuleCore } from 'vac-nest-regimen';
import { PIMAppModule } from 'vac-nest-pim-app';
import { VacOrderInjectionModule } from 'vac-nest-order-injection';
import { ScheduleEngineAppModule } from 'vac-nest-schedule-engine-app';
import { ScheduleCoreModule } from 'vac-nest-schedule';
import { VacHistoryModule } from 'vac-nest-history';
import { RegimentCloseController } from './controllers/regimen-close.controller';
import { IMSModule } from 'vac-nest-ims';
import { RegimenCloseService } from './services/regimen-close.service';
import { VaccinePricingTableService } from './services/vaccine-pricing-table.service';
import { VaccinePricingTableController } from './controllers/vaccine-pricing-table.controller';
import { PromotionModule } from 'vac-nest-promotion';
import { RedisModule } from '@shared';
import { CartAppModule } from 'vac-nest-cart-app';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { OsrModule } from 'vac-nest-osr';
import { RewardApiModule } from '@frt/nestjs-api/dist/reward-api';

@Module({
  imports: [
    RegimenModuleCore,
    PIMAppModule,
    VacOrderInjectionModule,
    ScheduleEngineAppModule,
    ScheduleCoreModule,
    VacHistoryModule,
    IMSModule,
    PromotionModule,
    RedisModule,
    CartAppModule,
    ExaminationCoreModule,
    OsrModule,
    RewardApiModule,
  ],
  controllers: [RegimensController, RegimentCloseController, VaccinePricingTableController],
  providers: [RegimensService, RegimenCloseService, VaccinePricingTableService],
  exports: [RegimensService, RegimenCloseService, VaccinePricingTableService],
})
export class RegimensModule {}
