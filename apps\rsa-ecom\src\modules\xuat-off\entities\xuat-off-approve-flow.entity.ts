import { AbstractEntity } from '@shared/database/typeorm/abtract.entity';
import { Column, Entity } from 'typeorm';

@Entity({ name: 'xuat_off_approve_flow' })
export class XuatOffApproveFlowEntity extends AbstractEntity<XuatOffApproveFlowEntity> {
  @Column({ name: 'creator_role' })
  creatorRole?: string;

  @Column({ name: 'approver_role' })
  approverRole?: string;

  @Column({ name: 'status_xo', type: 'int' })
  statusXO?: number;

  @Column({ name: 'is_active', default: true })
  isActive?: boolean;
}
