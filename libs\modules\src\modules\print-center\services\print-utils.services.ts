import { Inject, Injectable } from '@nestjs/common';
import { JSONPath } from 'jsonpath-plus';
import _ from 'lodash';
import moment from 'moment';
import { CartAppService, OrderAttribute } from 'vac-nest-cart-app';
import { District, DSMSService, Province, Ward } from 'vac-nest-dsms';
import {
  ExaminationCoreService,
  HealthCheck,
  HealthCheckQuestion,
  PaymentType,
  TicketDetailRes,
} from 'vac-nest-examination';
import { FamilyService, GetPersonByIdRes } from 'vac-nest-family';
import { FamilyPackageService } from 'vac-nest-family-package';
import { InsideService, ShopEmployee } from 'vac-nest-inside';
import { InvoiceAppService, InvoiceDetailRes } from 'vac-nest-invoice-app';
import { JourneyService, OrdersInfo } from 'vac-nest-journey';
import { MonitorCoreService } from 'vac-nest-monitor';
import { CreateOrderRes, EmployeeStep, OMSService, OrderStatus } from 'vac-nest-oms';
import {
  getDepositDetailAmount,
  getDepositedAmountByMethods,
  GetPaymentHistoryESLibResponse,
  PaymentGatewayService,
} from 'vac-nest-payment-gateway';
import { GetListProductBySkuRes, PIMAppService } from 'vac-nest-pim-app';
import { PRINT_TYPE, PrintCenterService as PrintCenterCoreService, PrintCenterRes } from 'vac-nest-print-center';
import { RegimenItem, RegimenService } from 'vac-nest-regimen';
import { BIOSIGNAL_FIELD, TemplateType } from '../constants';
import {
  PreorderData,
  PrintBillDataFamilyPackageRes,
  PrintBillDataRes,
  PrintInvoiceDto,
  PrintInvoiceFamilyPackageDto,
  ProductData,
  ProductDataFamilyPackageDto,
  PromotionFamilyPackageDto,
} from '../dto';
import { convertNumberToVietnamese, getFullAddress, getPaymentMethod, masterDataInjectionAndRoute } from '../functions';
import { CheckRuleNoteIndicationRes, OrderRuleEngineService } from 'vac-nest-order-rule-engine';
import { OsrService } from 'vac-nest-osr';
import { RedisService, SkuHard, translateVietnameseAccents } from '@shared';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class PrintUtilsService {
  constructor(
    private readonly printCenterService: PrintCenterCoreService,
    private readonly omsService: OMSService,
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly insideService: InsideService,
    private readonly familyService: FamilyService,
    private readonly invoiceAppService: InvoiceAppService,
    private readonly journeyService: JourneyService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly dsmsService: DSMSService,
    private readonly regimenService: RegimenService,
    private readonly monitorCoreService: MonitorCoreService,
    private readonly pimCoreService: PIMAppService,
    private readonly orderRuleEngineService: OrderRuleEngineService,
    private readonly osrService: OsrService,
    private readonly familyPackageService: FamilyPackageService,
    private readonly redisService: RedisService,
    private readonly cartAppService: CartAppService,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  async printInvoice(printInvoiceDto: PrintInvoiceDto, shopCode?: string, isBill?: boolean): Promise<PrintCenterRes> {
    /**
     * @TODO
     * Gọi qua OMS lấy thông tin order
     *    Lấy điểm của khách hàng từ Loyalty App
     *    Lấy thông tin ở Payment để tính voucher, cash
     *    Lấy thông tin nhân viên của shop từ Inside
     *    Lấy một số thông tin orderSummaryData từ Pos
     *    Lấy thông tin order từ Vaccine Order
     *        Lấy thông tin khách hàng từ Family
     *    Lấy thông xuất xứ, tên bệnh, dosage từ phiếu khám
     * => Tổng hợp thông tin gửi qua Print Center
     */

    const { orderCode, printType, isRePrint, totalPriceReturn, totalPriceCustomerPay, ticketCode } = printInvoiceDto;

    const orderInfor = await this.omsService.getOneOrder(orderCode);

    //  Đơn trả chậm => Lưu thêm ticketCode vào key S3
    const keyTicketCode = ticketCode && orderInfor?.orderAttribute === OrderAttribute.TRA_CHAM ? `_${ticketCode}` : '';
    const storageName =
      orderInfor?.shopCode + '/' + orderInfor?.orderCode + keyTicketCode + '_' + orderInfor?.orderStatus;

    // isRePrint = true thì lấy thông tin từ s3 để in
    // nếu không tồn tại file thì isRePrint = false và call lại các thông tin bên dưới
    if (isRePrint) {
      const printData: PrintBillDataRes = {
        ignorePrint: printInvoiceDto?.printerInfo?.ignorePrint,
        printType: printType,
        printerInfo: printInvoiceDto?.printerInfo,
        printData: {
          isRePrint: isRePrint,
          shopCode: shopCode,
          orderCode: orderInfor?.orderCode,
          orderStatus: orderInfor?.orderStatus,
          storageName,
        },
      };
      const printInvoice = await this.printCenterService.vaccineBillV2(printData);
      if (!!printInvoice?.url) return printInvoice;
    }

    let groupCode = '';
    let groupName = '';
    if (isBill) {
      const group = await this.getGroupInfoFromOrder(orderInfor);
      groupCode = group?.groupCode || '';
      groupName = group?.groupName || '';
    }

    let getJourney: OrdersInfo = {};
    if (orderInfor?.orderAttribute === OrderAttribute.TRA_CHAM) {
      if (!isBill) {
        getJourney = await this.journeyService.getOrderInfoByOrderOrTicket({
          orderCode: orderCode,
          orderType: 8,
        });
      } else if (ticketCode) {
        getJourney = await this.journeyService.getOrderInfoByOrderOrTicketDB({
          ticketCode: ticketCode,
          orderType: 8,
        });
        if (
          getJourney?.orderCodeOld &&
          getJourney?.orderCodeOld === orderCode &&
          getJourney?.isFinishPayment &&
          orderInfor?.orderStatus !== OrderStatus.Completed
        ) {
          orderInfor.totalDeposit = 0;
        }
        if (getJourney?.orderCodeOld && getJourney?.orderCodeOld !== orderCode) {
          const orderOldInfo = await this.omsService.getOneOrder(getJourney?.orderCodeOld);
          if (orderOldInfo?.orderStatus !== OrderStatus.Completed && getJourney?.isFinishPayment) {
            orderInfor.totalDeposit = 0;
          }
        }
      }
    }

    const caseInstance = {
      case1: orderInfor?.orderAttribute === OrderAttribute.TRA_CHAM && !getJourney?.orderCodeOld,
      case3: getJourney?.isFinishPayment,
    };

    if (caseInstance?.case1 && !getJourney?.partialPaymentAmount) {
      const arrPaymentCodePartial: string[] = _.flattenDeep(
        _.uniq(orderInfor?.orderPaymentCreate?.map((e) => e.paymentCode)),
      );

      const getListPayment = await this.paymentGatewayService.getPaymentRedis({
        paymentCodes: arrPaymentCodePartial,
      });
      const totalDaThanhToan2 = getListPayment?.reduce((acc, cur) => acc + getDepositDetailAmount(cur.detail), 0);
      getJourney.partialPaymentAmount = Math.max(
        totalDaThanhToan2 - orderInfor?.serviceFee - orderInfor?.totalDeposit,
        0,
      );
      getJourney.totalBill = Math.max(totalDaThanhToan2, 0);
    }

    let orderCodeVirtual = '';

    if (orderInfor?.orderAttribute === OrderAttribute.TRA_CHAM) {
      const searchOrder = await this.omsService.searchOrderForVaccine({ orderCode });

      const listOrderVirtual = searchOrder?.orders?.filter((order) => order?.orderAttribute === 5);

      orderCodeVirtual = JSONPath({
        path: '$.[*].details[*].detailAttachments[*]',
        json: listOrderVirtual,
      })?.find((detail) => detail?.ticketCode === ticketCode)?.orderCode;
    }

    const [paymentData] = await this.paymentGatewayService.getPaymentRedis({
      paymentCodes: [orderInfor?.paymentRequestCode],
    });

    const employeeCodeStep2 = _.orderBy(
      orderInfor?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeConfirmAndCollectMoney),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const employeeInfoStep5 = _.orderBy(
      orderInfor?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
      ['modifiedDate', 'desc'],
    )?.at(0);

    let invoiceData: InvoiceDetailRes = null;
    try {
      invoiceData = await this.invoiceAppService.getInvoiceByOrderCode(orderCode);
    } catch (error) {
      invoiceData = null;
    }

    let getTicket: TicketDetailRes = null;
    if (printInvoiceDto?.ticketCode) {
      getTicket = await this.examinationCoreService.getTicket({ ticketCode: printInvoiceDto?.ticketCode });
    }

    const lcvId = _.uniq(
      _.compact(
        JSONPath({
          path: '$.details[*].detailAttachments[*].personIdSub',
          json: orderInfor,
        }),
      ),
    );

    let customerInfor: GetPersonByIdRes = null;
    try {
      if (getTicket?.lcvId || lcvId) {
        customerInfor = await this.familyService.getFamilyByLcvId({ lcvId: getTicket?.lcvId || lcvId });
      } else customerInfor = null;
    } catch (error) {
      customerInfor = null;
    }

    const arrPaymentCode = _.uniq(
      _.compact(
        orderInfor.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );

    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGatewayService.getPaymentRedis({
      paymentCodes: arrPaymentCode,
    });

    // Improvement Trả chậm khác shop, đơn trả chậm lấy thông tin nhân viên trong payment
    let createBy = '';
    const paymentDetail: Array<any> = JSONPath({
      path: '$[*].detail[*][*]',
      json: arrPaymentES,
    });

    paymentDetail?.forEach((entry) => {
      if (entry?.transaction?.referenceId === printInvoiceDto?.ticketCode) {
        createBy = entry?.transaction?.createdBy;
      } else if (entry?.referenceId === printInvoiceDto?.ticketCode) {
        createBy = entry?.createdBy;
      }
    });

    //filter theo shop code
    const employeeCode =
      orderInfor?.orderAttribute === OrderAttribute.TRA_CHAM
        ? createBy || employeeInfoStep5?.employeeCode || employeeCodeStep2?.employeeCode || orderInfor?.createdBy
        : employeeInfoStep5?.employeeCode || employeeCodeStep2?.employeeCode || orderInfor?.createdBy;
    const listEmployeeInfor = await this.insideService.getListShopByEmployee(employeeCode);
    const shopCodeInBill = getTicket?.shopCode || orderInfor?.shopCode;
    const employeeInfor = listEmployeeInfor?.shops?.find((shop) => shop?.shopCode === shopCodeInBill);

    let voucherDiscount = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
    }, 0);

    let arrReferenceVoucher: Array<any> = [];
    if (orderInfor?.orderAttribute === 4) {
      arrReferenceVoucher = JSONPath({
        path: `$.[*].detail.vouchersAll[*].[?(@ && @.referenceId === '${getTicket?.ticketCodeOld || ticketCode}' )]`,
        json: arrPaymentES,
      });
      if (!arrReferenceVoucher?.length) {
        arrReferenceVoucher = JSONPath({
          path: `$.[*].detail.vouchersAll[*].[?(@ && @.referenceId === null )]`,
          json: arrPaymentES,
        });
      }
      voucherDiscount = 0;
    } else {
      // đơn từng phần
      if (getTicket?.orderCodeOld) {
        arrReferenceVoucher = JSONPath({
          path: `$.[*].detail.vouchersAll[*].[?(@ && @.referenceId === '${ticketCode}' )]`,
          json: arrPaymentES,
        });
        voucherDiscount = 0;
      }
    }
    if (arrReferenceVoucher?.length) {
      voucherDiscount = arrReferenceVoucher?.reduce((acc, cur) => acc + cur?.amount || 0, 0);
    }
    const arrSku = _.compact(
      _.uniq(
        JSONPath({
          path: '$.details[*].detailAttachments[*].itemCode',
          json: orderInfor,
        }),
      ),
    );
    const lstProduct = await this.pimCoreService.getListProductBySku(arrSku);

    const productDatas: ProductData[] = orderInfor?.details
      ?.filter((detail) => !(detail?.isPromotion === 'Y' && detail?.isInventoryManagement === 'N'))
      ?.map((item) => ({
        isVaccineCombo: !!item.detailAttachments.find((detail) => detail?.serviceFee !== 0),
        itemCode: item?.itemCode ?? '',
        itemName: item?.itemName ?? '',
        measureUnit: item.unitName ?? '',
        discount: item?.discount ?? 0,
        discountType: item?.discountType ?? '',
        discountPromotion: item?.discountPromotion ?? 0,
        totalPrice: item?.price * item?.quantity,
        totalDisCount: item?.discount * item?.quantity,
        isExpiredDate: item?.isExpiredDate,
        isPromotion: item?.isPromotion,
        quantity: item?.quantity ?? 0,
        price: item?.price ?? 0,
        total: item?.total ?? 0,
        totalVaccineCombo: item?.price * item.quantity,
        haveAttachments: !!item?.detailAttachments?.length,
        //seriesCode
        seriesCode: [],
        dosageItems: _.uniqBy(
          item?.detailAttachments?.map((detail) => {
            const productDataIndication = getTicket?.indications?.find(
              (indication) => indication?.sku === detail?.itemCode,
            );
            const productFind = lstProduct?.listProduct?.find((product) => product?.sku === detail?.itemCode);

            const productDataSchedule = getTicket?.schedules?.find((schedule) => schedule?.sku === detail?.itemCode);

            const currentVaccine = item?.detailAttachments?.filter((vaccine) => vaccine?.itemCode === detail?.itemCode);
            const totalDiscount = currentVaccine?.reduce(
              (accumulator, currentValue) => accumulator + currentValue.discountPromotion,
              0,
            );
            const totalDiscountAdjustment = currentVaccine?.reduce(
              (accumulator, currentValue) => accumulator + currentValue.discount,
              0,
            );

            return {
              itemCode: detail?.itemCode || '',
              itemName: detail?.itemName ?? '',
              manufactor:
                productFind?.manufactor || productDataIndication?.manufactor || productDataSchedule?.manufactor || '',
              diseaseName: productDataIndication?.taxonomies || productDataSchedule?.taxonomies || '',
              dosage: productDataIndication?.dosage || productDataSchedule?.dosage || '',
              quantity: currentVaccine?.length,
              price: Math.max(
                0,
                (detail?.price * currentVaccine?.length - totalDiscount - totalDiscountAdjustment) /
                  currentVaccine?.length,
              ),
              serviceFee: detail?.serviceFee ?? 0,
              totalAmount: Math.max(
                0,
                detail?.price * currentVaccine?.length - totalDiscount - totalDiscountAdjustment,
              ),
            };
          }),
          'itemCode',
        ),
      }));

    const orderCreatedAt =
      orderInfor?.orderAttribute === OrderAttribute.TRA_CHAM
        ? getTicket?.createdDate.slice(0, getTicket?.createdDate.length - 6)
        : employeeInfoStep5?.createdDate || employeeCodeStep2?.createdDate || orderInfor?.createdDate || '';

    /**
     * @description Kiểm tra điều kiện đơn preorder (orderAttribute = 7)
     * - Gọi OSR lấy thông tin phaseId
     * -> Case 1: Nếu phaseId = 1 thì render template bill preorder
     * -> Case 2: Nếu khác phase 1 thì render template bill thông thường + có thông tin tiền đặt cọc
     * @returns {boolean} isRenderPreOrderTemplate: true => render template bill preorder, false => render template bill thông thường
     * @returns {boolean} isShowDeposit: true => hiển thị thông tin tiền đặt cọc, false => không hiển thị thông tin tiền đặt cọc
     * @returns {number} totalDeposit: số tiền đặt cọc
     */

    const isPreOrderAttribute = orderInfor?.orderAttribute === OrderAttribute.PRE_ORDER;

    const preorderData: PreorderData = {
      isRenderPreOrderTemplate: false,
      isShowDeposit: false,
      totalDeposit: 0,
    };

    // Khi hàm dùng để gen bill và đơn là đơn preorder
    if (isBill && isPreOrderAttribute) {
      const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({ listSku: arrSku });

      const phase1 = osrDepositAmountBySku?.find((e) => e?.phaseId === 1);
      const isPhase1 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase1?.fromDate).format('YYYY-MM-DD'),
        moment(phase1?.toDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );
      let isHaveTicketCode = ticketCode ? true : false;
      if (!isHaveTicketCode) {
        let caching = await this.redisService.get(`TICKET_CREATE:${orderCode}`);
        if (!caching) {
          caching = (await this.examinationCoreService.getTicketByOrderCode({ orderCodes: [orderCode] }))?.items?.at(
            0,
          )?.ticketCode;
        }
        if (caching) {
          isHaveTicketCode = true;
        }
      }

      if (isPhase1 && phase1 && !isHaveTicketCode) {
        preorderData.isShowDeposit = false;
        preorderData.isRenderPreOrderTemplate = true;
      } else {
        preorderData.isShowDeposit = true;
        preorderData.totalDeposit = orderInfor?.totalDeposit ?? 0;
        preorderData.isRenderPreOrderTemplate = false;
      }
    }

    const printData: PrintBillDataRes = {
      ignorePrint: printInvoiceDto?.printerInfo?.ignorePrint,
      printType: printType,
      printerInfo: printInvoiceDto?.printerInfo,
      printData: {
        isRePrint: false,
        shopCode: shopCode,
        preorderData: preorderData,
        storageName,
        groupCode: groupCode,
        groupName: groupName,
        employeeData: {
          employeeCode:
            employeeInfor?.employeeCode ||
            employeeInfoStep5?.employeeCode ||
            employeeCodeStep2?.employeeCode ||
            orderInfor?.createdBy,
          employeeName:
            employeeInfor?.employeeName ||
            employeeInfoStep5?.employeeName ||
            employeeCodeStep2?.employeeName ||
            orderInfor?.createdByName,
        },
        moneyData: {
          finalQuantity: orderInfor?.details?.reduce((accumulator, currentValue) => {
            return accumulator + currentValue?.quantity;
          }, 0),
          finalQuantityWithoutPromotion: orderInfor?.details
            ?.filter((entry) => entry?.isPromotion !== 'Y')
            ?.reduce((accumulator, currentValue) => {
              const totalCurrentValue = currentValue?.detailAttachments?.reduce((acc, cur) => {
                return acc + cur?.quantity;
              }, 0);
              return accumulator + totalCurrentValue;
            }, 0),
          totalBill: orderInfor?.totalBill ?? 0,
          finalPrice: orderInfor?.total ?? 0,
          finalPriceDiscount: orderInfor?.totalDiscount ?? 0,
          totalPricePay: orderInfor?.totalBill - voucherDiscount || 0,
          totalPricePayWord: orderInfor?.totalBill ? convertNumberToVietnamese(orderInfor?.totalBill) : '', // tiền dạng chữ
          voucherPrice: voucherDiscount || 0,
          totalPriceReturn: totalPriceReturn || 0, //totalPriceReturn hard tien thua khach tra
          totalPriceCustomerPay: totalPriceCustomerPay || 0,
          paymentMethod: getPaymentMethod(paymentData?.detail),
          serviceFee: orderInfor?.serviceFee,
          totalWithFee: orderInfor?.totalBill ?? 0,
          totalDiscountAndVoucher: orderInfor?.totalDiscount + voucherDiscount,
          // Thông tin trả chậm
          isPartialPayment: orderInfor?.orderAttribute === OrderAttribute.TRA_CHAM,
          orderAttribute: orderInfor?.orderAttribute || 0,
          totalBillPartialPayment: caseInstance.case3
            ? getJourney?.partialPaymentAmount - orderInfor?.totalDeposit
            : caseInstance.case1
            ? getJourney?.totalBill || 0
            : getJourney?.partialPaymentAmount || 0,
          isFinishPayment: getJourney?.isFinishPayment || false,
          // Lần đầu và lần cuối thì hiện cọc
          totalDepositBill: caseInstance.case1 || caseInstance.case3 ? orderInfor?.totalDeposit || 0 : 0,
          // Lần đầu thì có serviceFee
          serviceFeePartialPayment: caseInstance.case1 ? orderInfor?.serviceFee : 0,
          totalDeposit: orderInfor?.totalDeposit || 0,
          totalDepositWord: convertNumberToVietnamese(orderInfor?.totalDeposit),
          partialPaymentAmount: getJourney?.partialPaymentAmount || 0,
          partialPaymentAmountWord: convertNumberToVietnamese(getJourney?.partialPaymentAmount || 0),
          totalBillPartialPaymentWord: convertNumberToVietnamese(getJourney?.totalBill || 0),
          // Đơn trả châm: tiền cần thanh toán
          totalNeedPay: orderInfor?.totalBill - voucherDiscount - orderInfor?.totalDeposit,
        },
        orderSummaryData: {
          invoiceCode: orderCode,
          orderCode,
          orderCodeVirtual: orderCodeVirtual || orderCode,
          shopName: employeeInfor?.shopName || orderInfor?.shopName,
          shopCode: shopCodeInBill || '',
          shopAddress: employeeInfor?.uAddress ?? '',
          orderCreatedAt: orderCreatedAt,
          orderCodeShorted: orderInfor?.orderCode,
          storageName,
          customerPhone: customerInfor?.phoneNumber || '',
          customerName: customerInfor?.name ?? `Khách Lẻ ${orderInfor?.shopName}`,
          customerBuyerName: orderInfor?.custName ?? '',
          customerBuyerPhone: orderInfor?.phone ?? '',
          customerDOB: customerInfor?.dateOfBirth,
          customerAddress: customerInfor ? getFullAddress(customerInfor, true) : orderInfor?.custAddress, // Địa chỉ tạm trú
          vaccinationCode: customerInfor?.nationalVaccineCode ?? '',
          customerCode: customerInfor?.lcvId ?? '',
          gender: customerInfor?.gender,
          nameOnBill: employeeInfor?.nameOnBill ?? '',
          friendSell: orderInfor?.rewardPoints || 0,
          casherName: employeeInfor?.employeeName ?? '',
          custName: invoiceData?.custName ?? '',
          custBuy: invoiceData?.custBuy ?? '',
          address: invoiceData?.custAddress ?? '',
          lictradnum: invoiceData?.lictradNum ?? '',
        },
        productDatas: productDatas?.filter((product) => product?.isPromotion === 'N'),
        promotionDatas: productDatas?.filter((product) => product?.isPromotion === 'Y'),
      },
    };

    return this.printCenterService.vaccineBillV2(printData);
  }

  async mappingBiosignal(healthChecks: HealthCheck[]) {
    const weight = healthChecks?.find((healthCheck) => healthCheck?.fieldName === BIOSIGNAL_FIELD.WEIGHT);
    const temperature = healthChecks?.find((healthCheck) => healthCheck?.fieldName === BIOSIGNAL_FIELD.BODY_TEMP);
    const bloodPressure = healthChecks?.find(
      (healthCheck) => healthCheck?.fieldName === BIOSIGNAL_FIELD.BLOOD_PRESSURE,
    );
    const heartbeat = healthChecks?.find((healthCheck) => healthCheck?.fieldName === BIOSIGNAL_FIELD.HEART_RATE);
    const isMotherCheckHbsAg = healthChecks?.find(
      (healthCheck) => healthCheck?.fieldName === BIOSIGNAL_FIELD.IS_MOTHER_CHECK_HBS_AG,
    );
    const isHbsAg = healthChecks?.find((healthCheck) => healthCheck?.fieldName === BIOSIGNAL_FIELD.IS_HBS_AG);

    return {
      weight: weight?.biosignalValue || '',
      temperature: temperature?.biosignalValue || '',
      bloodPressure: bloodPressure?.biosignalValue || '',
      heartbeat: heartbeat?.biosignalValue || '',
      isMotherCheckHbsAg: isMotherCheckHbsAg?.biosignalValue === 'true' || false,
      isHbsAg: isHbsAg?.biosignalValue === 'true' || false,
    };
  }

  async getAddressMultiLanguage(
    customerInfor: GetPersonByIdRes,
    type: string,
    ticketInfor: TicketDetailRes,
    healthCheckQuestions: HealthCheckQuestion[],
    employeeInfor: ShopEmployee,
  ): Promise<{
    custAddress?: string;
    custPermanentAddress?: string;
    locationAndTime?: string;
    indications?: any;
    lstNotes?: CheckRuleNoteIndicationRes[];
    trackingTime?: string;
    examinedTimeText?: string;
  }> {
    const custAddress = getFullAddress(
      {
        temporaryAddress: customerInfor?.temporaryAddress,
        temporaryWardName: customerInfor?.temporaryWardName,
        temporaryDistrictName: customerInfor?.temporaryDistrictName,
        temporaryProvinceName: customerInfor?.temporaryProvinceName,
      },
      false,
    );
    const custPermanentAddress = getFullAddress(
      {
        frequentlyAddress: customerInfor?.frequentlyAddress,
        frequentlyWardName: customerInfor?.frequentlyWardName,
        frequentlyDistrictName: customerInfor?.frequentlyDistrictName,
        frequentlyProvinceName: customerInfor?.frequentlyProvinceName,
      },
      true,
    );

    const examinedTime = ticketInfor?.examinedTime || ticketInfor?.trackingTime;

    const currentDate = examinedTime ? moment(examinedTime).utcOffset(7) : moment().utcOffset(7);

    let examinedTimeText = `${currentDate.format('HH')} giờ ${currentDate.format('mm')} phút, ngày ${currentDate.format(
      'DD',
    )} tháng ${currentDate.format('MM')} năm ${currentDate.format('YYYY')}`;

    let locationAndTime = `${employeeInfor?.provinceName}, ${examinedTimeText}`;

    const trackingTimeData = moment(ticketInfor?.trackingTime).utcOffset(7);
    let trackingTime = `${trackingTimeData.format('HH')} giờ ${trackingTimeData.format(
      'mm',
    )} phút, ngày ${trackingTimeData.format('DD')} tháng ${trackingTimeData.format('MM')} năm ${trackingTimeData.format(
      'YYYY',
    )}`;

    // Mapping translate regimen
    const arrRegimen: string[] = _.uniq(JSONPath({ path: '$[*].regimenId', json: ticketInfor?.indications }));
    let listRegimen: RegimenItem[] = [];
    if (type === TemplateType.EN && arrRegimen?.length) {
      try {
        listRegimen = await this.regimenService.getRegimenByIds({ regimenIds: arrRegimen });
      } catch (error) {}
    }

    let indications = ticketInfor?.indications?.map((indication) => {
      return {
        diseaseName: indication?.taxonomies || '',
        dosage: indication?.dosage || '',
        vaccineName: indication?.vaccineName || '',
        manufactor: indication?.manufactor || '',
        injectionRoute: indication?.injectionRoute || '',
        position: indication?.position || '',
        lotDate: indication?.lotDate,
        lotNumber: indication?.lotNumber,
        isColdStorage: indication?.sku === SkuHard.QDENGA || false,
      };
    });

    // check rule note
    const lstNotes = await this.orderRuleEngineService.checkRuleNote({
      skus: ticketInfor?.indications?.map((indication) => indication?.sku),
      gender: customerInfor?.gender || 0,
      birthday: new Date(customerInfor?.dateOfBirth).toISOString(),
      schedules: ticketInfor?.schedules,
    });

    if (type !== TemplateType.EN)
      return {
        custAddress,
        custPermanentAddress,
        locationAndTime,
        indications,
        lstNotes,
        trackingTime,
        examinedTimeText,
      };

    examinedTimeText = currentDate.format('hh:mm A, DD/MM/YYYY');
    trackingTime = trackingTimeData.format('hh:mm A, DD/MM/YYYY');

    const employeeInfoProvince: Province[] = await this.dsmsService.getAddress('province', {
      code: employeeInfor?.provinceCode,
    });
    locationAndTime = `${employeeInfoProvince?.at(0)?.nameProvinceEnglish}, ${examinedTimeText}`;

    // Lấy thông tin địa chỉ tiếng anh
    const temporaryProvince: Province[] = await this.dsmsService.getAddress('province', {
      code: customerInfor?.temporaryProvinceCode,
    });
    const frequentlyProvince: Province[] = await this.dsmsService.getAddress('province', {
      code: customerInfor?.frequentlyProvinceCode,
    });
    const temporaryDistrict: District[] = await this.dsmsService.getAddress('district', {
      province: customerInfor?.temporaryProvinceCode,
    });
    const frequentlyDistrict: District[] = await this.dsmsService.getAddress('district', {
      province: customerInfor?.frequentlyProvinceCode,
    });
    const temporaryWard: Ward[] = await this.dsmsService.getAddress('ward', {
      district: customerInfor?.temporaryDistrictCode,
    });
    const frequentlyWard: Ward[] = await this.dsmsService.getAddress('ward', {
      district: customerInfor?.frequentlyDistrictCode,
    });

    const custAddressEN = getFullAddress(
      {
        temporaryAddress: customerInfor?.temporaryAddress,
        temporaryWardName: temporaryWard?.find((ward) => ward?.code === customerInfor?.temporaryWardCode)
          ?.nameWardEnglish,
        temporaryDistrictName: temporaryDistrict?.find(
          (district) => district?.code === customerInfor?.temporaryDistrictCode,
        )?.nameDistrictEnglish,
        temporaryProvinceName: temporaryProvince?.at(0)?.nameProvinceEnglish,
      },
      false,
    );

    const custPermanentAddressEN = getFullAddress(
      {
        frequentlyAddress: customerInfor?.frequentlyAddress,
        frequentlyWardName: frequentlyWard?.find((ward) => ward?.code === customerInfor?.frequentlyWardCode)
          ?.nameWardEnglish,
        frequentlyDistrictName: frequentlyDistrict?.find(
          (district) => district?.code === customerInfor?.frequentlyDistrictCode,
        )?.nameDistrictEnglish,
        frequentlyProvinceName: frequentlyProvince?.at(0)?.nameProvinceEnglish,
      },
      true,
    );

    const arrSku: string[] = _.uniq(JSONPath({ path: '$[*].sku', json: ticketInfor?.indications }));

    let getListProduct: GetListProductBySkuRes = null;
    try {
      getListProduct = await this.pimCoreService.getListProductBySku(arrSku);
    } catch (error) {}

    indications = ticketInfor?.indications?.map((indication) => {
      const regimenFind = listRegimen?.find((regimen) => regimen?.id === indication?.regimenId);
      const productFind = getListProduct?.listProduct?.find((product) => product?.sku === indication?.sku);
      return {
        diseaseName: regimenFind?.diseaseGroup?.engName || '',
        dosage: indication?.dosage || '',
        vaccineName: indication?.vaccineName || '',
        manufactor: productFind?.manufactorEng || '',
        injectionRoute: masterDataInjectionAndRoute(indication?.injectionRoute),
        position: masterDataInjectionAndRoute(indication?.position),
        lotDate: indication?.lotDate,
        lotNumber: indication?.lotNumber,
        isColdStorage: indication?.sku === SkuHard.QDENGA || false,
      };
    });

    const arrQuestionId: string[] = _.uniq(JSONPath({ path: '$[*].questionId', json: healthCheckQuestions }));

    const listQuestionEN = await this.monitorCoreService.searchByIds(arrQuestionId);

    healthCheckQuestions?.forEach((question) => {
      const questionFind = listQuestionEN?.find((entry) => entry?.id === question?.questionId);
      question.questionContext = questionFind?.questionContextEng;
    });

    return {
      custAddress: custAddressEN,
      custPermanentAddress: custPermanentAddressEN,
      locationAndTime,
      indications,
      lstNotes,
      trackingTime,
      examinedTimeText,
    };
  }

  async printInvoiceFamilyPackage(printInvoiceDto: PrintInvoiceFamilyPackageDto): Promise<PrintCenterRes> {
    /**
     * @TODO
     * Gọi qua OMS lấy thông tin danh sách đơn theo orderIdInter
     *    Lấy thông tin nhân viên của shop từ Inside
     *    Lấy thông tin khách hàng tiêm của từng đơn
     *    Lấy thông tin ở Payment để tính voucher, cash
     *    Lấy thông tin product từ Pim
     * => Tổng hợp thông tin gửi qua Print Center
     */

    const shopCode = (this.req.headers?.['shop-code'] as string) || '';

    const { orderIdInter, isRePrint, totalPriceReturn, totalPriceCustomerPay } = printInvoiceDto;

    const lstOrderInfo = await this.omsService.searchOrderDynamicES({
      maxResultCount: 20, //BA nguyennt81 confirm 20
      skipCount: 0,
      searchMatchPhraseFields: {
        orderIdInter: orderIdInter,
      },
    });
    const orderInfo = lstOrderInfo?.items?.at(0);
    const { orderCode } = orderInfo;

    //  Đơn trả chậm => Lưu thêm ticketCode vào key S3
    const storageName = `${lstOrderInfo?.items?.at(0)?.shopCode}/${orderIdInter}`;

    // isRePrint = true thì lấy thông tin từ s3 để in
    // nếu không tồn tại file thì isRePrint = false và call lại các thông tin bên dưới
    if (isRePrint) {
      const printData: PrintBillDataFamilyPackageRes = {
        ignorePrint: printInvoiceDto?.printerInfo?.ignorePrint,
        printType: PRINT_TYPE.BILL_FAMILY_PACKAGE,
        printerInfo: printInvoiceDto?.printerInfo,
        printData: {
          isRePrint: isRePrint,
          shopCode: shopCode,
          orderIdInter: orderIdInter,
          storageName,
        },
      };
      const printInvoice = await this.printCenterService.billFamilyPackage(printData);
      if (!!printInvoice?.url) return printInvoice;
    }

    const { groupCode, groupName } = await this.getGroupInfoFromOrder(lstOrderInfo?.items?.at(0));

    // Lấy thông tin nhân viên hiển thị
    const employeeCodeStep2 = _.orderBy(
      orderInfo?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeConfirmAndCollectMoney),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const employeeInfoStep5 = _.orderBy(
      orderInfo?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
      ['modifiedDate', 'desc'],
    )?.at(0);

    //filter theo shop code
    const employeeCode = employeeInfoStep5?.employeeCode || employeeCodeStep2?.employeeCode || orderInfo?.createdBy;
    const listEmployeeInfor = await this.insideService.getListShopByEmployee(employeeCode);
    const shopCodeInBill = orderInfo?.shopCode;
    const employeeInfor = listEmployeeInfor?.shops?.find((shop) => shop?.shopCode === shopCodeInBill);

    // Ngày hiển thị trên bill
    const orderCreatedAt =
      employeeInfoStep5?.createdDate || employeeCodeStep2?.createdDate || orderInfo?.createdDate || '';

    // Lấy Thông tin Khách hàng tiêm từng đơn
    const lcvIds = _.uniq(
      _.compact(
        JSONPath({
          path: '$.items[*].details[*].detailAttachments[*].personIdSub',
          json: lstOrderInfo,
        }),
      ),
    );
    let persons: GetPersonByIdRes[] = [];
    try {
      if (lcvIds?.length) {
        persons = await this.familyService.getManyByLcvId({ lcvId: lcvIds });
      } else persons = [];
    } catch (error) {
      persons = [];
    }

    // Thông tin payment, voucher, lấy đại diện 1 đơn
    const arrPaymentCode = _.uniq(
      _.compact(
        orderInfo.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );
    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGatewayService.getPaymentRedis({
      paymentCodes: arrPaymentCode,
    });

    const voucherDiscount = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
    }, 0);

    // Lấy thông tin product từ PIM
    const arrSku = _.compact(
      _.uniq(
        JSONPath({
          path: '$.items[*].details[*].detailAttachments[*].itemCode',
          json: lstOrderInfo,
        }),
      ),
    );
    const lstProduct = await this.pimCoreService.getListProductBySku(arrSku);

    /**
     * @Handle Xử lý data để in bill
     */
    const orderDetails: ProductDataFamilyPackageDto[] = [];
    const promotions: PromotionFamilyPackageDto[] = [];
    lstOrderInfo?.items?.forEach((order) => {
      const product = JSONPath({
        path: '$.details[*].detailAttachments[*]',
        json: order,
      });
      const dosageItems = _.uniqBy(
        product?.map((detail) => {
          const productFind = lstProduct?.listProduct?.find((e) => e?.sku === detail?.itemCode);
          const currentVaccine = product?.filter((vaccine) => vaccine?.itemCode === detail?.itemCode);
          const totalDiscount = currentVaccine?.reduce(
            (accumulator, currentValue) => accumulator + currentValue.discountPromotion,
            0,
          );
          const totalDiscountAdjustment = currentVaccine?.reduce(
            (accumulator, currentValue) => accumulator + currentValue.discount,
            0,
          );

          return {
            itemCode: detail?.itemCode || '',
            itemName: detail?.itemName ?? '',
            manufactor: productFind?.manufactor || '',
            quantity: currentVaccine?.length,
            price: Math.max(
              0,
              (detail?.price * currentVaccine?.length - totalDiscount - totalDiscountAdjustment) /
                currentVaccine?.length,
            ),
            totalAmount: Math.max(0, detail?.price * currentVaccine?.length - totalDiscount - totalDiscountAdjustment),
          };
        }),
        'itemCode',
      );

      const personFind = persons?.find((person) => person?.lcvId === product?.at(0)?.personIdSub);

      const orderDetail: ProductDataFamilyPackageDto = {
        orderCode: order?.orderCode,
        dosageItems: dosageItems,
        name: personFind?.name,
        lcvId: product?.at(0)?.personIdSub,
        serviceFee: order?.serviceFee,
        total: order?.totalBill,
      };
      orderDetails.push(orderDetail);

      order?.details
        ?.filter((detail) => detail?.isPromotion === 'Y' && detail?.isInventoryManagement === 'Y')
        ?.forEach((promotion) => {
          const promotionData = plainToInstance(PromotionFamilyPackageDto, promotion, {
            excludeExtraneousValues: true,
            exposeUnsetFields: false,
          });
          promotions.push(promotionData);
        });
    });

    const printData: PrintBillDataFamilyPackageRes = {
      ignorePrint: printInvoiceDto?.printerInfo?.ignorePrint,
      printType: PRINT_TYPE.BILL_FAMILY_PACKAGE,
      printerInfo: printInvoiceDto?.printerInfo,
      printData: {
        isRePrint: false,
        shopCode: shopCode,
        storageName,
        orderCodes: lstOrderInfo?.items?.map((item) => item?.orderCode),
        groupCode: groupCode,
        groupName: groupName,
        employeeData: {
          employeeCode:
            employeeInfor?.employeeCode ||
            employeeInfoStep5?.employeeCode ||
            employeeCodeStep2?.employeeCode ||
            orderInfo?.createdBy,
          employeeName:
            employeeInfor?.employeeName ||
            employeeInfoStep5?.employeeName ||
            employeeCodeStep2?.employeeName ||
            orderInfo?.createdByName,
        },
        moneyData: {
          totalPricePay: lstOrderInfo?.items?.reduce((acc, cur) => acc + cur?.totalBill, 0) - voucherDiscount || 0,
          voucherPrice: voucherDiscount || 0,
          totalPriceReturn: totalPriceReturn || 0,
          totalPriceCustomerPay: totalPriceCustomerPay || 0,
          serviceFee: lstOrderInfo?.items?.reduce((acc, cur) => acc + cur?.serviceFee, 0),
        },
        orderSummaryData: {
          orderCode,
          shopName: employeeInfor?.shopName || orderInfo?.shopName,
          shopCode: shopCodeInBill || '',
          shopAddress: employeeInfor?.uAddress ?? '',
          orderCreatedAt: orderCreatedAt,
          storageName,
          customerBuyerName: orderInfo?.custName ?? '',
          customerBuyerPhone: orderInfo?.phone ?? '',
          nameOnBill: employeeInfor?.nameOnBill ?? '',
          friendSell: lstOrderInfo?.items?.reduce((acc, cur) => acc + cur?.rewardPoints, 0),
          casherName: employeeInfor?.employeeName ?? '',
        },
        orderDetails,
        promotions,
      },
    };

    return this.printCenterService.billFamilyPackage(printData);
  }

  async getAddressEN(
    customer: GetPersonByIdRes,
    isFrequently: boolean = false,
  ): Promise<{ lcvId: string; address: string; nationality: string }> {
    let customerInfo = null;
    let nationality = null;
    if (isFrequently) {
      const [frequentlyProvince, frequentlyDistrict, frequentlyWard, frequentlyNationality] = await Promise.all([
        this.dsmsService.getAddress('province', {
          code: customer?.frequentlyProvinceCode,
        }),
        this.dsmsService.getAddress('district', {
          code: customer?.frequentlyDistrictCode,
        }),
        this.dsmsService.getAddress('ward', {
          code: customer?.frequentlyWardCode,
        }),
        this.dsmsService.getAddress('country', {
          code: customer?.nationalityCode,
        }),
      ]);

      customerInfo = {
        frequentlyAddress: translateVietnameseAccents(customer?.frequentlyAddress),
        frequentlyWardName: frequentlyWard?.at(0)?.englishName || '',
        frequentlyDistrictName: frequentlyDistrict?.at(0)?.englishName || '',
        frequentlyProvinceName: frequentlyProvince?.at(0)?.englishName || '',
      };
      nationality = frequentlyNationality?.at(0)?.name || '';
    } else {
      const [temporaryProvince, temporaryDistrict, temporaryWard, temporaryNationality] = await Promise.all([
        this.dsmsService.getAddress('province', {
          code: customer?.temporaryProvinceCode,
        }),
        this.dsmsService.getAddress('district', {
          code: customer?.temporaryDistrictCode,
        }),
        this.dsmsService.getAddress('ward', {
          code: customer?.temporaryWardCode,
        }),
        this.dsmsService.getAddress('country', {
          code: customer?.nationalityCode,
        }),
      ]);

      customerInfo = {
        temporaryAddress: translateVietnameseAccents(customer?.temporaryAddress),
        temporaryWardName: temporaryWard?.at(0)?.englishName || '',
        temporaryDistrictName: temporaryDistrict?.at(0)?.englishName || '',
        temporaryProvinceName: temporaryProvince?.at(0)?.englishName || '',
      };
      nationality = temporaryNationality?.at(0)?.name || '';
    }

    const address = getFullAddress(customerInfo, isFrequently);
    const lcvId = customer?.lcvId || '';
    return { lcvId, address, nationality };
  }

  async getGroupInfoFromOrder(orderInfo: CreateOrderRes): Promise<{ groupCode: string; groupName: string }> {
    try {
      // Get personSubIds from order
      const personSubIds = _.uniq(
        _.compact(
          JSONPath({
            path: '$.details[*].detailAttachments[*].personIdSub',
            json: orderInfo,
          }),
        ),
      );

      // Run 3 API calls in parallel using Promise.all
      const [cartConfirmData, groupTypeData, groupFamilyPackage] = await Promise.all([
        this.cartAppService.getCartConfirmByOrderCode(orderInfo?.orderCode),
        this.familyPackageService.getRankGroupTypeDataByCode({
          groupTypeCode: '1',
        }),
        this.familyPackageService.getGroupFamilyPackage({
          lcvIds: [personSubIds?.at(0)],
        }),
      ]);

      // Parse promotion data to get familyRank
      const cartAppInfo = JSON.parse(cartConfirmData?.cartAppInfo);
      const familyRank = cartAppInfo?.checkPromotion?.familyRank || '';

      const groupCode = groupFamilyPackage?.at(0)?.code || '';
      const groupName =
        groupTypeData?.groupRanking?.find((group) => group?.level === +familyRank)?.groupRankingName || '';

      return { groupCode, groupName };
    } catch (error) {
      return { groupCode: '', groupName: '' };
    }
  }
}
