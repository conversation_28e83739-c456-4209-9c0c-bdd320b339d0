import { Modu<PERSON> } from '@nestjs/common';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { FamilyModule } from 'vac-nest-family';
import { JourneyModule } from 'vac-nest-journey';
import { MonitorCoreModule } from 'vac-nest-monitor';
import { OMSModule } from 'vac-nest-oms';
import { OsrModule } from 'vac-nest-osr';
import { PIMAppModule } from 'vac-nest-pim-app';
import { RegimenModule } from 'vac-nest-regimen';
import { FilesModule } from '../../../../../libs/modules/src/modules/files/files.module';
import { OrdersModule } from '../orders/orders.module';
import { TicketController } from './controllers/ticket.controller';
import { TicketUtilsService } from './services/ticket-utils.service';
import { TicketService } from './services/ticket.service';
import { ScheduleCoreModule } from 'vac-nest-schedule';
import { PaymentGatewayModule } from 'vac-nest-payment-gateway';
import { VoucherCoreModule } from 'vac-nest-voucher-core';
import { IMSModule } from 'vac-nest-ims';
import { IMSBookingModule } from 'vac-nest-ims-booking';
import { CartAppModule } from 'vac-nest-cart-app';
import { VacCoreAssignJobModule } from 'vac-nest-assign-job';
import { LoyaltyAppModule } from 'vac-nest-loyalty-app';
import { ScheduleRequestsModule } from 'apps/rsa-ecom/src/modules/schedule-requests/schedule-requests.module';
import { RsaEcomModule } from 'vac-nest-rsa-ecom';
import { VacOrderInjectionModule } from 'vac-nest-order-injection';
import { NotificationModule } from '../notification';
import { CamundaSpeakerApiModule, VacOrderCoreApiModule } from '@frt/nestjs-api';
// import { CustomersModule } from '../customers/customers.module';
@Module({
  imports: [
    ExaminationCoreModule,
    FamilyModule,
    MonitorCoreModule,
    RegimenModule,
    JourneyModule,
    OMSModule,
    OsrModule,
    PIMAppModule,
    IMSModule,
    FilesModule,
    OrdersModule,
    ScheduleCoreModule,
    PaymentGatewayModule,
    VoucherCoreModule,
    IMSBookingModule,
    CartAppModule,
    VacCoreAssignJobModule,
    LoyaltyAppModule,
    ScheduleRequestsModule,
    RsaEcomModule,
    VacOrderInjectionModule,
    OsrModule,
    // CustomersModule,
    NotificationModule,
    CamundaSpeakerApiModule,
    VacOrderCoreApiModule,
  ],
  controllers: [TicketController],
  providers: [TicketService, TicketUtilsService],
  exports: [TicketService],
})
export class TicketModule {}
