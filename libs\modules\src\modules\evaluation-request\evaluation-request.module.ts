import { <PERSON>du<PERSON> } from '@nestjs/common';
import { EvaluationRequestController } from './controllers/evaluation-request.controller';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { FamilyModule } from 'vac-nest-family';
import { EvaluationRequestService } from './services/evaluation-request.service';
import { NotificationModule } from 'modules/modules/modules/notification';
import { RegimenModule } from 'vac-nest-regimen';
import { FilesModule } from '../files/files.module';
import { IAMCoreModule } from 'vac-nest-iam';

@Module({
  imports: [ExaminationCoreModule, NotificationModule, FamilyModule, RegimenModule, FilesModule, IAMCoreModule],
  controllers: [EvaluationRequestController],
  providers: [EvaluationRequestService],
})
export class EvaluationRequestModule {}
