import { HttpException, HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { ErrorCode, formatDate, getExpiredTime, RedisService } from '@shared';
import { ShopHub } from 'apps/rsa/src/modules/shops/entities/shopHub.entity';
import { Request } from 'express';
import { InjectS3, S3Client } from 'ict-nest-s3';
import { S3_OPERATIONS } from 'ict-nest-s3/dist/s3.constant';
import { JSONPath } from 'jsonpath-plus';
import _ from 'lodash';
import moment from 'moment';
import { Repository } from 'typeorm';
import { DSMSService } from 'vac-nest-dsms';
import { ExaminationCoreService, SearchTicketDto, TicketAlwaysIndicationRes } from 'vac-nest-examination';
import { FamilyService } from 'vac-nest-family';
import {
  GetInventoryProductItemByShopSkuBody,
  GetInventoryProductItemByShopSkuResponse,
  GetListStockMedicAtShopDto,
  IMSService,
  StockMedicRes,
  WHS_CODE_NORMAL,
  whsCodeWithShopCodeWhsType,
} from 'vac-nest-ims';
import { InsideService } from 'vac-nest-inside';
import {
  GetInventoryHistoryBySkuItem,
  GetInventoryHistoryBySkuResponse,
  InventoryHistoryService,
} from 'vac-nest-inventory-history';
import { MonitorCoreService } from 'vac-nest-monitor';
import { OsrService } from 'vac-nest-osr';
import { GetListProductBySkuRes, PIMAppService } from 'vac-nest-pim-app';
import { GetBySkuCustomResponse, IMSProductItemService } from 'vac-nest-product-item';
import {
  DailyVaccineSumReportReq,
  GetDetailedGeneralFundReportsDto,
  ItemDataRes,
  RabiesReportAppendixOneDto,
  RabiesVaccinePreventionReportRequestDto,
  ReportCoreService,
  UsedVaccineReportDto,
} from 'vac-nest-report';
import { RsaReportCoreService } from 'vac-nest-rsa-report';
import { getFullAddressCertificate } from '../../print-center/functions';
import {
  GetProductItemBySkuBody,
  GetStockBySkuAndProductItemResponse,
  GetStockBySkuResponse,
  GetStockHistoryBySkuResponse,
} from '../../stocks/dto';
import { PRE_SIGNED_LINK_EXPIRATION, REPORT_S3_TYPE, REPORT_TEMPLATE_TYPE } from '../constants';
import {
  DailyVaccineReportDTO,
  DailyVaccinSumReportDTO,
  DeleteCacheShopHubDto,
  ExcelInventoryDto,
  GetXntReportDto,
  InjectionStatisticsDto,
  SearchTicketExcelDto,
} from '../dto';
import { OutlineData, VACExcelData } from '../dto/excel-data.dto';
import { GetVacExcelReportDTO } from '../dto/get-vac-excel-report.dto';
import {
  generateStringByDateTime,
  getExcelInventory,
  getVACExcelReport,
  getVACExcelReportAppendixOne,
  getVACExcelReportUsed,
  getVACExcelSymptomAfterInjection,
} from '../functions/reports';
import { getExcelInjectionStatistics, getVAT6In1ExcelReport } from '../functions';
import { GetVac6In1VATReportDto } from '../dto/get-vac-6in1-vat.dto';
import { ReportHexavalentDto } from 'vac-nest-rsa-report/dist/dto/report-vac-hexavalent.dto';

@Injectable()
export class ReportsService {
  shopCode: string = '';
  constructor(
    private readonly vacReportService: ReportCoreService,
    private readonly rsaReportCoreService: RsaReportCoreService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly familyService: FamilyService,
    private readonly monitorCoreService: MonitorCoreService,
    private readonly insideService: InsideService,
    private inventoryHistoryService: InventoryHistoryService,
    private rsaReportService: RsaReportCoreService,

    @InjectS3(process.env.S3_CONNECTION)
    private readonly s3Client: S3Client,
    private readonly osrService: OsrService,
    private imsProductItemService: IMSProductItemService,
    private imsService: IMSService,
    @Inject(REQUEST)
    private readonly req: Request,
    @InjectRepository(ShopHub)
    private readonly shopHubRepository: Repository<ShopHub>,
    private dsmsService: DSMSService,
    private pimService: PIMAppService,
    private readonly redisService: RedisService,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
  }

  async getDailyVaccineReport(payload: DailyVaccineReportDTO) {
    const { shopCode, fromDate, toDate, pageIndex, pageSize, sku } = payload;
    const request = {
      shopcode: shopCode,
      fromdate: fromDate,
      todate: toDate,
      pageindex: pageIndex,
      pagesize: pageSize,
    } as any;
    if (sku) request.sku = sku;

    const res = await this.vacReportService.getUsedDailyVaccineReport(request);

    if (res?.status !== 200) {
      throw new HttpException(
        {
          code: res?.status || ErrorCode.INTERNAL_SERVER,
          message: res?.data || ErrorCode.getError(ErrorCode.INTERNAL_SERVER),
        },
        res?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return { ...res?.data, frDate: payload?.fromDate, toDate: payload?.toDate };
  }

  async getDailySumVaccineReport(payload: DailyVaccinSumReportDTO) {
    const { shopCode, fromDate, toDate } = payload;

    const request: DailyVaccineSumReportReq = {
      shopcode: shopCode,
      fromdate: fromDate,
      todate: toDate,
    };

    const res = await this.vacReportService.getDailyVaccineSumReport(request);
    if (res?.status !== 200) {
      throw new HttpException(
        {
          code: res?.status || ErrorCode.INTERNAL_SERVER,
          message: res?.data || ErrorCode.getError(ErrorCode.INTERNAL_SERVER),
        },
        res?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return { ...res?.data, frDate: payload?.fromDate, toDate: payload?.toDate };
  }

  async getExcelReport(payload: GetVacExcelReportDTO) {
    const { shopCode, countBy, frMonth, frYear, mergeBy } = payload;
    let reportType = REPORT_S3_TYPE.SHOP;
    const requestParams: RabiesVaccinePreventionReportRequestDto = {
      shopcode: shopCode,
      month: frMonth,
      year: frYear,
      type: countBy,
    };
    let resData = {} as any;

    switch (mergeBy) {
      case REPORT_TEMPLATE_TYPE.SHOP:
        reportType = REPORT_S3_TYPE.SHOP;
        resData = await this.vacReportService.getReportRabiesPreventionShop(requestParams);
        break;
      case REPORT_TEMPLATE_TYPE.SKU:
        reportType = REPORT_S3_TYPE.SKU;
        resData = await this.vacReportService.getReportRabiesPreventionVaccine(requestParams);
        break;
      case REPORT_TEMPLATE_TYPE.EXPOSURE:
        reportType = REPORT_S3_TYPE.EXPOSURE;
        resData = await this.vacReportService.getReportRabiesPreventionExposure(requestParams);
        break;
      default:
        resData = await this.vacReportService.getReportRabiesPreventionShop(requestParams);
        reportType = REPORT_S3_TYPE.SHOP;
        break;
    }
    const { items, shopName, monthYear, reportDate, provinceName } = resData?.data;

    const outlineData: OutlineData = { shopName, monthYear, reportDate, provinceName };

    const excelDatas: VACExcelData = { dataForm: items, tempType: mergeBy, outlineData };

    const buffer = await getVACExcelReport(excelDatas);

    const dateString = generateStringByDateTime();

    await this.s3Client.uploadFileToS3(buffer, `${reportType}/baocaodai_${dateString}.xlsx`);

    const arrLinks = await this.s3Client.presignObjects(
      [`${reportType}/baocaodai_${dateString}.xlsx`],
      S3_OPERATIONS.GET_OBJECT,
      PRE_SIGNED_LINK_EXPIRATION,
    );

    return { url: arrLinks[0] };
  }

  async getExcelReportAppendixOne(payload: RabiesReportAppendixOneDto) {
    const resData = await this.vacReportService.getRabiesReportAppendixOne(payload);

    if (!resData?.length) return { url: '' };

    const monthDatas = {};

    resData?.forEach((entry) => {
      const month = moment(entry?.vaccinatedDate).utcOffset(7).format('MM');
      if (monthDatas?.[month]) monthDatas[month].push(entry);
      else monthDatas[month] = [entry];
    });
    const excelDatas = [];
    for (const month in monthDatas) {
      excelDatas.push([month, monthDatas[month]]);
    }

    excelDatas.sort((a, b) => a[0] - b[0]);

    const buffer = await getVACExcelReportAppendixOne(excelDatas);

    const dateString = generateStringByDateTime();

    await this.s3Client.uploadFileToS3(buffer, `${REPORT_S3_TYPE.APPENDIX_ONE}/BaoCaoDaiPhuLuc1_${dateString}.xlsx`);

    const arrLinks = await this.s3Client.presignObjects(
      [`${REPORT_S3_TYPE.APPENDIX_ONE}/BaoCaoDaiPhuLuc1_${dateString}.xlsx`],
      S3_OPERATIONS.GET_OBJECT,
      PRE_SIGNED_LINK_EXPIRATION,
    );

    return { url: arrLinks[0] };
  }

  async getExcelReportUsed(payload: UsedVaccineReportDto) {
    // const resData = await this.vacReportService.getRabiesReportAppendixOne(payload);

    const resData = await this.vacReportService.getReportUsedVaccine(payload);

    // if (!resData?.length) return { url: '' };

    const buffer = await getVACExcelReportUsed(resData);

    const dateString = generateStringByDateTime();

    await this.s3Client.uploadFileToS3(buffer, `${REPORT_S3_TYPE.REPORT_USE}/BaoCaoSuDungVaccine_${dateString}.xlsx`);

    const arrLinks = await this.s3Client.presignObjects(
      [`${REPORT_S3_TYPE.REPORT_USE}/BaoCaoSuDungVaccine_${dateString}.xlsx`],
      S3_OPERATIONS.GET_OBJECT,
      PRE_SIGNED_LINK_EXPIRATION,
    );

    return { url: arrLinks[0] };
  }

  async symptomAfterInjection(payload: SearchTicketExcelDto) {
    const getTicket: TicketAlwaysIndicationRes = {
      totalCount: 0,
      indicationCount: 0,
      personCount: 0,
      ticketDetails: [],
    };

    let allDataFetched = false;
    let skipCount = 0;
    const maxResultCount = 100;

    while (!allDataFetched) {
      try {
        const { ticketDetails: batchData } = await this.examinationCoreService.searchTicketAlwaysIndication({
          fromDate: payload?.fromDate,
          toDate: payload?.toDate,
          statuses: [8],
          skipCount: skipCount,
          maxResultCount: maxResultCount,
          shopCode: payload?.shopCode,
        });

        // Assuming batchData is an array of the data fetched
        if (batchData.length > 0) {
          // Process and add the batch data to arrReturn
          // For example, if you need to transform or directly add the batchData
          getTicket.ticketDetails.push(...batchData); // Adjust this line based on the actual structure of batchData
          if (batchData.length < maxResultCount) {
            // If the number of items in the batch is less than maxResultCount, it means this is the last batch
            allDataFetched = true;
          } else {
            // Prepare for the next iteration
            skipCount += maxResultCount;
          }
        } else {
          // No more data to fetch
          allDataFetched = true;
        }
      } catch (error) {
        allDataFetched = true;
        Logger.log({
          fields: {
            info: '[searchTicketAlwaysIndication] Error fetching data',
            error: error,
          },
        });
      }
    }

    //filter theo shop code
    const listEmployeeInfo = await this.insideService.getListShopByEmployee(payload?.employeeCode);
    const employeeInfo = listEmployeeInfo?.shops?.find((shop) => shop?.shopCode === payload?.shopCode);

    const arrLcvId = JSONPath({
      path: `$.ticketDetails[*].lcvId`,
      json: getTicket,
    });

    const arrTicketCode = JSONPath({
      path: `$.ticketDetails[*].ticketCode`,
      json: getTicket,
    });

    const getListFamily = await this.familyService.getFamilyForReportSymptom(arrLcvId);

    const getListSymptomAfterInjection = await this.monitorCoreService.getListTicketCode(arrTicketCode);

    const excelSymptom = getTicket?.ticketDetails?.map((ticket) => {
      const person = getListFamily?.find((family) => family?.lcvId === ticket?.lcvId);
      const symptomAfterInjection = getListSymptomAfterInjection?.find(
        (sympton) => sympton?.ticketCode === ticket?.ticketCode,
      );
      const personHost = person?.familyProfileDetails?.at(0);
      return {
        name: person?.name || '',
        nationalVaccineCode: person?.nationalVaccineCode || '',
        dateOfBirth: person?.dateOfBirth || '',
        personHostName: personHost?.name || '',
        address: getFullAddressCertificate(person, true),
        phone: person?.phoneNumber || personHost?.phoneNumber || '',
        vaccineName: ticket?.['indications']
          ?.filter((entry) => !entry?.orderReturnCode)
          ?.map((indication) => indication?.vaccineName)
          ?.join(', '),
        trackingTime: ticket?.trackingTime,
        completedTrackingTime: ticket?.completedTrackingTime,
        textSymptom: symptomAfterInjection?.details?.at(0)?.monitorTypeName,
        note: _.compact(symptomAfterInjection?.details?.map((entry) => entry.context))?.join(', '),
      };
    });

    const buffer = await getVACExcelSymptomAfterInjection(
      excelSymptom,
      `${payload?.shopCode} - ${employeeInfo?.uAddress || ''}`,
      payload?.fromDate,
    );

    const dateString = generateStringByDateTime();

    await this.s3Client.uploadFileToS3(
      buffer,
      `${REPORT_S3_TYPE.SYMPTOM_AFTER_INJECTION}/BaoCaoDanhSachTiemChiTiet_${dateString}.xlsx`,
    );

    const arrLinks = await this.s3Client.presignObjects(
      [`${REPORT_S3_TYPE.SYMPTOM_AFTER_INJECTION}/BaoCaoDanhSachTiemChiTiet_${dateString}.xlsx`],
      S3_OPERATIONS.GET_OBJECT,
      PRE_SIGNED_LINK_EXPIRATION,
    );

    return { url: arrLinks[0] };
  }

  async getMonitorAfterInjection(searchTicketDto: SearchTicketDto) {
    const res = await this.examinationCoreService.searchTicketAlwaysIndication(searchTicketDto);
    if (!res?.ticketDetails?.length) return res;
    const arrLcvId = _.uniq(res?.ticketDetails?.map((item) => item?.lcvId));
    const familyRes = await this.familyService.getManyByLcvId({ lcvId: arrLcvId });
    const arrTicketCode = res?.ticketDetails?.map((item) => item?.ticketCode);
    const monitorRes = await this.monitorCoreService.getListTicketCode(arrTicketCode);

    res?.ticketDetails?.forEach((item) => {
      const family = familyRes?.find((f) => f?.lcvId === item?.lcvId);
      const monitor = monitorRes?.find((m) => m['ticketCode'] === item?.ticketCode);
      item['indications'] = item?.['indications']?.filter((entry) => !entry?.orderReturnCode);
      item['family'] = family || null;
      item['monitors'] = monitor?.details || [];
      if (item?.['family']?.familyProfileDetails?.length) {
        item['family']['familyProfileDetails'] = item['family']['familyProfileDetails']?.filter(
          (e) => e.isHost === true,
        );
      }
    });
    return res;
  }

  async getExcelInventory(payload: ExcelInventoryDto) {
    const { sku, fromDate, toDate } = payload;
    const resData = await this.getStockHistoryBySku(payload);
    // Lấy ngày đầu tháng tới cuối tháng, nếu tháng hiện tại thì đầu tháng tới ngày hiện tại

    const fromDateFormat = moment(fromDate, 'YYYY-MM-DD').utcOffset(7).format('DD/MM/YYYY');
    const toDateFormat = moment(toDate, 'YYYY-MM-DD').utcOffset(7).format('DD/MM/YYYY');

    //filter theo shop code
    const listEmployeeInfo = await this.insideService.getListShopByEmployee(payload?.employeeCode);
    const employeeInfo = listEmployeeInfo?.shops?.find((shop) => shop?.shopCode === payload?.shopCode);

    const buffer = await getExcelInventory(
      resData,
      fromDateFormat,
      toDateFormat,
      payload?.shopCode,
      employeeInfo?.uAddress,
      sku,
    );

    const dateString = generateStringByDateTime();

    await this.s3Client.uploadFileToS3(buffer, `${REPORT_S3_TYPE.REPORT_USE}/BaoCaoXuatNhapTon_${dateString}.xlsx`);

    const arrLinks = await this.s3Client.presignObjects(
      [`${REPORT_S3_TYPE.REPORT_USE}/BaoCaoXuatNhapTon_${dateString}.xlsx`],
      S3_OPERATIONS.GET_OBJECT,
      PRE_SIGNED_LINK_EXPIRATION,
    );

    return { url: arrLinks[0] };
  }

  // START REPORT STOCK
  async getStockBySkuWithShopHub(body: GetListStockMedicAtShopDto): Promise<GetStockBySkuResponse> {
    try {
      let imsData = { inventories: [] };
      // lấy thêm data shopHub
      const shopHubs = await this._getAndCacheShopHub(body?.shopCodes?.[0]);

      const shopCodeMix = [...body.shopCodes, ...shopHubs];
      let whsCodesMix = body?.whsCodes;
      if (body?.whsCodes?.length) {
        const whsCodes57 = body?.whsCodes.map((x) => `57${x.slice(2)}`);
        whsCodesMix = [...whsCodesMix, ...whsCodes57];
      }

      imsData = await this.imsService.getListStockMedicAtShop({
        ...body,
        whsCodes: _.uniq(whsCodesMix),
        shopCodes: shopCodeMix,
      });

      const mixInventory = await this.mixInventoryToShop(imsData?.inventories, body?.shopCodes?.[0]);
      return await this.formatInventory(mixInventory);
    } catch (error) {
      return {
        inventories: [],
      };
    }
  }

  /*
  tồn kho có thể có shop và shop hub
  cần merge lại và trả về shop (có thể có case chỉ trả về shopHub nhưng data trả ra vẫn là shop)
  */
  async mixInventoryToShop(inventories, shopCodeBody) {
    if (shopCodeBody.slice(0, 2) === '57') {
      return inventories;
    }
    // group theo sku,kho,unit
    const groupedBy = _.groupBy(
      inventories,
      (i) => `${i?.sku}-${i?.unitLevel}-${i?.whsCode?.slice(-3)}-${i?.productItemCode || ''}`,
    );

    // duyệt và sum lại
    const whsCodes = Object?.keys(groupedBy);
    if (!whsCodes || !whsCodes.length) {
      return [];
    }
    const rs = [];

    const whsCode58 = _.uniq(
      inventories?.map((x) => {
        const shopCode = x.whsCode.slice(0, 5);
        return `58${shopCode.slice(-3)}`;
      }),
    );

    const whsCode57 = _.uniq(
      inventories?.map((x) => {
        const shopCode = x.whsCode.slice(0, 5);
        return `57${shopCode.slice(-3)}`;
      }),
    );
    const rsWhsCode = await this.dsmsService.getWarehouseByShopCode({
      code: [...whsCode58, ...whsCode57],
    });

    whsCodes.forEach((key) => {
      // ưu tiên shop vaccine
      const shop58 = groupedBy?.[key]?.find((x) => x?.shop58?.slice(0, 2) === '58');
      const baseData = shop58 ? shop58 : groupedBy?.[key]?.[0];

      // lất tổng all shop
      const totalQuantity = groupedBy?.[key]?.reduce((acc, cur) => acc + cur?.quantity, 0) || 0;
      const totalQuantityAvailable = groupedBy?.[key]?.reduce((acc, cur) => acc + cur?.quantityAvailable, 0) || 0;
      const totalQuantityOrder = groupedBy?.[key]?.reduce((acc, cur) => acc + cur?.quantityOrder, 0) || 0;

      // lấy name theo shop vaccine
      const currShopCode58 = rsWhsCode?.items?.find(
        (x) =>
          x?.code ===
          `58${baseData?.shopCode ? baseData?.shopCode?.slice(-3) : baseData?.whsCode?.slice(0, 5)?.slice(-3)}`,
      );
      const currShopCode57 = rsWhsCode?.items?.find(
        (x) =>
          x?.code ===
          `57${baseData?.shopCode ? baseData?.shopCode?.slice(-3) : baseData?.whsCode?.slice(0, 5)?.slice(-3)}`,
      );

      const currWareHouse58 = currShopCode58?.wareHouse?.find((x) => x?.code === `58${baseData?.whsCode.slice(-6)}`);
      const currWareHouse57 = currShopCode57?.wareHouse?.find((x) => x?.code === `57${baseData?.whsCode.slice(-6)}`);

      const whsCodeMix =
        currWareHouse58?.code || (shop58 ? baseData.whsCode || '' : `58${baseData?.whsCode?.slice(2)}`);
      const whsNameMix = currWareHouse58?.name || currWareHouse57?.name;
      const shopCodeMix =
        currShopCode58?.code || (shop58 ? baseData.shopCode || '' : `58${baseData?.shopCode?.slice(2)}`);

      const dataToPush = {
        ...baseData,
        quantity: totalQuantity,
        quantityAvailable: totalQuantityAvailable,
        quantityOrder: totalQuantityOrder,
        whsCode: whsCodeMix,
        whsName: whsNameMix,
        shopCode: shopCodeMix,
      };
      rs.push(dataToPush);
    });

    return rs;
  }

  async formatInventory(inventories: StockMedicRes[]) {
    if (!inventories || !inventories?.length) {
      return {
        inventories: [],
      };
    }

    const skus = inventories.map((item) => item.sku);
    let productData: GetListProductBySkuRes = { listProduct: [] };
    // call api get list product by sku and create product name map
    try {
      productData = await this.pimService.getListProductBySkuNoRule(skus);
    } catch (error) {}
    const productMap = productData?.listProduct?.reduce(function (map, obj) {
      map[obj.sku] = _.get(obj, 'name', '');
      return map;
    }, {});
    // add product name to response
    const inventoriesFormat = inventories.map((item) => ({
      ...item,
      skuName: productMap[item?.sku] || '',
    }));
    // get max unit level inventory
    const groupBySkuAndWhs = _.groupBy(inventoriesFormat, (obj) => obj?.sku + obj?.whsCode);
    let maxUnitLevelItems = [];
    for (const skuPlusWhs in groupBySkuAndWhs) {
      const item = _.maxBy(groupBySkuAndWhs[skuPlusWhs], 'unitLevel');
      if (item) {
        maxUnitLevelItems.push(item);
      }
    }

    maxUnitLevelItems = _.sortBy(maxUnitLevelItems, (x) => -x?.quantity);
    const response = {
      inventories: maxUnitLevelItems,
    };

    return response;
  }

  async _getAndCacheShopHub(shopCode: string) {
    if (!shopCode) return [];
    const key = `SHOP_HUB:${shopCode}`;

    const dataCache = await this.redisService.getRedisClient().get(key);

    if (dataCache) {
      const dataCacheJson = JSON.parse(dataCache || '{}');
      return dataCacheJson?.shopHub ? [dataCacheJson?.shopHub] : [];
    }

    const shopHubData = await this.shopHubRepository.findOne({
      where: {
        shopCode,
      },
    });

    await this.redisService.set(key, JSON.stringify(shopHubData || {}), 'EX', getExpiredTime('day', 1));

    return [shopHubData?.shopHub];
  }

  async clearCacheShopHub(body: DeleteCacheShopHubDto) {
    const keyRedis = 'SHOP_HUB';
    const shopCodes = body?.shopCodes;
    if (shopCodes && shopCodes?.length) {
      const pipeline = this.redisService.getRedisClient().pipeline();
      shopCodes.forEach((shopCode) => {
        pipeline.del(`${keyRedis}:${shopCode}`);
      });
      return await pipeline.exec();
    }

    // Tìm tất cả các khóa có tiền tố cụ thể
    const keys = await this.redisService.getRedisClient().keys(`*:${keyRedis}:*`);
    // Xóa các khóa tìm được
    const pipeline = this.redisService.getRedisClient().pipeline();
    keys.forEach((key) => {
      pipeline.del(key?.replace(process.env.REDIS_PREFIX, ''));
    });
    return await pipeline.exec();
  }

  async getProductItemBySkuInShopAndHubOld(body: GetProductItemBySkuBody): Promise<GetBySkuCustomResponse> {
    let productItems = { items: [] };
    try {
      productItems = await this.imsProductItemService.getBySku(body);
    } catch (error) {}
    // rule: return item with quantity > 0 and expiryDate < today
    let quantityMap = {};
    let inventoryData = {
      items: [],
    };
    try {
      // get lotdate quantity
      inventoryData = await this.imsService.getInventoryProductItemByShopSku({
        skus: [body.sku],
        shopCode: body?.shopCode || this.shopCode,
        whsCodes: [whsCodeWithShopCodeWhsType(body?.shopCode || this.shopCode, WHS_CODE_NORMAL)],
      });
      const maxUnitLevelData = [];
      for (const sku of inventoryData.items) {
        if (sku.sku === body?.sku) {
          const groupData = _.groupBy(sku.productItems, 'productItemCode');
          for (const key in groupData) {
            const maxUnitLevel = _.maxBy(groupData[key], 'unitLevel');
            maxUnitLevelData.push(maxUnitLevel);
          }
        }
      }
      quantityMap = maxUnitLevelData.reduce((map, obj) => {
        map[obj?.productItemCode] = obj?.quantity;
        return map;
      }, {});
    } catch (error) {}
    if ((body?.shopCode || this.shopCode) && body?.hasFilter) {
      productItems.items = productItems.items.filter(
        (p) => quantityMap[p.productItemCode] > 0 && moment().diff(p.expiryDate, 'days') <= 0,
      );
    }
    // sort: add quantity field and sort by quantity (with negative number)
    productItems.items = productItems.items.map((x) => ({ ...x, quantity: quantityMap[x.productItemCode] }));
    productItems.items = _.sortBy(productItems.items, (x) => -x.quantity);
    // remove quantity after sorted
    productItems.items = productItems.items.map((x) => ({ ...x, quantity: undefined }));
    return productItems;
  }

  async getStockBySkuAndProductItem(
    body: GetInventoryProductItemByShopSkuBody,
  ): Promise<GetStockBySkuAndProductItemResponse> {
    let imsDataShopVac = { items: [] };
    let imsDataShopHub = { items: [] };
    let imsData = { items: [] };

    let whsCodes57 = [];
    if (body?.whsCodes?.length) {
      whsCodes57 = body?.whsCodes.map((x) => `57${x.slice(2)}`);
    }

    try {
      imsDataShopVac = await this.imsService.getInventoryProductItemByShopSku(body);
      // lấy thêm data shopHub
      const shopHubs = await this._getAndCacheShopHub(body?.shopCode);

      if (shopHubs && shopHubs?.length) {
        imsDataShopHub = await this.imsService.getInventoryProductItemByShopSku({
          ...body,
          whsCodes: whsCodes57,
          shopCode: shopHubs[0],
        });
      }

      //mapping data
      const tempData = [];
      imsDataShopVac?.items?.forEach((data) => {
        tempData.push(...data.productItems.map((x) => ({ ...x, shopCode: data.shopCode, sku: data.sku })));
      });
      imsDataShopHub?.items?.forEach((data) => {
        tempData.push(...data.productItems.map((x) => ({ ...x, shopCode: data.shopCode, sku: data.sku })));
      });

      const mixInventory = await this.mixInventoryToShop(tempData, body?.shopCode);
      imsData = {
        items: this._transferModalInventory(mixInventory),
      };
    } catch (error) {}
    if (_.isEmpty(imsData.items)) {
      return { items: [] };
    }
    // create sku map
    const skuMap = imsData.items.reduce(function (map, obj) {
      map[obj.sku] = obj;
      return map;
    }, {});
    // call api get list product by sku and create product name map
    const productData = await this.pimService.getListProductBySkuNoRule(Object.keys(skuMap));
    const productMap = productData.listProduct.reduce(function (map, obj) {
      map[obj.sku] = obj.name;
      return map;
    }, {});
    const response = [];
    for (const sku in skuMap) {
      // get product item code
      const productItemCodes = skuMap[sku]?.productItems?.map((item) => item.productItemCode);
      // get product item data by product item code
      let expiryData: GetBySkuCustomResponse;
      try {
        expiryData = await this.imsProductItemService.getByCode({ productItemCodes: _.uniq(productItemCodes) });
      } catch (error) {}
      // create expiryDate - lotNumber map
      const expiryMap = expiryData?.items?.reduce(function (map, obj) {
        map[obj.productItemCode] = {
          expiryDate: _.get(obj, 'expiryDate', ''),
          lotNumber: _.get(obj, 'lotNumber', ''),
        };
        return map;
      }, {});
      // get max unit level
      const maxUnitLevel = _.maxBy(skuMap[sku]?.productItems, 'unitLevel')?.unitLevel;
      // map expiryDate - lotNumber data and filter by max unit level
      const productItems = skuMap[sku]?.productItems
        ?.map((item) => ({
          ...item,
          expiryDate: _.get(expiryMap[item.productItemCode], 'expiryDate', ''),
          lotNumber: _.get(expiryMap[item.productItemCode], 'lotNumber', ''),
          sku: _.get(skuMap[sku], 'sku', ''),
          skuName: productMap[_.get(skuMap[sku], 'sku', '')] || '',
        }))
        .filter((item) => item?.unitLevel === maxUnitLevel);
      // sort by expiryDate <= BA
      const oneItem = {
        ...skuMap[sku],
        productItems: _.sortBy(productItems, (x) => -x?.quantity),
      };
      response.push(oneItem);
    }
    return { items: response };
  }

  _transferModalInventory(inventory: StockMedicRes[]): GetInventoryProductItemByShopSkuResponse[] {
    const groupedByShopCode = _.groupBy(inventory, (x) => `${x.shopCode}`);

    return Object?.keys(groupedByShopCode)?.map((key) => {
      const currentInvent = groupedByShopCode[key][0];
      return {
        shopCode: currentInvent?.shopCode,
        sku: currentInvent?.sku,
        skuName: '',
        productItems: groupedByShopCode[key],
      };
    });
  }

  async getStockHistoryBySku(body: GetXntReportDto): Promise<GetStockHistoryBySkuResponse> {
    const response: GetInventoryHistoryBySkuResponse = {
      page: null,
      pageSize: null,
      totalItem: 0,
      totalPage: 0,
      data: [],
    };
    const { fromDate, toDate } = body;
    // Lấy data từ ngày đầu tiên của tháng để tính tồn đầu kỳ cho những ngày sau
    const fromDateFormat = moment(fromDate, 'YYYY-MM-DD').utcOffset(7).startOf('M').format('YYYY-MM-DDTHH:mm:ss');
    const toDateFormat = moment(toDate, 'YYYY-MM-DD').utcOffset(7).format('YYYY-MM-DDTHH:mm:ss');
    const yearMonth = moment(fromDate, 'YYYY-MM-DD').utcOffset(7).format('YYYYMM');

    try {
      let allDataFetched = false;
      let page = 1;
      const maxResultCount = +process.env.PAGE_SIZE_REPORT_INVENTORY;

      while (!allDataFetched) {
        try {
          const { data: batchData } = await this.inventoryHistoryService.getInventoryHistoryBySku({
            sku: body.sku || '',
            shopCodes: [body.shopCode],
            whsCodes: [body.whsCode],
            toDate: toDateFormat,
            fromDate: fromDateFormat,
            productItemCodes: [body?.productItemCode].filter((x) => x),
            page,
            pagesize: maxResultCount,
          });
          // Assuming batchData is an array of the data fetched
          if (batchData?.length > 0) {
            // Process and add the batch data to arrReturn
            // For example, if you need to transform or directly add the batchData
            response.data.push(...batchData); // Adjust this line based on the actual structure of batchData
            if (batchData?.length < maxResultCount) {
              // If the number of items in the batch is less than maxResultCount, it means this is the last batch
              allDataFetched = true;
            } else {
              // Prepare for the next iteration
              page++;
            }
          } else {
            // No more data to fetch
            allDataFetched = true;
          }
        } catch (error) {
          allDataFetched = true;
          Logger.log({
            fields: {
              info: '[getInventoryHistoryBySku] Error fetching data',
              error: error,
            },
          });
        }
      }

      const employeeCodes = [];
      _.forEach(response?.data, (item: GetInventoryHistoryBySkuItem) => {
        // get employee code and remove redundant fields
        if (!_.isEmpty(item?.transCreatedBy)) {
          employeeCodes.push(item.transCreatedBy);
        }
        delete item?.productItemInventory;
      });
      // get employee name and map to response
      if (!_.isEmpty(employeeCodes)) {
        const employeeData = await this.insideService.getEmployeeByCode({ listEmployeeCode: employeeCodes });
        const employeeMap = employeeData?.reduce((map, obj) => {
          map[obj?.employeeCode] = obj?.employeeName;
          return map;
        }, {});
        _.forEach(response?.data, (item) => {
          item.transCreatedName = employeeMap[item?.transCreatedBy] || '';
        });
      }
    } catch (error) {}
    let dataBeginMonth;
    try {
      // get data đầu kì
      dataBeginMonth = await this.osrService.getInventoryPeriod({
        sku: `${body.sku}`,
        period: yearMonth,
        whsCode: `${body?.whsCode}`,
        productItemCode: `${body?.productItemCode}`,
      });
    } catch (error) {
      console.log(error);
    }

    const rs = _.orderBy(
      response.data.map((r) => ({ ...r, ...r.item })),
      ['lotNumber', 'expiryDate', 'transDate'],
      ['asc', 'asc'],
    ).map((x) => {
      delete x?.skuInventory;
      delete x?.item;
      return x;
    });

    const groupByData = _.groupBy(rs, (s) => `${s.productItemCode}`);
    const productItemCodes = [];
    const rsFinal = [];
    Object?.keys(groupByData).forEach((x, i) => {
      const fistRetrod = groupByData[x][0];
      const beginMonth = dataBeginMonth?.find((m) => m?.productItemCode === x)?.quantity || 0;
      let begin = beginMonth;
      let end = beginMonth;
      let isExitHeaderLoDate = false;
      let lineHeader = null;
      const rsTemp = groupByData[x].map((item, index) => {
        begin = end;
        end = begin + item?.quantity;
        // Check line start làm header
        if (moment(fromDate, 'YYYY-MM-DD').isSameOrBefore(moment(moment(item?.transDate)?.format('YYYY-MM-DD')))) {
          productItemCodes.push(item?.productItemCode);
          if (!isExitHeaderLoDate) {
            isExitHeaderLoDate = true;
            lineHeader = {
              begin: begin,
              in: 0,
              out: 0,
              end: end,
              lotNumber: fistRetrod?.lotNumber,
              expiryDate: fistRetrod?.expiryDate,
              isHeader: true,
            };
            return {
              begin,
              in: item?.quantity < 0 ? 0 : +item?.quantity,
              out: item?.quantity < 0 ? Math.abs(item?.quantity) : 0,
              end,
              isHeader: false,
              ...item,
            };
          } else {
            return {
              begin,
              in: item?.quantity < 0 ? 0 : +item?.quantity,
              out: item?.quantity < 0 ? Math.abs(item?.quantity) : 0,
              end,
              isHeader: false,
              ...item,
            };
          }
        }
      });

      const totalIn = rsTemp?.reduce((acc, cur) => acc + cur?.in, 0) || 0;
      const totalOut = rsTemp?.reduce((acc, cur) => acc + cur?.out, 0) || 0;

      if (lineHeader) rsFinal.push({ ...lineHeader, in: totalIn, out: totalOut });
      rsFinal.push(..._.compact(rsTemp));
      // Gắn tồn cuối lại cho header
      const findIndex = rsFinal?.findIndex((r) => r?.lotNumber === fistRetrod?.lotNumber && r?.isHeader);
      rsFinal[findIndex] = {
        ...rsFinal[findIndex],
        end: rsTemp?.at(-1)?.end,
      };
    });

    // Lọc ra những dataBeginMonth không có trong productItemCodes
    const dataBeginMonthNotInProductItemCodes = dataBeginMonth?.filter(
      (item) => !productItemCodes.includes(item.productItemCode),
    );

    // Thêm lineHeader từ dataBeginMonthNotInProductItemCodes
    dataBeginMonthNotInProductItemCodes?.forEach((item) => {
      rsFinal.push({
        begin: item?.quantity || 0,
        in: 0,
        out: 0,
        end: item?.quantity || 0,
        lotNumber: item?.lotNumber,
        expiryDate: item?.expiryDate,
        isHeader: true,
      });
    });

    // Tính line tổng
    const totalBegin = rsFinal.filter((x) => x?.isHeader).reduce((acc, cur) => acc + cur?.begin, 0) || 0;
    const totalEnd = rsFinal.filter((x) => x?.isHeader).reduce((acc, cur) => acc + cur?.end, 0) || 0;
    const totalIn = rsFinal.filter((x) => !x?.isHeader).reduce((acc, cur) => acc + cur?.in, 0) || 0;
    const totalOut = rsFinal.filter((x) => !x?.isHeader).reduce((acc, cur) => acc + cur?.out, 0) || 0;
    rsFinal.push({
      begin: totalBegin || 0,
      in: totalIn,
      out: totalOut,
      end: totalEnd || 0,
      isHeader: true,
    });

    return rsFinal as any;
  }
  // END REPORT STOCK

  async getExcelInjectionStatistics(payload: InjectionStatisticsDto) {
    const dataStatistics: ItemDataRes[] = [];

    let allDataFetched = false;
    let numPage = 0;

    while (!allDataFetched) {
      try {
        const { page, items } = await this.rsaReportCoreService.getReportInjectionStatistics({
          ...payload,
          page: numPage,
        });
        // Assuming batchData is an array of the data fetched
        if (items?.length > 0) {
          // Process and add the batch data to arrReturn
          // For example, if you need to transform or directly add the batchData
          dataStatistics.push(...items); // Adjust this line based on the actual structure of batchData
          if (page.totalPages === page?.currentPage + 1) {
            // If the number of items in the batch is less than maxResultCount, it means this is the last batch
            allDataFetched = true;
          } else {
            // Prepare for the next iteration
            numPage++;
          }
        } else {
          // No more data to fetch
          allDataFetched = true;
        }
      } catch (error) {
        allDataFetched = true;
        Logger.log({
          fields: {
            info: '[getReportInjectionStatistics] Error fetching data',
            error: error,
          },
        });
      }
    }

    const buffer = await getExcelInjectionStatistics(dataStatistics);

    const dateString = generateStringByDateTime();

    await this.s3Client.uploadFileToS3(
      buffer,
      `${REPORT_S3_TYPE.REPORT_INJECTION_STATISTICS}/BaoCaoThongKeSoLieuTiem_${dateString}.xlsx`,
    );

    const arrLinks = await this.s3Client.presignObjects(
      [`${REPORT_S3_TYPE.REPORT_INJECTION_STATISTICS}/BaoCaoThongKeSoLieuTiem_${dateString}.xlsx`],
      S3_OPERATIONS.GET_OBJECT,
      PRE_SIGNED_LINK_EXPIRATION,
    );

    return { url: arrLinks[0] };
  }

  //Start 6 in 1 VAT report
  async getVac6In1VATExcelReport(payload: GetVac6In1VATReportDto, employeeCode: string) {
    const { frMonth, frYear, shopCode } = payload;
    const month = `${frMonth?.toString()?.length === 1 ? '0' + frMonth : frMonth}`;
    const request: ReportHexavalentDto = {
      shopCode,
      yearMonth: `${frYear}${month}`,
      size: 10000,
      page: 0,
    };
    const data = await this.rsaReportService.getReportVacHexavanlent(request);

    const excelData = data?.items?.map((x) => ({
      ...x,
      gender: x?.gender,
      dateOfBirth: moment(x?.dateOfBirth).format('DD/MM/YYYY'),
      sixInOneFirstDate: this.checkVacDate(x?.sixInOneFirstDate),
      sixInOneSecondDate: this.checkVacDate(x?.sixInOneSecondDate),
      sixInOneThirdDate: this.checkVacDate(x?.sixInOneThirdDate),
      sixInOneFourthDate: this.checkVacDate(x?.sixInOneFourthDate),
      uonVanFirstDate: this.checkVacDate(x?.uonVanFirstDate),
      uonVanSecondDate: this.checkVacDate(x?.uonVanSecondDate),
    }));
    const listShopByEmployee = await this.insideService.getListShopByEmployee(employeeCode);
    const shopInfo = listShopByEmployee?.shops?.find((shop) => shop?.shopCode === payload?.shopCode);
    const buffer = await getVAT6In1ExcelReport({
      shopName: `${shopCode} - ${shopInfo?.uAddress || ''}`,
      excelData,
      month,
    });

    const dateString = generateStringByDateTime();

    await this.s3Client.uploadFileToS3(buffer, `${REPORT_S3_TYPE.VAT_6IN1}/BaoCao6Trong1UonVan_${dateString}.xlsx`);

    const arrLinks = await this.s3Client.presignObjects(
      [`${REPORT_S3_TYPE.VAT_6IN1}/BaoCao6Trong1UonVan_${dateString}.xlsx`],
      S3_OPERATIONS.GET_OBJECT,
      PRE_SIGNED_LINK_EXPIRATION,
    );

    return { url: arrLinks[0] };
  }

  checkVacDate(date: Date) {
    const formattedDate = moment(date).format('DD/MM/YYYY HH:mm');
    return formattedDate && formattedDate !== 'Invalid date' ? formattedDate : '';
  }
  //End 6 in 1 VAT report

  async getDetailedGeneralFundReports(payload: GetDetailedGeneralFundReportsDto) {
    const resReport = await this.vacReportService.getReportBCQVaccine(payload);

    const shopType = 'VACCINE';
    const resDsms = await this.dsmsService.getAllShopVaccine(shopType);
    resReport?.details?.items?.forEach((item) => {
      const shopInfo = resDsms?.items?.find((itemDsms) => itemDsms.code === item.transaction_ShopCode);
      item.transaction_ShopName = shopInfo?.name || '';
    });
    return resReport;
  }
}
