import { Logger } from '@nestjs/common';
import { EnumDistanceType, isSameDate } from '@shared';
import { plainToInstance } from 'class-transformer';
import { JSONPath } from 'jsonpath-plus';
import { compact, forEach, orderBy, sortBy, uniq, groupBy } from 'lodash';
import moment from 'moment';
import { VaccineHistoryDetailDto, VacHistoryService } from 'vac-nest-history';
import {
  ItemRegimenCloseRes,
  OrderInjectionRes,
  OrderInjectionStatus,
  VacOrderInjectionService,
} from 'vac-nest-order-injection';
import { GetOneProductBySkuRes, PIMAppService } from 'vac-nest-pim-app';
import { RegimenService as RegimenAppService, RegimenItem } from 'vac-nest-regimen';
import { FutureV2 } from 'vac-nest-schedule-engine-app';
import { ItemRegimenOrderInjectionPrevious, RegimenVaccineItem } from '../dto';
import { HistoryToVacOrderDto, VacOrderToRegimenDto } from '../dto/pre-process-data-regimen.dto';
import _ from 'lodash';

export abstract class RegimensAdapter {
  constructor(
    protected readonly historyCoreService: VacHistoryService,
    protected readonly vacOrderInjectionService: VacOrderInjectionService,
    protected readonly pimAppService: PIMAppService,
    protected readonly regimenAppService: RegimenAppService,
  ) {}

  private addDateInDefined(date: Date, distanceType: number, distanceValueByType: number) {
    const newDate = moment(date);
    switch (distanceType) {
      case EnumDistanceType.days:
        newDate.add(distanceValueByType, 'days');
        break;
      case EnumDistanceType.months:
        newDate.add(distanceValueByType, 'months');
        break;
      case EnumDistanceType.years:
        newDate.add(distanceValueByType, 'years');
        break;
      default:
        break;
    }
    return new Date(newDate.format());
  }

  addScheduleAppointmentReminder(
    dataReturn: ItemRegimenOrderInjectionPrevious[],
  ): Array<ItemRegimenOrderInjectionPrevious> {
    // Group theo nhóm bệnh để tính lịch hẹn nhắc lại
    const groupedData = groupBy(
      dataReturn,
      (entry: ItemRegimenOrderInjectionPrevious) => `${entry?.regimenInfo?.diseaseGroupId}-${entry?.lcvId}`,
    );

    _.forEach(groupedData, (items: ItemRegimenOrderInjectionPrevious[]) => {
      const arrListVaccine: RegimenVaccineItem[] = _.sortBy(
        JSONPath({
          path: '$[*].listVaccine[*]',
          json: items,
        }),
        'date',
      );
      const element = items?.at(0);
      const detailRepeatFrequency = element?.regimenInfo?.details?.find((e) => e.repeatFrequency);
      if (!detailRepeatFrequency) return;
      // Check xem có mua nhà mình k?
      const arrItsMeAdd = arrListVaccine?.filter((e) => !e.sourceId || [0, 1].includes(e.sourceId));
      if (!arrItsMeAdd?.length) return;
      // check đủ mũi hay chưa?
      if (arrListVaccine?.length < element?.regimenInfo?.details?.length) return;
      // check lịch hẹn
      const arrCurrentSchedule = arrListVaccine.filter((e) => e.status === 0);
      if (arrCurrentSchedule?.length) return;
      // Add lịch hẹn
      const { distanceType, distanceValueByType } = detailRepeatFrequency;
      let dateTheBest = this.addDateInDefined(
        new Date(arrListVaccine?.at(-1)?.date),
        distanceType,
        distanceValueByType,
      );

      if (new Date() > new Date(dateTheBest)) {
        dateTheBest = new Date();
      }

      const indexFind = dataReturn?.findIndex(
        (dataEntry) => dataEntry?.regimenId === element?.regimenId && dataEntry?.lcvId === element?.lcvId,
      );
      if (indexFind === -1) return;
      dataReturn[indexFind].listVaccine.push({
        ...arrListVaccine?.at(-1),
        status: 0,
        date: dateTheBest,
        appointmentDate: dateTheBest.toISOString(),
        sourceId: 0,
        orderInjections:
          _.maxBy(
            arrListVaccine?.filter((e) => e.regimenId === dataReturn[indexFind]?.regimenId),
            'orderInjections',
          )?.orderInjections + 1,
        orderCode: '',
        ticketCode: '',
        orderDetailAttachmentId: null,
        orderDetailAttachmentCode: '',
        vacOrderCode: '',
        scheduleId: '',
      });
    });

    // dataReturn.forEach((element) => {
    //   // Check có thông tin mũi nhắc lại không?
    //   // Nếu có thì check xem có lịch hẹn => Có thì bỏ qua. K có thì add lịch hẹn
    //   const detailRepeatFrequency = element?.regimenInfo?.details?.find((e) => e.repeatFrequency);
    //   if (!detailRepeatFrequency) return;
    //   // Check xem có mua nhà mình k?
    //   const arrItsMeAdd = element?.listVaccine?.filter((e) => !e.sourceId || [0, 1].includes(e.sourceId));
    //   if (!arrItsMeAdd?.length) return;
    //   // check đủ mũi hay chưa?
    //   if (element?.listVaccine?.length < element?.regimenInfo?.details?.length) return;
    //   // check lịch hẹn
    //   const arrCurrentSchedule = element?.listVaccine.filter((e) => e.status === 0);
    //   if (arrCurrentSchedule?.length) return;
    //   // Add lịch hẹn
    //   const { distanceType, distanceValueByType } = detailRepeatFrequency;
    //   let dateTheBest = this.addDateInDefined(
    //     new Date(element?.listVaccine?.at(-1)?.date),
    //     distanceType,
    //     distanceValueByType,
    //   );

    //   if (new Date() > new Date(dateTheBest)) {
    //     dateTheBest = new Date();
    //   }

    //   element?.listVaccine?.push({
    //     ...element?.listVaccine?.at(-1),
    //     status: 0,
    //     date: dateTheBest,
    //     sourceId: 0,
    //     orderInjections: _.maxBy(element?.listVaccine, 'orderInjections')?.orderInjections + 1,
    //     orderCode: '',
    //     ticketCode: '',
    //     orderDetailAttachmentId: null,
    //     orderDetailAttachmentCode: '',
    //     vacOrderCode: '',
    //     scheduleId: '',
    //   });
    //   return;
    // });

    return dataReturn;
  }

  /**
   * @description Tính lại mũi thứ
   */
  convertOrderInjection(
    dataReturn: ItemRegimenOrderInjectionPrevious[],
    isCheckAppointmentDate?: boolean,
  ): ItemRegimenOrderInjectionPrevious[] {
    const listVaccine: RegimenVaccineItem[] = JSONPath({
      path: '$[*].listVaccine[*]',
      json: dataReturn,
    });
    const groupedData = groupBy(listVaccine, (entry: RegimenVaccineItem) => `${entry.sku}-${entry.lcvId}`);
    const arrData: {
      sku: string;
      date: string;
      status: string;
      orderInjections: number;
      regimenId: string;
      lcvId: string;
    }[] = [];
    forEach(groupedData, (entry: RegimenVaccineItem[]) => {
      // parse string mới sort theo date dc
      entry.forEach((e) => {
        e.date = new Date(isCheckAppointmentDate ? e?.appointmentDate : e?.date || e?.appointmentDate).toISOString();
        e.appointmentDate = new Date(isCheckAppointmentDate ? e?.appointmentDate : e?.date).toISOString();
      });
      entry = orderBy(entry, ['date'], ['asc']);
      let index = 0;
      forEach(entry, (item) => {
        arrData.push({
          sku: item.sku,
          date: item?.date || item?.appointmentDate,
          status: item.status,
          orderInjections: ++index,
          regimenId: item.regimenId,
          lcvId: item.lcvId,
        });
      });
    });

    forEach(dataReturn, (item: ItemRegimenOrderInjectionPrevious) => {
      item['listVaccine'].forEach((element) => {
        const dataFind = arrData.find(
          (e) =>
            e.sku === element.sku &&
            isSameDate(new Date(e?.date), new Date(element?.date)) &&
            e.regimenId === element.regimenId &&
            e.lcvId === element.lcvId,
        );
        if (dataFind) {
          element.orderInjections = dataFind.orderInjections;
        }
      });
    });

    return dataReturn;
  }

  /**
   * @description Pre process data regimen
   */
  async addFieldProduct(dataReturn: ItemRegimenOrderInjectionPrevious[]) {
    const arrSku = dataReturn.map((e) => e?.regimenInfo?.vaccine?.sku).flat();
    if (!arrSku?.length) return dataReturn;
    const { listProduct } = await this.pimAppService.getListProductBySkuNoRule(arrSku);
    dataReturn.forEach((item) => {
      item['regimenInfo']['vaccine']['isMultiDose'] = listProduct.find(
        (e) => e.sku === item?.regimenInfo?.vaccine?.sku,
      )?.isMultiDose;
    });
    return dataReturn;
  }

  async preProcessDataRegimen(
    lcvIds: Array<string>,
    isRegimenClose?: boolean,
  ): Promise<{
    dataReturn: Array<ItemRegimenOrderInjectionPrevious>;
    singleRegimens: Array<RegimenItem>;
    arrFutureV2: Array<FutureV2 & { lcvId: string }>;
  }> {
    Logger.log(`============== START PRE PROCESS DATA REGIMEN ==============`);
    const historyOfUser = await this.historyCoreService.getManyByLcvIds(lcvIds);
    const dataReturn: ItemRegimenOrderInjectionPrevious[] = [];
    const singleRegimens: Array<RegimenItem> = [];
    const arrFutureV2: Array<FutureV2 & { lcvId: string }> = [];
    const { items } = await this.vacOrderInjectionService.searchByPersons({
      personCodes: lcvIds,
      personCode: lcvIds?.at(0),
    });

    for (const lcvId of lcvIds) {
      Logger.log(`============== START lcvID: ${lcvId} ==============`);
      // vacOrder
      const vacOrderUserCurrent = isRegimenClose
        ? []
        : _.sortBy(
            items.filter((item) => item.personId === lcvId && item?.status !== 2),
            'appointmentDate',
          );
      Logger.log(`vacOrderUserCurrent: ${JSON.stringify(vacOrderUserCurrent)}`);
      // history
      const historyUserCurrent = historyOfUser
        .find((item) => item.lcvId === lcvId)
        ?.history?.filter((history) => history?.isRegimenClose === isRegimenClose);

      Logger.log(`historyUserCurrent: ${JSON.stringify(vacOrderUserCurrent)}`);
      const skus: Array<string> = compact(uniq(historyUserCurrent?.map((e) => e.sku)))?.concat(
        compact(uniq(vacOrderUserCurrent.map((e) => e.sku))),
      );
      const products: GetOneProductBySkuRes[] = [];
      if (skus?.length) {
        const { listProduct } = await this.pimAppService.getListProductBySkuNoRule(skus);
        products.push(...listProduct);
      }
      // Map history to vacOrder
      forEach(historyUserCurrent, (historyEntry: VaccineHistoryDetailDto & { priority?: number }) => {
        // Bỏ qua nhửng history nào k chất lượng
        Logger.log(`============== START HISTORY ==============`);

        Logger.log(`history RegimenID: ${historyEntry?.regimenId}`);
        Logger.log(`history sku: ${historyEntry?.sku}`);
        if (!historyEntry?.regimenId || !historyEntry?.sku) return;

        const product = products?.find((p) => p.sku === historyEntry.sku);
        const measure = product?.measures?.find((m) => m.isSellDefault);
        Logger.log(`measure: ${JSON.stringify(measure)}`);
        const vacOrderFindSku = vacOrderUserCurrent?.find((vacOrder) => vacOrder?.sku === historyEntry?.sku);
        Logger.log(`vacOrderFindSku: ${JSON.stringify(vacOrderFindSku)}`);
        Logger.log(`plain: ${JSON.stringify({ ...historyEntry, ...measure })}`);
        // đánh thêm priority cho history để sort theo mũi thứ
        // historyEntry['priority'] = 1;
        const item = plainToInstance(
          HistoryToVacOrderDto,
          { ...historyEntry, ...measure },
          {
            excludeExtraneousValues: true,
            exposeUnsetFields: false,
          },
        );
        Logger.log(`item: ${JSON.stringify(item)}`);
        vacOrderUserCurrent.push(item);
        Logger.log(`============== END HISTORY ==============`);
      });

      const regimentIds: Array<string> = compact(uniq(vacOrderUserCurrent.map((e) => e.regimenId)));
      Logger.log(`regimentIds: ${JSON.stringify(regimentIds)}`);
      if (!regimentIds?.length) continue;
      const regimens = await this.regimenAppService.getRegimenByIds({
        regimenIds: regimentIds,
      });
      singleRegimens.push(...regimens);

      forEach(vacOrderUserCurrent, (vacOrder: OrderInjectionRes & { priority?: number }) => {
        Logger.log(`==================== START VAC ORDER ====================`);

        const indexFind = dataReturn?.findIndex(
          (dataEntry) => dataEntry?.lcvId === lcvId && dataEntry?.regimenId === vacOrder?.regimenId,
        );
        Logger.log(`DataReturn: ${JSON.stringify(dataReturn)}`);
        Logger.log(`indexFind: ${indexFind}`);
        const regimentInfo = singleRegimens.find((regimentEntry) => regimentEntry.id === vacOrder.regimenId);
        Logger.log(`regimentInfo: ${JSON.stringify(regimentInfo)}`);
        if (!regimentInfo) return;
        const product = products?.find((p) => p.sku === vacOrder.sku);
        Logger.log(`plain: ${JSON.stringify({ regimentInfo, ...vacOrder, product })}`);
        const dataInsert = plainToInstance(
          VacOrderToRegimenDto,
          { regimentInfo, ...vacOrder, product, lcvId },
          {
            excludeExtraneousValues: true,
            exposeUnsetFields: false,
          },
        );
        const isBefore = moment(moment(dataInsert.date).utcOffset(7).format('YYYY-MM-DD')).isBefore(
          moment(moment().format('YYYY-MM-DD')),
          'D',
        );
        Logger.log(`isBefore: ${isBefore}`);

        Logger.log(`status: ${vacOrder?.status}`);
        Logger.log(`DA_TIEM: ${OrderInjectionStatus.Da_tiem}`);
        let isPushFuture = true;
        if (isBefore && dataInsert.date && vacOrder.status !== OrderInjectionStatus.Da_tiem) {
          // dataInsert.date = '';
          isPushFuture = false;
        }

        if (dataInsert?.date && OrderInjectionStatus.Da_tiem === dataInsert?.status) {
          arrFutureV2.push({
            date: moment(dataInsert.date).format(),
            sku: vacOrder.sku,
            regimenId: regimentInfo?.id,
            status: vacOrder?.status,
            diseaseGroupId: regimentInfo?.diseaseGroupId,
            lcvId: lcvId,
          });
        }

        // trường hợp mũi status = 5 hoặc status = 1 (đã mua) và k trễ hẹn thì push vào arrFuture
        if ((vacOrder.status === 5 || vacOrder.status === 1) && !isBefore && dataInsert.date) {
          arrFutureV2.push({
            date: moment(dataInsert.date).format(),
            sku: vacOrder.sku,
            regimenId: regimentInfo?.id,
            status: vacOrder?.status,
            diseaseGroupId: regimentInfo?.diseaseGroupId,
            lcvId: lcvId,
          });
        }

        if (indexFind === -1) {
          dataReturn.push({
            listVaccine: [dataInsert],
            regimenId: vacOrder['regimenId'],
            lcvId: lcvId,
            regimenInfo: regimentInfo,
          });
        } else {
          dataReturn[indexFind].listVaccine.push(dataInsert);
          dataReturn[indexFind].listVaccine = orderBy(dataReturn[indexFind].listVaccine, ['orderInjections', 'date']);
        }

        Logger.log(`==================== END VAC ORDER ====================`);
      });

      Logger.log(`============== END lcvID: ${lcvId} ==============`);
    }
    // sort dataReturn
    // dataReturn.forEach((item) => {
    //   item.listVaccine = sortBy(item.listVaccine, (z: RegimenVaccineItem) => {
    //     return moment(z.date || '9999-12-31').valueOf();
    //   });
    // });

    dataReturn.forEach((item) => {
      item.listVaccine = _.orderBy(
        item.listVaccine,
        [
          'priority',
          (z: RegimenVaccineItem) => {
            return moment(z.date || '9999-12-31').valueOf();
          },
        ],
        ['desc', 'asc'],
      );
    });

    Logger.log(`DataReturn: ${JSON.stringify(dataReturn)}`);
    Logger.log(`============== END PRE PROCESS DATA REGIMEN ==============`);
    return { dataReturn, singleRegimens, arrFutureV2: arrFutureV2 };
  }

  /**
   * @description Lấy thông tin lịch tiêm theo phác đồ
   */
  abstract getRegimenVaccineOrderV2(lcvIds: string[]): Promise<{
    items: ItemRegimenOrderInjectionPrevious[];
  }>;

  /**
   * @description Lấy thông tin phác đồ toàn phần
   */
  abstract getRegimentCloseFull(lcvIds: string[]): Promise<{
    items: ItemRegimenOrderInjectionPrevious[];
  }>;

  /**
   * @description Lấy Lịch tiêm không mua
   */
  abstract getScheduleNotBuy(lcvIds: string): Promise<{
    items: ItemRegimenOrderInjectionPrevious[];
  }>;
}
