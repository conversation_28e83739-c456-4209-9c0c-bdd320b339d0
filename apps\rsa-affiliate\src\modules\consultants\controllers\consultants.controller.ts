import { Body, Controller, HttpCode, HttpStatus, Post, Req } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, Public, generalSchema } from '@shared';
import { ConsultantsService } from '../services/consultants.service';
import { CreateConsultantDto, CreateConsultantRes } from '@libs/modules/consultants/dto';

@Controller({ path: 'consultants', version: '1' })
@ApiTags('Consultant')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(ClassResponse, CreateConsultantRes)
export class ConsultantsController {
  constructor(private readonly advisesService: ConsultantsService) {}

  @Public()
  @Post()
  @ApiOperation({
    summary: 'Nút tư vấn',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Journey',
    schema: generalSchema(CreateConsultantRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  async createConsultant(@Body() createdAdvisesDto: CreateConsultantDto, @Req() req: any) {
    return this.advisesService.createConsultant(createdAdvisesDto);
  }
}
