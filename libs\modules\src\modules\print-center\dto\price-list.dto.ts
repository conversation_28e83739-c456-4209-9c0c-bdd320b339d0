import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { PrinterInfoDto } from 'vac-nest-print-center';

export class IndicationDto {
  @ApiProperty()
  @Expose()
  diseaseName?: string;

  @ApiProperty()
  @Expose()
  vaccineName?: string;

  @ApiProperty()
  @Expose()
  manufactor?: string;

  @ApiProperty()
  @Expose()
  quantity?: number;

  @ApiProperty()
  @Expose()
  price?: number;

  @ApiProperty()
  @Expose()
  priceDiscountPromotion?: number;

  @ApiProperty()
  @Expose()
  priceDiscountAdjustment?: number;

  @ApiProperty()
  @Expose()
  priceAfterDiscount?: number;

  @ApiProperty()
  @Expose()
  serviceFee?: number;

  @ApiProperty()
  @Expose()
  amount?: number;

  @ApiProperty()
  @Expose()
  injectionDate?: string;
}

export class PrintDataDto {
  @ApiProperty()
  @Expose()
  shopName?: string;

  @ApiProperty()
  @Expose()
  shopAddress?: string;

  @ApiProperty()
  @Expose()
  hotline?: string;

  @ApiProperty()
  @Expose()
  date?: string;

  @ApiProperty({ type: IndicationDto, isArray: true })
  @Expose()
  @Type(() => IndicationDto)
  indication?: IndicationDto;

  @ApiProperty({ type: IndicationDto, isArray: true })
  @Expose()
  @Type(() => IndicationDto)
  schedule?: IndicationDto[];

  @ApiProperty()
  @Expose()
  totalPrice?: number;

  @ApiProperty()
  @Expose()
  totalDiscount?: number;

  @ApiProperty()
  @Expose()
  totalPriceAfterDiscount?: number;

  @ApiProperty()
  @Expose()
  totalServiceFee?: number;

  @ApiProperty()
  @Expose()
  totalAmount?: number;

  @ApiProperty()
  @Expose()
  voucher?: number;

  @ApiProperty()
  @Expose()
  totalBill?: number;

  @ApiProperty()
  @Expose()
  promotionName?: string[];

  @ApiProperty()
  @Expose()
  totalDiscountAdjustment?: number;

  @ApiProperty()
  @Expose()
  totalDirectDiscount?: number;

  @ApiProperty()
  @Expose()
  totalQuantity?: number;
}

export class PriceListDto {
  @ApiProperty({ type: PrinterInfoDto })
  @IsOptional()
  @Expose()
  printerInfo: PrinterInfoDto;

  @ApiProperty({ type: PrintDataDto })
  @IsOptional()
  @Expose()
  printData: PrintDataDto;
}
