import { PagedRewardTransactionResponseDto, RewardTransactionResponseDto } from '@frt/nestjs-api/dist/reward-api';
import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray } from 'class-validator';

export class RewardTransactionTotalByTypeDto {
  @ApiProperty()
  @Expose()
  rewardType: number;

  @ApiProperty()
  @Expose()
  totalRewardValue: number;
}

export class RewardTransactionItemResponseDto extends RewardTransactionResponseDto {
  @ApiProperty({ type: [RewardTransactionTotalByTypeDto] })
  @Expose()
  @Type(() => RewardTransactionTotalByTypeDto)
  totalRewardValueByType: RewardTransactionTotalByTypeDto[];
}

export class RewardTransactionPagedResponseDto extends PagedRewardTransactionResponseDto {
  @IsArray()
  @Type(() => RewardTransactionItemResponseDto)
  @Expose()
  @ApiProperty({ type: [RewardTransactionItemResponseDto] })
  items: RewardTransactionItemResponseDto[];
}
