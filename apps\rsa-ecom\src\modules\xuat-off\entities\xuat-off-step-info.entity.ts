import { AbstractEntity } from '@shared/database/typeorm/abtract.entity';
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { XuatOffEntity } from './xuat-off.entity';

@Entity({ name: 'xuat_off_step_info' })
export class XuatOffStepInfoEntity extends AbstractEntity<XuatOffStepInfoEntity> {
  @Column({ name: 'xo_code', type: 'varchar', length: 100 })
  xoCode?: string;

  @Column({ name: 'employee_code', type: 'varchar', length: 100 })
  employeeCode: string;

  @Column({ name: 'employee_name', type: 'varchar', length: 100 })
  employeeName: string;

  @Column({ type: 'int' })
  step: number;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @ManyToOne(() => XuatOffEntity, (xuatOff) => xuatOff.steps, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'xo_id' })
  xuatOff?: XuatOffEntity;
}
