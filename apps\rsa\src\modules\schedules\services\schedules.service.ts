import {
  SearchVaccineHistoryRes,
  ScheduleInjectionFamilyRes,
  PayloadInjectionHistoryDto,
  ItemVaccineHistoryRes,
} from './../../../../../../libs/modules/src/modules/schedules/dto';
import { Injectable } from '@nestjs/common';
import { STATUS_CALENDAR, STATUS_PAYMENT } from '@shared/enum/payment';
import * as _ from 'lodash';
import { FamilyService, GetPersonByIdRes } from 'vac-nest-family';
import { VacHistoryService } from 'vac-nest-history';
import { OMSService } from 'vac-nest-oms';
import { UpdateAppointmentDateDto, VacOrderInjectionService } from 'vac-nest-order-injection';
import { ScheduleCoreService } from 'vac-nest-schedule';
import { ItemSchedule, PayloadSchedule, ScheduleEngineAppService } from 'vac-nest-schedule-engine-app';
import { UpdateManySchedule } from '../../../../../../libs/modules/src/modules/customers/dto';
import { SyncHistoryTCQG } from '../../../../../../libs/modules/src/modules/schedules/dto';
import { SearchHistoryOrderOmsDto } from '../../../../../../libs/modules/src/modules/schedules/dto';

@Injectable()
export class SchedulesService {
  constructor(
    private readonly scheduleEngineAppService: ScheduleEngineAppService,
    private readonly familyCoreService: FamilyService,
    private readonly vacHistoryService: VacHistoryService,
    private readonly scheduleCoreService: ScheduleCoreService,
    private readonly omsCoreService: OMSService,
    private readonly vacOrderCoreService: VacOrderInjectionService,
  ) {}

  /**
   * @description lấy danh sách lịch hẹn của cá nhân và người liên hệ
   */
  async getInjectionSchedule(body: PayloadSchedule): Promise<ScheduleInjectionFamilyRes> {
    const { personId, familyId } = body;

    let familysforCurrentPerson = []; // danh sách người liên hệ bao gồm person hiện tại => phục vụ cho việc lấy tên, lcvid, chức danh
    try {
      let person: GetPersonByIdRes;

      if (!familyId) {
        person = await this.familyCoreService.getFamilyById(personId);
      } else {
        person = await this.familyCoreService.getFamilyById(personId, { familyId });
      }

      if (person?.familyProfileDetails.length > 0) {
        familysforCurrentPerson = person?.familyProfileDetails; // lấy thông tin người quan hệ
      }

      familysforCurrentPerson.push({
        dateOfBirth: person?.dateOfBirth,
        familyProfiledId: person?.familyProfileId,
        gender: person?.gender,
        lcvId: person?.lcvId,
        name: person?.name,
        personId: person?.id,
        titleName: person?.jobTitle,
      });
    } catch (error) {}

    const personIds = familysforCurrentPerson?.map((person) => person?.personId);

    const listSchedule = await this.scheduleEngineAppService.getListInjectionScheduleByPersons({
      personIds,
    });

    if (listSchedule?.items?.length <= 0) {
      return {
        items: [],
      };
    }

    const arrSchedule =
      listSchedule?.items?.map((item) => {
        const personInfo = familysforCurrentPerson?.find((info) => info?.personId === item?.personId);

        return {
          ...item,
          personName: personInfo?.name ?? '',
          titleName: personInfo?.jobTitle ?? '',
          lcvId: personInfo?.lcvId ?? null,
        };
      }) || [];

    return {
      items: arrSchedule,
    };
  }

  /**
   * @description cập nhật lịch tiêm
   * @param body
   * @returns
   */
  async updateManySchedule(body: UpdateManySchedule) {
    const { arrSchedule } = body;

    // update vac order
    // map appointmentDate có orderDetailAttachmentCode khác null để phục vụ cho việc update vac order
    const listUpdateVacOrder: UpdateAppointmentDateDto[] = [];

    for (const item of arrSchedule) {
      if (item?.orderDetailAttachmentCode === '') continue;
      const itemVacOrder = {
        appointmentDate: item?.appointmentDate,
        orderDetailAttachmentCode: item?.orderDetailAttachmentCode,
      };
      listUpdateVacOrder.push(itemVacOrder);
    }

    if (listUpdateVacOrder?.length > 0) {
      await this.vacOrderCoreService.updateAppointmentDates(listUpdateVacOrder);
    }

    // update schedule
    const data = await this.scheduleEngineAppService.updateManySchedule({ items: arrSchedule });
    return data;
  }

  async syncHistoryTCQG(body: SyncHistoryTCQG) {
    const { personId } = body;
    let name: string = '';
    try {
      const person = await this.familyCoreService.getPersonById(personId);
      name = person?.name;
    } catch (error) {}

    const vaccineScheduleHistory = await this.vacHistoryService.syncHistoryTCQG(body);

    let arrScheduleHistory: ItemSchedule[] = [];

    if (vaccineScheduleHistory?.length)
      vaccineScheduleHistory?.map((schedule) => {
        const itemSchedule = {
          id: schedule?.id,
          appointmentDate: schedule?.vaccinatedDate,
          sku: schedule?.sku ?? '',
          taxonomies: schedule?.taxonomies ?? '',
          manufactor: schedule?.manufactor ?? '',
          injections: schedule?.injection ?? 0,
          vaccineName: schedule?.vaccineName ?? '',
          shopName: schedule?.shopName ?? '',
          shopCode: schedule?.shopCode ?? '',
          statusAppointment: STATUS_CALENDAR.DA_TIEM,
          statusPayment: STATUS_PAYMENT.DA_THANH_TOAN,
          customerNote: '',
          createdByName: '',
          note: '',
          isScheduleFromHistory: true,
          personName: name,
        };
        arrScheduleHistory = [...arrScheduleHistory, itemSchedule];
      });

    const listSchedule = await this._getScheduleFromSchedule(personId);
    if (listSchedule?.length <= 0)
      return {
        items: arrScheduleHistory,
      };
    const arrSchedule =
      listSchedule
        ?.filter((entry) => !entry?.isScheduleFromHistory)
        ?.map((item) => {
          return {
            ...item,
            personName: name,
          };
        }) || [];
    return {
      items: [...arrScheduleHistory, ...arrSchedule],
    };
  }

  /**
   * @TODO lấy danh sách lịch tiêm từ schedule tương lai
   * @param body personId
   */
  async _getScheduleFromSchedule(personId: string) {
    const { items } = await this.scheduleCoreService.getScheduleByPerson({ personId: personId });

    let arrSchedule: ItemSchedule[] = [];

    if (items.length === 0) return arrSchedule;

    items?.map((injection) => {
      const status_payment = injection?.orderDetailAttachmentCode
        ? STATUS_PAYMENT.DA_THANH_TOAN
        : STATUS_PAYMENT.CHUA_THANH_TOAN;
      const schedule: ItemSchedule = {
        id: injection?.id,
        appointmentDate: injection?.appointmentDate,
        injections: injection?.injections ?? null,
        manufactor: injection?.manufactor ?? '',
        vaccineName: injection?.skuName ?? '',
        taxonomies: injection?.taxonomies ?? '',
        sku: injection?.sku ?? '',
        shopName: injection?.shopName ?? '',
        shopCode: injection?.shopCode ?? '',
        statusPayment: status_payment,
        statusAppointment: injection?.status,
        createdByName: injection?.createdByName ?? '',
        customerNote: injection?.customerNote ?? '',
        note: injection?.note ?? '',
        isScheduleFromHistory: false,
      };
      arrSchedule = [...arrSchedule, schedule];
    });

    return arrSchedule;
  }

  /**
   * @TODO tra cứu lịch sử tiêm
   */
  async searchInjectionHistory(payload: PayloadInjectionHistoryDto): Promise<SearchVaccineHistoryRes> {
    let outPut: ItemVaccineHistoryRes[] = [];

    const data = await this.vacHistoryService.searchByCustomer(payload);
    if (!data || data.totalCount === 0) {
      return {
        totalCount: 0,
        items: outPut,
      };
    }

    // call family
    // lấy lcvId
    const { items } = await this.familyCoreService.getPersonByCustId(payload?.customerId);

    const { lcvId } = items?.at(0) || null;

    data['items']?.map((item) => {
      const result: ItemVaccineHistoryRes = {
        customerName: item?.personName,
        nationVaccineCode: item?.nationVaccineCode,
        gender: item?.gender,
        birthday: item?.birthday,
        vaccineName: item?.vaccineName,
        noNumberVaccine: item?.noNumberVaccine,
        vaccineDate: item?.vaccineDate,
        scheduleDate: item?.scheduleDate,
        shopCode: item?.shopCode,
        shopAddress: item?.shopAddress,
        shopName: item?.shopName,
        statusCode: item?.statusCode,
        statusname: item?.statusname,
        note: item?.note,
        lcvId,
      };
      outPut.push(result);
    });

    outPut = _.orderBy(outPut, [(obj) => new Date(obj?.vaccineDate)], ['desc']);

    return {
      totalCount: data?.totalCount,
      items: outPut,
    };
  }

  /**
   * @TODO tra cứu lịch hẹn tiêm
   */
  async searchInjectionSchedule(payload: PayloadInjectionHistoryDto): Promise<SearchVaccineHistoryRes> {
    const { customerId } = payload;
    let outPut: ItemVaccineHistoryRes[] = [];

    // call family
    // lấy lcvId
    const { items } = await this.familyCoreService.getPersonByCustId(customerId);

    if (!items || items?.length === 0) {
      return {
        totalCount: 0,
        items: outPut,
      };
    }

    const { lcvId, name, nationalVaccineCode, gender, dateOfBirth } = items?.at(0) || null;

    const data = await this.scheduleCoreService.getScheduleByPersonCode({
      ...payload,
      personCode: lcvId,
    });
    if (!data || data.totalCount === 0) {
      return {
        totalCount: 0,
        items: outPut,
      };
    }

    data['items']?.map((item) => {
      const genderName = gender === 0 ? 'Nam' : gender === 1 ? 'Nữ' : 'Khác';
      const statusName = item?.status === 0 ? 'Đã hẹn' : item?.status === 1 ? 'Đã đến' : 'Huỷ';
      const note = `${item?.taxonomies} - ${item?.manufactor}`;
      const result: ItemVaccineHistoryRes = {
        customerName: name,
        nationVaccineCode: nationalVaccineCode ?? null,
        gender: genderName,
        birthday: dateOfBirth,
        vaccineName: item?.skuName,
        noNumberVaccine: item?.injections,
        vaccineDate: null,
        scheduleDate: item?.appointmentDate,
        shopCode: item?.shopCode,
        shopAddress: null,
        shopName: item?.shopName,
        statusCode: item?.status,
        statusname: statusName,
        note,
        lcvId,
      };
      outPut.push(result);
    });

    outPut = _.orderBy(outPut, [(obj) => new Date(obj?.scheduleDate)], ['desc']);

    return {
      totalCount: data?.totalCount,
      items: outPut,
    };
  }

  /**
   * @TODO tra cứu lịch sử đơn
   */
  async searchHistoryOrder(body: SearchHistoryOrderOmsDto) {
    const payload = {
      ...body,
      orderType: ['8'], // filter only so1 for vaccine
    };
    const data = await this.omsCoreService.searchOrderESForZalo(payload);
    return data;
  }
}
