import { Module } from '@nestjs/common';
import { OrderPromissingWrapService } from './services/order-promissing-wrap.services';
import { OrderPromissingWrapController } from './controllers/order-promissing-wrap.controller';
import { OrderPromissingModule } from 'vac-order-promising';

@Module({
  imports: [OrderPromissingModule],
  controllers: [OrderPromissingWrapController],
  providers: [OrderPromissingWrapService],
  exports: [],
})
export class OrderPromissingWrapModule {}
