import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldListRefSessionIdCallLogV2 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE call_log_v2
      ADD COLUMN list_ref_session_id VARCHAR(255) ARRAY
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE call_log_v2
      DROP COLUMN list_ref_session_id
    `);
  }
}
