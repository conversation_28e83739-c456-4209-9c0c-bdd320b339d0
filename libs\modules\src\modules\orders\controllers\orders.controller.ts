import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import {
  BlockAdjustOrderInterceptor,
  ClassErrorResponse,
  ClassResponse,
  CustomHeaders,
  Public,
  generalSchema,
} from '@shared';
import { GetDetailsByTicketCodeDTO } from 'modules/modules/modules/orders/dto/get-details-by-ticket-code.dto';
import { CreateTicketDto, TicketDetailRes } from 'vac-nest-examination';
import {
  CancelOrderLibResponse,
  CreateOrderRes,
  FinishOrderLibDto,
  GetOneOrderLibResponse,
  SearchOrderESLibDto,
  SearchOrderESLibResponse,
} from 'vac-nest-oms';
import {
  CalculatePaymentLateDto,
  CalculatePaymentLateRes,
  CancelOrderDto,
  CheckRuleLoyaltyDto,
  ContinueBuyingRes,
  ContinueBuyingTicketCodeRes,
  CreateOrderResponse,
  CreateTicketPartialPaymentDto,
  CreateTicketPartialPaymentRes,
  EmployeeInfoContinueBuyingDto,
  FinishPaymentExtraDto,
  GetGiftByOrderFromEcomRes,
  GetGiftDeliveryRes,
  GetOrderEsRes,
  IndicationButtonForDoctorDto,
  IndicationButtonForDoctorRes,
  PlaceOrderDto,
  UpdateStatusOrderPartialPaymentDto,
  UpdateStatusOrderPayloadDto,
  UpdateStatusOrderResponse,
} from '../dto';
import { ChangeShopCodeDto } from '../dto/change-shop-code.dto';
import { PushsOrderPayloadDto } from '../dto/push-order.dto';
import { OrderUtilsService } from '../services/order-utils.services';
import { OrdersService } from '../services/orders.service';
import { GetJourneyByOrderOrTicketDto, OrdersInfo } from 'vac-nest-journey';
import { OrdersFamilyPackageService } from '../services/orders-family-package.service';
import { CheckRuleCreateOrderDto } from '../dto/check-rule-create-order.dto';

@Controller({ path: 'orders', version: '1' })
@ApiTags('Order')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(
  ClassResponse,
  UpdateStatusOrderResponse,
  SearchOrderESLibResponse,
  GetOrderEsRes,
  CreateOrderRes,
  ContinueBuyingRes,
  ContinueBuyingTicketCodeRes,
  IndicationButtonForDoctorRes,
  CheckRuleLoyaltyDto,
  CalculatePaymentLateRes,
  CreateOrderResponse,
  GetGiftDeliveryRes,
  GetGiftByOrderFromEcomRes,
  GetOneOrderLibResponse,
  CreateTicketPartialPaymentRes,
)
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@CustomHeaders()
export class OrdersController {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly orderUtilsService: OrderUtilsService,
    private readonly ordersFamilyPackageService: OrdersFamilyPackageService,
  ) {}

  @Post('place')
  @ApiOperation({
    summary: 'Tạo đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(CreateOrderResponse, 'object'),
  })
  placeOrder(@Body() placeOrder: PlaceOrderDto) {
    return this.ordersService.placeOrder(placeOrder, true);
  }

  @Put('place/:orderCode')
  @ApiOperation({
    summary: 'Cập nhật đơn hàng dành cho lễ tân',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(CreateOrderResponse, 'object'),
  })
  updatePlaceOrder(@Param('orderCode') orderCode: string, @Body() placeOrderDto: PlaceOrderDto) {
    return this.ordersService.updatePlaceOrder(orderCode, placeOrderDto, true);
  }

  @Put('indication-button/:ticketCode')
  @ApiOperation({
    summary: 'API phục vụ nút chỉ định tiêm cho bác sĩ',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(IndicationButtonForDoctorRes, 'object'),
  })
  indicationButtonForDoctor(
    @Param('ticketCode') ticketCode: string,
    @Body() indicationButtonForDoctorDto: IndicationButtonForDoctorDto,
  ) {
    return this.ordersService.indicationButtonForDoctor(ticketCode, indicationButtonForDoctorDto);
  }

  @Public()
  @Post('search')
  @ApiOperation({
    summary: 'Tìm kiếm thông tin đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Tìm kiếm thông tin đơn hàng',
    schema: generalSchema(SearchOrderESLibResponse, 'object'),
  })
  searchOrder(@Body() searchOrderDto: SearchOrderESLibDto) {
    return this.ordersService.searchOrder(searchOrderDto);
  }

  @Post('search/for-stc')
  @Public()
  @ApiOperation({
    summary: 'Tìm kiếm thông tin đơn hàng giành cho STC',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách đơn hàng.',
    schema: generalSchema(SearchOrderESLibResponse, 'object'),
  })
  searchOrderForSTC(@Body() searchOrderDto: SearchOrderESLibDto) {
    return this.ordersService.searchOrderForSTC(searchOrderDto);
  }

  @Get(':orderCode')
  @ApiOperation({
    summary: 'Thông tin chi tiết đơn hàng trong db',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin đơn hàng trong db',
    schema: generalSchema(CreateOrderRes, 'object'),
  })
  orderDetail(@Param('orderCode') orderCode: string) {
    return this.ordersService.orderDetail(orderCode);
  }

  @Get('es/:orderCode')
  @ApiOperation({
    summary: 'Thông tin chi tiết đơn hàng trên ES',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin đơn hàng trên ES',
    schema: generalSchema(GetOrderEsRes, 'object'),
  })
  getOrderOnEs(@Param('orderCode') orderCode: string, @Query('ticketCode') ticketCode?: string) {
    return this.ordersService.getOrderOnEs(orderCode, ticketCode);
  }

  @Public()
  @Get('es/pre-order/tracking/:orderCode')
  @ApiOperation({
    summary: 'checking đơn hàng cọc.',
  })
  @HttpCode(HttpStatus.OK)
  trackingPreOrder(@Param('orderCode') orderCode: string) {
    // description: 'https://reqs.fptshop.com.vn/browse/FV-8360',
    return this.ordersService.trackingPreOrder(orderCode);
  }

  @Get('es/for-stc/:orderCode')
  @Public()
  @ApiOperation({
    summary: 'Thông tin chi tiết đơn hàng trên ES',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin đơn hàng trên ES',
    schema: generalSchema(GetOrderEsRes, 'object'),
  })
  getOrderOnEsForSTC(@Param('orderCode') orderCode: string) {
    return this.ordersService.getOrderOnEsForSTC(orderCode);
  }

  @Post('update-status-order-deposit')
  @ApiOperation({
    summary: 'Cập nhật trạng thái đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Cập nhật trạng thái đơn hàng',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  @UseInterceptors(BlockAdjustOrderInterceptor)
  updatedStatusOrderDeposit(@Body() body: UpdateStatusOrderPayloadDto) {
    return this.ordersService.updateStatusOrder(body);
  }

  @Post('finish-payment-extra')
  @ApiOperation({
    summary: 'Hoàn tất thanh toan thêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Cập nhật trạng thái đơn hàng',
    schema: generalSchema(TicketDetailRes, 'object'),
  })
  finishPaymentExtra(@Body() body: FinishPaymentExtraDto) {
    return this.ordersService.finishPaymentExtra(body);
  }

  @Post('cancel-order')
  @ApiOperation({
    summary: 'Hủy đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả hủy đơn hàng',
    schema: generalSchema(CancelOrderLibResponse, 'object'),
  })
  cancelOrder(@Body() body: CancelOrderDto) {
    return this.ordersService.cancelOrder(body, true);
  }

  @Get('continue-buying/:orderCode')
  @ApiOperation({
    summary: 'Tiếp tục đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả hủy đơn hàng',
    schema: generalSchema(ContinueBuyingRes, 'object'),
  })
  @UseInterceptors(BlockAdjustOrderInterceptor)
  continueBuying(@Param('orderCode') orderCode: string, @Query() payload: EmployeeInfoContinueBuyingDto) {
    return this.ordersService.continueBuying(orderCode, false, payload);
  }

  @Get('continue-buying-ticket-code/:ticketCode')
  @ApiOperation({
    summary: 'Tiếp tục đơn hàng trả chậm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả hủy đơn hàng',
    schema: generalSchema(ContinueBuyingTicketCodeRes, 'object'),
  })
  continueBuyingTicketCode(@Param('ticketCode') ticketCode: string) {
    return this.ordersService.continueBuyingTicketCode(ticketCode);
  }

  @Post('push-order')
  @ApiOperation({
    summary: 'Cập nhật ecomDisplay = 1',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Cập nhật ecomDisplay = 1',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  pushOrder(@Body() body: PushsOrderPayloadDto) {
    return this.ordersService.pushOrder(body);
  }

  @Put('debit-promotion/:orderCode')
  @ApiOperation({
    summary: 'Cập nhật ecomDisplay = 1',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Cập nhật ecomDisplay = 1',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  debitPromotion(@Param('orderCode') orderCode: string) {
    return this.ordersService.debitPromotion(orderCode);
  }

  /**
   * @TODO xem chi tiết theo ticket code
   */
  @Get('tickets/:ticketCode')
  @ApiOperation({
    summary: 'Xem chi tiết theo ticket code',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Xem chi tiết theo ticket code',
  })
  async getDetailsByTicketCode(
    @Param('ticketCode') ticketCode: string,
    @Query() getDetailsByTicketCodeDTO: GetDetailsByTicketCodeDTO,
  ) {
    return this.ordersService.getDetailsByTicketCode(ticketCode, getDetailsByTicketCodeDTO);
  }

  /**
   * @TODO xem chi tiết theo ticket code
   */
  @Get('tickets/:ticketCode')
  @Version('2')
  @ApiOperation({
    summary: 'Xem chi tiết theo ticket code',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Xem chi tiết theo ticket code',
  })
  async getDetailsByTicketCodeV2(
    @Param('ticketCode') ticketCode: string,
    @Query() getDetailsByTicketCodeDTO: GetDetailsByTicketCodeDTO,
  ) {
    return this.ordersService.getDetailsByTicketCodeV2(ticketCode, getDetailsByTicketCodeDTO);
  }

  /**
   * @TODO finish order
   */
  @Put('finish-order/:orderId')
  @ApiOperation({
    summary: 'Finish order',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Finish order',
  })
  async finishOrder(@Param('orderId') orderId: string, @Body() body: FinishOrderLibDto) {
    return this.ordersService.finishOrder(orderId, body);
  }

  /**
   * @TODO change shop
   */
  @Put('shop-change')
  @ApiOperation({
    summary: 'Thay đổi thông tin shop trong đơn hàng và phiếu khám',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thay đổi thông tin shop trong đơn hàng và phiếu khám',
  })
  async changeShop(@Body() body: ChangeShopCodeDto, @Req() req: any) {
    return this.ordersService.changeShopCode(body, req.user);
  }

  @Post('check-rule-loyalty')
  @ApiOperation({
    summary: 'Check rule loyalty trước khi chỉ định',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Cart mới sau khi check rule',
    schema: generalSchema(CheckRuleLoyaltyDto, 'object'),
  })
  checkRuleLoyalty(@Body() placeOrder: PlaceOrderDto) {
    return this.ordersService.checkRuleLoyalty(placeOrder);
  }

  /**
   * @TODO đơn hủy khi không có ticket
   */
  @Get('ticket/order-cancel')
  @ApiOperation({
    summary: 'Đơn hủy khi không có ticket',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Finish order',
  })
  async cancelOrderNotTicket(@Query('orderCode') orderCode: string) {
    return this.ordersService.orderCancelNotTicket(orderCode);
  }

  /**
   * @TODO đơn hủy khi không có ticket
   */
  @Post('filter-dup-ticket-schedule')
  @ApiOperation({
    summary: 'filter dup ticket schedule',
  })
  @HttpCode(HttpStatus.OK)
  async filterDataScheduleTicket(@Body() ticket: CreateTicketDto[]) {
    return this.orderUtilsService.filterDupTicketSchedule(ticket);
  }

  /**
   * @TODO tính tiền thanh toán chậm và tiền phải trả
   */
  @Post('order/calculate-payment-late')
  @ApiOperation({
    summary: 'Thông tin tính tiền thanh toàn chậm và tiền phải trả',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'totalAmountLate: tổng tiền thanh toán chậm <br> totalAmount: tiền phải trả',
    schema: generalSchema(CalculatePaymentLateRes, 'object'),
  })
  async calculatePaymentLate(@Body() body: CalculatePaymentLateDto) {
    return this.ordersService.getCalculatePaymentLate(body);
  }

  /**
   * @TODO lấy danh sách quà
   */
  @Get('order/get-gift-delivery')
  @ApiOperation({
    summary: 'Thông tin quà tặng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin quà tặng',
    schema: generalSchema(GetGiftDeliveryRes, 'object'),
  })
  async getGiftDelivery(@Query() query: GetJourneyByOrderOrTicketDto) {
    return this.ordersService.getGiftByOrderCode(query);
  }

  /**
   * @description lấy danh sách quà chưa giao với khách hàng có đơn ecom
   */
  @Get('gift/get-gift-for-order-ecom')
  @ApiOperation({
    summary: 'Danh sách quà chưa giao với khách hàng có đơn từ ecom',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách quà chưa giao với khách hàng có đơn từ ecom',
    // schema: generalSchema(GetGiftByOrderFromEcomRes, 'object'),
  })
  async getGiftForOrderEcom(@Query('lcvId') lcvId: string) {
    return this.ordersService.getGiftForOrderEcomService(lcvId);
  }
  /*
   * @TODO Tạo phiếu khám với đơn trả chậm lần thứ không mix
   */
  @Post('create-ticket-partial-payment')
  @ApiOperation({
    summary: 'Tạo phiếu khám với đơn trả chậm lần thứ không mix',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Chi tiết đơn và chi tiết phiếu',
    schema: generalSchema(CreateTicketPartialPaymentRes, 'object'),
  })
  async createTicketPartialPayment(@Body() body: CreateTicketPartialPaymentDto) {
    return this.ordersService.createTicketPartialPayment(body);
  }

  @Post('update-status-order-partial-payment')
  @ApiOperation({
    summary: 'Cập nhật trạng thái đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Chi tiết phiếu',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  updateStatusOrderPartialPayment(@Body() body: UpdateStatusOrderPartialPaymentDto) {
    return this.ordersService.updateStatusOrderPartialPayment(body);
  }

  @Post('oms/update-status-order-deposit')
  @ApiOperation({
    summary: 'Cập nhật trạng thái hoàn tất cọc đơn hàng trực tiếp sang OMS',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  updateStatusOmsOrderDeposit(@Body() body: UpdateStatusOrderPartialPaymentDto) {
    return this.ordersService.updateStatusOmsOrderDeposit(body);
  }

  @Get('orders-info/get-deposit-order-by-lcvid/:lcvId')
  @ApiOperation({
    summary: 'Danh sách đơn hàng đặt cọc theo LCV',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    // schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  getDepositOrderByLCV(@Param('lcvId') lcvId?: string) {
    return this.ordersService.getOrderDepositByLcvId(lcvId);
  }

  @Post('check-rule-create-order')
  @ApiOperation({
    summary: 'Check rule create order cho rsa, rsa ecom',
  })
  @HttpCode(HttpStatus.OK)
  checkRuleCreateOrder(@Body() body: CheckRuleCreateOrderDto) {
    return this.ordersService.checkRuleCreateOrder(body.lcvId);
  }
}
