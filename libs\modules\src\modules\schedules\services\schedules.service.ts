import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { HttpStatus, Inject, Injectable, LoggerService } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ErrorCode,
  GUID_EMPTY,
  IAuthUser,
  IError,
  PreventionAttributeConstant,
  SystemException,
  TypeVoucherSchedule,
  VAC_APPLICATION_ID,
  VAC_TENANT_ID,
  getDayMonthYear,
  getRemainingSecondsToMidnight,
  getShortedLcvId,
  isSameDate,
} from '@shared';
import { STATUS_CALENDAR, STATUS_PAYMENT } from '@shared/enum/payment';
import { StatusInjected } from '@shared/enum/purchaseHistory';
import { PaidStatus } from 'apps/rsa-ecom/src/modules/appointment-reminder/enum/paid-status.enum';
import { plainToClass, plainToInstance } from 'class-transformer';
import { Request } from 'express';
import Redis from 'ioredis';
import { JSONPath } from 'jsonpath-plus';
import jwtDecode from 'jwt-decode';
import * as _ from 'lodash';
import moment from 'moment';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { DataSource, Repository } from 'typeorm';
import { calculateTimeDifference } from 'vac-commons';
import { DSMSService } from 'vac-nest-dsms';
import { EnmStatusTicket, EnmTicketType, ExaminationCoreService } from 'vac-nest-examination';
import { TicketItem } from 'vac-nest-examination/dist/dto/update-appointment-date.dto';
import { FamilyProfileDetails, FamilyService } from 'vac-nest-family';
import { CreateHistoryOptionDto, VacHistoryService, VaccineHistoryDetailDto } from 'vac-nest-history';
import { IAMCoreService } from 'vac-nest-iam';
import { JourneyService } from 'vac-nest-journey';
import { NotificationService } from 'vac-nest-notification';
import { OMSService } from 'vac-nest-oms';
import {
  UpdateAppointmentDateDto,
  UpdateInjectionDatesV2Dto,
  VacOrderInjectionService,
} from 'vac-nest-order-injection';
import { CheckRuleScheduleConsultantDto, OrderRuleEngineService } from 'vac-nest-order-rule-engine';
import { CreateLogScheduleDto, OsrService } from 'vac-nest-osr';
import { GetOneProductBySkuRes, PIMAppService } from 'vac-nest-pim-app';
import { OrderInjectionStatus, RegimenItem, RegimenService } from 'vac-nest-regimen';
import {
  CancelScheduleByKeyDto,
  CreateFrom,
  ItemScheduleByPerson,
  ItemUpdateManyScheduleDto,
  ItemUpdateManyScheduleV2Dto,
  ScheduleCoreService,
  ScheduleItem,
  SearchByShopDto,
  SearchScheduleConsultationDto,
} from 'vac-nest-schedule';
import { ItemSchedule, ScheduleEngineAppService, SuggestInjectionSchedulesDto } from 'vac-nest-schedule-engine-app';
import { UpdateScheduleEcomEarlyD2StatusDto } from 'vac-nest-schedule/dist/dto/update-schedule-ecom-early-d2-status.dto';
import { LoadVaccineBookDto, VaccineBookCoreService } from 'vac-nest-vaccine-book';
import { TransferInjection } from '../../../../../../apps/rsa/src/modules/schedules/entities/transferInjection.entity';
import { UpdateManySchedule } from '../../customers/dto';
import { getFullAddress } from '../../print-center/functions';
import {
  REDIS_CHECK_SYNC_DOUBLE_HISTORY_TCGQ,
  REDIS_SYNC_SCHEDULE_TCGQ_EXPIRED_TIMEOUT,
  REDIS_SYNC_SCHEDULE_TCGQ_ONE_DAY,
  SourceId,
} from '../constants';
import {
  CheckRuleBlockScheduleDto,
  CheckRuleDupScheduleRes,
  CreateScheduleBodyDto,
  CreateScheduleRSADto,
  GetListScheduleHistoryDto,
  GroupScheduleRes,
  GroupScheduleSunAndRainDto,
  InjectionHistoryDto,
  ItemReplaceSkuDto,
  ItemVaccineHistoryRes,
  OrderInjectionPurchaseRes,
  PayloadInjectionHistoryDto,
  PayloadInjectionScheduleDto,
  ReqDeleteHistoryDto,
  ScheduleInjectionFamilyRes,
  ScheduleSunAndRainDto,
  SchedulesRes,
  SearchByShopRes,
  SearchVaccineHistoryRes,
  UpdateHistoryOptionDto,
  VaccinatorRes,
  VaccinatorSunAndRainDto,
} from '../dto';
import { ChangeShopDto } from '../dto/change-shop.dto';
import { GetSaveOrderScheduleQuery } from '../dto/get-save-order-schedules.dto';
import { GetScheduleEcomEarlyD2ConvertDto } from '../dto/get-schedule-ecom-early.dto';
import { IsBannedItem } from '../dto/is-sku-banned.dto';
import { SearchHistoryOrderOmsDto } from '../dto/search-oms.dto';
import { SendMessageSunAndRainDto } from '../dto/send-message-sun-and-rain.dto';
import { SyncHistoryTCQG } from '../dto/sync-tcqg.dto';
import { SCHEDULE_ACTION_TYPE, SOURCE_ID_HISTORY } from '../enum';
import { SchedulesRuleService } from './schedule-rule.service';
import { SchedulesUtilsService } from './schedules-utils.service';
import { EnmVerifyMethod } from '@libs/modules/customers/enum';
import { ITEM_CODE_HANG_HIEM } from 'apps/rsa-ecom/src/modules/payment/enum';

@Injectable()
export class SchedulesService {
  shopCode: string;
  token: string;
  constructor(
    @InjectRepository(TransferInjection)
    private readonly transferInjectionRepository: Repository<TransferInjection>,
    private readonly dataSource: DataSource,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly scheduleEngineAppService: ScheduleEngineAppService,
    private readonly familyCoreService: FamilyService,
    private readonly vacHistoryService: VacHistoryService,
    private readonly scheduleCoreService: ScheduleCoreService,
    private readonly omsCoreService: OMSService,
    private readonly vacOrderCoreService: VacOrderInjectionService,
    private readonly vaccineBookCoreService: VaccineBookCoreService,
    private readonly dsmsService: DSMSService,
    private readonly regimenCoreService: RegimenService,
    private readonly schedulesUtilsService: SchedulesUtilsService,
    private readonly schedulesRuleService: SchedulesRuleService,
    private readonly iamCoreService: IAMCoreService,
    private readonly examinationCoreService: ExaminationCoreService,

    @InjectRedis(process.env.REDIS_CONNECTION)
    private readonly redis: Redis,
    private readonly orderRuleEngineService: OrderRuleEngineService,
    private readonly osrService: OsrService,
    private readonly notificationService: NotificationService,
    private readonly pimAppService: PIMAppService,
    private readonly journeyService: JourneyService,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
    this.token = (this.req.headers?.authorization as string) || (this.req.headers?.Authorization as string) || '';
  }

  /**
   * @description lấy danh sách lịch hẹn của cá nhân và người liên hệ
   */
  async getSchedule(lcvId: string): Promise<ScheduleInjectionFamilyRes> {
    const listSchedule = await this.scheduleCoreService.getScheduleByPersonCodeV2({
      personCode: lcvId,
      skipCount: 0,
      maxResultCount: 200,
      status: [0],
    });

    const history = await this.vacHistoryService.getByLcvId(lcvId, true);
    const dataSort = this.schedulesUtilsService.calculateAndOverwriteSchedule([
      ...history.map((h) => ({
        ...h,
        appointmentDate: h.vaccinatedDate,
        skuName: h.vaccineName,
        isHistory: true,
      })),
      ...listSchedule.items,
    ]);

    return await this.schedulesUtilsService.mapFieldSchedule(
      dataSort.filter((x) => listSchedule.items.map((i) => i.id).includes(x.id)),
    );
  }

  /**
   * @description cập nhật lịch hẹn
   * @param body
   * @returns
   */
  async updateManySchedule(body: UpdateManySchedule, user: IAuthUser) {
    //--------- START get data--------
    const { arrSchedule, lcvId, screenType, updateReason } = body;
    const listOldSchedule = await this.scheduleCoreService.getScheduleByPersonCodeV2({
      personCode: lcvId,
      skipCount: 0,
      maxResultCount: 200,
      status: [0],
    });

    const history = await this.vacHistoryService.getByLcvId(lcvId, true);

    // lấy danh sách phiếu khám của lcvid
    const tickets = await this.examinationCoreService.searchByLcsIds({
      lCVIds: [lcvId],
    });
    //--------- END get data--------

    //--------- START check rule--------
    // check trùng ngày và sku
    const payloadCheckRule = [
      ...(listOldSchedule?.items?.filter((s) => !arrSchedule.map((schedule) => schedule.id).includes(s.id)) as any),
      ...arrSchedule.map((x) => ({
        ...x,
        isFromPayload: true,
        injections: listOldSchedule?.items?.find((el) => el.id === x.id)?.injections,
      })),
    ].map((x) => ({
      skuName: x.skuName,
      sku: x.sku,
      appointmentDate: x.appointmentDate,
      isPaid: x.isPaid,
      sourceId: x.sourceId,
      orderCode: x.orderCode,
      orderDetailAttachmentCode: x.orderDetailAttachmentCode,
      diseaseName: x.diseaseName,
      diseaseGroupId: x.diseaseGroupId,
      isFromPayload: x?.isFromPayload || false,
      regimenId: x.regimenId,
      taxonomies: x.taxonomies,
      injections: x?.injections,
      id: x?.id,
    }));

    // check rule khi chỉnh sửa phác đồ
    // comment code check rule cập nhật phác đồ
    this.schedulesRuleService.checkRuleEditRegimenSchedule({
      arrSchedule: arrSchedule,
      screenType,
      arrScheduleFromCore: listOldSchedule?.items,
      tickets,
    });

    //check trùng mũi nhưng thời gian trước phiếu đã thanh toán hoặc ecom
    this.schedulesRuleService.checkRuleCreateAndUpdateByPaidOrEcomSchedule(payloadCheckRule);
    this.schedulesRuleService.checkRuleCreateAndUpdateSchedule(payloadCheckRule);

    // check ngày hiện tại hiện và 1 ngày sau
    const messageWaring = [
      ...this.schedulesRuleService.checkRuleSameDateCreateAndUpdateSchedule(
        payloadCheckRule.filter((x) => x.isFromPayload),
        listOldSchedule,
      ),
      ...this.schedulesRuleService.checkRuleRegimenSchedule(payloadCheckRule),
    ];

    // check trùng ngày với lịch sử
    this.schedulesRuleService.checkRuleHistoryAndSchedule(
      payloadCheckRule.filter((x) => x.isFromPayload),
      history,
    );
    //--------- END check rule--------

    //--------- START update vac order--------
    const listUpdateVacOrder: UpdateAppointmentDateDto[] = [];

    // Get dosages by regimen IDs
    const regimenIds = _.uniq(arrSchedule?.filter((item) => item?.regimenId).map((item) => item?.regimenId));

    let regimensWithDosages = [];
    if (regimenIds.length > 0) {
      regimensWithDosages = await this.regimenCoreService.getDosagesByRegimenIds({ regimenIds });
    }

    for (const item of arrSchedule) {
      if (!item?.orderDetailAttachmentCode) continue;

      // Find regimen data for this item
      const regimenData = regimensWithDosages.find((regimen) => regimen.id === item.regimenId);
      const dosage = regimenData?.dosages?.at(0) || '';

      const itemVacOrder: UpdateAppointmentDateDto = {
        appointmentDate: item?.appointmentDate,
        orderDetailAttachmentCode: item?.orderDetailAttachmentCode,
        regimenId: item?.regimenId,
        dosage: dosage,
        updatedBy: item?.updatedBy,
      };
      listUpdateVacOrder.push(itemVacOrder);
    }

    if (listUpdateVacOrder?.length > 0) {
      await this.vacOrderCoreService.updateAppointmentDates(listUpdateVacOrder);
    }
    //--------- END update vac order--------

    //--------- START update ticket --------
    const listUpdateScheduleTicket = await this.getListUpdateScheduleTicket(listOldSchedule?.items, arrSchedule, lcvId);

    if (listUpdateScheduleTicket?.length > 0) {
      this.examinationCoreService.updateScheduleTicketAppointmentDate({
        details: listUpdateScheduleTicket,
        modifiedBy: user?.employee_code,
      });
    }
    //--------- END update ticket--------

    // -------START update schedule---------
    const dataOverwrite = await this._shouldOverwriteSchedule({
      lcvId,
      dataSchedule: [
        ...history.map((h) => ({
          ...h,
          appointmentDate: h.vaccinatedDate,
          skuName: h.vaccineName,
          isHistory: true,
        })),
        ...listOldSchedule.items.filter((s) => !arrSchedule.map((schedule) => schedule.id).includes(s.id)),
        ...arrSchedule,
      ],
    });

    const payloadUpdate: ItemUpdateManyScheduleV2Dto[] = dataOverwrite.map((x) => ({
      ...x,
      updateReason,
      updatedBy: user?.employee_code || x.updatedBy || 'RSA Calculate Injection System',
      updatedByName: user?.full_name || x.updatedByName || 'RSA Calculate Injection System',
    }));

    const data = await this.scheduleCoreService.updateManyScheduleV2(payloadUpdate);
    // -------END update schedule---------
    return {
      ...(await this.schedulesUtilsService.mapFieldSchedule(data)),
      messageWaring,
    };
  }

  /**
   * [Phiếu khám] Lấy danh sách lịch hẹn cập nhật trong phiếu khám
   */
  async getListUpdateScheduleTicket(
    oldSchedules: ItemScheduleByPerson[],
    updateSchedules: ScheduleItem[],
    lCVIds: string,
  ) {
    // lấy danh sách ticket của lcvid
    const ticketByLcvIds = await this.examinationCoreService.searchByLcsIds({
      lCVIds: [lCVIds],
    });

    // Tạo danh sách mới bao gồm ngày khám mới
    const scheduleCheckList = [];
    updateSchedules?.forEach((item) => {
      const availableSchedule = oldSchedules?.find((ele) => ele?.id === item?.id);
      if (availableSchedule) {
        scheduleCheckList.push({
          ...availableSchedule,
          apppointmentUpdateDate: item?.appointmentDate,
          regimenId: item?.regimenId,
        });
      }
    });

    // sort lại để mapping với schedule trong phiếu khám
    const scheduleCheckListUpdate = _.orderBy(scheduleCheckList, ['sku', 'apppointmentUpdateDate']);
    const arrSkuSchedule = _.compact(_.uniq(updateSchedules?.map((entry) => entry?.sku)));

    // nếu không có phiếu nào thì by pass
    if (!ticketByLcvIds?.length || scheduleCheckList?.length === 0) return [];
    // lấy danh sách sku thêm lịch tay để check tồn tại trong phiếu
    const listSkuBody = _.uniq(scheduleCheckList?.map((schedule) => schedule?.sku)) || [];
    const listDiseaseGroupId = _.uniq(scheduleCheckList?.map((schedule) => schedule?.diseaseGroupId)) || [];

    const listUpdateScheduleTicket: TicketItem[] = [];
    ticketByLcvIds?.map((item) => {
      // chờ khám (1), đang khám (2), đã khám (3), chờ thanh toán (4), chờ tiêm (5), đang tiêm (6)
      if (
        // dk để vô check
        ([1, 2, 3, 4, 5, 6].includes(item?.status) && item?.ticketType === EnmTicketType.INDICATION) || // ticket ofline
        ([2, 3, 4, 5, 6].includes(item?.status) && item?.ticketType === EnmTicketType.NOT_INDICATION) //  // ticket online
      ) {
        if (listSkuBody?.length && item?.schedules?.length) {
          //   if ([1, 2, 3, 4].includes(item?.status)) {
          //     item?.schedules?.map((ticketSchedule) => {
          //       if (
          //         listSkuBody?.includes(ticketSchedule?.sku) ||
          //         listDiseaseGroupId?.includes?.(ticketSchedule?.diseaseGroupId)
          //       ) {
          //         const exception: IError = {
          //           code: ErrorCode.RSA_UPDATE_SCHEDULE_MANUALLY_VALID_TICKET,
          //           message: ErrorCode.getError(ErrorCode.RSA_UPDATE_SCHEDULE_MANUALLY_VALID_TICKET)?.replace(
          //             `{ticketCode}`,
          //             item?.ticketCode,
          //           ),
          //           details: ErrorCode.getError(ErrorCode.RSA_UPDATE_SCHEDULE_MANUALLY_VALID_TICKET)?.replace(
          //             `{ticketCode}`,
          //             item?.ticketCode,
          //           ),
          //           validationErrors: null,
          //         };
          //         throw new SystemException(exception, HttpStatus.BAD_REQUEST);
          //       }
          //     });
          //   }

          /**
           * Check điều kiện để cập nhật lại lịch hẹn phiếu khám
           * Mapping 1-1 với index từ payload(sort lại theo sku, appoinmentDate) truyền vào và schedule (cũng sort theo sku và appoinmentDate)
           * Trừ những mũi chỉ định không update lại ngày
           */
          const scheduleTicketFilter = _.orderBy(
            item?.schedules?.filter(
              (entry) => [0, 1, 5].includes(entry?.status) && arrSkuSchedule?.includes(entry?.sku),
            ),
            ['sku', 'appointmentDate'],
          );

          scheduleTicketFilter?.forEach((schedule, index) => {
            if (!isSameDate(new Date(schedule?.appointmentDate), new Date())) {
              const ticketItem: TicketItem = {
                appointmentDate: scheduleCheckListUpdate?.[index]?.apppointmentUpdateDate,
                regimenId: scheduleCheckListUpdate?.[index]?.regimenId || schedule?.regimenId,
                id: schedule?.id,
              };
              listUpdateScheduleTicket.push(ticketItem);
            }
          });
        }
      }
    });

    return listUpdateScheduleTicket;
  }

  async syncHistoryTCQG(body: SyncHistoryTCQG) {
    const { personId } = body;
    let name: string = '';
    try {
      const person = await this.familyCoreService.getPersonById(personId);
      name = person?.name;
    } catch (error) {}

    const vaccineScheduleHistory = await this.vacHistoryService.syncHistoryTCQG(body);

    let arrScheduleHistory: ItemSchedule[] = [];

    if (vaccineScheduleHistory?.length)
      vaccineScheduleHistory?.map((schedule) => {
        const itemSchedule = {
          id: schedule?.id,
          appointmentDate: schedule?.vaccinatedDate,
          sku: schedule?.sku ?? '',
          taxonomies: schedule?.taxonomies ?? '',
          manufactor: schedule?.manufactor ?? '',
          injections: schedule?.injection ?? 0,
          vaccineName: schedule?.vaccineName ?? '',
          shopName: schedule?.shopName ?? '',
          shopCode: schedule?.shopCode ?? '',
          statusAppointment: STATUS_CALENDAR.DA_TIEM,
          statusPayment: STATUS_PAYMENT.DA_THANH_TOAN,
          customerNote: '',
          createdByName: '',
          note: '',
          isScheduleFromHistory: true,
          personName: name,
          personId,
        };
        arrScheduleHistory = [...arrScheduleHistory, itemSchedule];
      });

    const listSchedule = await this._getScheduleFromSchedule(personId);
    if (listSchedule?.length <= 0)
      return {
        items: arrScheduleHistory,
      };
    const arrSchedule =
      listSchedule
        ?.filter((entry) => !entry?.isScheduleFromHistory)
        ?.map((item) => {
          return {
            ...item,
            personName: name,
            personId,
          };
        }) || [];
    return {
      items: [...arrScheduleHistory, ...arrSchedule],
    };
  }

  /**
   * @TODO lấy danh sách lịch tiêm từ schedule tương lai
   * @param body personId
   */
  async _getScheduleFromSchedule(personId: string) {
    const { items } = await this.scheduleCoreService.getScheduleByPerson({ personId: personId });

    let arrSchedule: ItemSchedule[] = [];

    if (items.length === 0) return arrSchedule;

    items?.map((injection) => {
      const status_payment = injection?.orderDetailAttachmentCode
        ? STATUS_PAYMENT.DA_THANH_TOAN
        : STATUS_PAYMENT.CHUA_THANH_TOAN;
      const schedule: ItemSchedule = {
        id: injection?.id,
        appointmentDate: injection?.appointmentDate,
        injections: injection?.injections ?? null,
        manufactor: injection?.manufactor ?? '',
        vaccineName: injection?.skuName ?? '',
        taxonomies: injection?.taxonomies ?? '',
        sku: injection?.sku ?? '',
        shopName: injection?.shopName ?? '',
        shopCode: injection?.shopCode ?? '',
        statusPayment: status_payment,
        statusAppointment: injection?.status,
        createdByName: injection?.createdByName ?? '',
        customerNote: injection?.customerNote ?? '',
        note: injection?.note ?? '',
        isScheduleFromHistory: false,
      };
      arrSchedule = [...arrSchedule, schedule];
    });

    return arrSchedule;
  }

  /**
   * @TODO tra cứu lịch sử tiêm
   */
  async searchInjectionHistory(payload: PayloadInjectionHistoryDto): Promise<SearchVaccineHistoryRes> {
    let outPut: ItemVaccineHistoryRes[] = [];
    let personIds: string[] = [];

    // call family
    // lấy lcvId
    const { items } = await this.familyCoreService.getPersonByCustIdV2(payload?.customerId);

    if (!items || items?.length === 0) {
      return {
        totalCount: 0,
        items: outPut,
      };
    }

    const { familyProfileDetails } = items?.at(0) || null;

    if (familyProfileDetails?.length) {
      personIds = familyProfileDetails?.map((person) => person?.personId);
    }

    // lấy danh sách lịch sử tiêm dựa vào personId
    const { items: schedules, totalCount } = await this.vacHistoryService.getByPersonIdsPaging({
      pageIndex: payload?.skipCount,
      pageSize: payload?.maxResultCount || 100,
      personIds,
    });

    if (totalCount === 0) {
      return {
        totalCount,
        items: outPut,
      };
    }

    const allShopVaccine = await this._getAllShopVaccine();

    schedules?.map((item) => {
      const person = familyProfileDetails?.find((x) => x.personId === item?.personId);

      const itemShop = allShopVaccine?.items?.find((shop) => shop?.code === item?.shopCode);

      const genderName = person?.gender === 0 ? 'Nam' : person?.gender === 1 ? 'Nữ' : 'Khác';

      const note = item?.manufactor ? `${item?.taxonomies} - ${item?.manufactor}` : item?.taxonomies;

      const result: ItemVaccineHistoryRes = {
        customerName: person?.name,
        nationVaccineCode: person?.nationalVaccineCode,
        gender: genderName,
        birthday: person?.dateOfBirth,
        vaccineName: item?.vaccineName,
        noNumberVaccine: item?.injection,
        vaccineDate: item?.vaccinatedDate,
        scheduleDate: null,
        shopCode: item?.shopCode,
        shopAddress: itemShop?.shortAddress ?? null,
        shopName: item?.shopName ?? item?.locationName,
        statusCode: 2,
        statusname: 'Đã tiêm',
        note,
        lcvId: person?.lcvId,
        diseaseName: item?.disease,
      };
      outPut.push(result);
    });

    outPut = _.orderBy(outPut, [(obj) => new Date(obj?.vaccineDate)], ['desc']);

    return {
      totalCount: totalCount,
      items: outPut,
    };
  }

  /**
   * @TODO tra cứu lịch hẹn tiêm
   */
  async searchInjectionSchedule(payload: PayloadInjectionScheduleDto): Promise<SearchVaccineHistoryRes> {
    const { customerId } = payload;
    let outPut: ItemVaccineHistoryRes[] = [];
    let lcvIds: string[] = [];
    // call family
    // lấy lcvId
    const { items } = await this.familyCoreService.getPersonByCustIdV2(customerId);

    if (!items || items?.length === 0) {
      return {
        totalCount: 0,
        items: outPut,
      };
    }

    const { familyProfileDetails } = items?.at(0) || null;

    if (familyProfileDetails?.length) {
      lcvIds = familyProfileDetails?.map((person) => person?.lcvId);
    }

    // lấy danh sách lịch hẹn
    const { items: scheduleRes, totalCount } = await this.scheduleCoreService.getScheduleByPersonCodes({
      ...payload,
      status: [payload?.status ?? 0],
      personCodes: lcvIds,
    });

    if (!items || totalCount === 0) {
      return {
        totalCount: 0,
        items: outPut,
      };
    }

    const allShopVaccine = await this._getAllShopVaccine();

    scheduleRes?.map((item) => {
      if (item?.status === 0) {
        // lấy các lịch đã hẹn status:0 (đã hẹn) status: 1 (đã đến) status: 2(hủy)
        const person = familyProfileDetails?.find((p) => p.personId === item?.personId);
        const genderName = person?.gender === 0 ? 'Nam' : person?.gender === 1 ? 'Nữ' : 'Khác';
        const statusName = item?.status === 0 ? 'Đã hẹn' : item?.status === 1 ? 'Đã đến' : 'Huỷ';

        const note = item?.manufactor ? `${item?.taxonomies} - ${item?.manufactor}` : item?.taxonomies;

        const itemShop = allShopVaccine?.items?.find((shop) => shop?.code === item?.shopCode);

        const result: ItemVaccineHistoryRes = {
          customerName: person?.name,
          nationVaccineCode: person?.nationalVaccineCode,
          gender: genderName,
          birthday: person?.dateOfBirth,
          vaccineName: item?.skuName,
          noNumberVaccine: item?.injections,
          vaccineDate: null,
          scheduleDate: item?.appointmentDate,
          shopCode: item?.shopCode,
          shopAddress: itemShop?.shortAddress ?? null,
          shopName: item?.shopName,
          statusCode: item?.status,
          statusname: statusName,
          note,
          lcvId: person?.lcvId,
          diseaseName: item?.taxonomies,
        };
        outPut.push(result);
      }
    });

    outPut = _.orderBy(outPut, [(obj) => new Date(obj?.scheduleDate)], ['asc']);

    return {
      totalCount: outPut?.length || 0,
      items: outPut,
    };
  }

  /**
   * @TODO tra cứu lịch sử đơn
   */
  async searchHistoryOrder(body: SearchHistoryOrderOmsDto) {
    // const payload = {
    //   ...body,
    //   orderType: ['8'], // filter only so1 for vaccine

    // };
    /**
     * có 3 loại đơn
     * - đơn thường: orderType = 8 và orderAttribute = 0
     * - đơn từng phần: orderType = 8 và orderAttribute = 4 (đơn gốc), orderAttribute = 5 (đơn ảo)
     * - đơn pre-order: orderType = 8 và orderAttribute = 7
     * - đơn affiliate: orderType = 8 và orderAttribute = 8
     */
    const data = await this.omsCoreService.searchOrderESForZalo({
      ...body,
      orderType: ['8'], // filter only so1 for vaccine
      orderAttribute: [0, 4, 7, 8, 9], // chỉ lấy đơn gốc orderAttribute = 0
    });
    return data;
  }

  /**
   * @TODO lấy danh sách lịch tiêm theo Shop
   */
  async searchScheduleByShop(body: SearchScheduleConsultationDto) {
    const data = await this.scheduleCoreService.searchAggs(body);
    return data;
  }

  /*
   * @TODO Tạo lịch sử tiêm ngoài hệ thóng
   */
  async createHistoryOption(createHistoryOption: CreateHistoryOptionDto) {
    const { lcvId, details, isCheckTicket } = createHistoryOption;
    // createHistoryOption.shopCode = this.shopCode; remove default shopCode vì liên quan tới stc

    const historyForPerson = await this.vacHistoryService.getByLcvId(lcvId, false);

    // Chặn thêm lịch sử tiêm nếu có ticket "Đang mở" trong ngày
    if (isCheckTicket) {
      await this.schedulesRuleService._checkRuleAddHistoryWhenOpenTicket(lcvId);
    }

    // check rule
    await this.schedulesRuleService._checkRuleCreateAndUpdateHistory({ lcvId, details, type: 'ADD', historyForPerson });

    createHistoryOption.shopCode = this.shopCode;
    createHistoryOption.details?.map((item) => {
      item.locationName = item?.locationName || 'Chưa xác định';
      return item;
    });

    const payloadCreate = this.schedulesUtilsService.calculateAndOverwriteInjection([
      ...historyForPerson,
      ...createHistoryOption.details.map((d) => ({ ...d, isQualify: true })),
    ]);

    const createdData = await this.vacHistoryService.createHistoryOption({
      ...createHistoryOption,
      details: payloadCreate.filter((p) => !p.id),
    });

    return await this._shouldOverwriteHistory({ dataFromHistory: createdData, lcvId, shouldUpdateInjection: true });
  }

  async _shouldOverwriteHistory({ dataFromHistory: historyData, lcvId, shouldUpdateInjection }) {
    // historyData là data từ history trả về
    // nếu cần update thì mới update
    // không thì trả về data đã sắp xếp
    const _haveUpdateData = [];
    const dataOverwrite = this.schedulesUtilsService.calculateAndOverwriteInjection(historyData);

    dataOverwrite.forEach((x) => {
      if (x.injectionsOld !== x.injection || x.injectionsOld !== x.injections) {
        _haveUpdateData.push(x);
      }
    });

    if (!_haveUpdateData.length) {
      return await this.schedulesUtilsService.mapFieldHistory(historyData);
    }

    this.logger.log(
      {
        message: 'HISTORY INJECTION OVER WRITE',
        fields: {
          bodyReq: JSON.stringify(_haveUpdateData),
          info: `${lcvId}`,
        },
      },
      false,
    );

    if (shouldUpdateInjection) {
      return await this.schedulesUtilsService.mapFieldHistory(
        await this.vacHistoryService.updateIndexInjection(_haveUpdateData),
      );
    }

    return await this.schedulesUtilsService.mapFieldHistory(dataOverwrite);
  }

  /**
   * @TODO lấy danh sách lịch sử tiêm
   */
  async getInjectionHistory(params: InjectionHistoryDto) {
    const { personId } = params;
    const data = await this.vacHistoryService.getByPerson({ personId });
    if (!data) {
      return {
        items: [],
      };
    }

    return await this._shouldOverwriteHistory({
      dataFromHistory: data,
      lcvId: params?.lcvId || '',
      shouldUpdateInjection: false,
    });
  }

  /**
   * @TODO Sync lịch sử tiêm
   */
  async syncInjectionHistory(body: SyncHistoryTCQG) {
    const { personId, lcvId } = body;

    if (
      !(await this.redis.set(
        `${REDIS_SYNC_SCHEDULE_TCGQ_ONE_DAY}${lcvId}`,
        Date.now(),
        'EX',
        getRemainingSecondsToMidnight() || REDIS_SYNC_SCHEDULE_TCGQ_EXPIRED_TIMEOUT,
        'NX',
      ))
    ) {
      this.logger.log(
        {
          message: `KEEP HISTORY not sync ${lcvId}`,
          fields: {
            info: `${lcvId}`,
          },
        },
        false,
      );
      const result = await this.getInjectionHistory({ personId });
      return {
        ...result,
        error: null,
      };
    }
    // thêm key check call double giữ màn lịch sử và nút kéo lịch sử 10s
    await this.redis.set(`${REDIS_CHECK_SYNC_DOUBLE_HISTORY_TCGQ}${body.lcvId}`, Date.now(), 'EX', 10, 'NX');
    const { data, error } = await this.vacHistoryService.syncHistoryTCQGV2(body);

    let returnErr = null;
    if (error) {
      await this.redis.expire(`${REDIS_SYNC_SCHEDULE_TCGQ_ONE_DAY}${lcvId}`, 0);
      returnErr = { ...error };
      if (error.status === HttpStatus.REQUEST_TIMEOUT || error.status === HttpStatus.INTERNAL_SERVER_ERROR) {
        returnErr = {
          code: ErrorCode.RSA_LIMIT_TIME_SYNC_TCQG,
          message: ErrorCode.getError(ErrorCode.RSA_LIMIT_TIME_SYNC_TCQG),
        };
      }
    }

    if (!data) {
      return {
        items: [],
      };
    }
    const result = await this._shouldOverwriteHistory({ dataFromHistory: data, lcvId, shouldUpdateInjection: false });

    return {
      ...result,
      error: returnErr,
    };
  }

  /**
   * @TODO đồng bộ mũi tiêm từ tcqg về lc có check thời gian kéo
   */
  async syncHistoryFromTcqg(body: UpdateHistoryOptionDto | SyncHistoryTCQG) {
    if (!(await this.redis.set(`${REDIS_CHECK_SYNC_DOUBLE_HISTORY_TCGQ}${body.lcvId}`, Date.now(), 'EX', 10, 'NX'))) {
      return {
        isSuccess: false,
        warning: {
          code: ErrorCode.RSA_HISTORY_SYNC_TCQG_LIMIT,
          message: ErrorCode.getError(ErrorCode.RSA_HISTORY_SYNC_TCQG_LIMIT),
        },
      };
    }
    const rs = await this.vacHistoryService.syncHistoryTCQGV2(body);
    return {
      isSuccess: true,
      ...rs,
    };
  }

  /**
   * @TODO cập nhật lịch sử tiêm ngoài hệ thống
   */
  async updateScheduleHistory(body: UpdateHistoryOptionDto) {
    const { isCheckTicket, lcvId, detail, ticketCode } = body;
    const historyForPerson = await this.vacHistoryService.getByLcvId(body.lcvId, false);
    const trackingKey = `RSA:HISTORY:${ticketCode}`;

    // Chặn thêm lịch sử tiêm nếu có ticket "Đang mở" trong ngày
    if (isCheckTicket) {
      await this.schedulesRuleService._checkRuleAddHistoryWhenOpenTicket(lcvId);
    }

    // check rule
    await this.schedulesRuleService._checkRuleCreateAndUpdateHistory({
      lcvId: body?.lcvId,
      details: body?.detail,
      type: 'UPDATE',
      historyForPerson,
    });

    // check rule chỉ cho sửa 1 ngày đối với nguồn BS LC
    // get role 8733/AMS
    let getRoleSupport = await this.iamCoreService.getUserRPF({
      tenantId: VAC_TENANT_ID,
      applicationId: VAC_APPLICATION_ID,
      rpfNames: ['VAC_Support', 'Vac_Admin'],
      type: 3,
    });

    let isEmployeeRoleSupport = false;
    if (getRoleSupport?.length > 0) {
      getRoleSupport = _.uniqBy(getRoleSupport, (i) => {
        return i?.employeeCode;
      });
      isEmployeeRoleSupport = getRoleSupport?.find((i) => i?.employeeCode === detail?.at(0)?.updatedBy) ? true : false;
    }

    if (!isEmployeeRoleSupport) {
      if (+detail?.at(0)?.sourceId === SourceId.LCVId) {
        const isHasKey = await this.redis.get(trackingKey);

        const oldVaccineDate = getDayMonthYear(detail?.at(0)?.oldVaccinatedDate); // ngày tiêm cũ
        const vaccineDate = getDayMonthYear(detail?.at(0)?.vaccinatedDate); // ngày tiêm mới
        const isDiffMont = oldVaccineDate?.month !== vaccineDate?.month;
        const isDiffYear = oldVaccineDate?.year !== vaccineDate?.year;
        const isThanOneDay = oldVaccineDate?.day - vaccineDate?.day > 1 || vaccineDate?.day - oldVaccineDate?.day > 0;

        // rule khác tháng và năm
        if (isDiffMont || isDiffYear || isThanOneDay || isHasKey) {
          throw new SystemException(
            {
              code: ErrorCode.RSA_UPDATE_HISTORY_DIFF_DAY,
              message: isHasKey
                ? ErrorCode.getError(ErrorCode.RSA_UPDATE_HISTORY_THAN_ONE_DAY)
                : ErrorCode.getError(ErrorCode.RSA_UPDATE_HISTORY_DIFF_DAY),
              validationErrors: null,
            },
            HttpStatus.FORBIDDEN,
          );
        }
      }
    }

    // update orderVac
    const updateInjectionDatesDto: Array<UpdateInjectionDatesV2Dto> = [];
    body?.detail?.forEach((item) => {
      if (+item?.sourceId === 0) {
        const curentItem = historyForPerson.find((h) => h.id === item.id);
        // 0 : nguồn bác sĩ long châu thì cho update bên vac-order
        updateInjectionDatesDto.push({
          injectionDate: moment(item?.vaccinatedDate).utcOffset(7).toISOString(),
          lcvId: body?.lcvId,
          modifiedBy: item?.updatedBy,
          oldInjectionDate: moment(curentItem?.vaccinatedDate).utcOffset(7).toISOString(),
          sku: item?.sku,
          orderDetailAttachmentCode: curentItem?.orderDetailAttachmentCode,
        });
      }
    });

    if (updateInjectionDatesDto?.length && body?.lcvId) {
      await this.vacOrderCoreService.updateInjectionDateV2(updateInjectionDatesDto);
    }

    // should update all
    const payloadUpdate = this.schedulesUtilsService.calculateAndOverwriteInjection([
      ...historyForPerson.filter((h) => !body?.detail?.map((x) => x?.id)?.includes(h?.id)),
      ...body?.detail?.map((d) => ({ ...d, isQualify: true })),
    ]);

    this.logger.log(
      {
        message: 'HISTORY INJECTION OVER WRITE',
        fields: {
          bodyReq: JSON.stringify(payloadUpdate.filter((s) => body?.detail?.map((d) => d?.sku).includes(s?.sku))),
          info: `${body?.lcvId}`,
        },
      },
      false,
    );

    const payloadShouldUpdateInjection = payloadUpdate.filter((s) => !body?.detail?.map((d) => d?.id).includes(s?.id));
    if (payloadShouldUpdateInjection?.length) {
      await this.vacHistoryService.updateIndexInjection(payloadShouldUpdateInjection);
    }

    const payloadFinalUpdate = payloadUpdate.filter((s) => body?.detail?.map((d) => d?.id).includes(s?.id));
    const his = await this.vacHistoryService.updateHistory(payloadFinalUpdate);

    // update trackingTime
    if (payloadFinalUpdate?.length && payloadFinalUpdate?.at(0)?.sourceId === SourceId.LCVId) {
      await this.examinationCoreService.updateTrackingTime({
        modifiedBy: payloadFinalUpdate?.at(0)?.updatedBy,
        ticketCode: ticketCode,
        trackingTime: moment(payloadFinalUpdate?.at(0)?.vaccinatedDate).utcOffset(7).toISOString(),
      });
      // lưu ticket vao redis
      this.redis.set(trackingKey, JSON.stringify({ ticketCode, data: detail }));
    }

    return await this.schedulesUtilsService.mapFieldHistory(his);
  }

  /**
   * @TODO xóa lịch sử tiêm
   */
  async deleteHistory(id: string, lcvId: string) {
    await this.vacHistoryService.deleteHistory({ historyDetailId: id });

    const historyForPerson = await this.vacHistoryService.getByLcvId(lcvId, false);

    // update all
    const payloadUpdate = historyForPerson.filter((x) => x.id !== id);
    if (!payloadUpdate.length) {
      return {
        itemGroup: [],
        items: [],
      };
    }

    return await this._shouldOverwriteHistory({ dataFromHistory: payloadUpdate, lcvId, shouldUpdateInjection: true });
  }

  async suggestInjectionSchedule(suggestInjectionSchedulesDto: SuggestInjectionSchedulesDto) {
    return this.scheduleEngineAppService.suggestInjectionSchedule(suggestInjectionSchedulesDto);
  }

  async getPurchaseHistory(lcvId: string) {
    const { items } = await this.vacOrderCoreService.searchByPerson({
      personCode: lcvId,
      skipCount: 0,
    });

    const listPurchaseHistory = items?.filter(
      (itemEntry) =>
        itemEntry?.status === OrderInjectionStatus.Da_thanh_toan || itemEntry?.status === OrderInjectionStatus.Da_tiem,
    );

    const groupSku = {};
    listPurchaseHistory?.forEach((entry) => {
      if (groupSku[entry?.sku]?.length) {
        groupSku[entry?.sku].push(entry);
      } else {
        groupSku[entry?.sku] = [entry];
      }
    });

    const listHistory = listPurchaseHistory?.filter(
      (entry, index) => listPurchaseHistory?.findIndex((e) => e?.sku === entry?.sku) === index,
    );

    const orderInjectionPurchaseRes: OrderInjectionPurchaseRes[] =
      listHistory?.map((entry) => {
        const historyItem: OrderInjectionPurchaseRes = { ...entry };
        historyItem.quantityBuy = groupSku[entry.sku]?.length;
        historyItem.quantityInjected = groupSku[entry.sku]?.filter(
          (e) => e?.status === OrderInjectionStatus.Da_tiem,
        )?.length;

        historyItem.statusInjected =
          historyItem.quantityBuy === historyItem?.quantityInjected
            ? StatusInjected.Completed
            : historyItem?.quantityInjected === 0
            ? StatusInjected.NotYet
            : StatusInjected.Pending;
        historyItem.nextAppointmentDate =
          groupSku[entry.sku]?.find((e) => e?.status === OrderInjectionStatus.Da_thanh_toan)?.appointmentDate || null;

        return historyItem;
      }) || [];

    return orderInjectionPurchaseRes;
  }

  /**
   * @TODO sổ tiêm chủng
   */
  async getVaccineBook(params: LoadVaccineBookDto) {
    return await this.vaccineBookCoreService.getVaccineBook(params);
    // const listTicketOpen = await this.schedulesUtilsService._getOpenTickets(params?.personCode);
    // const items = data?.items.map((item) => {
    //   const vaccinationRecords = item?.vaccinationRecords?.map((ele) => {
    //     const record = ele as any;
    //     if (
    //       !moment(ele?.date).isAfter(moment()) &&
    //       [2, 4].includes(ele?.status) &&
    //       listTicketOpen &&
    //       listTicketOpen?.length
    //     ) {
    //       record.listTicketOpen = listTicketOpen || [];
    //       record.message = 'Không thể chỉnh sửa lịch hẹn do khách hàng đang có phiếu khám mở';
    //     }
    //     return record;
    //   });
    //   return { ...item, vaccinationRecords };
    // });
    // return { ...data, items };
  }

  async cancelScheduleByKey(cancelScheduleByKeyDto: CancelScheduleByKeyDto) {
    const lcvId = cancelScheduleByKeyDto?.lcvId;

    const listOldSchedule = await this.scheduleCoreService.getScheduleByPersonCodeV2({
      personCode: lcvId,
      skipCount: 0,
      maxResultCount: 200,
      status: [0],
    });

    const filteredSchedule: CheckRuleBlockScheduleDto[] = listOldSchedule?.items
      ?.filter((item) => cancelScheduleByKeyDto?.calendarIds?.includes(item?.id))
      ?.map((x) => {
        return { sku: x?.sku, diseaseGroupId: x?.diseaseGroupId };
      });

    await this.schedulesRuleService.checkRuleBlockScheduleManually(
      filteredSchedule,
      lcvId,
      SCHEDULE_ACTION_TYPE.CANCEL,
    );

    // SETP 1: xóa
    const dataSchedule = await this.scheduleCoreService.cancelScheduleByKeyV2(cancelScheduleByKeyDto);
    // data history
    const historyForPerson = await this.vacHistoryService.getByLcvId(lcvId, true);
    const dataNeedOverwrite = await this._shouldOverwriteSchedule({
      lcvId,
      dataSchedule: [
        ...historyForPerson.map((h) => ({
          ...h,
          appointmentDate: h.vaccinatedDate,
          skuName: h.vaccineName,
          isHistory: true,
        })),
        ...dataSchedule,
      ],
    });

    // update mũi thứ bằng api update
    if (dataNeedOverwrite?.length) {
      const dataUpdated = await this.scheduleCoreService.updateManyScheduleV2(
        dataNeedOverwrite.map((x) => ({
          ...x,
          updatedBy: x.updatedBy || 'RSA Calculate Injection System',
          updatedByName: x.updatedByName || 'RSA Calculate Injection System',
        })),
      );
      return await this.schedulesUtilsService.mapFieldSchedule(dataUpdated);
    }

    return await this.schedulesUtilsService.mapFieldSchedule(dataSchedule);
  }

  /*
   * @TODO get all shop
   */
  private async _getAllShopVaccine() {
    const shopType = 'VACCINE';
    return this.dsmsService.getAllShopVaccine(shopType);
  }

  async createScheduleManually(body: CreateScheduleBodyDto) {
    const skus = body?.schedules?.map((b) => b?.sku);

    const listBannedSku = await this.osrService.getBannedSkuList();
    const checkBannedItemList: IsBannedItem[] = skus.map((skuItem) =>
      this.schedulesUtilsService.isSkuBanned(listBannedSku, skuItem),
    );
    const bannedItem = checkBannedItemList.find((item) => item.isBanned);
    if (bannedItem) {
      const messageError = ErrorCode.getError(ErrorCode.SCHEDULE_BANNED_SKU)
        .replace('<sku>', bannedItem?.bannedItem?.itemCode)
        .replace('<skuName>', bannedItem?.bannedItem?.itemName)
        .replace(
          '<sellRestrictFromDate>',
          moment(bannedItem?.bannedItem?.sellRestrictFromDate?.split('T')?.at(0)).utcOffset(7).format(`DD/MM/YYYY`),
        )
        .replace(
          '<sellRestrictToDate>',
          moment(bannedItem?.bannedItem?.sellRestrictToDate?.split('T')?.at(0)).utcOffset(7).format(`DD/MM/YYYY`),
        );
      throw new SystemException(
        {
          code: ErrorCode.SCHEDULE_BANNED_SKU,
          message: messageError,
          details: messageError,
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    // FV_6216: prevent creation of a schedule by product attributes
    const products = await this.pimAppService.getListProductBySku(skus);
    let prevent: boolean = false;
    const productNames: string[] = [];
    let isRareGood = false;
    let productNameRareGood = '';
    _.forEach(products.listProduct, (product: GetOneProductBySkuRes) => {
      if (_.includes(PreventionAttributeConstant.ProductAttributes, product?.attributeShop?.attributeOptionId)) {
        prevent = true;
        productNames.push(product?.name);
      }
      if (product?.attributeShop?.attributeOptionId === ITEM_CODE_HANG_HIEM) {
        isRareGood = true;
        productNameRareGood = product?.name;
      }
    });

    if (isRareGood) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_PREVENTION_PRODUCT_ATTRIBUTE_RARE_GOOD,
          message: ErrorCode.getError(ErrorCode.RSA_PREVENTION_PRODUCT_ATTRIBUTE_RARE_GOOD)?.replace(
            '{name}',
            productNameRareGood,
          ),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    if (prevent) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_PREVENTION_PRODUCT_ATTRIBUTE,
          message: ErrorCode.getError(ErrorCode.RSA_PREVENTION_PRODUCT_ATTRIBUTE)?.replace(
            '{name}',
            productNames.join(', '),
          ),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    //
    //--------- START get data--------
    const schedulesBody =
      body?.schedules?.map((b) => {
        const findMeasuresBySku = products?.listProduct
          ?.find((i) => i?.sku === b?.sku)
          ?.measures?.find((i) => i?.isSellDefault);
        return {
          ...b,
          unitCodeSale: findMeasuresBySku?.measureUnitId || null,
          unitNameSale: findMeasuresBySku?.measureUnitName || '',
          createFroms: CreateFrom.FromOtherSchedule,
        };
      }) || [];
    const lcvId = schedulesBody?.at(0)?.lcvId;
    // RULE 1: chẵn chờ tiêm không cho tạo lịch tay
    await this.schedulesRuleService.checkRuleBlockScheduleManually(schedulesBody, lcvId, SCHEDULE_ACTION_TYPE.CREATE);

    const history = await this.vacHistoryService.getByLcvId(lcvId, true);

    const { items: scheduleData } = await this.scheduleCoreService.getScheduleByPersonCodeV2({
      personCode: lcvId,
      skipCount: 0,
      maxResultCount: 200,
      status: [0],
    });

    //--------- END get data--------

    //--------- START check rule--------
    const payloadCheckRule = [
      ...(scheduleData.filter((s) => !schedulesBody.map((schedule) => schedule.id).includes(s.id)) as any),
      ...schedulesBody.map((x) => ({ ...x, isFromPayload: true })),
    ].map?.((x) => ({
      skuName: x.skuName,
      sku: x.sku,
      appointmentDate: x.appointmentDate,
      isPaid: x.isPaid,
      sourceId: x.sourceId,
      orderCode: x.orderCode,
      orderDetailAttachmentCode: x.orderDetailAttachmentCode,
      diseaseName: x.diseaseName,
      diseaseGroupId: x.diseaseGroupId,
      isFromPayload: x?.isFromPayload || false,
      regimenId: x.regimenId,
    }));
    // check nếu ngày thêm là ngày hiện tại thì báo lỗi
    const messageWaring = [
      ...this.schedulesRuleService.checkRuleSameDateCreateAndUpdateSchedule(
        payloadCheckRule.filter((x) => x.isFromPayload),
      ),
      ...this.schedulesRuleService.checkRuleRegimenSchedule(payloadCheckRule),
    ];
    // check trùng mũi nhưng thời gian trước phiếu đã thanh toán hoặc ecom
    this.schedulesRuleService.checkRuleCreateAndUpdateByPaidOrEcomSchedule(payloadCheckRule);
    // check trùng ngày or không có ngày
    this.schedulesRuleService.checkRuleCreateAndUpdateSchedule(payloadCheckRule);
    // check trùng ngày với lịch sử
    this.schedulesRuleService.checkRuleHistoryAndSchedule(
      payloadCheckRule.filter((x) => x.isFromPayload),
      history,
    );

    //--------- END check rule--------

    //--------- START update schedule--------
    const dataOverwrite = await this._shouldOverwriteSchedule({
      lcvId,
      dataSchedule: [
        ...scheduleData.filter((s) => s.status === 0),
        ...history.map((h) => ({
          ...h,
          appointmentDate: h.vaccinatedDate,
          skuName: h.vaccineName,
          isHistory: true,
        })),
        ...schedulesBody,
      ],
    });

    const payloadCreate = dataOverwrite.filter((i) => !i.id);
    const payloadUpdateỊnecttion = dataOverwrite
      .filter((x) => x.id)
      .map((x) => ({
        ...x,
        updatedBy: x?.updatedBy || 'RSA Calculate Injection System',
        updatedByName: x?.updatedByName || 'RSA Calculate Injection System',
      }));
    // update mũi thứ bằng api update
    if (payloadUpdateỊnecttion.length) {
      await this.scheduleEngineAppService.updateManySchedule({
        items: payloadUpdateỊnecttion,
      });
    }

    const dataCreated = await this.scheduleCoreService.createScheduleV2(payloadCreate);
    //--------- END update schedule--------
    return {
      ...(await this.schedulesUtilsService.mapFieldSchedule(dataCreated)),
      messageWaring,
    };
  }

  async _shouldOverwriteSchedule({ dataSchedule, lcvId }) {
    // dataSchedule là data từ schedule trả về
    // nếu cần update thì mới update
    // không thì trả về data đã sắp xếp
    const _haveScheduleUpdateData = [];
    const dataOverwrite = this.schedulesUtilsService.calculateAndOverwriteSchedule(dataSchedule);

    dataOverwrite.forEach((x) => {
      if (!x.isHistory && (x.injectionsOld !== x.injection || x.injectionsOld !== x.injections)) {
        _haveScheduleUpdateData.push(x);
      }
    });

    if (_haveScheduleUpdateData.length) {
      this.logger.log(
        {
          message: 'SCHEDULE INJECTION OVER WRITE',
          fields: {
            bodyReq: JSON.stringify(_haveScheduleUpdateData),
            info: `${lcvId}`,
          },
        },
        false,
      );
      return _haveScheduleUpdateData;
    }

    return [];
  }

  getFirstOrLast({ data, isFirst = true }: { data: any; isFirst: boolean }) {
    // lấy hết các mũi  đã tiêm thành array
    const scheduledInjections = data
      ?.map((s) => s?.injections || s?.injection)
      ?.sort((a, b) => {
        return a - b;
      });

    // lấy hết các ngày đã tiêm thành array
    const scheduledAppointmentDate = data
      ?.map((s) => moment(s?.appointmentDate || s?.vaccinatedDate)?.format('YYYY-MM-DD'))
      ?.sort((a, b) => {
        return +new Date(a) - +new Date(b);
      });

    return {
      injections: scheduledInjections?.at(isFirst ? 0 : -1),
      appointmentDate: scheduledAppointmentDate?.at(isFirst ? 0 : -1),
    };
  }

  checkRule05({
    history,
    scheduled,
    body,
    sku,
  }: {
    history: VaccineHistoryDetailDto[];
    scheduled: ItemScheduleByPerson[];
    body: CreateScheduleRSADto[];
    sku: string;
  }) {
    // history
    const historyArray = history
      ?.filter((s) => s?.sku === sku)
      ?.map((s) => ({
        injection: s.injection,
        vaccinatedDate: s.vaccinatedDate,
      }));

    // scheduled
    const scheduledArray = scheduled
      ?.filter((s) => s?.status === 0 && s?.sku === sku)
      ?.map((s) => ({
        injection: s.injections,
        vaccinatedDate: s.appointmentDate,
      }));

    // body
    const bodyArray = body
      ?.filter((s) => s?.sku === sku)
      ?.map((s) => ({
        injection: s?.injections,
        vaccinatedDate: s.appointmentDate,
      }));

    const arrayInjections = [...historyArray, ...scheduledArray, ...bodyArray]
      ?.sort((a, b) => {
        return a.injection - b.injection;
      })
      ?.map((a) => a.injection);

    const arrayDay = [...historyArray, ...scheduledArray, ...bodyArray]
      ?.sort((a, b) => {
        return +new Date(a.injection) - +new Date(b.injection);
      })
      ?.map((a) => a.vaccinatedDate);

    if (!_.every(arrayInjections, (value, index) => index === 0 || value === arrayInjections[index - 1] + 1)) {
      const currentBody = body?.find((s) => s?.sku === sku);
      return {
        ...currentBody,
        rule: 'RULE:SCHEDULE:00011',
        lastInjections: currentBody?.injections,
        firstInjections: currentBody?.injections - 1,
      };
    }

    if (
      !_.every(
        arrayDay,
        (dateCurr, index) => index === 0 || moment(dateCurr)?.isSameOrAfter(moment(arrayDay[index - 1]), 'day'),
      )
    ) {
      const currentBody = body?.find((s) => s?.sku === sku);
      return {
        ...currentBody,
        rule: 'RULE:SCHEDULE:00033',
        lastInjections: currentBody?.injections,
        firstInjections: currentBody?.injections - 1,
      };
    }

    return null;
  }

  /*
   * @TODO Cập nhật lịch sử tiêm ngoài hệ thóng
   */
  // async updateHistoryOption(body: UpdateHistoryOptionDto) {
  //   const { lcvId, detail } = body;

  //   await this.schedulesRuleService._checkRuleCreateAndUpdateHistory({ lcvId, details: detail, type: 'UPDATE' });

  //   return await this.vacHistoryService.updateHistory(detail);
  // }

  async syncTcqgHistoryDetail(body: string[]) {
    try {
      const { employee_code } = jwtDecode<IAuthUser>(this.token);
      const result = await this.vacHistoryService.syncTCQGByHistoryDetailV2(body, employee_code);

      if (result) {
        const { response } = result?.at(0);
        if (response?.status >= 400) {
          throw new SystemException(
            {
              code: response?.error?.error?.code || ErrorCode.BAD_REQUEST,
              message: response?.error?.error?.message || ErrorCode.getError(ErrorCode.RSA_SYNC_TCQG_DETAIL_ERROR),
              validationErrors: null,
            },
            HttpStatus.BAD_REQUEST,
          );
        }
      }
      return {
        items: result || [],
      };
    } catch (error) {
      throw new SystemException(
        {
          code: error?.response?.code || ErrorCode.BAD_REQUEST,
          message: error?.response?.message || ErrorCode.getError(ErrorCode.RSA_SYNC_TCQG_DETAIL_ERROR),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Chuyển mũi tiêm
   */
  async replacePersonOfSkus(body: ItemReplaceSkuDto) {
    const { items, insideCode, otp } = body;

    const skus = items?.map((i) => i?.sku);
    const itemLogger = {
      shopCode: this.shopCode || '',
      personId: items?.at(0)?.personId || '',
      lcvIdOld: items?.at(0)?.lcvIdOld || '',
      lcvIdNew: items?.at(0)?.lcvIdNew || '',
      isSuccess: false,
      updatedBy: items?.at(0)?.updatedBy || '',
      sku: skus?.join() || '',
    };

    try {
      const resultVerifyOTPUser = await this.iamCoreService.verifyOtp({
        function: 'Replace person sku',
        otp,
        system: 1,
        token: this.token,
        userName: insideCode,
      });

      if (!resultVerifyOTPUser) {
        const exception: IError = {
          code: ErrorCode.RSA_VERIFY_USER,
          message: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
          details: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }

      // rule lcv đang tồn tại phiếu khám
      await this.schedulesRuleService.checkRuleTicketExits(items);

      // rule lcv new tồn tại sku trong history
      // await this.schedulesRuleService.checkRuleSkuExitsHistoryLcvIDNew(items);

      // rule lcv new tồn tại các mũi sku trong vac-order
      // await this.schedulesRuleService.checkRuleSkuExitsVacOrderLcvIDNew(items);

      // rule tăng thứ tự mũi tiêm
      const { scheduleVacOrder, idCancelSchedule, updateScheduleKHB } =
        await this.schedulesRuleService.checkIncreaseInjectionsV3(items);

      // update vac-order
      await this.vacOrderCoreService.replacePersonOfSkus(scheduleVacOrder);
      // update schedule
      await this.scheduleCoreService.replacePersonOfSkus(updateScheduleKHB);

      // cancel lịch hẹn
      await this.scheduleCoreService.cancelScheduleByKey({
        calendarIds: idCancelSchedule,
        cancelBy: items?.at(0)?.updatedBy,
        note: 'Cancel schedule',
      });

      await this.create({ ...itemLogger, isSuccess: true });

      return {
        isSuccess: true,
      };
    } catch (error) {
      if (error?.status === 400) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_REPLACE_SKU_ERROR,
            message: error?.message,
            details: error?.message,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // báo lỗi otp
      if (error?.status === 403) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_VERIFY_USER,
            message: error?.message,
            details: error?.message,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
      await this.create(itemLogger);
      return {
        isSuccess: false,
      };
    }
  }

  async create(logTransferInjection: any) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const transferInjection = plainToClass(TransferInjection, logTransferInjection);
      const dataSave = await queryRunner.manager.save(transferInjection);
      await queryRunner.commitTransaction();
      return dataSave;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async changeShop({ shopCode, shopName, note, customerNote, orderCode, listSKu, ...body }: ChangeShopDto, user: any) {
    const getTicket = await this.examinationCoreService.getTicket({ ticketCode: body.ticketCode });
    const { items: schedules } = await this.scheduleCoreService.getScheduleByPersonCodes({
      personCodes: [getTicket?.lcvId],
      status: [0],
    });

    const findTicketSchedule = (schedule: ItemScheduleByPerson) =>
      (getTicket?.schedules || []).find(
        (ticketSchedule) =>
          ticketSchedule.sku === schedule.sku &&
          listSKu.includes(schedule.sku) &&
          ticketSchedule.orderInjections === schedule.injections &&
          ticketSchedule.regimenId === schedule.regimenId &&
          ((schedule.isPaid === false && schedule.status === 0) ||
            (schedule.isPaid === true && schedule.status === 0 && schedule.orderCode === orderCode)),
      );

    const updateSchedulePayload = schedules?.filter((schedule) => findTicketSchedule(schedule));

    if (!updateSchedulePayload.length) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_NO_SCHEDULE_TO_UPDATE,
          message: ErrorCode.getError(ErrorCode.RSA_NO_SCHEDULE_TO_UPDATE),
          details: '',
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    const updateManyRes = await this.scheduleCoreService.updateManySchedule(
      updateSchedulePayload.map(
        (schedule) =>
          ({
            id: schedule.id,
            shopCode,
            shopName,
            appointmentDate: new Date(schedule.appointmentDate),
            updatedBy: user?.employee_code || '',
            updatedByName: user?.full_name || '',
            note,
            customerNote,
          } as ItemUpdateManyScheduleDto),
      ),
    );

    return updateManyRes;
  }
  /**
   * kiểm tra trùng ngày tiêm khi nhấn nút tư vấn
   */
  async checkDupSchedule(payload: CheckRuleScheduleConsultantDto): Promise<CheckRuleDupScheduleRes> {
    const outPut: CheckRuleDupScheduleRes = {
      isDupSchedule: false,
      message: '',
    };

    if (payload?.arrSchedule?.length === 0) return outPut;

    const listRegimen = await this.regimenCoreService.getRegimenByListSku({
      skus: payload?.arrSchedule?.map((schedule) => schedule?.sku),
    });

    payload?.arrSchedule?.forEach((schedule) => {
      schedule.diseaseGroupId = listRegimen?.data?.find(
        (regimen) => regimen?.vaccine?.sku === schedule?.sku,
      )?.diseaseGroupId;
    });

    const { isDupSchedule, listSchedules } = await this.orderRuleEngineService.checkRuleDupSchedule(payload);

    if (isDupSchedule) {
      // const arrMessage = [];
      const arrMessage = listSchedules?.map((i) => {
        return (
          `nhóm bệnh ` +
          listRegimen?.data?.find((x) => x?.diseaseGroupId === i?.diseaseGroupId)?.diseaseGroup?.name +
          ` trùng ngày ` +
          moment(i?.vaccineDate).utcOffset(7).format('DD-MM-YYYY')
        );
      });
      outPut.isDupSchedule = isDupSchedule;
      outPut.message = ErrorCode.getError(ErrorCode.RSA_DUP_SCHEDULE)?.replace('{diseaseGroup}', arrMessage.join(', '));
      return outPut;
    }

    return outPut;
  }

  async searchByShop(payload: SearchByShopDto): Promise<SearchByShopRes> {
    // docs: https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=150558435
    const searchByShop = await this.scheduleCoreService.searchByShopVoucherXanh(payload);
    const groupSchedule: GroupScheduleRes[] = [];
    const arrFamilyId: string[] = [];

    const arrLcvId: string[] =
      JSONPath({
        path: '$.items[*].lcvId',
        json: searchByShop,
      }) || [];

    const getListFamily = await this.familyCoreService.getManyByLcvId({ lcvId: arrLcvId });

    const arrLcvIdHost: string[] = JSONPath({
      path: '$[*].familyProfileDetails[*]',
      json: getListFamily,
    })
      ?.filter((e: FamilyProfileDetails) => e.isHost)
      ?.map((e: FamilyProfileDetails) => e.lcvId);

    const getListHostFamily = await this.familyCoreService.getManyByLcvId({ lcvId: arrLcvIdHost });

    for (const schedule of searchByShop?.items) {
      if (!schedule?.lcvId) continue;
      const getFamily = getListFamily.find((family) => family?.lcvId === schedule?.lcvId);
      const isFamily = arrFamilyId?.includes(getFamily?.familyProfileId);
      //group theo familyProfileId
      if (isFamily) {
        const findIndex = groupSchedule?.findIndex(
          (scheduleItem) => scheduleItem?.familyProfileId === getFamily?.familyProfileId,
        );
        if (findIndex === -1) {
          continue;
        }
        const scheduleTemp = { ...groupSchedule?.[findIndex] };
        if (!scheduleTemp?.address) {
          scheduleTemp.address = getFullAddress(getFamily, false);
        }
        // check person trong list đã có chưa, chưa thì thêm mới, có thì thêm schedule
        const posPersonExist = scheduleTemp.vaccinator?.findIndex((detail) => detail?.lcvId === schedule?.lcvId);
        if (posPersonExist === -1) {
          const scheduleTransform = plainToInstance(ScheduleSunAndRainDto, schedule, {
            excludeExtraneousValues: true,
            exposeUnsetFields: false,
          });

          const newPerson: VaccinatorRes = plainToInstance(
            VaccinatorSunAndRainDto,
            { ...getFamily, schedules: [scheduleTransform], lcvIdDisplay: getShortedLcvId(getFamily?.lcvId) },
            {
              excludeExtraneousValues: true,
              exposeUnsetFields: false,
            },
          );

          if (newPerson?.isHost) {
            groupSchedule[findIndex] = { ...scheduleTemp, vaccinator: [newPerson, ...scheduleTemp.vaccinator] };
          } else {
            scheduleTemp.vaccinator.push(newPerson);
          }
        } else {
          const newSchedulePerson: SchedulesRes = plainToInstance(ScheduleSunAndRainDto, schedule, {
            excludeExtraneousValues: true,
            exposeUnsetFields: false,
          });
          // Lấy ra 2 mũi ưu tiên đã thanh toán, sort theo mũi đã thanh toán
          if (schedule?.isPaid) {
            scheduleTemp.vaccinator[posPersonExist] = {
              ...scheduleTemp?.vaccinator[posPersonExist],
              schedules: [newSchedulePerson, ...scheduleTemp?.vaccinator[posPersonExist]?.schedules],
            };
          } else {
            scheduleTemp.vaccinator[posPersonExist].schedules.push(newSchedulePerson);
          }
          if (scheduleTemp.vaccinator[posPersonExist].schedules?.length > 2) {
            scheduleTemp.vaccinator[posPersonExist].schedules = scheduleTemp.vaccinator[
              posPersonExist
            ].schedules?.slice(0, 2);
          }
        }
      } else {
        const personHost = getFamily?.familyProfileDetails?.find((detail) => detail?.isHost);
        // Không hiển thị KH không có sđt chủ hộ
        if (!personHost?.phoneNumber) continue;

        const getPersonHost = personHost?.lcvId
          ? getListHostFamily?.find((hostFamily) => hostFamily?.lcvId === personHost?.lcvId)
          : getFamily;

        const scheduleTransform = plainToInstance(ScheduleSunAndRainDto, schedule, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        });

        const newPersonTranform: VaccinatorRes = plainToInstance(
          VaccinatorSunAndRainDto,
          { ...getFamily, schedules: [scheduleTransform], lcvIdDisplay: getShortedLcvId(getFamily?.lcvId) },
          {
            excludeExtraneousValues: true,
            exposeUnsetFields: false,
          },
        );

        const scheduleItem: GroupScheduleRes = plainToInstance(
          GroupScheduleSunAndRainDto,
          {
            ...getFamily,
            ...getPersonHost,
            vaccinator: [newPersonTranform],
            lcvIdDisplay: getShortedLcvId(getPersonHost?.lcvId),
            address: getFullAddress(getPersonHost, false) || getFullAddress(getFamily, false),
          },
          {
            excludeExtraneousValues: true,
            exposeUnsetFields: false,
          },
        );
        groupSchedule.push(scheduleItem);

        if (getFamily?.familyProfileId && getFamily?.familyProfileId !== GUID_EMPTY)
          arrFamilyId.push(getFamily?.familyProfileId);
      }
    }

    // TotalCount
    const listPerson =
      JSONPath({
        path: '$[*].vaccinator[*]',
        json: groupSchedule,
      }) || [];

    // check send voucher today
    const arrPhone: string[] =
      JSONPath({
        path: '$[*].phoneNumber',
        json: groupSchedule,
      }) || [];

    const getLogSchedule = await this.osrService.getLogSchedule({
      listPhoneNumber: arrPhone,
      scheduleSentFromDate: moment().utcOffset('+07:00').format('YYYY-MM-DDT00:00:00'),
      scheduleSentToDate: moment().utcOffset('+07:00').format('YYYY-MM-DDTHH:mm:ss'),
    });

    const arrPhoneSchedule: string[] =
      JSONPath({
        path: '$[*].phoneNumber',
        json: getLogSchedule,
      }) || [];

    for (const schedule of groupSchedule) {
      if (arrPhoneSchedule?.includes(schedule?.phoneNumber)) {
        schedule.isSendedVoucher = true;
      }
    }

    return {
      totalCount: searchByShop?.totalCount || 0,
      items: groupSchedule,
    };
  }

  async sendMessageSunAndRain(payload: SendMessageSunAndRainDto) {
    if (!payload?.phoneNumber) {
      const exception: IError = {
        code: ErrorCode.RSA_SCHEDULE_EMPTY_PHONE_NUMBER,
        message: ErrorCode.getError(ErrorCode.RSA_SCHEDULE_EMPTY_PHONE_NUMBER),
        details: ErrorCode.getError(ErrorCode.RSA_SCHEDULE_EMPTY_PHONE_NUMBER),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    const getLogSchedule = await this.osrService.getLogSchedule({
      listPhoneNumber: [payload?.phoneNumber],
      scheduleSentFromDate: moment(moment().utcOffset('+07:00').subtract(15, 'minutes')).format('YYYY-MM-DDTHH:mm:ss'),
      scheduleSentToDate: moment().utcOffset('+07:00').format('YYYY-MM-DDTHH:mm:ss'),
    });

    if (getLogSchedule?.length) {
      const exception: IError = {
        code: ErrorCode.RSA_SCHEDULE_ALREADY_SEND_MESSAGE,
        message: ErrorCode.getError(ErrorCode.RSA_SCHEDULE_ALREADY_SEND_MESSAGE),
        details: ErrorCode.getError(ErrorCode.RSA_SCHEDULE_ALREADY_SEND_MESSAGE),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    const listLogSchedule: CreateLogScheduleDto[] = [];
    payload?.lcvIds?.forEach((lcvId) => {
      listLogSchedule.push({
        phoneNumber: payload?.phoneNumber,
        lcvId,
        createdBy: payload?.createdBy,
        scheduleSentTime: moment().utcOffset('+07:00').format('YYYY-MM-DDTHH:mm:ss'),
        scheduleType: payload?.type,
      });
    });

    if (listLogSchedule?.length) await this.osrService.createLogSchedule(listLogSchedule);

    const arrAge = payload?.dateOfBirth?.map((e) =>
      moment(moment().utcOffset(7).format('YYYY-MM-DD')).diff(moment(e).utcOffset(7).format('YYYY-MM-DD'), 'years'),
    );

    let templateId = process.env.NOTIFICATION_SCHEDULE_VOUCHER_OLD_SUN;
    if (arrAge?.filter((age) => age < 14)?.length) {
      if (payload.type === TypeVoucherSchedule.Sun) templateId = process.env.NOTIFICATION_SCHEDULE_VOUCHER_CHILD_SUN;
      else templateId = process.env.NOTIFICATION_SCHEDULE_VOUCHER_CHILD_RAIN;
    } else {
      if (payload.type === TypeVoucherSchedule.Sun) templateId = process.env.NOTIFICATION_SCHEDULE_VOUCHER_OLD_SUN;
      else templateId = process.env.NOTIFICATION_SCHEDULE_VOUCHER_OLD_RAIN;
    }

    return this.notificationService.sendNotification({
      FromSys: process.env.APP_NAME, //Hệ thống gửi
      Sender: payload?.createdBy,
      Messages: [
        {
          TemplateId: templateId,
          To: [payload?.phoneNumber],
          Param: {
            Title: {},
            Content: {},
            ContentFailOver: {
              voucherCode: process.env.VOUCHER_XANH,
              expirationDate: process.env.EXPRIDED_DATE_VOUCHER_XANH,
            },
            ExtraProperties: {
              customerName: payload?.customerName || '',
              customerID: `xxxxxxx${payload?.phoneNumber.slice(-3)}`,
              voucherCode: process.env.VOUCHER_XANH,
              expirationDate: process.env.EXPRIDED_DATE_VOUCHER_XANH,
            },
          },
        },
      ],
    });
  }

  async getSchedulesCallCenter(params: InjectionHistoryDto) {
    // Get from exist func
    const [injectionHistory, injectionSchedule] = await Promise.all([
      this.getInjectionHistory(params),
      this.getSchedule(params.lcvId),
    ]);

    try {
      this.logger.log(
        {
          message: `GET SCHEDULE CALL CENTER ${params.lcvId}`,
          fields: {
            bodyReq: JSON.stringify({ injectionHistory, injectionSchedule }),
            info: `${params.lcvId}`,
          },
        },
        false,
      );
    } catch (error) {}

    // Combine schedules from ecomNeedHandle and schedulesFromShop, filtering for late appointments
    let schedules = (injectionSchedule as any)?.schedulesFromEcomNeedHandle
      .concat((injectionSchedule as any)?.schedulesFromShop || []) // Handle potential undefined value
      .filter((e) => e.isLateAppointment);

    // Get history day D and D-1
    const startOfYesterday = moment().utcOffset('+07:00').subtract(1, 'days').startOf('days');
    const historyItems = injectionHistory.items?.filter((e) =>
      moment(e.appointmentDate).isSameOrAfter(startOfYesterday),
    );
    const orderDetailAttachmentCodes = _.compact(_.uniq(historyItems?.map((e) => e.orderDetailAttachmentCode)));
    // Get tracking time
    let tickets = [];
    if (orderDetailAttachmentCodes?.length > 0) {
      tickets = await this.examinationCoreService.getListTicketByOrderDetailAttachmentCodes({
        orderDetailAttachmentCodes,
      });
    }
    for (const item of historyItems) {
      item.trackingTime = null;
      const ticket = tickets?.find(
        (e) =>
          e.status === 8 &&
          e.indications?.find((indication) => indication.orderDetailAttachmentCode === item.orderDetailAttachmentCode),
      );
      if (ticket) {
        item.trackingTime = ticket.completedTrackingTime;
      }
    }

    // Efficiently update itemGroup with vaccines directly within the filter
    const itemGroup = (injectionHistory as any).itemGroup?.filter((item) => {
      item.vaccines = historyItems?.filter(
        (e) => e.diseaseGroupId === item.diseaseId && e.disease === item.diseaseName,
      );
      return item.vaccines?.length > 0;
    });

    schedules = schedules.sort((a, b) => {
      return moment(a.date, 'DD/MM/YYYY').isBefore(moment(b.date, 'DD/MM/YYYY')) ? -1 : 1;
    });

    return {
      schedules: {
        items: _.flatMapDeep(schedules, (e) => e.data),
        schedules,
      },
      histories: {
        itemGroup,
        items: _.flatMapDeep(itemGroup, (e) => e.vaccines)?.sort((a, b) => {
          return new Date(b?.appointmentDate).getTime() - new Date(a?.appointmentDate).getTime();
        }),
      },
    };
  }

  /**
   * lấy danh sách lịch sử cùng ticketCode bởi orderDetailAttachmentCode
   */
  async getListHistoryByDetailAttachmentCode(body: GetListScheduleHistoryDto) {
    const outPut: { items: (VaccineHistoryDetailDto & { scheduleType?: string })[]; ticketCode: string } = {
      items: [],
      ticketCode: '',
    };
    // lấy danh sách ticketCode có orderDetailAttachmentCode
    const tickets = await this.examinationCoreService.getListTicketByOrderDetailAttachmentCodesInIndication({
      ...body,
      status: [EnmStatusTicket.WAIT_FOR_FOLLOW, EnmStatusTicket.DONE], // 7,8 chờ theo dõi và hoàn thành
      size: 100,
    });

    if (tickets?.length === 0) return outPut;

    // lấy danh sách lịch sử của lcvid
    const histories = await this.vacHistoryService.getByLcvId(tickets?.at(0)?.lcvId);

    // lọc những orderDetailAttachmentCode có trong ticket
    const orderDetailAttachmentCodes: string[] = [];
    tickets?.forEach((i) => {
      if (i?.indications?.length) {
        i?.indications?.forEach((f) => {
          if (f?.orderDetailAttachmentCode) {
            orderDetailAttachmentCodes.push(f?.orderDetailAttachmentCode);
          }
        });
      }
    });

    if (orderDetailAttachmentCodes?.length === 0) return outPut;

    // filter những lịch sử có cùng orderDetailAttachmentCode
    const filterSameOrderDetail =
      histories?.filter((h) => orderDetailAttachmentCodes?.includes(h?.orderDetailAttachmentCode)) || [];

    if (filterSameOrderDetail?.length === 0) return outPut;

    const regimenIds = filterSameOrderDetail?.map((r) => r?.regimenId);
    const listRegimentById = await this.regimenCoreService.getRegimenByIds({ regimenIds: _.uniq(regimenIds) });

    outPut.items = filterSameOrderDetail?.map((f) => {
      const { scheduleType } = listRegimentById?.find((r) => r?.id === f?.regimenId) || { scheduleType: '' };
      return {
        ...f,
        scheduleType,
      };
    });
    outPut.ticketCode = tickets?.at(0)?.ticketCode || '';
    return outPut;
  }

  async getScheduleEcomEarlyD2(getScheduleEcomEarlyD2Dto: GetScheduleEcomEarlyD2ConvertDto) {
    const payload = {
      ...getScheduleEcomEarlyD2Dto,
      skipCount: (getScheduleEcomEarlyD2Dto.page - 1) * getScheduleEcomEarlyD2Dto?.pageSize || 0,
      maxResultCount: getScheduleEcomEarlyD2Dto?.pageSize || 10,
      ecomProcessEstimatedDateFrom: getScheduleEcomEarlyD2Dto?.ecomProcessEstimatedDateFrom || undefined,
      ecomProcessEstimatedDateTo: getScheduleEcomEarlyD2Dto?.ecomProcessEstimatedDateTo || undefined,
      ecomProcessStatus: getScheduleEcomEarlyD2Dto?.ecomProcessStatus
        ?.map((item) => {
          if (item === 1) {
            return false;
          }

          if (item === 2) {
            return true;
          }

          return undefined;
        })
        ?.filter((item) => item !== undefined),
      page: undefined,
      pageSize: undefined,
    };

    if (payload?.paidStatus?.length) {
      payload.isPaid = payload?.paidStatus?.map((statusItem) => (statusItem === PaidStatus.PAID ? true : false));
    }

    const res = await this.scheduleCoreService.getScheduleEcomEarlyD2(_.omit(payload, ['paidStatus']));
    const listOrderCode = _.uniq(
      JSONPath({
        path: '$.items[*].scheduleEcoms[*].orderCode',
        json: res,
      }),
    )?.filter(Boolean) as string[];
    const arrLcvIds = _.uniq(
      JSONPath({
        path: '$.items[*].scheduleEcoms[*].lcvId',
        json: res,
      }),
    )?.filter(Boolean) as string[];
    const [listJourney, customers] = await Promise.all([
      listOrderCode?.length
        ? this.journeyService.getJourneysByListOrderCode({
            orderCodes: listOrderCode,
          })
        : [],
      arrLcvIds?.length ? this.familyCoreService.getListPrimaryPerson(arrLcvIds) : [],
    ]);
    const ageRanges = await this.regimenCoreService.getAgeRanges();
    const formattedItems = res.items.map((item) => {
      const scheduleEcomsGroupByOrderCode = _.groupBy(item.scheduleEcoms, 'orderCode');
      return {
        ...item,
        scheduleEcoms: Object.keys(scheduleEcomsGroupByOrderCode).map((orderCode) => {
          // loop each order.
          const formattedSchedules = scheduleEcomsGroupByOrderCode?.[orderCode]?.map((schedule) => {
            const lcvId = schedule?.lcvId;
            const findPerson = customers?.find((customerItem) => customerItem?.lcvId === lcvId);
            const customerAge = calculateTimeDifference(
              findPerson?.dateOfBirth,
              findPerson?.from,
              findPerson?.to,
              ageRanges,
              findPerson.ageUnitCode,
            );
            // end handle lcv
            const findJourney = listJourney?.find((journeyItem) => {
              const listTransactionNums =
                journeyItem?.journeySteps?.map((itemJourneySteps) => itemJourneySteps?.transactionNums) || [];
              return listTransactionNums?.includes(schedule?.orderCode);
            });
            return {
              ...schedule,
              paidStatus: schedule?.isPaid ? 2 : 1, // Màn hình này 100% là chưa thanh toán
              // journey
              journeyId: findJourney?.id || '',
              // lcv
              lcvGender: !isNaN(findPerson?.gender as any) ? findPerson?.gender : -1,
              ageInDays: customerAge?.day || 0,
              ageInMonths: customerAge?.month || 0,
              ageInYears: customerAge?.year || 0,
              ageTextDisplay: customerAge?.textDisplay || '',
              orderStatus: schedule?.orderStatus || null,
            };
          });
          return {
            orderCode,
            ..._.pick(formattedSchedules[0], [
              'lcvId',
              'lcvName',
              'lcvPhoneNumber',
              'lcvGender',
              'ageInDays',
              'ageInMonths',
              'ageInYears',
              'ageTextDisplay',
              'orderStatus',
              'paidStatus',
              'journeyId',
              'shopCode',
              'shopName',
              'receptionistCode',
              'shopCode',
              'shopName',
              'orderCode',
              'orderStatus',
              'updatedBy',
              'updatedByName',
              'updatedDate',
              'receptionistCode',
              'receptionistName',
              'needAdvice',
              'note',
              'ecomDisplay',
              'journeyId',
              'lcvId',
              'lcvName',
              'lcvPhoneNumber',
              'lcvGender',
              'ageInDays',
              'ageInMonths',
              'ageInYears',
              'ecomProcessBy',
              'ecomProcessName',
              'ecomProcessDate',
              'ecomProcessStatus',
              'ecomProcessResultStatus',
              'ecomProcessNote',
            ]),
            schedules: formattedSchedules,
          };
        }),
      };
    });

    return {
      totalCount: res?.totalCount || 0,
      items: formattedItems,
    };
  }

  async updateScheduleEcomEarlyD2Status(updateScheduleEcomEarlyD2StatusDto: UpdateScheduleEcomEarlyD2StatusDto) {
    return await this.scheduleCoreService.updateScheduleEcomEarlyD2Status(updateScheduleEcomEarlyD2StatusDto);
  }

  async getAllStatusScheduleRemindEarly() {
    return await this.scheduleCoreService.getAllStatusScheduleRemindEarly();
  }

  async getSaveOrderSchedules(queries: GetSaveOrderScheduleQuery) {
    const saveOrderSchedules = await this.scheduleCoreService.getSaveOrderSchedules(queries.lcvId);

    const allItemCodes = _.uniq(saveOrderSchedules?.items?.map((item) => item.sku));
    const allRegimenIds = _.uniq(saveOrderSchedules.items?.map((item) => item.regimenID));
    const regimens: RegimenItem[] = [];
    const products = [];
    if (allItemCodes.length) {
      products.push(...((await this.pimAppService.getListProductBySku(allItemCodes))?.listProduct || []));
    }

    if (allRegimenIds.length) {
      regimens.push(...(await this.regimenCoreService.getRegimenByIds({ regimenIds: allRegimenIds })));
    }

    const formattedSchedules = saveOrderSchedules?.items?.map((item) => {
      const productDetail = products?.find((prodItem) => prodItem.sku === item.sku);
      const regimenDetail = regimens?.find((regimenItem) => regimenItem.id === item.regimenID);
      return {
        ..._.pick(item, ['appointmentDate', 'isPaid', 'sku', 'shopCode', 'shopName', 'id']),
        diseaseName: regimenDetail?.diseaseGroup?.name || null,
        vaccineName: item?.skuName,
        manufactor: productDetail?.manufactor,
        scheduleType: regimenDetail?.scheduleType,
        orderInjection: item?.injections,
        unitCodeSale: item.unitCodeSale,
        unitNameSale: item.unitNameSale,
        status: item.status,
      };
    });

    const unusedScheduleIds = (formattedSchedules || [])
      ?.filter((scheduleItem) => scheduleItem.status !== 0)
      ?.map((unusedSchedule) => unusedSchedule.id);

    return {
      items: _.orderBy(
        formattedSchedules.filter((scheduleItem) => !unusedScheduleIds.includes(scheduleItem.id)),
        ['appointmentDate', 'diseaseName'],
        ['asc'],
      ),
      totalCount: saveOrderSchedules.totalCount - unusedScheduleIds.length,
    };
  }

  /**
   * @TODO xóa lịch sử tiêm
   */
  async deleteHistoryByOtp(body: ReqDeleteHistoryDto) {
    /**
     * https://reqs.fptshop.com.vn/browse/FV-12985
     * xóa lịch sử nguồn tcqg
     */
    const orderChannelRequest = this.req.headers['order-channel'] as string;
    const shopCode = this.req.headers['shop-code'] as string;
    const { sourceId, inside, otp, phoneNumber, verifyMethod, id, lcvId, createdBy, imgKeys } = body;

    if (sourceId === SOURCE_ID_HISTORY.TCQG) {
      // verifyMethod: 1 => SMS ; 2 => Staff
      switch (verifyMethod) {
        case EnmVerifyMethod.OTP:
          await this.schedulesRuleService.verifyOtpBySMSDeleteHistory({ otp, phoneNumber });
          break;
        case EnmVerifyMethod.Staff:
          await this.schedulesRuleService.verifyOtpByInsideDeleteHistory({
            otp,
            token: this.token,
            shopCode: shopCode,
            inside,
            orderChannelRequest,
          });
          break;
      }
    }

    await this.vacHistoryService.deleteHistoryTcqg({
      lcvId: lcvId,
      source: sourceId,
      updatedBy: createdBy,
      approvedBy: inside ?? '',
      historyDetailId: id,
      imageKeys: imgKeys ?? [],
    });

    const historyForPerson = await this.vacHistoryService.getByLcvId(lcvId, false);

    // update all
    const payloadUpdate = historyForPerson.filter((x) => x.id !== id);
    if (!payloadUpdate.length) {
      return {
        itemGroup: [],
        items: [],
      };
    }
    return await this._shouldOverwriteHistory({ dataFromHistory: payloadUpdate, lcvId, shouldUpdateInjection: true });
  }
}
