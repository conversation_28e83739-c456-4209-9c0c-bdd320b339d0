import { applyDecorators, Type } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';

export function ApiPaginatedResponse<TModel extends Type<any>>(model: TModel, description = 'success') {
  return applyDecorators(
    ApiExtraModels(model),
    ApiOkResponse({
      description,
      schema: {
        allOf: [
          {
            type: 'object',
            properties: {
              status: {
                type: 'number',
                example: 200,
              },
              message: {
                type: 'string',
                example: description,
              },
              datas: {
                type: 'object',
                properties: {
                  items: {
                    type: 'array',
                    items: { $ref: getSchemaPath(model) },
                  },
                  totalCount: {
                    type: 'number',
                    example: 100,
                  },
                },
              },
            },
          },
        ],
      },
    }),
  );
}
