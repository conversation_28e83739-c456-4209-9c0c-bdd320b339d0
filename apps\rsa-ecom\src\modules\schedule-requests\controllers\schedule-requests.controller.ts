import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ValidationPipeClass } from '@shared/common/validations/class.validtor';
import {
  AssignOnlineOrderRequestDTO,
  AssignScheduleRequestDTO,
  CheckQuotaPreOrderDto,
  CreateNewOnlineOrderRequestDto,
  CreateScheduleRequestDTO,
  GetManyOrdersBody,
  GetScheduleRequestDTO,
  GetScheduleRequestPreOrderDTO,
  ProcessingOutOfQuotaRequestDTO,
  UpdateScheduleRequestDTO,
} from '../dtos/request';
import { ScheduleRequestsService } from '../services/schedule-requests.service';
import { CurrentUser, Public } from '@shared';
import { ROOT_PATH, ROUTES, TAG, VERSION } from '../constants/route.constants';
import { ScheduleRequestType } from '../enums/schedule-request-type.enum';
import { CreatePreOrderRequestDTO } from '../dtos/request/create-pre-order-request.dto';
import { SearchByLcvIdAndPhoneDto } from '../dtos/request/search-by-lcvid-or-phone.dto';
import { MasterDataResponseDto } from '../dtos/response/master-data.response.dto';
import { GetMasterDataCampaignType } from '../dtos/request/get-master-data-campaign-type.dto';

@ApiTags(TAG)
@Controller({
  version: VERSION,
  path: ROOT_PATH,
})
@ApiBearerAuth('defaultJWT')
export class ScheduleRequestsController {
  constructor(private readonly scheduleRequestsService: ScheduleRequestsService) {}

  @Get(ROUTES.GET)
  @Public()
  @HttpCode(HttpStatus.OK)
  filter(@Query() query: GetScheduleRequestDTO) {
    return this.scheduleRequestsService.filter(query, ScheduleRequestType.All);
  }

  @Get(ROUTES.GET_FOR_PRE_ORDER)
  @Public()
  @HttpCode(HttpStatus.OK)
  filterForPreOrder(@Query() query: GetScheduleRequestPreOrderDTO) {
    return this.scheduleRequestsService.filterPreOrder(query);
  }

  @Get(ROUTES.GET_BY_ID)
  @Public()
  @HttpCode(HttpStatus.OK)
  getById(@Param('uuid') uuid: string) {
    return this.scheduleRequestsService.getById(uuid);
  }

  @Get('get-by-order-code/:orderCode')
  @Public()
  @HttpCode(HttpStatus.OK)
  getByOrderCode(@Param('orderCode') orderCode: string) {
    return this.scheduleRequestsService.getByOrderCode(orderCode);
  }

  @Post(ROUTES.POST)
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Create new schedule request',
    description: 'Example night shift date: 2024-03-11T16:50:00.00Z',
  })
  create(@Body(new ValidationPipeClass()) data: CreateScheduleRequestDTO) {
    data.user = {} as any;
    return this.scheduleRequestsService.create(data);
  }

  @Post(ROUTES.CREATE_PRE_ORDER_REQUEST)
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Create new pre-order request',
    description: 'Example night shift date: 2024-03-11T16:50:00.00Z',
  })
  createPreOrder(@Body(new ValidationPipeClass()) data: CreatePreOrderRequestDTO) {
    data.user = {} as any;
    return this.scheduleRequestsService.createPreOrder(data);
  }

  @Post(ROUTES.CHECK_QUOTA_LIMIT_PRE_ORDER)
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Check quota limit',
    description: 'Check quota limit to block pre-order',
  })
  checkQuotaLimitation(@Body(new ValidationPipeClass()) data: CheckQuotaPreOrderDto) {
    return this.scheduleRequestsService.checkQuotaLimitaion(data);
  }

  // from here to below, review needed
  @Put(ROUTES.UPDATE_BY_ID)
  @HttpCode(HttpStatus.OK)
  async update(@Param('uuid') uuid: string, @CurrentUser() user, @Body() body: UpdateScheduleRequestDTO) {
    return await this.scheduleRequestsService.update(uuid, user, body);
  }

  @Put(ROUTES.UPDATE_FOR_PRE_ORDER_BY_ID)
  @HttpCode(HttpStatus.OK)
  async updateForPreOrder(@Param('uuid') uuid: string, @CurrentUser() user, @Body() body: UpdateScheduleRequestDTO) {
    return await this.scheduleRequestsService.updateForPreOrder(uuid, user, body);
  }

  @Post(ROUTES.SYNC_ES_BY_ID)
  @HttpCode(HttpStatus.OK)
  async syncES(@Param('uuid') uuid: string) {
    return this.scheduleRequestsService.syncES(uuid);
  }

  @Delete(ROUTES.CANCEL_BY_ID)
  @HttpCode(HttpStatus.OK)
  async cancel(@Param('uuid') uuid: string, @CurrentUser() user, @Body() body: UpdateScheduleRequestDTO) {
    return await this.scheduleRequestsService.cancel(uuid, user, body, ScheduleRequestType.Default);
  }

  @Delete(ROUTES.CANCEL_FOR_PRE_ORDER_BY_ID)
  @HttpCode(HttpStatus.OK)
  async cancelForPreOrder(@Param('uuid') uuid: string, @CurrentUser() user, @Body() body: UpdateScheduleRequestDTO) {
    return await this.scheduleRequestsService.cancel(uuid, user, body, ScheduleRequestType.PreOrder);
  }

  @Put(ROUTES.ASSIGN_BY_ID)
  @Public()
  @HttpCode(HttpStatus.OK)
  async assign(
    @Param('uuid') uuid: string,
    @Param('insideCode') insideCode: string,
    @CurrentUser() user,
    @Body() body: AssignScheduleRequestDTO,
  ) {
    return await this.scheduleRequestsService.assign(uuid, insideCode, user, body);
  }

  @Put(ROUTES.ASSIGN_BY_FOR_PRE_ORDER_ID)
  @Public()
  @HttpCode(HttpStatus.OK)
  async assignForPreOrder(
    @Param('uuid') uuid: string,
    @Param('insideCode') insideCode: string,
    @CurrentUser() user,
    @Body() body: AssignScheduleRequestDTO,
  ) {
    return await this.scheduleRequestsService.assignForPreOrder(uuid, insideCode, user, body);
  }

  @Post(ROUTES.PROCESSING_OUT_OF_QUOTA_REQUEST)
  // @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({})
  processingOutOfQuotaRequest(@Body() data: ProcessingOutOfQuotaRequestDTO, @CurrentUser() user) {
    return this.scheduleRequestsService.processingOutOfQuotaRequest(data, user);
  }

  @Post(ROUTES.GET_MANY_ORDERS)
  @Public()
  @HttpCode(HttpStatus.OK)
  getManyOrders(@Body() body: GetManyOrdersBody) {
    return this.scheduleRequestsService.getManyOrders(body);
  }

  @Post(ROUTES.SEARCH_BY_LCVID_OR_PHONE)
  @Public()
  @HttpCode(HttpStatus.OK)
  searchByLcvIdOrPhone(@Body() body: SearchByLcvIdAndPhoneDto) {
    return this.scheduleRequestsService.searchByLcvIdOrPhone(body);
  }

  @Get(ROUTES.GET_MASTER_DATA_CAMPAIGN_TYPE)
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get master data for CampaignType',
    description: 'Returns a list of all campaign types as enums',
  })
  async getMasterDataCampaignType(@Query() query: GetMasterDataCampaignType): Promise<MasterDataResponseDto> {
    return this.scheduleRequestsService.getMasterDataCampaignType(query);
  }

  @Get(ROUTES.GET_MASTER_DATA_LANDING_TYPE)
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get master data for LandingType',
    description: 'Returns a list of all landing types as enums',
  })
  async getMasterDataLandingType(): Promise<MasterDataResponseDto> {
    return this.scheduleRequestsService.getMasterDataLandingType();
  }

  @Get(ROUTES.GET_MASTER_DATA_CUSTPOMER_TYPE)
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get master data for CustomerType',
    description: 'Returns a list of all customer types as enums',
  })
  async getMasterDataCustomerType(): Promise<MasterDataResponseDto> {
    return this.scheduleRequestsService.getMasterDataCustomerType();
  }

  @Post(ROUTES.CREATE_ONLINE_ORDER)
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Create new online order request',
  })
  createOnlineOrderRequest(@Body() body: CreateNewOnlineOrderRequestDto) {
    return this.scheduleRequestsService.createOnlineOrderRequest(body);
  }

  @Put(ROUTES.ASSIGN_BY_FOR_ONLINE_ORDER_ID)
  @Public()
  @HttpCode(HttpStatus.OK)
  async assignForOnlineOrderRequest(
    @Param('uuid') uuid: string,
    @Param('insideCode') insideCode: string,
    @Body() body: AssignOnlineOrderRequestDTO,
  ) {
    return await this.scheduleRequestsService.assignForOnlineOrderRequest(
      uuid,
      insideCode,
      body?.empCodeCurrent,
      body?.isAutoAssign,
    );
  }

  @Get(ROUTES.CHECK_EXIST_ONLINE_ORDER)
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'check exist online order request',
  })
  checkOnlineOrderRequestExist(@Param('orderCode') orderCode: string) {
    return this.scheduleRequestsService.checkOnlineOrderRequestExist(orderCode);
  }

  @Get(ROUTES.GET_EXIST_ONLINE_ORDER)
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'check exist online order request',
  })
  getOnlineOrderRequest(@Param('orderCode') orderCode: string) {
    return this.scheduleRequestsService.getOnlineOrderRequest(orderCode);
  }
}
