import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, CurrentUser, generalSchema, Public, UserDto } from '@shared';
import { ApiPaginatedResponse, PaginatedResultDto } from '@shared/dto/pagination';
import { PaginationOptionsDto } from '@shared/dto/pagination/pagination-options.dto';
import { GetOneOrderLibResponse } from 'vac-nest-oms';
import {
  ApproveMultipleXuatOffDto,
  CancelXuatOffDto,
  CreateXuatOffDto,
  GetOneOrderInfoDto,
  GetOrderInfoServiceDto,
  SearchXuatOffDto,
  UpdateXuatOffDto,
  XuatOffResponseDto,
} from '../dto';
import { GetXuatOffDto } from '../dto/get-xuat-off.dto';
import { XuatOffService } from '../services/xuat-off.service';

@ApiTags('xuat-off')
@Controller({ path: 'xuat-off', version: '1' })
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(XuatOffResponseDto, GetOneOrderLibResponse, UpdateXuatOffDto)
export class XuatOffController {
  constructor(private readonly xuatOffService: XuatOffService) {}

  @Post('create')
  @ApiOperation({ summary: 'Tạo xuất off' })
  @ApiOkResponse({
    description: 'Chi tiết thông tin xuat off',
    schema: generalSchema(XuatOffResponseDto, 'object'),
  })
  create(@Body() xuatOffDto: CreateXuatOffDto, @CurrentUser() user: UserDto): Promise<XuatOffResponseDto> {
    return this.xuatOffService.create(xuatOffDto, user);
  }

  @Get('search')
  @ApiOperation({ summary: 'Get all xuat off records' })
  @ApiPaginatedResponse(GetXuatOffDto, 'Danh sách xuất off ')
  findAll(
    @Query() payloadQuery: SearchXuatOffDto,
    @Query() paginate: PaginationOptionsDto,
  ): Promise<PaginatedResultDto<GetXuatOffDto>> {
    return this.xuatOffService.findAll(payloadQuery, paginate);
  }

  @Get('detail/:id')
  @ApiOperation({ summary: 'Get a xuat off record by id' })
  @ApiOkResponse({
    description: 'Chi tiết thông tin xuất off',
    schema: generalSchema(GetXuatOffDto, 'object'),
  })
  findOne(@Param('id', new ParseUUIDPipe()) id: string): Promise<GetXuatOffDto> {
    return this.xuatOffService.findOne(id);
  }

  @Patch('update/:id')
  @ApiOperation({ summary: 'Update xuất off' })
  @ApiOkResponse({
    description: 'Chi tiết thông tin xuất off',
    schema: generalSchema(GetXuatOffDto, 'object'),
  })
  update(
    @Param('id') id: string,
    @Body() updateXuatOffDto: UpdateXuatOffDto,
    @CurrentUser() user: UserDto,
  ): Promise<GetXuatOffDto> {
    return this.xuatOffService.updateXuatOff(id, updateXuatOffDto, user);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a xuat off record' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Record successfully deleted' })
  remove(@Param('id') id: string) {
    return this.xuatOffService.remove(id);
  }

  @Post('verify-order/:orderCode')
  @Public()
  @ApiOperation({ summary: 'Xác minh tạo xuất off' })
  @ApiOkResponse({
    description: 'kết quả verify xuat off',
    schema: generalSchema(GetOneOrderLibResponse, 'object'),
  })
  verify(@Param('orderCode') orderCode: string): Promise<GetOneOrderLibResponse> {
    return this.xuatOffService.verify(orderCode);
  }

  /**
   * @description lấy danh sách order xuất off
   */
  @Public()
  @Post('orders-info')
  @ApiOperation({
    summary: 'Lấy danh sách đơn xuất off theo sđt',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách đơn xuất off',
    schema: generalSchema(GetOneOrderLibResponse, 'array'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  async getOrderInfo(@Body() body: GetOrderInfoServiceDto): Promise<GetOneOrderLibResponse[]> {
    return this.xuatOffService.getOrderInfoService(body);
  }

  @Post('order/:orderCode')
  @Public()
  @ApiOperation({ summary: 'Kéo đơn hàng tay ở shop' })
  @ApiOkResponse({
    description: 'Chi tiết đơn hàng',
    schema: generalSchema(GetOneOrderLibResponse, 'object'),
  })
  getOneOrder(
    @Param('orderCode') orderCode: string,
    @Body() payload: GetOneOrderInfoDto,
  ): Promise<GetOneOrderLibResponse> {
    return this.xuatOffService.getOneOrder(orderCode, payload);
  }

  @Patch('cancel/:id')
  @ApiOperation({ summary: 'Huỷ xuất off' })
  @ApiOkResponse({
    description: 'Huỷ xuất off',
    type: Boolean,
  })
  cancel(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() xuatOffDto: CancelXuatOffDto,
    @CurrentUser() user: UserDto,
  ): Promise<boolean> {
    return this.xuatOffService.cancelXuatOff(id, xuatOffDto, user);
  }

  @Post('approve-multiple')
  @ApiOperation({ summary: 'Duyệt xuất off' })
  @ApiOkResponse({
    description: 'Duyệt xuất off',
    type: Boolean,
  })
  approveXuatOff(@Body() payload: ApproveMultipleXuatOffDto, @CurrentUser() user: UserDto) {
    return this.xuatOffService.approveXuatOff(user, payload);
  }

  @Post('reject-multiple')
  @ApiOperation({ summary: 'Từ chối xuất off' })
  @ApiOkResponse({
    description: 'Từ chối xuất off',
    type: Boolean,
  })
  rejectXuatOff(@Body() payload: ApproveMultipleXuatOffDto, @CurrentUser() user: UserDto) {
    return this.xuatOffService.rejectXuatOff(user, payload);
  }

  @Post('insert-xuatoff0d')
  @Public()
  @ApiOperation({ summary: 'Thực thi stored procedure insert data xuat off 0d' })
  @ApiOkResponse({
    description: 'Đã gọi function insert data xuat off 0d',
    schema: generalSchema(true, 'boolean'),
  })
  @HttpCode(HttpStatus.OK)
  insertXuatOff0d(): Promise<boolean> {
    return this.xuatOffService.insertXuatOff0d();
  }
}
