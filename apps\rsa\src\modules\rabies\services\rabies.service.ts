import { Injectable } from '@nestjs/common';
import { ExaminationCoreService, PostRabiesDeclarationDto, UpdateRabiesByTicketDto } from 'vac-nest-examination';
import { MonitorCoreService } from 'vac-nest-monitor';
import { ReportCoreService, UsedVaccineReportDto } from 'vac-nest-report';

@Injectable()
export class RabiesService {
  constructor(
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly monitorCoreService: MonitorCoreService,
    private readonly reportCoreService: ReportCoreService,
  ) {}

  /**
   * @TODO lấy danh sách lịch sử tiêm
   */
  async getHistoryRabies(lcvId: string) {
    // lấy danh sách lịch sử khai báo tiêm tại
    const res_history = await this.examinationCoreService.getRabiesDeclaration({ lcvId: lcvId });

    // lấy câu hỏi tiêm dại
    const res_questions = await this.monitorCoreService.searchQuestionRabies();
    return {
      items: res_history,
      questions: res_questions,
    };
  }

  /**
   * @TODO lưu thông tin khai báo dại
   */
  async createRabies(body: PostRabiesDeclarationDto) {
    const data = await this.examinationCoreService.postRabiesDeclaration(body);
    return {
      items: data,
    };
  }

  /**
   * @TODO khai báo sử dụng dại
   */
  async getUsedVaccine(body: UsedVaccineReportDto) {
    return await this.reportCoreService.getReportUsedVaccine(body);
  }

  /**
   * @TODO khai báo dại tổng hợp
   */
  async getRabiesVaccine(body: UsedVaccineReportDto) {
    return await this.reportCoreService.getReportRabiesVaccine(body);
  }

  /**
   * @TODO khai báo dại theo khách hàng tiêm
   */
  async getRabiesMonitoringVaccine(body: UsedVaccineReportDto) {
    return await this.reportCoreService.getReportRabiesMonitoringVaccine(body);
  }

  async getRabiesByTicketCode(ticketCode?: string) {
    return this.examinationCoreService.getRabiesByTicket(ticketCode);
  }

  /**
   * @TODO update khai báo dại
   * @param id
   * @param body
   */
  async updateRabiesByTicket(id?: string, body?: UpdateRabiesByTicketDto) {
    return await this.examinationCoreService.updateRabiesByTicket(id, body);
  }
}
