import { Module } from '@nestjs/common';
import { FtelController } from './ftel.controller';
import { FtelModule as FtelLibModule } from 'modules/modules/modules/ftel/ftel.module';
import { FtelService } from './ftel.service';
import { RedisModule } from '@shared';
import { RSAIntegrationModule } from 'vac-nest-rsa-integration';

@Module({
  imports: [FtelLibModule, RedisModule, RSAIntegrationModule],
  controllers: [FtelController],
  providers: [FtelService],
})
export class FtelModule {}
