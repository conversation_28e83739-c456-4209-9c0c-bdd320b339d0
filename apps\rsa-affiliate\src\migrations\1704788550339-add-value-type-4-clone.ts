import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddValueType4Clone1704788550339 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '4', '1', '<PERSON>h<PERSON><PERSON> không tiêm được hôm nay')`,
    );
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '4', '1', '<PERSON>h<PERSON>ch có nhu cầu tiêm thêm mũi')`,
    );
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '4', '1', '<PERSON>h<PERSON>ch muốn đổi vắc xin')`,
    );
    await queryRunner.query(`INSERT INTO reasons ("type", "status", "name") VALUES( '4', '1', '<PERSON> thao tác')`);
    await queryRunner.query(`INSERT INTO reasons ("type", "status", "name") VALUES( '4', '1', 'Khác')`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('');
  }
}
