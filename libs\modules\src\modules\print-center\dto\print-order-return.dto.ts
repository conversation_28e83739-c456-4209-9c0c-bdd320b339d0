import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { PrinterInfo } from './get-printer.dto';

export class PrintOrderReturnDto {
  @ApiProperty({
    description: 'Thông tin máy in',
    required: true,
  })
  @IsNotEmpty()
  printerInfo: PrinterInfo;

  @ApiProperty({
    description: 'ID đơn trả hàng',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  orderReturnId: string;
}

export class PrintOrderReturnDataDto {
  @ApiProperty({
    description: 'Mã thanh toán',
  })
  @IsOptional()
  @IsString()
  paymentCode?: string;

  @ApiProperty({
    description: 'Tổng phí bồi thường vi phạm hợp đồng',
  })
  @IsOptional()
  totalFeeCompensationForBreachOfContract?: number;

  @ApiProperty({
    description: '<PERSON>ại thanh toán',
  })
  @IsOptional()
  typePayment?: number;

  @ApiProperty({
    description: '<PERSON><PERSON>y hoàn tất đơn',
  })
  @IsOptional()
  @IsString()
  completedDate?: string;

  @ApiProperty({
    description: 'Tên nhân viên',
  })
  @IsOptional()
  @IsString()
  employeeName?: string;

  @ApiProperty({
    description: 'Mã nhân viên',
  })
  @IsOptional()
  @IsString()
  employeeCode?: string;

  @ApiProperty({
    description: 'Mã chức danh nhân viên',
  })
  @IsOptional()
  @IsString()
  jobTitleName?: string;

  @ApiProperty({
    description: 'Số tài khoản',
  })
  @IsOptional()
  @IsString()
  accountNum?: string;

  @ApiProperty({
    description: 'Tên tài khoản',
  })
  @IsOptional()
  @IsString()
  accountName?: string;

  @ApiProperty({
    description: 'Tên ngân hàng',
  })
  @IsOptional()
  @IsString()
  bankName?: string;

  @ApiProperty({
    description: 'Mã đơn hàng gốc',
  })
  @IsOptional()
  @IsString()
  orderCode?: string;

  @ApiProperty({
    description: 'Mã đơn trả hàng',
  })
  @IsOptional()
  @IsString()
  orderReturnCode?: string;
}
