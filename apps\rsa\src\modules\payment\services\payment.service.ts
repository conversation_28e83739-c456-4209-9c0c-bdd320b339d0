import { Inject, Injectable } from '@nestjs/common';
import {
  CheckTransactionVaccineMBankDto,
  CreatedQRMBankDto,
  PayloadGetListBank,
  PaymentGatewayService,
  ServiceType,
  VerifyVoucherGotitLibDto,
  VerifyVoucherUrboxLibDto,
} from 'vac-nest-payment-gateway';
import {
  PaymentFromSystem,
  PaymentInitMethod,
  PaymentPartnerType,
  PaymentQRCodeType,
  PaymentTerminalId,
} from '../constants';
import { CheckBillDto, GetPaymentMethodDto, PayloadCancelQRMBankDto, PayloadCreatedQRMBankDto } from '../dto';
import { Request } from 'express';
import { GetListPaymentPayVaccineDto } from '../dto/get-list-payment-pay-vaccine.dto';
import { concurrentPromise, OrderChanel, OrderChannels } from '../../../../../../libs/shared/src';
import { DepositCancelService } from '../../../../../../libs/modules/src/modules/deposit-cancel/services/depositCancel.service';
import { PaymentMethodsEnum } from '../enum';
import { OrdersService } from '@libs/modules/orders/services/orders.service';
import { OMSService } from 'vac-nest-oms';
@Injectable()
export class PaymentService {
  shopCode: string;
  constructor(
    @Inject('REQUEST')
    private readonly request: Request,
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly depositCancelService: DepositCancelService,
    private readonly orderService: OrdersService,
    private readonly omsService: OMSService,
  ) {
    this.shopCode = this.request.headers['shop-code'] as string;
  }

  async getListPaymentMethod({ orderCode }: GetPaymentMethodDto) {
    const paymentMethods = await this.paymentGatewayService.getListPaymentMethod({
      serviceTypeId: ServiceType.Vaccine,
    });

    //reqs.fptshop.com.vn/browse/FV-10303
    //https://reqs.fptshop.com.vn/browse/FV-10321
    if (orderCode) {
      const order = await this.orderService.getOrderForPaymentChecking(orderCode);
      if (
        (OrderChannels.RSA_AFFILIATE.includes(order.orderChanel) && order?.paymentInfo?.depositedAmount > 0) ||
        (OrderChannels.RSA.includes(order.orderChanel) && order?.orderAttribute === 9)
      ) {
        return paymentMethods?.filter((item) => item.id !== PaymentMethodsEnum.HOME_PAY_LATER);
      }
    }

    return paymentMethods;
  }

  async checkBill(checkBillDto: CheckBillDto, orderChannel?: string) {
    if (
      orderChannel &&
      (OrderChannels.RSA_ECOM.includes(orderChannel) || OrderChannels.RSA_AFFILIATE.includes(orderChannel))
    ) {
      // check bill ecom hoặc affiliate
      return this.checkBillEcom(checkBillDto);
    }

    const { total, cash, transfer, card, voucher, cod, ewallet, point, topUp, homePaylater } = checkBillDto;

    const totalBill = +total || 0;
    const cashBill = +cash || 0;
    const transferBill = +transfer || 0;
    const cardBill = +card || 0;
    const voucherBill = +voucher || 0;
    const codBill = +cod || 0;
    const eWalletBill = +ewallet || 0;
    const rewardPoints = +point || 0;
    const topUpBill = +topUp || 0;
    const homePaylaterBill = +homePaylater || 0;

    const totalPriceMethodNoChange = transferBill + voucherBill + cardBill + topUpBill + homePaylaterBill;
    const totalPriceMethodRemain = totalBill - totalPriceMethodNoChange;

    //f.sell point payment
    const pointFriendSell =
      rewardPoints - Math.floor(voucherBill / 1000) > 0 ? rewardPoints - Math.floor(voucherBill / 1000) : 0;

    //price for order deposit
    const depositPrice = Number(Math.round((totalBill * 0.3) / 1000) * 1000);

    // Total price
    if (totalPriceMethodRemain < 0) {
      return {
        total: totalPriceMethodNoChange,
        remain: 0,
        voucher: voucher,
        depositPrice: depositPrice,
        depositRemain: 0,
        point: pointFriendSell,
      };
    }

    let totalCustomerPay = cashBill + transferBill + cardBill + codBill + voucherBill + topUpBill + homePaylaterBill;
    let totalCustomerPayRemain = totalCustomerPay - totalBill;
    // E-wallet with voucher
    if (eWalletBill) {
      totalCustomerPay = eWalletBill + voucherBill + homePaylaterBill;
      totalCustomerPayRemain = totalCustomerPay - totalBill;
    }

    const dataRes: any = {
      // Total don't calculate with voucher
      total: totalCustomerPay,
      remain: totalCustomerPayRemain,
      voucher: voucher,
      depositPrice: depositPrice,
      depositRemain: totalCustomerPayRemain,
      point: pointFriendSell,
    };

    return dataRes;
  }

  async checkBillEcom(checkBillDto: CheckBillDto) {
    const { total, cash, transfer, card, voucher, cod, ewallet, point, topUp, payAtShop } = checkBillDto;

    const totalBill = +total || 0;
    const cashBill = +cash || 0;
    const transferBill = +transfer || 0;
    const cardBill = +card || 0;
    const voucherBill = +voucher || 0;
    const codBill = +cod || 0;
    const eWalletBill = +ewallet || 0;
    const rewardPoints = +point || 0;
    const topUpBill = +topUp || 0;
    const payAtShopBill = +payAtShop || 0;

    const totalPriceMethodNoChange = transferBill + voucherBill + cardBill + topUpBill;
    const totalPriceMethodRemain = totalBill - totalPriceMethodNoChange;

    //f.sell point payment
    const pointFriendSell =
      rewardPoints - Math.floor(voucherBill / 1000) > 0 ? rewardPoints - Math.floor(voucherBill / 1000) : 0;

    //price for order deposit
    const depositPrice = Number(Math.round((totalBill * 0.3) / 1000) * 1000);

    // Total price
    if (totalPriceMethodRemain < 0) {
      return {
        total: totalPriceMethodNoChange,
        remain: 0,
        voucher: voucher,
        depositPrice: depositPrice,
        depositRemain: 0,
        point: pointFriendSell,
      };
    }

    let totalCustomerPay = cashBill + transferBill + cardBill + codBill + voucherBill + topUpBill + payAtShopBill;
    let totalCustomerPayRemain = totalCustomerPay - totalBill;
    // E-wallet with voucher
    if (eWalletBill) {
      totalCustomerPay = eWalletBill + voucherBill;
      totalCustomerPayRemain = totalCustomerPay - totalBill;
    }

    const dataRes: any = {
      // Total don't calculate with voucher
      total: totalCustomerPay,
      remain: totalCustomerPayRemain,
      voucher: voucher,
      depositPrice: depositPrice,
      depositRemain: totalCustomerPayRemain,
      point: pointFriendSell,
      payAtShop: payAtShopBill,
    };

    return dataRes;
  }

  /**
   * @TODO lấy danh sách phương thức chi tiền
   */
  async getPaymentPayVaccine(getPaymentPayVaccine: GetListPaymentPayVaccineDto) {
    const { orderCode, type } = getPaymentPayVaccine;
    let res = await this.paymentGatewayService.getListPaymentPayVaccine();
    if (!res) {
      return {
        items: [],
      };
    }
    res?.forEach((item) => {
      if (item?.id === 1) {
        item?.detail?.forEach((e) => {
          e['disabled'] = false;
        });
      }
      if (item?.id === 2) {
        item['disabled'] = false;
      }
    });
    /**
     * @TODO Nếu kênh ecom thì trả disable hết chỉ trả ck nếu có tiền không thì chỉ có
     */
    const [getTotalTien, order] = await concurrentPromise(
      this.depositCancelService.getDetail(orderCode),
      this.omsService.getOneOrder(orderCode),
    );
    // hủy cọc
    if (type === 1) {
      if (!getTotalTien?.totalRefund) {
        res?.forEach((item) => {
          if (item?.id === 1) {
            item?.detail?.forEach((e) => {
              if (e?.id === 1 && !OrderChannels.RSA_AFFILIATE.includes(order?.orderChanel)) {
                e['disabled'] = true;
              }
            });
          }
          if (item?.id === 5) {
            item['disabled'] = true;
          }
        });
      }
    }
    // chi tiền
    if (type === 2) {
      res = res.filter((item) => item?.id === 1);
    }

    return {
      items: res,
    };
  }

  /**
   * @TODO tạo QR code MBank
   */
  async createQRCodeMBank(body: PayloadCreatedQRMBankDto) {
    const payloadGenQRCodeMBank: CreatedQRMBankDto = {
      ...body,
      shopCode: body?.shopCode || this.shopCode,
      terminalID: PaymentTerminalId.LONGCHAU,
      qrcodeType: PaymentQRCodeType.ORDER_OFFLINE,
      initMethod: PaymentInitMethod.DONG,
      partnerType: PaymentPartnerType.VietQR,
      fromSystem: PaymentFromSystem.RSA,
    };
    return await this.paymentGatewayService.createdQRMBank(payloadGenQRCodeMBank);
  }

  /**
   * @TODO hủy QR code MBank
   */
  async cancelQRCodeMBank(body: PayloadCancelQRMBankDto) {
    return await this.paymentGatewayService.cancelQRMBank(body);
  }

  /**
   * @TODO check transaction vaccine payer MBank
   */
  async checkTransactionMBank(body: CheckTransactionVaccineMBankDto) {
    return await this.paymentGatewayService.checkTransactionVaccinePayedMBank(body);
  }

  /**
   * @TODO get list bank
   */
  async getListBankVaccine(param: PayloadGetListBank) {
    const result = (await this.paymentGatewayService.getListBankVaccine(param)) || [];

    if (result?.length > 0) {
      result?.sort((a, b) => {
        if (a?.bankCode?.trim() === 'MBbank' && b?.bankCode?.trim() !== 'MBbank') return -1;
        if (a?.bankCode?.trim() !== 'MBbank' && b?.bankCode?.trim() === 'MBbank') return 1;
        return (a?.bankCode as any) - (b?.bankCode as any);
      });
    }

    return result;
  }

  /**
   * @TODO verifyVoucherGotit
   */
  async verifyVoucherGotit(param: VerifyVoucherGotitLibDto) {
    return this.paymentGatewayService.verifyVoucherGotit(param);
  }

  /**
   * @TODO verifyVoucherGotit
   */
  async verifyVoucherUrbox(param: VerifyVoucherUrboxLibDto) {
    return this.paymentGatewayService.verifyVoucherUrboxV2({ ...param, channel: null });
  }
}
