import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import {
  ErrorCode,
  formatDate,
  getYear,
  haveDifferentValues,
  IAuthUser,
  IError,
  OrderChannels,
  parseDateTimeZone,
  parsePhoneNumber,
  SystemException,
} from '@shared';
import { RSA_ECOM_HARD_DEFAULT_SHOP_CODE } from '@shared/common/hard-code';
import { calculateYearByRange } from '@shared/utilities/caculate-age-by-range';
import { Request } from 'express';
import Redis from 'ioredis';
import jwtDecode from 'jwt-decode';
import _ from 'lodash';
import moment from 'moment';
import { calculateTimeDifference } from 'vac-commons';
import { CustomerAppService } from 'vac-nest-customer-app';
import {
  CreateCustomerAddressDto,
  CreateCustomerAddressParasDto,
  CreateCustomerAddressResponseDto,
  CustomerCoreService,
  DeleteCustomerAddressParasDto,
  GetCustomerAddressDto,
  UpdateCustomerAddressDto,
  UpdateCustomerAddressParasDto,
  ValidateOtpDto,
} from 'vac-nest-customer-core';
import { DSMSService } from 'vac-nest-dsms';
import { EnmStatusTicket, ExaminationCoreService } from 'vac-nest-examination';
import {
  AddFamilyMemberDto,
  CreatePersonDto,
  CreatePersonRes,
  DeleteFamilyProfileDto,
  DeletePregnancyDto,
  FamilyService as FamilyCoreService,
  FamilyProfileDetails,
  GetDetailFamilyDto,
  GetFamilyByLcvIdDto,
  GetPersonByIdRes,
  PayloadGetManyLcvIdDto,
  Person,
  Pregnancy,
  PregnancyDetailDto,
  SearchFamilyByKeywordDto,
  SearchPersonDto,
  UpdatePersonDto,
  UpdatePregnancyDto,
  VerifyPhoneNumberDto,
} from 'vac-nest-family';
import { FamilyPackageService } from 'vac-nest-family-package';
import { VacHistoryService } from 'vac-nest-history';
import { JourneyService } from 'vac-nest-journey';
import { OMSService, OrderStatus } from 'vac-nest-oms';
import { GetAgeRangeResponseDto, RegimenService } from 'vac-nest-regimen';
import { ScheduleCoreService } from 'vac-nest-schedule';
import {
  ChildCarer,
  Gender,
  GetCustomerDetailRes,
  PersonType,
  PregnantsDto,
  TCQGIntegrationService,
  UpdateGuardianDto,
  UpdateProfileDto,
  UserProfileDto,
} from 'vac-nest-tcqg-integration';
import { VaccineCarCoreService } from 'vac-nest-vaccine-car';
import { calculateTimeDifferenceV2 } from '../../../../../shared/src/utilities/caculate-age-by-dob';
import { validateVietnamesePhoneNumber } from '../../family/functions';
import { FamilyUtilsService } from '../../family/services/family-utils.services';
import { FamilyService } from '../../family/services/family.service';
import { QuotasService } from '../../quotas/services/quotas.service';
import { SourceId } from '../../schedules/constants';
import { STATUS_TICKET } from '../../ticket/constants';
import {
  MEMBER_UPDATE_FIELD,
  PERSON_COMPARE_FIELD,
  PHONE_NUMBER_BUT_NOT_SHOP_PHONE,
  KEY_REDIS_FAMILY_PACKAGE,
} from '../constant';
import {
  ChangeFamilyMemberProfileDto,
  CreateCustomerTcqgBody,
  CreateCustomerTcqgResponse,
  CreateManyPersonDto,
  DeletePhoneNumberForPersonDto,
  GetCustomerByPhoneDTO,
  GetTcqgDto,
  InactivePersonPayload,
  MapTcqgToPersonBody,
  PayloadCreateOtpDto,
  PayloadVerifyOTPDto,
  pregnancyDto,
  SearchPersonByNationalVaccineCodeDto,
  SearchPersonByOrderCodeDto,
  UpdateMemberDto,
  VerifyUpdateChangePhoneDto,
  VerifyUpdateHostForPersonDto,
  VerifyAddFamilyMemberOtpDto,
  VerifyAndTransferMemberDto,
} from '../dto';
import { ATTR_OTP, EnmVerifyMethod, MAJOR_OTP, PersonFrom, SOURCE, TYPE_OTP } from '../enum';
import { CustomersRuleService } from './customers-rule.service';
import { OsrService } from 'vac-nest-osr';
import { PERMISSION } from '@shared/enum/auth';

const TIMEOUT_SEARCH_TCQG = +process.env.TIMEOUT_SEARCH_TCQG || 15000;

@Injectable()
export class CustomersService {
  shopCode: string;
  token: string;
  orderChannel: string;

  constructor(
    private readonly familyCoreService: FamilyCoreService,
    private readonly tcqgIntegrationService: TCQGIntegrationService,
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly familyUtilsService: FamilyUtilsService,
    private readonly customerCoreService: CustomerCoreService,
    private readonly familyLocalService: FamilyService,
    private readonly dsmsService: DSMSService,
    private readonly scheduleCoreService: ScheduleCoreService,
    private readonly customerRuleService: CustomersRuleService,
    private readonly historyCoreService: VacHistoryService,
    private readonly quotasService: QuotasService,
    private readonly dmsService: DSMSService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly vaccineCarCoreService: VaccineCarCoreService,
    private readonly omsService: OMSService,
    private readonly regimenCoreService: RegimenService,
    private readonly customerAppService: CustomerAppService,
    private readonly journeyCoreService: JourneyService,
    private readonly configService: ConfigService,
    private readonly familyPackageService: FamilyPackageService,
    private readonly osrCoreService: OsrService,
    @InjectRedis(process.env.REDIS_CONNECTION)
    private readonly redis: Redis,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
    this.orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    this.token = (this.req.headers?.authorization as string) || (this.req.headers?.Authorization as string) || '';
  }

  async searchCustomer(query: SearchPersonDto, employeeCode: string) {
    const newQuery = _.pick(_.cloneDeep(query), [
      'keyword',
      'name',
      'phoneNumber',
      'dateOfBirthFrom',
      'dateOfBirthTo',
      'pageNumber',
      'pageSize',
    ]);

    //#region search theo orderCode
    // check thêm điều kiện để search person theo đơn
    // chỉ search ở RSA
    const isSearchByOrderCode =
      _.isNumber(+newQuery?.keyword) &&
      newQuery?.keyword?.length === 23 &&
      OrderChannels.RSA.includes((this.req.headers?.['order-channel'] as string) || '');

    const orderCode = isSearchByOrderCode ? newQuery?.keyword : null;
    if (isSearchByOrderCode) {
      const { lcvId } = await this.journeyCoreService.getOrderInfoByOrderOrTicket({ orderCode: newQuery?.keyword });
      if (lcvId) newQuery.keyword = lcvId;
    }
    // #endRegion search theo orderCode

    // STEP 1: tìm nhà mình
    const familyData = await this.familyCoreService.searchPerson(newQuery);
    if (familyData.items.length) {
      //#region trả thông tin khách hàng kèm orderCode khi search theo orderCode
      // trả thêm thông tin orderCode khi search customer theo orderCode
      if (isSearchByOrderCode) {
        familyData.items = familyData?.items?.map((i) => {
          return {
            ...i,
            familyProfileDetails: i?.familyProfileDetails?.map((f) => {
              return {
                ...f,
                orderCode: (f?.lcvId === newQuery?.keyword && orderCode) || '',
              };
            }),
          };
        });
      }
      //#endregion trả thông tin khách hàng kèm orderCode khi search theo orderCode

      return {
        totalCount: familyData.totalCount,
        items: familyData.items,
        source: PersonFrom.LCV,
      };
    }

    // khác channel 16 thì không tìm trên tiêm chủng quốc gia
    if (this.isChannelAffilate() && this.req.headers?.['order-channel'] !== OrderChannels.RSA_AFFILIATE.at(1)) {
      return {
        totalCount: 0,
        items: [],
        source: PersonFrom.LCV,
      };
    }

    const disableCallTCQG =
      this.configService.get<string>('DISABLE_CALL_TCQG', 'false') === 'true' &&
      this.req.headers?.['order-channel'] === OrderChannels.RSA_AFFILIATE.at(1);
    if (disableCallTCQG) {
      return {
        totalCount: 0,
        items: [],
        source: PersonFrom.LCV,
      };
    }
    // STEP 2: tìm nhà tcqg
    const dataSearchFromTCQG = await this._searchOnTCQG1(query, employeeCode);

    return {
      totalCount: dataSearchFromTCQG?.totalCount || 0,
      items: dataSearchFromTCQG?.personMapTcqg,
      source: PersonFrom.TCQG,
      payloadSearch: {
        payloadSearch: dataSearchFromTCQG?.payloadSearch,
        totalCount: dataSearchFromTCQG?.totalCount,
        caseSearch: dataSearchFromTCQG?.caseSearch,
      },
    };
  }

  async _searchOnTCQG1(query, employeeCode) {
    // Nếu không có trong family thì gọi lên tiêm chủng quốc gia
    // kiểm tra keyword nhập vào
    // nếu valid phone thì truyền vào phone
    // nếu invalid phone thì truyền vào mã tiêm chủng quốc gia
    // chưa cover search theo name ==> khi search name đẩy vào tiem chung quoc gia
    // check case: search basic

    // nguồn affiliate thì lấy trong env
    // mobile thì lấy trong config
    let shopCode = this.shopCode || this.configService.get<string>('SHOP_TCQG_CODE') || '';
    if (this.orderChannel === OrderChannels.RSA_AFFILIATE.at(1)) {
      shopCode = this.configService.get<string>('SHOP_TCQG_CODE', '58002');
    }

    let payloadSearch = {
      shopCode: shopCode,
      pageNumber: query.pageNumber || 1,
      pageSize: query.pageSize || 10,
      phone: query?.phoneNumber,
      fullName: query?.name,
      dateOfBirthFrom: query?.dateOfBirthFrom,
      dateOfBirthTo: query?.dateOfBirthTo,
      provinceCode: undefined,
      districtCode: undefined,
      wardCode: undefined,
      hamletCode: undefined,
      nationalVaccineCode: query?.nationalVaccineCode,
      gender: query?.gender,
      employeeCode,
      source: 1,
    } as any;

    let isPhone = false;
    let isNationalVaccineCode = false;
    let isFullname = false;
    if (query.keyword) {
      if (validateVietnamesePhoneNumber(query.keyword)) {
        isPhone = true;
      } else if (!isNaN(+query.keyword)) {
        isNationalVaccineCode = true;
      } else {
        isFullname = true;
      }
    }

    if (query?.provinceCode && query?.districtCode && query?.wardCode) {
      const { wards } = await this.dsmsService.getWard({ district: query?.districtCode });
      const { tcqgDistrict, tcqgProvince, tcqgWard } = wards.find(({ code }) => parseInt(code) === +query?.wardCode);
      payloadSearch.provinceCode = +tcqgProvince;
      payloadSearch.districtCode = +tcqgDistrict;
      payloadSearch.wardCode = +tcqgWard;
    } else if (query?.provinceCode && query?.districtCode) {
      const { districts } = await this.dsmsService.getDistrict({ province: query?.provinceCode });
      const { tcqgDistrict, tcqgProvince } = districts?.find(({ code }) => parseInt(code) === +query?.districtCode);
      payloadSearch.districtCode = +tcqgDistrict;
      payloadSearch.provinceCode = +tcqgProvince;
    } else if (query?.provinceCode) {
      const { provinces } = await this.dsmsService.getProvince();
      const { tcqgProvince } = provinces.find(({ code }) => parseInt(code) === +query?.provinceCode);
      payloadSearch.provinceCode = +tcqgProvince;
    }

    if (!_.isEmpty(query.keyword) && isPhone) {
      payloadSearch = {
        ..._.pick(payloadSearch, ['pageSize', 'pageNumber', 'shopCode', 'employeeCode', 'source']),
        phone: query.keyword,
      };
    }

    if (!_.isEmpty(query.keyword) && isNationalVaccineCode) {
      payloadSearch = {
        ..._.pick(payloadSearch, [
          'nationalVaccineCode',
          'pageSize',
          'pageNumber',
          'shopCode',
          'employeeCode',
          'source',
        ]),
        nationalVaccineCode: query.keyword,
      };
    }

    if (!_.isEmpty(query.keyword) && isFullname) {
      payloadSearch = {
        ..._.pick(payloadSearch, [
          'nationalVaccineCode',
          'pageSize',
          'pageNumber',
          'shopCode',
          'employeeCode',
          'source',
        ]),
        fullName: query.keyword,
      };
    }

    for (const prop in payloadSearch) {
      if (!payloadSearch[prop]) {
        delete payloadSearch[prop];
      }
    }

    // gọi api tiêm chủng quốc gia để lấy thông tin
    let data = {
      totalCount: 0,
      items: [],
    };
    try {
      data = await this.tcqgIntegrationService.searchCustomer(payloadSearch, {
        timeout: TIMEOUT_SEARCH_TCQG,
      });
    } catch (error) {}

    let caseSearch = '0';
    try {
      if (
        _.isEmpty(data.items) &&
        !_.isEmpty(payloadSearch?.dateOfBirthFrom) &&
        !_.isEmpty(payloadSearch?.dateOfBirthTo) &&
        !_.isEmpty(payloadSearch?.phone)
      ) {
        caseSearch = '1/ phone, dateOfBirthFrom, dateOfBirthTo';
        data = await this.tcqgIntegrationService.searchCustomer(
          _.pick(payloadSearch, [
            'pageNumber',
            'pageSize',
            'phone',
            'dateOfBirthFrom',
            'dateOfBirthTo',
            'shopCode',
            'employeeCode',
            'source',
          ]),
          {
            timeout: TIMEOUT_SEARCH_TCQG,
          },
        );
      }
    } catch (error) {}
    try {
      if (_.isEmpty(data.items) && !_.isEmpty(payloadSearch?.fullName) && !_.isEmpty(payloadSearch?.phone)) {
        caseSearch = '2/ phone, fullName';
        data = await this.tcqgIntegrationService.searchCustomer(
          _.pick(payloadSearch, ['pageNumber', 'pageSize', 'phone', 'fullName', 'shopCode', 'employeeCode', 'source']),
          {
            timeout: TIMEOUT_SEARCH_TCQG,
          },
        );
      }
    } catch (error) {}
    try {
      if (
        _.isEmpty(data.items) &&
        !_.isEmpty(payloadSearch?.dateOfBirthFrom) &&
        !_.isEmpty(payloadSearch?.dateOfBirthTo) &&
        !_.isEmpty(payloadSearch?.fullName)
      ) {
        caseSearch = '3/ fullName, dateOfBirthFrom, dateOfBirthTo';
        data = await this.tcqgIntegrationService.searchCustomer(
          _.pick(payloadSearch, [
            'pageNumber',
            'pageSize',
            'fullName',
            'dateOfBirthFrom',
            'dateOfBirthTo',
            'shopCode',
            'employeeCode',
            'source',
          ]),
          {
            timeout: TIMEOUT_SEARCH_TCQG,
          },
        );
      }
    } catch (error) {}

    const personMapTcqg = await this._convertData2(data?.items);

    return {
      totalCount: personMapTcqg?.length || 0,
      personMapTcqg: personMapTcqg || [],
      payloadSearch,
      caseSearch,
    };
  }

  async _convertData2(tcqgDataArray) {
    if (!tcqgDataArray || !tcqgDataArray.length) return [];
    return await this.convertVaccinationAttrToFamilyAttr2(tcqgDataArray);

    // const personMapTcqg = [];
    // for (let index = 0; index < tcqgDataArray?.length; index++) {
    //   const convert = await this.convertVaccinationAttrToFamilyAttr(tcqgDataArray[index]);
    //   personMapTcqg.push(convert);
    // }
    // return personMapTcqg;
  }

  async _searchOnTCQG2(query) {
    const payloadSearch = {
      shopCode: this.shopCode,
      pageNumber: query.pageNumber || 1,
      pageSize: query.pageSize || 10,
      phone: query?.phoneNumber,
      fullName: query?.name,
      dateOfBirthFrom: query?.dateOfBirthFrom,
      dateOfBirthTo: query?.dateOfBirthTo,
      provinceCode: undefined,
      districtCode: undefined,
      wardCode: undefined,
      gender: query?.gender,
      employeeCode: query?.employeeCode,
      source: 1,
    } as any;

    if (query?.provinceCode && query?.districtCode && query?.wardCode) {
      const { wards } = await this.dsmsService.getWard({ district: query?.districtCode });
      const { tcqgDistrict, tcqgProvince, tcqgWard } = wards.find(({ code }) => parseInt(code) === +query?.wardCode);
      payloadSearch.provinceCode = +tcqgProvince;
      payloadSearch.districtCode = +tcqgDistrict;
      payloadSearch.wardCode = +tcqgWard;
    } else if (query?.provinceCode && query?.districtCode) {
      const { districts } = await this.dsmsService.getDistrict({ province: query?.provinceCode });
      const { tcqgDistrict, tcqgProvince } = districts?.find(({ code }) => parseInt(code) === +query?.districtCode);
      payloadSearch.districtCode = +tcqgDistrict;
      payloadSearch.provinceCode = +tcqgProvince;
    } else if (query?.provinceCode) {
      const { provinces } = await this.dsmsService.getProvince();
      const { tcqgProvince } = provinces.find(({ code }) => parseInt(code) === +query?.provinceCode);
      payloadSearch.provinceCode = +tcqgProvince;
    }

    for (const prop in payloadSearch) {
      if (!payloadSearch[prop]) {
        delete payloadSearch[prop];
      }
    }
    // fix gender
    if ([0, 1, 2].includes(+query?.gender)) {
      payloadSearch.gender = query?.gender;
    } else {
      delete payloadSearch.gender;
    }

    // gọi api tiêm chủng quốc gia để lấy thông tin
    let data = {
      totalCount: 0,
      items: [],
    };

    try {
      data = await this.tcqgIntegrationService.searchCustomer(payloadSearch, { timeout: TIMEOUT_SEARCH_TCQG });
    } catch (error) {
      throw error;
    }

    // có kết quả thì trả về
    let personMapTcqg = [];
    if (data?.items?.length) {
      personMapTcqg = await this._convertData2(data?.items);
      return {
        totalCount: personMapTcqg?.length || 0,
        personMapTcqg: personMapTcqg || [],
        payloadSearch,
      };
    }

    if (query?.arrayCheck?.length === 2) {
      try {
        data = await this.tcqgIntegrationService.searchCustomer(
          _.pickBy(
            {
              pageNumber: 1,
              pageSize: 10,
              phone: payloadSearch?.phone,
              dateOfBirthFrom: payloadSearch?.dateOfBirthFrom,
              dateOfBirthTo: payloadSearch?.dateOfBirthTo,
              fullName: payloadSearch?.fullName,
              shopCode: payloadSearch?.shopCode,
            },
            _.identity,
          ),
          {
            timeout: TIMEOUT_SEARCH_TCQG,
          },
        );
      } catch (error) {
        throw error;
      }
      if (data?.items?.length) {
        personMapTcqg = await this._convertData2(data?.items);
        return {
          totalCount: personMapTcqg?.length || 0,
          personMapTcqg: personMapTcqg || [],
          payloadSearch,
        };
      } else {
        return {
          totalCount: 0,
          personMapTcqg: [],
          payloadSearch,
        };
      }
    }
    // không có kết quả thì tìm tổ hợp
    const jobs = [];
    try {
      if (
        _.isEmpty(data.items) &&
        !_.isEmpty(payloadSearch?.dateOfBirthFrom) &&
        !_.isEmpty(payloadSearch?.dateOfBirthTo) &&
        !_.isEmpty(payloadSearch?.phone)
      ) {
        jobs.push(
          this.tcqgIntegrationService.searchCustomer(
            _.pick(payloadSearch, [
              'pageNumber',
              'pageSize',
              'phone',
              'dateOfBirthFrom',
              'dateOfBirthTo',
              'shopCode',
              'employeeCode',
              'source',
            ]),
            {
              timeout: TIMEOUT_SEARCH_TCQG,
            },
          ),
        );
      }
    } catch (error) {}
    try {
      if (_.isEmpty(data.items) && !_.isEmpty(payloadSearch?.fullName) && !_.isEmpty(payloadSearch?.phone)) {
        jobs.push(
          this.tcqgIntegrationService.searchCustomer(
            _.pick(payloadSearch, [
              'pageNumber',
              'pageSize',
              'phone',
              'fullName',
              'shopCode',
              'employeeCode',
              'source',
            ]),
            {
              timeout: TIMEOUT_SEARCH_TCQG,
            },
          ),
        );
      }
    } catch (error) {}
    try {
      if (
        _.isEmpty(data.items) &&
        !_.isEmpty(payloadSearch?.dateOfBirthFrom) &&
        !_.isEmpty(payloadSearch?.dateOfBirthTo) &&
        !_.isEmpty(payloadSearch?.fullName)
      ) {
        jobs.push(
          this.tcqgIntegrationService.searchCustomer(
            _.pick(payloadSearch, [
              'pageNumber',
              'pageSize',
              'fullName',
              'dateOfBirthFrom',
              'dateOfBirthTo',
              'shopCode',
              'employeeCode',
              'source',
            ]),
            {
              timeout: TIMEOUT_SEARCH_TCQG,
            },
          ),
        );
      }
    } catch (error) {}

    let dataCombine;

    try {
      dataCombine = await Promise.allSettled(jobs);
    } catch (error) {
      throw error;
    }

    const errorFromTCQG = !dataCombine.find((entry) => entry.status === 'fulfilled');

    const dataSearchRs = dataCombine.map((entry) => {
      if (entry.status === 'fulfilled') {
        return entry?.value?.items?.length || 0;
      }
    });

    dataCombine = dataCombine.map((entry) => {
      if (entry.status === 'fulfilled') {
        return entry?.value?.items;
      }
      return [];
    });

    dataCombine = _.flatten(dataCombine);

    const dataCombineUniq = _.uniqBy(dataCombine, 'maDoiTuong');

    personMapTcqg = await this._convertData2(dataCombineUniq);

    return {
      totalCount: personMapTcqg?.length || 0,
      personMapTcqg: personMapTcqg || [],
      errorFromTCQG,
      payloadSearch: {
        ...payloadSearch,
        combine: {
          'phone+dob': dataSearchRs?.[0],
          'fullName+phone': dataSearchRs?.[1],
          'fullName+bod': dataSearchRs?.[2],
        },
      },
    };
  }

  async mapTcqgToPerson(mapTcqgToPersonDto: MapTcqgToPersonBody) {
    // check mã tcqg đã có nhà mình chưa
    const rsPerson = await this.familyCoreService.getByNationalCode(mapTcqgToPersonDto?.nationalVaccineCode);

    if (rsPerson) {
      const exception: IError = {
        code: ErrorCode.RSA_SYNC_TCQG_EXIST,
        message: ErrorCode.getError(ErrorCode.RSA_SYNC_TCQG_EXIST),
        details: ErrorCode.getError(ErrorCode.RSA_SYNC_TCQG_EXIST),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    const rsPerson2 = await this.familyCoreService.getPersonByLcvId(mapTcqgToPersonDto?.lcvId);
    // check person đã map mã tcqg rồi hay chưa
    if (!rsPerson2?.dateOfBirth) {
      const exception: IError = {
        code: ErrorCode.RSA_SYNC_PERSON_NOT_HAVE_DOB,
        message: ErrorCode.getError(ErrorCode.RSA_SYNC_PERSON_NOT_HAVE_DOB),
        details: ErrorCode.getError(ErrorCode.RSA_SYNC_PERSON_NOT_HAVE_DOB),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    if (rsPerson2 && rsPerson2?.nationalVaccineCode && rsPerson2?.nationalVaccineId) {
      const exception: IError = {
        code: ErrorCode.RSA_SYNC_PERSON_EXIST,
        message: ErrorCode.getError(ErrorCode.RSA_SYNC_PERSON_EXIST),
        details: ErrorCode.getError(ErrorCode.RSA_SYNC_PERSON_EXIST),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    if (
      !(await this.redis.set(
        `check_double_history:${mapTcqgToPersonDto?.nationalVaccineCode}:${mapTcqgToPersonDto?.lcvId}`,
        Date.now(),
        'EX',
        10,
        'NX',
      ))
    ) {
      const exception: IError = {
        code: ErrorCode.RSA_CHECK_DOUBLE,
        message: ErrorCode.getError(ErrorCode.RSA_CHECK_DOUBLE).replace('{second}', '10'),
        details: ErrorCode.getError(ErrorCode.RSA_CHECK_DOUBLE).replace('{second}', '10'),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    const person = await this.familyCoreService.updateNationalVaccineCode({
      lcvId: mapTcqgToPersonDto?.lcvId,
      nationalVaccineCode: mapTcqgToPersonDto?.nationalVaccineCode,
      nationalVaccineId: mapTcqgToPersonDto?.nationalVaccineId,
      modifiedBy: mapTcqgToPersonDto?.modifiedBy,
    });

    const personWithNewAddress = await this._shouldUpdateAddress({ person, body: mapTcqgToPersonDto });

    const history = await this.historyCoreService.syncHistoryTCQG({
      nationalVaccineId: mapTcqgToPersonDto?.nationalVaccineId,
      shopCode: this.shopCode,
      createdBy: mapTcqgToPersonDto?.modifiedBy,
      lcvId: mapTcqgToPersonDto?.lcvId,
    });

    if (personWithNewAddress || person) {
      const ageRanges = await this.regimenCoreService.getAgeRanges();
      if (personWithNewAddress && personWithNewAddress.familyProfileDetails?.length > 0) {
        personWithNewAddress.familyProfileDetails = this.handleFamilyProfileDetailsWithAge(
          personWithNewAddress?.familyProfileDetails,
          ageRanges,
        );
      } else if (person && person?.familyProfileDetails?.length > 0) {
        person.familyProfileDetails = this.handleFamilyProfileDetailsWithAge(person?.familyProfileDetails, ageRanges);
      }
    }

    return {
      person: personWithNewAddress || person,
      history,
      isUpdatedAddress: personWithNewAddress ? true : false,
    };
  }

  async _shouldUpdateAddress({ person, body }: { person: Person; body: MapTcqgToPersonBody }) {
    // STEP 1: check các person có các field nào cần update không?
    const personPick = _.pickBy(
      // chỉ lấy các trường có value
      {
        ..._.pick(person, [
          'frequentlyProvinceCode',
          // 'frequentlyProvinceName',
          'frequentlyDistrictCode',
          // 'frequentlyDistrictName',
          'frequentlyWardCode',
          // 'frequentlyWardName',
          'frequentlyAddress',
          'temporaryProvinceCode',
          // 'temporaryProvinceName',
          'temporaryDistrictCode',
          // 'temporaryDistrictName',
          'temporaryWardCode',
          // 'temporaryWardName',
          'temporaryAddress',
        ]),
      },
      (x) => !x,
    );
    const fieldsPersonPick = Object.keys(personPick) || [];
    if (!fieldsPersonPick?.length) {
      return;
    }
    // STEP 2: check các body có data cho các field cần update không?
    const bodyPick = _.pickBy(
      //  chỉ lấy các trường có data từ person
      {
        ..._.pick(body, [...fieldsPersonPick]),
      },
      (x) => x,
    );

    const fieldsBodyPick = Object.keys(bodyPick) || [];
    if (!fieldsBodyPick?.length) {
      return;
    }

    // let temporary;
    // let frequently;
    // if (body?.temporaryWardCode) {
    //   temporary = await this.familyUtilsService.getTemporaryAddressV2({
    //     tcQG_Ward: body?.temporaryWardCode,
    //   });
    // }

    // if (body?.frequentlyWardCode) {
    //   frequently = await this.familyUtilsService.getFrequentlyAddressV2({
    //     tcQG_Ward: body?.frequentlyWardCode,
    //   });
    // }

    if (body?.temporaryWardCode || body?.frequentlyWardCode) {
      return await this.familyCoreService.updateAddress({
        lcvId: body?.lcvId,
        modifiedBy: body?.modifiedBy,
        frequentlyAddress: body?.frequentlyAddress || '',
        temporaryAddress: body?.temporaryAddress || '',
        // ...(temporary || {}),
        // ...(frequently || {}),
        ..._.pick(body, [
          'frequentlyProvinceCode',
          'frequentlyProvinceName',
          'frequentlyDistrictCode',
          'frequentlyDistrictName',
          'frequentlyWardCode',
          'frequentlyWardName',
          'frequentlyAddress',
          'temporaryProvinceCode',
          'temporaryProvinceName',
          'temporaryDistrictCode',
          'temporaryDistrictName',
          'temporaryWardCode',
          'temporaryWardName',
          'temporaryAddress',
        ]),
        isSameAddress: body?.temporaryWardCode === body?.frequentlyWardCode,
      });
    }
  }

  async createTcqg(payload: CreateCustomerTcqgBody, employeeCode: string): Promise<CreateCustomerTcqgResponse> {
    const rs = {
      isSuccess: false,
      message: '',
      createdCustomer: {
        nationalVaccineCode: '',
        nationalVaccineId: '',
      },
    };
    const personPayload = payload?.createCustomerPayload || {};
    const customerTcqgRs = await this._createTcqg({
      createCustomerPayload: {
        ...payload.createCustomerPayload,
        frequentlyTCQGProvinceCode: `${personPayload?.frequentlyTCQGProvinceCode}`,
        frequentlyTCQGDistrictCode: `${personPayload?.frequentlyTCQGDistrictCode}`,
        frequentlyTCQGWardCode: `${personPayload?.frequentlyTCQGWardCode}`,
        temporaryTCQGProvinceCode: `${personPayload?.temporaryTCQGProvinceCode}`,
        temporaryTCQGDistrictCode: `${personPayload?.temporaryTCQGDistrictCode}`,
        temporaryTCQGWardCode: `${personPayload?.temporaryTCQGWardCode}`,
      },
      lcvId: payload?.lcvId,
      employeeCode,
    });

    if (!customerTcqgRs) {
      return rs;
    }

    const formattedData = await this.convertVaccinationAttrToFamilyAttr(customerTcqgRs);

    const personExist = await this.familyCoreService.getByNationalCode(formattedData?.nationalVaccineCode);
    // remove current user
    const isExist = personExist && personExist?.lcvId !== payload?.lcvId ? true : false;

    if (!formattedData?.nationalVaccineCode || formattedData?.nationalVaccineId) {
      return {
        isSuccess: true,
        message: 'Tạo thành công mã TCQG: ' + _.get(formattedData, 'nationalVaccineCode', ''),
        createdCustomer: {
          nationalVaccineCode: !isExist ? formattedData?.nationalVaccineCode : '',
          nationalVaccineId: !isExist ? formattedData?.nationalVaccineId : '',
          warningMessage: !isExist
            ? null
            : {
                message: `Khách hàng này đã có hồ sơ tương tự ở hệ thống với mã ${formattedData?.lcvId}, ${formattedData?.nationalVaccineCode}, không thể tạo mã TCQG mới. Vui lòng kiểm tra lại.`,
                lcvId: formattedData?.lcvId,
                nationalVaccineCode: formattedData?.nationalVaccineCode,
                name: formattedData?.name,
              },
        },
      };
    }

    return {
      isSuccess: true,
      message: 'Tạo thành công mã TCQG: ' + _.get(formattedData, 'nationalVaccineCode', ''),
      createdCustomer: {
        nationalVaccineCode: !isExist ? formattedData?.nationalVaccineCode : '',
        nationalVaccineId: !isExist ? formattedData?.nationalVaccineId : '',
        warningMessage: !isExist
          ? null
          : {
              message: `Khách hàng này đã có hồ sơ tương tự ở hệ thống với mã ${formattedData?.lcvId}, ${formattedData?.nationalVaccineCode}, không thể tạo mã TCQG mới. Vui lòng kiểm tra lại.`,
              lcvId: formattedData?.lcvId,
              nationalVaccineCode: formattedData?.nationalVaccineCode,
              name: formattedData?.name,
            },
      },
    };
  }

  async _createTcqg({
    createCustomerPayload,
    lcvId,
    employeeCode,
  }: {
    createCustomerPayload: CreatePersonDto | any;
    lcvId: string;
    employeeCode: string;
  }): Promise<GetCustomerDetailRes | null> {
    try {
      const customerTcqgPayload: UserProfileDto = {
        shopCode: this.shopCode,
        TenDoiTuong: createCustomerPayload?.name || '',
        GioiTinh: createCustomerPayload?.gender || Gender.Male,
        NgaySinh: createCustomerPayload?.dateOfBirth,
        DanTocId: +createCustomerPayload.ethnicCode,
        DienThoai: createCustomerPayload?.phoneNumber,
        // DienThoaiNguoiGiamHo: createCustomerPayload?.guardianPhone,
        LoaiDoiTuong: PersonType.AdultMale,
        // NguoiChamSoc: ChildCarer.Guardian,
        TinhId: +createCustomerPayload?.frequentlyTCQGProvinceCode || 0,
        HuyenId: +createCustomerPayload?.frequentlyTCQGDistrictCode || 0,
        XaId: +createCustomerPayload?.frequentlyTCQGWardCode || 0,
        TinhDangKyId: +createCustomerPayload?.temporaryTCQGProvinceCode || 0,
        HuyenDangKyId: +createCustomerPayload?.temporaryTCQGDistrictCode || 0,
        XaDangKyId: +createCustomerPayload?.temporaryTCQGWardCode || 0,
        DiaChiChiTiet: `${
          createCustomerPayload?.frequentlyAddress === 'Không xác định'
            ? ''
            : createCustomerPayload?.frequentlyAddress || ''
        }`,
        DiaChiDangKyChiTiet: `${
          createCustomerPayload?.temporaryAddress === 'Không xác định'
            ? ''
            : createCustomerPayload?.temporaryAddress || ''
        }`,
        GhiChu: createCustomerPayload?.note || '',
        ...(await this._getGuardianTcqgInfo({ familyProfileDetails: createCustomerPayload?.familyProfileDetails })),
        lcvId,
        employeeCode,
      };

      if (
        !customerTcqgPayload.TinhId ||
        !customerTcqgPayload.HuyenId ||
        !customerTcqgPayload.XaId ||
        !customerTcqgPayload.TinhDangKyId ||
        !customerTcqgPayload.HuyenDangKyId ||
        !customerTcqgPayload.XaDangKyId
      ) {
        return;
      }
      return await this.tcqgIntegrationService.createCustomerProfile(customerTcqgPayload, {
        timeout: +process.env.TIMEOUT_CREATE_TCQG || 10000,
      });
    } catch (error) {
      return;
    }
  }

  /*
  Trả ra thông tin người giám hộ từ cây familyProfile hoặc từ hotPerson
  */
  async _getGuardianTcqgInfo({
    familyProfileDetails,
    hostPersonData,
  }: {
    familyProfileDetails: FamilyProfileDetails[];
    hostPersonData?: GetPersonByIdRes | any;
  }) {
    if (!familyProfileDetails?.length) {
      return {};
    }
    const hostPerson = familyProfileDetails?.find((p) => p?.isHost);
    if (!hostPerson) {
      return {};
    }

    let personData = hostPersonData;
    if (!personData) {
      // lấy thêm thông tin vì trong familyProfileDetails không đủ cmt, năm sinh
      personData = await this.familyCoreService.getPersonByLcvId(hostPerson?.lcvId);
    }

    Logger.log({
      message: `_getGuardianTcqgInfo ${personData.lcvId}`,
      fields: {
        info: JSON.stringify(personData),
      },
    });

    switch (hostPerson?.titleId) {
      case '4a9022bc-edab-4eee-8131-998159946bee': // Mẹ
        return {
          NguoiChamSoc: ChildCarer.Mother,
          DienThoaiMe: personData.phoneNumber || '',
          TenMe: personData.name || '',
          CmtMe: personData.identityCard || '',
          NamSinhMe: getYear(personData?.dateOfBirth),
        };

      case 'dfef731e-0c80-4f20-bd84-b962ee75f424': // Bố
        return {
          NguoiChamSoc: ChildCarer.Father,
          DienThoaiBo: personData.phoneNumber || '',
          TenBo: personData.name || '',
          CmtBo: personData.identityCard || '',
          NamSinhBo: getYear(personData?.dateOfBirth),
        };

      default:
        return {
          NguoiChamSoc: ChildCarer.Guardian,
          DienThoaiNguoiGiamHo: personData.phoneNumber || '',
          TenNguoiGiamHo: personData.name || '',
          NamSinhNguoiGiamHo: getYear(personData?.dateOfBirth),
          CmtNguoiGiamHo: personData.identityCard || '',
        };
    }
  }

  async createCustomerAddressLCV({
    createCustomerDto,
    person,
  }: {
    createCustomerDto: CreatePersonDto;
    person: CreatePersonRes;
  }): Promise<any> {
    try {
      const createCustomersAddressPayload: CreateCustomerAddressDto[] = [];
      createCustomersAddressPayload.push({
        mobilePhone: person?.phoneNumber || '',
        name: createCustomerDto?.name || '',
        address: createCustomerDto.temporaryAddress,
        districtCode: createCustomerDto.temporaryDistrictCode,
        provinceCode: createCustomerDto.temporaryProvinceCode,
        wardCode: createCustomerDto.temporaryWardCode,
        isValid: true,
        isPrimary: true,
      });
      if (!createCustomerDto.isSameAddress) {
        createCustomersAddressPayload.push({
          mobilePhone: person?.phoneNumber || '',
          name: createCustomerDto?.name || '',
          address: createCustomerDto.frequentlyAddress,
          districtCode: createCustomerDto.frequentlyDistrictCode,
          provinceCode: createCustomerDto.frequentlyProvinceCode,
          wardCode: createCustomerDto.frequentlyWardCode,
          isValid: true,
          isPrimary: false,
        });
      }
      await this.createCustomerAddress({ customerId: person.customerId }, createCustomersAddressPayload);
    } catch (error) {
      return;
    }
  }

  async createCustomer(createCustomerDto: CreatePersonDto, employeeCode: string) {
    // Format tên tiếng việt bỏ dấu, khoảng trắng
    const personName = createCustomerDto?.name
      ?.trim()
      ?.normalize('NFD')
      ?.replace(/[\u0300-\u036f\s]+/g, '');

    // chặn tạo KH với sdt shop
    if (
      !_.isEmpty(createCustomerDto?.phoneNumber) &&
      !PHONE_NUMBER_BUT_NOT_SHOP_PHONE.test(createCustomerDto?.phoneNumber)
    ) {
      const exception: IError = {
        code: ErrorCode.RSA_CHECK_SHOP_PHONE,
        message: ErrorCode.getError(ErrorCode.RSA_CHECK_SHOP_PHONE),
        details: ErrorCode.getError(ErrorCode.RSA_CHECK_SHOP_PHONE),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    await this.handleToAvoidDouble(`${employeeCode}_${personName}`, 'create_person');

    // ***********
    // @TODO: magic ở đây, kiểm tra familyProfileDetails
    // 1. nếu có 1 familyProfileDetails và có lcvId thì đi luồng mới
    // 2. còn lại đi như cũ
    // **********
    if (createCustomerDto?.familyProfileDetails?.length === 1 && createCustomerDto?.familyProfileDetails[0]?.lcvId) {
      try {
        const lcvIdInFamilyProfileDetail = createCustomerDto?.familyProfileDetails[0]?.lcvId;
        // STEP 2
        const personDetail = await this.familyCoreService.getPersonByLcvIdV2(lcvIdInFamilyProfileDetail);
        // STEP 3
        const hostPerson = personDetail?.familyProfileDetails?.find((item) => item?.isHost);

        if (!hostPerson) {
          return await this.createPerson(createCustomerDto, employeeCode);
        }

        return await this.createPerson(
          {
            ...createCustomerDto,
            familyProfileDetails: [
              {
                name: hostPerson?.name,
                phoneNumber: hostPerson?.phoneNumber,
                gender: hostPerson?.gender,
                titleId: '8d54cabe-f239-4c9a-b5de-4d9f8727ce7d',
                titleName: 'Khác',
                lcvId: hostPerson?.lcvId,
                dateOfBirth: hostPerson?.dateOfBirth,
              },
            ],
          },
          employeeCode,
        );
      } catch (error) {
        Logger.log(`CREATE CUSTOMER ERROR : ${error}`);
        return await this.createPerson(createCustomerDto, employeeCode);
      }
    }
    return await this.createPerson(createCustomerDto, employeeCode);
  }

  /**
   * @TODO Tạo khách hàng và Tạo Tiềm chủng quốc gia
   *  - createPerson in family
   *  - createCTQG -> mapping vòa lại person
   *  - update address
   */
  async createPerson(createCustomerDto: CreatePersonDto, employeeCode: string) {
    const orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    try {
      // fix ngày 05/10/2024 12:08
      const { employee_code } = this?.token
        ? jwtDecode<IAuthUser>(this.token) || { employee_code: null }
        : { employee_code: null };

      //check for pass source RSA/RSA_ECOM
      let source = SOURCE.RSA; // source = 0
      if (
        OrderChannels.RSA_ECOM.includes(orderChannel) ||
        OrderChannels.RSA_AFFILIATE.includes(orderChannel) ||
        OrderChannels.RSA.includes(orderChannel)
      ) {
        if (!_.isEmpty(createCustomerDto?.source)) {
          // nếu có source sẵn từ payload khi gọi API qua RSA-ECOM thì lấy (có thể được gọi từ STC source = 1)
          source = createCustomerDto?.source;
        } else {
          if (OrderChannels.RSA_ECOM.includes(orderChannel)) {
            source = SOURCE.RSA_ECOM; // source = 2
          } else if (OrderChannels.RSA_AFFILIATE.includes(orderChannel)) {
            const sourceMap = {
              [OrderChannels.RSA_AFFILIATE[0]]: SOURCE.RSA_AFFILIATE, // 15 -> 3
              [OrderChannels.RSA_AFFILIATE[1]]: SOURCE.RSA_AFFILIATE_MOBILE, // 16 -> 4
            };
            source = sourceMap[orderChannel];
          } else if (OrderChannels.RSA.includes(orderChannel)) {
            const sourceMap = {
              [OrderChannels.RSA[0]]: SOURCE.RSA, // 14 -> 0
              [OrderChannels.RSA[1]]: SOURCE.M_RSA_OFFLINE, // 2 -> 6
            };
            source = sourceMap[orderChannel];
          }
        }
      }

      createCustomerDto.source = source;
      if (createCustomerDto?.familyProfileDetails?.length) {
        createCustomerDto.familyProfileDetails = createCustomerDto?.familyProfileDetails?.map((x) => ({
          ...x,
          source,
        }));
      }
      //end check
      createCustomerDto.shopCode = this.shopCode;
      createCustomerDto.createdBy = employee_code || createCustomerDto?.createdBy;
      // tạo person

      let isRangeAge = false;
      if (createCustomerDto?.dateOfBirth) {
        const agePerson = moment().diff(createCustomerDto?.dateOfBirth, 'years');
        isRangeAge = agePerson < 12;
      } else {
        isRangeAge = createCustomerDto?.from < 12;
      }

      // mong muốn là nếu tạo person mà có mã tcqg thì không cần check rule 12 tuổi
      const familyPersonHavePhone = createCustomerDto?.familyProfileDetails?.filter((profile) => profile?.phoneNumber);
      if (!createCustomerDto?.nationalVaccineCode && isRangeAge && familyPersonHavePhone?.length === 0) {
        const exception: IError = {
          code: ErrorCode.RSA_CREATE_CUSTOMER_RULE,
          message: ErrorCode.getError(ErrorCode.RSA_CREATE_CUSTOMER_RULE),
          details: ErrorCode.getError(ErrorCode.RSA_CREATE_CUSTOMER_RULE),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }

      let createPerson = await this.familyCoreService.createPerson(createCustomerDto);

      // tạo phụ nữ mang thai
      if (createPerson && createCustomerDto?.pregnancy?.length > 0) {
        try {
          createPerson = await this.familyCoreService.createPregnancy({
            personId: createPerson?.personId,
            createdBy: employee_code,
            details: createCustomerDto?.pregnancy,
          });
        } catch (error) {
          Logger.log(
            `PAYLOAD CREATE PREGNANCY : ${JSON.stringify({
              personId: createPerson?.personId,
              createdBy: employee_code,
              details: createCustomerDto?.pregnancy,
            })}`,
          );
          Logger.log(`CREATE PREGNANCY ERROR : ${error}`);
        }
      }

      // dánh tags khách hàng mới
      if (createPerson && createPerson?.familyProfileDetails?.length) {
        const lcvIds = createPerson?.familyProfileDetails?.map((i) => i?.lcvId) || [];
        if (lcvIds?.length) {
          lcvIds?.forEach(async (lcvId) => {
            await this.customerRuleService.createTagForCustomer(lcvId);
          });
        }
      } else if (createPerson && createPerson?.familyProfileDetails?.length === 0 && createPerson?.lcvId) {
        await this.customerRuleService.createTagForCustomer(createPerson?.lcvId);
      }

      // ecom thì không tạo mã tcqg
      if (this.isChannelValidTCQG() && (!createPerson?.nationalVaccineId || !createPerson?.nationalVaccineCode)) {
        const tcqg: GetCustomerDetailRes = await this._createTcqg({
          createCustomerPayload: { ...createCustomerDto, ...createPerson },
          lcvId: createPerson?.lcvId,
          employeeCode,
        });
        if (tcqg?.maDoiTuong && tcqg?.doiTuong2Id) {
          // mapping mã tcqg vào person
          // check exist
          const personExist = await this.familyCoreService.getByNationalCode(tcqg?.maDoiTuong);
          // remove current user
          const isExist = personExist && personExist?.lcvId !== createPerson?.lcvId ? true : false;
          // Không tồn tại nhà mình thì update thông tin. Tồn tại thì k update
          if (!isExist) {
            await this.familyCoreService.updateNationalVaccineCode({
              lcvId: createPerson?.lcvId,
              nationalVaccineCode: tcqg?.maDoiTuong,
              nationalVaccineId: `${tcqg?.doiTuong2Id || ''}`,
              modifiedBy: createCustomerDto?.createdBy,
            });
            createPerson.nationalVaccineCode = tcqg?.maDoiTuong;
            createPerson.nationalVaccineId = `${tcqg?.doiTuong2Id || ''}`;
          } else {
            createPerson['warningMessage'] = {
              message: `Khách hàng này đã có hồ sơ tương tự ở hệ thống với mã ${createPerson?.lcvId}, ${tcqg?.maDoiTuong}, không thể tạo mã TCQG mới. Vui lòng kiểm tra lại.`,
              lcvId: createPerson?.lcvId,
              nationalVaccineCode: tcqg?.maDoiTuong,
              name: createPerson?.name,
            };
          }
        }
      }
      // update address
      if (createPerson.customerId) {
        await this.createCustomerAddressLCV({
          createCustomerDto,
          person: createPerson,
        });
      }

      const ageRanges = await this.regimenCoreService.getAgeRanges();

      createPerson.customerAge = calculateTimeDifference(
        createPerson?.dateOfBirth,
        createPerson?.from,
        createPerson?.to,
        ageRanges,
        createPerson?.ageUnitCode,
      );

      if (createPerson?.familyProfileDetails?.length > 0) {
        createPerson.familyProfileDetails = this.handleFamilyProfileDetailsWithAge(
          createPerson.familyProfileDetails,
          ageRanges,
        );
      }

      return createPerson;
    } catch (error) {
      throw error;
    }
  }

  async createManyCustomer(createManyCustomerDto: CreateManyPersonDto, employeeCode: string) {
    try {
      await this.handleToAvoidDouble(employeeCode, 'create_many_customer');

      // check rule
      const resultRuleCustomer = await this.customerRuleService._checkRuleCreateManyPerson(createManyCustomerDto);
      // const personsPayload = createManyCustomerDto?.persons;
      const rs = [];
      for (let index = 0; index < resultRuleCustomer?.length; index++) {
        const personPayload = resultRuleCustomer[index];
        if (personPayload?.familyProfileDetails?.length > 0) {
          personPayload.familyProfileDetails = this._mapTitleIdByTypeFromTcqg(personPayload?.familyProfileDetails);
        }
        rs.push(await this.createPerson(personPayload, employeeCode));
      }
      return {
        persons: rs,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateCustomer(updateCustomerDto: UpdatePersonDto, employeeCode: string) {
    try {
      const payloadTCQG: UserProfileDto = {
        shopCode: this.shopCode,
        TenDoiTuong: updateCustomerDto?.name || '',
        GioiTinh: updateCustomerDto?.gender || Gender.Male,
        NgaySinh: updateCustomerDto?.dateOfBirth,
        DanTocId: +updateCustomerDto.ethnicCode,
        CMT: updateCustomerDto?.identityCard,
        DienThoai: updateCustomerDto?.phoneNumber,
        // LoaiDoiTuong: PersonType.AdultMale,
        // NguoiChamSoc: ChildCarer.Guardian,
        TinhId: +updateCustomerDto.frequentlyTCQGProvinceCode || 0,
        HuyenId: +updateCustomerDto.frequentlyTCQGDistrictCode || 0,
        XaId: +updateCustomerDto.frequentlyTCQGWardCode || 0,
        //------
        TinhDangKyId: +updateCustomerDto.temporaryTCQGProvinceCode || 0,
        HuyenDangKyId: +updateCustomerDto.temporaryTCQGDistrictCode || 0,
        XaDangKyId: +updateCustomerDto.temporaryTCQGWardCode || 0,
        DiaChiChiTiet: `${
          updateCustomerDto?.frequentlyAddress === 'Không xác định' ? '' : updateCustomerDto?.frequentlyAddress || ''
        }`,
        DiaChiDangKyChiTiet: updateCustomerDto?.temporaryAddress || '',
        // TenNguoiGiamHo: updateCustomerDto?.guardianName || '',
        // DienThoaiNguoiGiamHo: updateCustomerDto?.guardianPhone,
        lcvId: updateCustomerDto.lcvId,
        GhiChu: updateCustomerDto?.note,
        employeeCode,
      };
      if (!updateCustomerDto?.isShouldNotCreateTcqg && !updateCustomerDto.nationalVaccineCode) {
        let tcqg: GetCustomerDetailRes = null;
        if (
          this.isChannelValidTCQG() &&
          payloadTCQG.TinhId &&
          payloadTCQG.HuyenId &&
          payloadTCQG.XaId &&
          payloadTCQG.TinhDangKyId &&
          payloadTCQG.HuyenDangKyId &&
          payloadTCQG.XaDangKyId
        ) {
          try {
            const createTCQGPayload = {
              ...payloadTCQG,
              ...(await this._getGuardianTcqgInfo({ familyProfileDetails: updateCustomerDto?.familyProfileDetails })),
            } as any;
            tcqg = await this.tcqgIntegrationService.createCustomerProfile(createTCQGPayload, {
              timeout: +process.env.TIMEOUT_CREATE_TCQG || 10000,
            });
            if (updateCustomerDto?.isHost) {
              const person = await this.familyCoreService.getPersonByLcvId(updateCustomerDto?.lcvId);
              this.updateTcqgForFamilyMember(person, updateCustomerDto, employeeCode);
            }
          } catch (error) {
            tcqg = null;
          }
        }

        // ưu tiên lấy từ FE truyền lên
        if (!updateCustomerDto?.nationalVaccineId) {
          updateCustomerDto.nationalVaccineId = String(tcqg?.doiTuong2Id || '');
        }

        if (!updateCustomerDto?.nationalVaccineCode) {
          updateCustomerDto.nationalVaccineCode = tcqg?.maDoiTuong || '';
        }
      } else if (
        updateCustomerDto?.nationalVaccineCode &&
        !_.isEmpty(updateCustomerDto?.nationalVaccineCode) &&
        this.isChannelValidTCQG()
      ) {
        try {
          this.checkAndUpdateTcqg(payloadTCQG, updateCustomerDto, employeeCode);
        } catch (error) {
          Logger.log(`Update TCQG Person Profile - ${error.message}`);
        }
      }

      // fix ngày 05/10/2024 12:08
      const { employee_code } = jwtDecode<IAuthUser>(this.token) || { employee_code: null };
      updateCustomerDto.shopCode = this.shopCode;
      updateCustomerDto.modifiedBy = employee_code || updateCustomerDto.modifiedBy;

      delete updateCustomerDto?.isShouldNotCreateTcqg;

      const familyPersonHavePhone = updateCustomerDto?.familyProfileDetails?.filter(
        (familyProfile) => familyProfile?.lcvId !== updateCustomerDto?.lcvId && familyProfile?.phoneNumber,
      );

      let isRangeAge = false;
      if (updateCustomerDto?.dateOfBirth) {
        const agePerson = moment().diff(updateCustomerDto?.dateOfBirth, 'years');
        isRangeAge = agePerson < 12;
      } else {
        isRangeAge = updateCustomerDto?.from < 12;
      }

      if (isRangeAge && familyPersonHavePhone?.length === 0) {
        const exception: IError = {
          code: ErrorCode.RSA_CREATE_CUSTOMER_RULE,
          message: ErrorCode.getError(ErrorCode.RSA_CREATE_CUSTOMER_RULE),
          details: ErrorCode.getError(ErrorCode.RSA_CREATE_CUSTOMER_RULE),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }

      /**
       * @link https://reqs.fptshop.com.vn/browse/FV-12256
       * call family group để lấy thông tin owner
       * call family để lấy thông tin số điện thoại
       */
      const resFamilyByLcvId = await this.familyPackageService.getGroupFamilyPackage({
        lcvIds: [updateCustomerDto?.lcvId],
      });
      // await this.customerRuleService.checkOwnerFamilyPackage({
      //   person: updateCustomerDto,
      //   resFamily: resFamilyByLcvId?.at(0),
      //   isUpdate: true,
      // });

      await this.handleToAvoidDouble(updateCustomerDto?.lcvId, 'update_customer');

      const updatePerson = await this.familyCoreService.updatePerson(updateCustomerDto);

      const ageRanges = await this.regimenCoreService.getAgeRanges();

      updatePerson.quotas = [];
      updatePerson.customerAge = calculateTimeDifference(
        updatePerson?.dateOfBirth,
        updatePerson?.from,
        updatePerson?.to,
        ageRanges,
        updatePerson?.ageUnitCode,
      );
      // check show logo fpt
      const { isFpter, customerTags } = await this.customerRuleService._verifyCustomerTag({
        email: updateCustomerDto?.email,
        phoneNumber: updateCustomerDto?.phoneNumber,
        includeExtra: true,
        vaccinationCode: updateCustomerDto?.lcvId,
      });

      // check người fpt và get remain nếu là người fpt
      if (updateCustomerDto?.email) {
        // const isFpter = await this.customerRuleService._verifyEmailUpdatePerson(updateCustomerDto?.email);
        if (isFpter) {
          try {
            const { quotas } = await this.quotasService.getRemainingQuotaForPerson({ email: updateCustomerDto?.email });
            updatePerson.quotas = quotas;
          } catch (error) {}
        }
      }

      updatePerson.isFpter = isFpter;
      updatePerson['customerTags'] = customerTags;
      if (this.isChannelValidTCQG() && updateCustomerDto?.familyProfileDetails?.some((x) => x?.isAdding)) {
        this.syncFamilyProfileDetailsToTcqg(updatePerson?.familyProfileDetails, employeeCode);
      }

      if (updatePerson?.familyProfileDetails?.length > 0) {
        updatePerson.familyProfileDetails = this.handleFamilyProfileDetailsWithAge(
          updatePerson?.familyProfileDetails,
          ageRanges,
        );
      }

      if (updatePerson && updatePerson?.pregnancy?.length) {
        updatePerson.pregnancy = this._checkRulePregnancy(updatePerson?.pregnancy);
      }

      return updatePerson;
    } catch (error) {
      throw error;
    }
  }

  async handleToAvoidDouble(lcvId: string, method: string, seconds = 3) {
    if (!(await this.redis.set(`check_double_${method}:${lcvId}`, Date.now(), 'EX', seconds, 'NX'))) {
      const exception: IError = {
        code: ErrorCode.RSA_CHECK_DOUBLE,
        message: ErrorCode.getError(ErrorCode.RSA_CHECK_DOUBLE).replace('{second}', seconds?.toString()),
        details: ErrorCode.getError(ErrorCode.RSA_CHECK_DOUBLE).replace('{second}', seconds?.toString()),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
  }

  handleFamilyProfileDetailsWithAge(familyProfileDetails: FamilyProfileDetails[], ageRanges: GetAgeRangeResponseDto[]) {
    const familyProfileDetailsWithAge = familyProfileDetails?.map((member: any) => {
      return this.handleDisableDeleteButtonWithAge(member, ageRanges);
    });

    return familyProfileDetailsWithAge;
  }

  handleDisableDeleteButtonWithAge(member, ageRanges: GetAgeRangeResponseDto[]) {
    const customerAge = calculateTimeDifference(
      member?.dateOfBirth,
      member?.from,
      member?.to,
      ageRanges,
      member?.ageUnitCode,
    );
    member.customerAge = customerAge;
    if (member?.isHost) {
      member.buttonHanlder = {
        delete: { isDisable: true, textDisplay: 'Không được xóa khách hàng đang là người giám hộ' },
        transfer: { isDisable: true, textDisplay: 'Không được chuyển khách hàng đang là người giám hộ' },
      };
    }
    if (customerAge?.textDisplay === 'Chưa có ngày sinh') {
      if (!member?.phoneNumber || !PHONE_NUMBER_BUT_NOT_SHOP_PHONE.test(member?.phoneNumber)) {
        member.buttonHanlder = {
          delete: {
            isDisable: true,
            textDisplay:
              'Không được xoá khách hàng không có SĐT ra khỏi người giám hộ.\nNếu muốn xoá khỏi người giám hộ, vui lòng cập nhật SĐT cho khách hàng rồi thao tác xoá. \nNếu hồ sơ bị tạo sai, vui lòng huỷ kích hoạt.',
          },
        };
      }
      return member;
    }

    let customerAgeYear = customerAge?.year;
    if (!member?.dateOfBirth) {
      // get customer age by age range
      const customerAgeByRange = ageRanges?.find(
        (ageRange) =>
          member?.from === ageRange?.from &&
          member?.to === ageRange?.to &&
          member?.ageUnitCode === ageRange?.ageUnitCode,
      );
      customerAgeYear = calculateYearByRange(customerAgeByRange?.from, customerAgeByRange?.ageUnitCode);
    }

    if (!member?.isHost) {
      if (
        customerAgeYear >= 12 &&
        (!member?.phoneNumber || !PHONE_NUMBER_BUT_NOT_SHOP_PHONE.test(member?.phoneNumber))
      ) {
        member.buttonHanlder = {
          delete: {
            isDisable: true,
            textDisplay:
              'Không được xoá khách hàng không có SĐT ra khỏi người giám hộ.\nNếu muốn xoá khỏi người giám hộ, vui lòng cập nhật SĐT cho khách hàng rồi thao tác xoá. \nNếu hồ sơ bị tạo sai, vui lòng huỷ kích hoạt.',
          },
        };
      } else if (customerAgeYear < 12) {
        member.buttonHanlder = {
          delete: { isDisable: true, textDisplay: 'Không được xoá khách hàng bé hơn 12 tuổi' },
        };
      }
    }
    return member;
  }

  async updateOnlyPerson(updateCustomerDto: UpdatePersonDto, employeeCode: string) {
    try {
      const payloadTCQG: UserProfileDto = {
        shopCode: this.shopCode,
        TenDoiTuong: updateCustomerDto?.name || '',
        GioiTinh: updateCustomerDto?.gender || Gender.Male,
        NgaySinh: updateCustomerDto?.dateOfBirth,
        DanTocId: +updateCustomerDto.ethnicCode,
        CMT: updateCustomerDto?.identityCard,
        DienThoai: updateCustomerDto?.phoneNumber,
        // LoaiDoiTuong: PersonType.AdultMale,
        // NguoiChamSoc: ChildCarer.Guardian,
        TinhId: +updateCustomerDto.frequentlyTCQGProvinceCode || 0,
        HuyenId: +updateCustomerDto.frequentlyTCQGDistrictCode || 0,
        XaId: +updateCustomerDto.frequentlyTCQGWardCode || 0,
        //------
        TinhDangKyId: +updateCustomerDto.temporaryTCQGProvinceCode || 0,
        HuyenDangKyId: +updateCustomerDto.temporaryTCQGDistrictCode || 0,
        XaDangKyId: +updateCustomerDto.temporaryTCQGWardCode || 0,
        DiaChiChiTiet: `${
          updateCustomerDto?.frequentlyAddress === 'Không xác định' ? '' : updateCustomerDto?.frequentlyAddress || ''
        }`,
        DiaChiDangKyChiTiet: updateCustomerDto?.temporaryAddress || '',
        // TenNguoiGiamHo: updateCustomerDto?.guardianName || '',
        // DienThoaiNguoiGiamHo: updateCustomerDto?.guardianPhone,
        lcvId: updateCustomerDto.lcvId,
        GhiChu: updateCustomerDto?.note,
        employeeCode,
      };
      if (!updateCustomerDto?.isShouldNotCreateTcqg && !updateCustomerDto.nationalVaccineCode) {
        let tcqg: GetCustomerDetailRes = null;
        if (
          this.isChannelValidTCQG() &&
          payloadTCQG.TinhId &&
          payloadTCQG.HuyenId &&
          payloadTCQG.XaId &&
          payloadTCQG.TinhDangKyId &&
          payloadTCQG.HuyenDangKyId &&
          payloadTCQG.XaDangKyId
        ) {
          try {
            const createTCQGPayload = {
              ...payloadTCQG,
              // TenNguoiGiamHo: updateCustomerDto?.guardianName || '',
              // DienThoaiNguoiGiamHo: updateCustomerDto?.guardianPhone,
            };
            tcqg = await this.tcqgIntegrationService.createCustomerProfile(createTCQGPayload, {
              timeout: +process.env.TIMEOUT_CREATE_TCQG || 10000,
            });
            if (updateCustomerDto?.isHost) {
              const person = await this.familyCoreService.getPersonByLcvId(updateCustomerDto?.lcvId);
              this.updateTcqgForFamilyMember(person, updateCustomerDto, employeeCode);
            }
          } catch (error) {
            tcqg = null;
          }
        }

        // ưu tiên lấy từ FE truyền lên
        if (!updateCustomerDto?.nationalVaccineId) {
          updateCustomerDto.nationalVaccineId = String(tcqg?.doiTuong2Id || '');
        }

        if (!updateCustomerDto?.nationalVaccineCode) {
          updateCustomerDto.nationalVaccineCode = tcqg?.maDoiTuong || '';
        }
      } else if (
        updateCustomerDto?.nationalVaccineCode &&
        !_.isEmpty(updateCustomerDto?.nationalVaccineCode) &&
        this.isChannelValidTCQG()
      ) {
        try {
          this.checkAndUpdateTcqg(payloadTCQG, updateCustomerDto, employeeCode);
        } catch (error) {
          Logger.log(`Update TCQG Person Profile - ${error.message}`);
        }
      }

      // fix ngày 05/10/2024 12:08
      const { employee_code } = jwtDecode<IAuthUser>(this.token) || { employee_code: null };
      updateCustomerDto.shopCode = this.shopCode;
      updateCustomerDto.modifiedBy = employee_code || updateCustomerDto.modifiedBy;

      delete updateCustomerDto?.isShouldNotCreateTcqg;

      const familyPersonHavePhone = updateCustomerDto?.familyProfileDetails?.filter(
        (familyProfile) => familyProfile?.lcvId !== updateCustomerDto?.lcvId && familyProfile?.phoneNumber,
      );

      let isRangeAge = false;
      if (updateCustomerDto?.dateOfBirth) {
        const agePerson = moment().diff(updateCustomerDto?.dateOfBirth, 'years');
        isRangeAge = agePerson < 12;
      } else {
        isRangeAge = updateCustomerDto?.from < 12;
      }

      if (isRangeAge && familyPersonHavePhone?.length === 0) {
        const exception: IError = {
          code: ErrorCode.RSA_CREATE_CUSTOMER_RULE,
          message: ErrorCode.getError(ErrorCode.RSA_CREATE_CUSTOMER_RULE),
          details: ErrorCode.getError(ErrorCode.RSA_CREATE_CUSTOMER_RULE),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }

      /**
       * @link https://reqs.fptshop.com.vn/browse/FV-12256
       * @bug https://reqs.fptshop.com.vn/browse/FV-13275
       * call family group để lấy thông tin owner
       * call family để lấy thông tin số điện thoại
       */
      const resFamilyByLcvId = await this.familyPackageService.getGroupFamilyPackage({
        lcvIds: [updateCustomerDto?.lcvId],
      });
      // await this.customerRuleService.checkOwnerFamilyPackage({
      //   person: updateCustomerDto,
      //   resFamily: resFamilyByLcvId?.at(0),
      //   isUpdate: true,
      // });

      await this.handleToAvoidDouble(updateCustomerDto?.lcvId, 'update_person');

      const updatePerson = await this.familyCoreService.updateOnlyPerson(updateCustomerDto);

      updatePerson.quotas = [];

      const ageRanges = await this.regimenCoreService.getAgeRanges();

      // updatePerson.customerAge = calculateTimeDifference(
      //   updatePerson?.dateOfBirth,
      //   updatePerson?.from,
      //   updatePerson?.to,
      //   ageRanges,
      //   updatePerson?.ageUnitCode,
      // );

      // check show logo fpt
      const { isFpter, customerTags } = await this.customerRuleService._verifyCustomerTag({
        email: updateCustomerDto?.email,
        phoneNumber: updateCustomerDto?.phoneNumber,
        includeExtra: true,
        vaccinationCode: updateCustomerDto?.lcvId,
      });

      // check người fpt và get remain nếu là người fpt
      if (updateCustomerDto?.email) {
        // const isFpter = await this.customerRuleService._verifyEmailUpdatePerson(updateCustomerDto?.email);
        if (isFpter) {
          try {
            const { quotas } = await this.quotasService.getRemainingQuotaForPerson({ email: updateCustomerDto?.email });
            updatePerson.quotas = quotas;
          } catch (error) {}
        }
      }

      updatePerson.isFpter = isFpter;
      updatePerson['customerTags'] = customerTags;

      if (updatePerson && updatePerson?.pregnancy?.length) {
        updatePerson.pregnancy = this._checkRulePregnancy(updatePerson?.pregnancy);
      }

      // xóa cache customer for stc
      try {
        if (updateCustomerDto?.phoneNumber) {
          await this.customerAppService.clearCacheCustomer({ customerPhoneList: [updateCustomerDto.phoneNumber] });
        }
      } catch (error) {}

      return this.handleDisableDeleteButtonWithAge(updatePerson, ageRanges);
    } catch (error) {
      throw error;
    }
  }

  /*
  POST 1 thành viên mới vào trong family
    - sau khi post thành viên xong -> có cây family mới:
      - update lại giám hộ cho tất cả thành viên trong gia đình với thông tin của host 
  */
  async addFamilyMember(body: AddFamilyMemberDto, employeeCode: string) {
    try {
      await this.handleToAvoidDouble(body?.lcvId, 'add_person');
      //check for pass source RSA/RSA_ECOM
      const source = OrderChannels.RSA_ECOM.includes(this.orderChannel) ? SOURCE.RSA_ECOM : SOURCE.RSA;

      const rs = await this.familyCoreService.addFamilyMember({
        ...body,
        shopCode: body?.shopCode || this.shopCode,
        createdBy: employeeCode ?? body?.createdBy,
        member: { ...body.member, source },
      });
      if (this.isChannelValidTCQG()) {
        this.syncFamilyProfileDetailsToTcqg(rs?.familyProfileDetails, employeeCode);
      }
      const ageRanges = await this.regimenCoreService.getAgeRanges();

      // đánh tag khách hàng mới khi tạo thành viên
      if (rs?.familyProfileDetails?.length) {
        const newMember = rs?.familyProfileDetails?.find((f) => f?.isNewInsert === true);
        if (newMember && newMember?.lcvId) {
          await this.customerRuleService.createTagForCustomer(newMember?.lcvId);
        }
      }

      return { familyProfileDetails: this.handleFamilyProfileDetailsWithAge(rs?.familyProfileDetails, ageRanges) };
    } catch (error) {
      throw error;
    }
  }

  async addFamilyMemberOtp(body: VerifyAddFamilyMemberOtpDto, orderChannel: string, employeeCode: string) {
    const { otp, major, verifyMethod, phoneNumber, insideCode, addMember, application } = body;
    switch (verifyMethod) {
      case EnmVerifyMethod.OTP:
        await this.customerRuleService.verifyChangePhoneWithOtp({ otp, major, phoneNumber: phoneNumber });
        break;
      case EnmVerifyMethod.Staff: {
        if (orderChannel && OrderChannels.RSA.includes(orderChannel)) {
          await this.customerRuleService.verifyInsideSMByPermission({
            otp,
            token: this.token,
            shopCode: (this.req.headers?.['shop-code'] as string) || RSA_ECOM_HARD_DEFAULT_SHOP_CODE,
            insideCode,
            orderChannel,
            functionName: 'Add Family Member',
            arrPermissionSM: [PERMISSION.ASM_PERMISSION],
          });
        } else {
          await this.customerRuleService.verifyChangePhoneWithInsideSM({
            otp,
            token: this.token,
            shopCode: (this.req.headers?.['shop-code'] as string) || RSA_ECOM_HARD_DEFAULT_SHOP_CODE,
            insideCode,
            orderChannel,
            application,
          });
        }
        break;
      }
    }
    return await this.addFamilyMember(
      { ...addMember, shopCode: (this.req.headers?.['shop-code'] as string) || RSA_ECOM_HARD_DEFAULT_SHOP_CODE },
      employeeCode,
    );
  }

  /*
  PUT 1 thành viên trong family -> hiện tại chỉ cho đổi mối quan hệ
    - với member -> chỉ đổi mối quan hệ ở LC
    - với host -> sau khi đổi ở LC, sẽ update lại giám hộ trên cổng TCQG các con trong cây gia đình  
  */
  async updateFamilyMember(body: UpdateMemberDto, employeeCode: string) {
    try {
      const { lcvId } = body;

      await this.handleToAvoidDouble(lcvId, 'update_member');
      const rs = await this.familyCoreService.updateFamilyMember(body);

      if (this.isChannelValidTCQG()) {
        const isUpdatingHost = rs?.familyProfileDetails?.find((d) => d.lcvId === lcvId)?.isHost || false;
        if (isUpdatingHost) {
          this.syncFamilyProfileDetailsToTcqg(rs?.familyProfileDetails, employeeCode);
        }
      }
      const ageRanges = await this.regimenCoreService.getAgeRanges();

      return { familyProfileDetails: this.handleFamilyProfileDetailsWithAge(rs?.familyProfileDetails, ageRanges) };
    } catch (error) {
      throw error;
    }
  }

  /**
   * @TODO Cập nhật thông tin khách hàng qua tcqg (Nếu là chủ hộ sẽ cập nhật cho cả thành viên gia đình)
   */
  async checkAndUpdateTcqg(payloadTCQG: UserProfileDto, updateCustomerDto: UpdatePersonDto, employeeCode: string) {
    const updateUserProfilePayload: UpdateProfileDto = {
      ...payloadTCQG,
      DoiTuongId: updateCustomerDto?.nationalVaccineId,
    };
    const person = await this.familyCoreService.getPersonByLcvId(updateCustomerDto?.lcvId);

    const formatUpdatePerson = {};
    const formatAvailPerson = {};
    if (person) {
      PERSON_COMPARE_FIELD.forEach((field) => {
        if (field === 'dateOfBirth') {
          formatUpdatePerson[field] = formatDate(updateCustomerDto[field]);
          formatAvailPerson[field] = formatDate(person[field]);
        } else {
          formatUpdatePerson[field] = updateCustomerDto[field];
          formatAvailPerson[field] = person[field];
        }
      });
    }

    // Kiểm tra nếu các trường cần thiết có cập nhật sẽ call cập nhật đối tượng qua tcqg
    if (
      haveDifferentValues(formatUpdatePerson, formatAvailPerson) &&
      !_.isEmpty(formatUpdatePerson) &&
      !_.isEmpty(formatAvailPerson)
    ) {
      this.tcqgIntegrationService.updateCustomerInfo(updateUserProfilePayload);

      if (person?.isHost) {
        // Nếu đối tượng cập là chủ hộ sẽ call cập nhật thông tin chủ hộ cho thành viên trong gia đình
        // Check các field để tránh cập nhật không cần thiết
        const checkDiffForUpdatingMember = MEMBER_UPDATE_FIELD.some((field) => {
          return (
            formatUpdatePerson[field] !== formatAvailPerson[field] &&
            !(formatUpdatePerson[field] == null && formatAvailPerson[field] == null)
          );
        });

        if (checkDiffForUpdatingMember) {
          this.updateTcqgForFamilyMember(person, updateCustomerDto, employeeCode);
        }
      }
    }
  }

  async updateTcqgForFamilyMember(person: GetPersonByIdRes, updateCustomerDto: UpdatePersonDto, employeeCode: string) {
    const listMemberHaveVaccineCode = person?.familyProfileDetails
      ?.filter((x) => !x?.isHost && x?.nationalVaccineCode && !_.isEmpty(x?.nationalVaccineCode))
      ?.map((item) => item?.lcvId);
    // nếu có ít nhất 1 member có mã TCQG
    if (listMemberHaveVaccineCode?.length > 0) {
      const listPerson = await this.familyCoreService?.getManyByLcvId({ lcvId: listMemberHaveVaccineCode });
      // Đồng bộ tcqg cho danh sách thành viên gia đình
      const syncJobs = listPerson?.map?.(async (item) => {
        const updatePersonPayload: UpdateGuardianDto = {
          employeeCode: employeeCode,
          shopCode: this.shopCode,
          lcvId: item?.lcvId,
          DoiTuongId: item?.nationalVaccineId,
          ...(await this._getGuardianTcqgInfo({
            familyProfileDetails: person?.familyProfileDetails,
            hostPersonData: updateCustomerDto,
          })),
        };
        return this.tcqgIntegrationService.updateGuardian(updatePersonPayload);
      });
      if (syncJobs && syncJobs?.length) Promise.all(syncJobs);
    }
  }

  /***
   * Đẩy lại các thông tin giám hộ của các member
   */
  async syncFamilyProfileDetailsToTcqg(familyProfileDetails: FamilyProfileDetails[], employeeCode: string) {
    try {
      const listMemberHaveVaccineCode = familyProfileDetails
        ?.filter((item) => !item?.isHost && item?.nationalVaccineCode)
        ?.map((x) => x.lcvId);

      if (!listMemberHaveVaccineCode?.length) {
        Logger.log({
          message: `syncFamilyProfileDetailsToTcqg member not tcqg code`,
        });
        return;
      }

      const hostMember = familyProfileDetails?.find((item) => item?.isHost);
      if (!hostMember || !hostMember.lcvId) {
        Logger.log({
          message: `syncFamilyProfileDetailsToTcqg host person not found`,
        });
        return;
      }

      const hostPersonData = await this.familyCoreService?.getPersonByLcvId(hostMember?.lcvId);
      if (!hostPersonData) {
        Logger.log({
          message: `syncFamilyProfileDetailsToTcqg host person get data not found`,
        });
        return;
      }

      // vì không có nationalVaccineId trong family nên mới cần get lại
      const listPersonMemberHaveVaccineCode = await this.familyCoreService?.getManyByLcvId({
        lcvId: listMemberHaveVaccineCode,
      });

      const syncJobs = listPersonMemberHaveVaccineCode?.map(async (person) => {
        const updateUserProfilePayload: UpdateGuardianDto = {
          employeeCode: employeeCode,
          shopCode: this.shopCode,
          lcvId: person?.lcvId,
          DoiTuongId: person?.nationalVaccineId,
          ...(await this._getGuardianTcqgInfo({ familyProfileDetails, hostPersonData })),
        };

        return this.tcqgIntegrationService.updateGuardian(updateUserProfilePayload);
      });

      if (syncJobs && syncJobs.length) {
        Promise.all(syncJobs);
      }
    } catch (error) {
      Logger.log({
        message: `ERRROR >> syncTCGQForNewFamilyMember`,
        fields: {
          info: JSON.stringify(error),
        },
      });
      return;
    }
  }

  async getCustomerById(id: string) {
    return await this.familyCoreService.getPersonById(id);
  }

  async getCustomerByForTransferInjection(keyWord: string): Promise<GetPersonByIdRes> {
    try {
      let person;

      try {
        person = await this.familyCoreService.getPersonByLcvId(keyWord);
      } catch (error) {}
      if (person) return person;

      try {
        person = await this.familyCoreService.getByNationalCode(keyWord);
      } catch (error) {}
      if (person) return person;

      const exception: IError = {
        code: ErrorCode.RSA_CUSTOMER_TRANSFER_INJECTION,
        message: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_TRANSFER_INJECTION),
        details: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_TRANSFER_INJECTION),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    } catch (error) {
      throw error;
    }
  }

  /**
   * @TODO lấy chi tiết khách hàng từ tcqg
   */
  async getCustomerByCustomerId(customerId: number, employeeCode: string) {
    let data;
    try {
      data = await this.tcqgIntegrationService.getCustomerDetailByID({
        customerID: +customerId,
        shopCode: this.shopCode,
        employeeCode,
      });
    } catch (error) {}

    if (!data) return {};

    const convert = this.convertVaccinationAttrToFamilyAttr(data);
    return convert;
  }

  async convertVaccinationAttrToFamilyAttr(tcqgPerson: GetCustomerDetailRes) {
    // lấy mã dân tộc và tên dân tộc
    const ethnic = await this.familyUtilsService.getEthnicName(tcqgPerson?.danTocId);

    const temporary = await this.familyUtilsService.getTemporaryAddressV2({
      tcQG_Ward: tcqgPerson?.xaDangKyId?.toString(),
    });

    const frequently = await this.familyUtilsService.getFrequentlyAddressV2({
      tcQG_Ward: tcqgPerson?.xaId?.toString(),
    });

    let searchPersonWithVaccineCode;
    if (tcqgPerson?.maDoiTuong) {
      searchPersonWithVaccineCode = await this.familyCoreService.searchPerson({
        keyword: tcqgPerson?.maDoiTuong ?? '',
        pageNumber: 1,
        pageSize: 1,
      });
    }

    const listVaccineSortDate = [];
    const groupDatBySku = _.groupBy(tcqgPerson?.listVaccineHistory, 'sku');
    if (Object?.keys(groupDatBySku)?.length) {
      Object?.keys(groupDatBySku)?.map((sku) => {
        const listItemBySku = tcqgPerson?.listVaccineHistory
          ?.filter((i) => i?.sku === +sku)
          ?.sort((a, b) => {
            return +new Date(b?.vaccineDate) - +new Date(a?.vaccineDate);
          });
        listVaccineSortDate.push(...listItemBySku);
      });
    }

    return {
      ...tcqgPerson?.personInfo,
      lcvId: searchPersonWithVaccineCode?.items?.at(0)?.lcvId || '',
      ethnicName: ethnic.ethnicName,
      ethnicCode: ethnic.ethnicCode,
      ...frequently,
      ...temporary,
      listVaccineHistory: listVaccineSortDate,
    };
  }

  async convertVaccinationAttrToFamilyAttr2(tcqgPersons: GetCustomerDetailRes[]) {
    let jobs = [];
    const ethnicJobs = _.uniq(tcqgPersons.map((x) => x.danTocId))
      .filter((x) => x)
      .map((x) => this.dmsService.getNationById({ code: String(x).padStart(2, '0') }));

    const temporaryWad = _.uniq(tcqgPersons.map((x) => x.xaDangKyId)).filter((x) => x);
    const frequentlyWard = _.uniq(tcqgPersons.map((x) => x.xaId)).filter((x) => x);

    const wardJobs = _.uniq([...temporaryWad, ...frequentlyWard]).map((x) => this.dmsService.getWard({ tcQG_Ward: x }));

    const personJobs = _.uniq(tcqgPersons.map((x) => x.maDoiTuong))
      .filter((x) => x)
      .map((x) => this.familyCoreService.getByNationalCode(x));

    jobs = [...ethnicJobs, ...wardJobs, ...personJobs];
    const jobsData = (await Promise.allSettled(jobs)).map((entry) => {
      if (entry.status === 'fulfilled') {
        return entry?.value?.nations || entry?.value?.wards?.[0] || entry?.value;
      }
    });

    return tcqgPersons.map((personTcqg) => {
      const listVaccineSortDate = [];
      const groupDatBySku = _.groupBy(personTcqg?.listVaccineHistory, 'sku');
      if (Object?.keys(groupDatBySku)?.length) {
        Object?.keys(groupDatBySku)?.map((sku) => {
          const listItemBySku = personTcqg?.listVaccineHistory
            ?.filter((i) => i?.sku === +sku)
            ?.sort((a, b) => {
              return +new Date(b?.vaccineDate) - +new Date(a?.vaccineDate);
            });
          listVaccineSortDate.push(...listItemBySku);
        });
      }

      const personCurrent = jobsData.find((item) => item?.nationalVaccineCode === personTcqg?.maDoiTuong);
      const ethnicCurrent = jobsData.find(
        (item) => item?.[0]?.code === String(personTcqg?.danTocId)?.padStart(2, '0'),
      )?.[0];

      const frequentlyWardCurrent = jobsData.find((item) => +item?.tcqgWard === personTcqg?.xaId);
      const temporaryWardCurrent = jobsData.find((item) => +item?.tcqgWard === personTcqg?.xaDangKyId);

      if (personTcqg?.pregnants?.length > 0) {
        personTcqg.pregnants = personTcqg?.pregnants?.map((i) => {
          return {
            ...i,
            status: i?.status - 1,
          };
        });
      }

      return {
        ...personTcqg?.personInfo,
        lcvId: personCurrent?.lcvId,
        listVaccineHistory: listVaccineSortDate,
        ethnicName: ethnicCurrent?.name,
        ethnicCode: ethnicCurrent?.code,
        frequentlyWardName: frequentlyWardCurrent?.name,
        frequentlyWardCode: frequentlyWardCurrent?.code,
        frequentlyDistrictName: frequentlyWardCurrent?.district?.name,
        frequentlyDistrictCode: frequentlyWardCurrent?.district?.code,
        frequentlyProvinceName: frequentlyWardCurrent?.province?.name,
        frequentlyProvinceCode: frequentlyWardCurrent?.province?.code,
        temporaryDistrictCode: temporaryWardCurrent?.district?.code,
        temporaryDistrictName: temporaryWardCurrent?.district?.name,
        temporaryProvinceCode: temporaryWardCurrent?.province?.code,
        temporaryProvinceName: temporaryWardCurrent?.province?.name,
        temporaryWardCode: temporaryWardCurrent?.code,
        temporaryWardName: temporaryWardCurrent?.name,
        pregnancy: personTcqg?.pregnants,
      };
    });
  }

  /**
   * @TODO tạo otp
   * type
   */
  async createOtp(createOtp: PayloadCreateOtpDto) {
    let extendContent = createOtp?.extendContent || undefined;
    const expired = parseDateTimeZone(
      new Date(new Date().getTime() + 15 * 60 * 1000), // + 15p
      '+07:00',
      'yyyy-MM-DD HH:mm:ss',
    );
    let major = createOtp?.major || MAJOR_OTP.OTP;
    let templateId = ATTR_OTP.TemplateId_SMS;

    switch (createOtp?.type) {
      case TYPE_OTP.CHANGE_PHONE:
        templateId = ATTR_OTP.TemplateId_SMS_Change_Phone;
        break;

      case TYPE_OTP.CHANGE_HOST_PERSON:
        templateId = ATTR_OTP.TemplateId_SMS_Change_Host_Person;
        break;

      case TYPE_OTP.CASH_BACK:
        templateId = ATTR_OTP.TemplateId_SMS_Cashback;
        major = MAJOR_OTP.Cashback;
        break;

      case TYPE_OTP.OTP_VOUCHER:
        templateId = ATTR_OTP.TemplateId_OTP_VOUCHER;
        major = MAJOR_OTP.OTP;
        extendContent = {
          ...extendContent,
          expiredTime: '15',
        };
        break;
      case TYPE_OTP.DELETE_SCHEDULE_HISTORY_TCQG:
        templateId = ATTR_OTP.TemplateId_OTP_Delete_Schedule_History_TCQG;
        major = MAJOR_OTP.OTP;
        break;

      case TYPE_OTP.CREATE_FAMILY_PACKAGE:
        templateId = this.configService.get('TEMPLATE_SEND_SMS_CREATE_GROUP_FAMILY_PACKAGE');
        major = MAJOR_OTP.OTP;
        break;

      case TYPE_OTP.UPDATE_FAMILY_PACKAGE:
        templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_GROUP_FAMILY_PACKAGE');
        major = MAJOR_OTP.OTP;
        break;

      case TYPE_OTP.UPDATE_OWNER_FAMILY_PACKAGE:
        templateId = this.configService.get('TEMPLATE_SEND_SMS_CHANGE_OWNER_GROUP_FAMILY_PACKAGE');
        major = MAJOR_OTP.OTP;
        break;

      case TYPE_OTP.INACTIVE_GROUP_FAMILY_PACKAGE:
        templateId = this.configService.get('TEMPLATE_SEND_SMS_INACTIVE_GROUP_FAMILY_PACKAGE');
        major = MAJOR_OTP.OTP;
        break;

      case TYPE_OTP.ADD_MEMBER_CUSTOMER:
        templateId = this.configService.get('TEMPLATE_SEND_SMS_ADD_MEMBER_CUSTOMER');
        major = MAJOR_OTP.OTP;
        extendContent = {
          ...extendContent,
          expiredTime: '15',
        };
        break;

      case TYPE_OTP.TRANSFER_MEMBER_CUSTOMER:
        templateId = this.configService.get('TEMPLATE_SEND_SMS_TRANSFER_MEMBER_CUSTOMER');
        major = MAJOR_OTP.OTP;
        extendContent = {
          ...extendContent,
          expiredTime: '15',
        };
        break;

      default:
        templateId = ATTR_OTP.TemplateId_SMS;
    }

    const payload = {
      ...createOtp,
      major,
      phoneNumber: parsePhoneNumber(createOtp?.phoneNumber, '84'),
      fromSystem: ATTR_OTP.FromSystem,
      templateId: templateId,
      expired: expired,
    };

    if (createOtp?.orderCode) {
      payload['extendContent'] = {
        orderCode: createOtp?.orderCode,
      };
    }

    if (extendContent) {
      payload['extendContent'] = {
        ...payload['extendContent'],
        ...extendContent,
      };
    }

    /**
     * https://reqs.fptshop.com.vn/browse/FV-13355
     * Tạo OTP xóa lịch sử tiêm nguồn tcqg
     */
    if (payload?.type === TYPE_OTP.DELETE_SCHEDULE_HISTORY_TCQG) {
      delete payload?.orderCode;
      delete payload?.extendContent;
    }

    const data = await this.customerCoreService.createOtp(payload);
    return data;
  }

  /**
   * @description Xác thực OTP dành cho nhiều nguồn
   */
  async verifyOtp(verifyOtpDto: PayloadVerifyOTPDto) {
    const payloadValidOtp: ValidateOtpDto = {
      phoneNumber: parsePhoneNumber(verifyOtpDto?.phoneNumber, '84'),
      otp: verifyOtpDto?.otp,
      fromSystem: ATTR_OTP.FromSystem,
      major: MAJOR_OTP.OTP,
    };

    switch (verifyOtpDto?.type) {
      case TYPE_OTP.OTP_VOUCHER:
        payloadValidOtp.major = MAJOR_OTP.OTP;
        break;

      default:
        payloadValidOtp.major = MAJOR_OTP.OTP;
        break;
    }

    const { isValid } = await this.customerCoreService.validateOtp(payloadValidOtp);
    if (!isValid) {
      const exception: IError = {
        code: ErrorCode.RSA_VERIFY_USER,
        message: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        details: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }
    return true;
  }

  async getCustomerByLcvId(lcvId: string, filter: GetDetailFamilyDto, employeeCode: string) {
    const { familyId } = filter;
    const person = await this.familyCoreService.getPersonByLcvIdV2(lcvId, { familyId });

    // sort person theo chủ hộ -> thông tin profile -> thành viên còn lại
    if (person?.familyProfileDetails?.length > 0) {
      let newArrPerson: FamilyProfileDetails[] = [];
      const itemIsHost = person?.familyProfileDetails?.find((i) => i?.isHost); // lấy item của isHost
      if (itemIsHost) newArrPerson?.push(itemIsHost);
      const itemProfileCurrent = person?.familyProfileDetails?.find((s) => s?.lcvId === lcvId && s?.isHost !== true); // lấy item của profile hiện tại
      if (itemProfileCurrent) newArrPerson?.push(itemProfileCurrent);

      if (newArrPerson?.length > 0) {
        const itemProfileOrther = person?.familyProfileDetails
          ?.filter((ar) => !newArrPerson?.find((rm) => rm?.lcvId === ar?.lcvId))
          ?.sort((a: FamilyProfileDetails, b: FamilyProfileDetails) => {
            if (a?.name < b?.name) return -1;
            if (a?.name > b?.name) return 1;
            return 0;
          }); // loại trừ các item của isHost, profileCurrent
        if (itemProfileOrther) newArrPerson = [...newArrPerson, ...itemProfileOrther];
      }
      person.familyProfileDetails = newArrPerson;
    }

    // check customer tag để hiễn thị logo fpt
    const { isFpter, customerTags } = await this.customerRuleService._verifyCustomerTag({
      email: person?.email,
      phoneNumber: person?.phoneNumber,
      includeExtra: true,
      vaccinationCode: person?.lcvId,
    });

    // check người fpt khi vào chi tiết dựa vào lcvId để lấy email
    // isFpter true => nếu là người fpt, false => nếu k phải là người fpt
    // const { isFpter } = await this.customerRuleService._verifyEmailFpter([lcvId]);

    person.quotas = [];
    if (person?.email && isFpter) {
      try {
        const { quotas } = await this.quotasService.getRemainingQuotaForPerson({ email: person?.email });
        person.quotas = quotas?.filter((quota) => quota?.quotaRespone?.isActive) || [];
      } catch (error) {}
    }

    const ageRanges = await this.regimenCoreService.getAgeRanges();

    person['customerTags'] = customerTags;
    person.isFpter = isFpter;
    person.customerAge = calculateTimeDifference(
      person?.dateOfBirth,
      person?.from,
      person?.to,
      ageRanges,
      person.ageUnitCode,
    );

    if (person.familyProfileDetails?.length > 0) {
      person.familyProfileDetails = this.handleFamilyProfileDetailsWithAge(person.familyProfileDetails, ageRanges);
    }

    if (person && person?.pregnancy?.length > 0) {
      person.pregnancy = this._checkRulePregnancy(person?.pregnancy);
    }

    return person;
  }

  async searchPersonOrTcqg(getTcqgDto: GetTcqgDto, employeeCode: string) {
    // -> tìm nhà mình -> tìm tcqg (ecom thì không tìm tcqg) -> trả về
    let person;
    if (getTcqgDto?.lcvId) {
      person = await this.familyCoreService.getPersonByLcvIdV2(getTcqgDto?.lcvId);
    }

    const name = getTcqgDto?.name?.trim() || person?.name;
    const dob = this._getDob(getTcqgDto?.dateOfBirth, person?.dateOfBirth);
    const frequentlyProvinceCode = getTcqgDto?.frequentlyProvinceCode || person?.frequentlyProvinceCode;
    const frequentlyDistrictCode = getTcqgDto?.frequentlyDistrictCode || person?.frequentlyDistrictCode;
    const frequentlyWardCode = getTcqgDto?.frequentlyWardCode || person?.frequentlyWardCode;
    const temporaryProvinceCode = getTcqgDto?.temporaryProvinceCode || person?.temporaryProvinceCode;
    const temporaryDistrictCode = getTcqgDto?.temporaryDistrictCode || person?.temporaryDistrictCode;
    const temporaryWardCode = getTcqgDto?.temporaryWardCode || person?.temporaryWardCode;
    let phoneNumber = getTcqgDto?.phoneNumber?.trim() || person?.phoneNumber?.trim();
    const regex = /^0{5}/;
    if (regex.test(phoneNumber)) {
      phoneNumber = '';
    }

    const searchPrimaryPersonExactlyPayload = _.pickBy(
      {
        pageNumber: 1,
        pageSize: 10,
        name,
        dateOfBirthFrom: dob,
        dateOfBirthTo: dob,
        frequentlyProvinceCode,
        frequentlyDistrictCode,
        frequentlyWardCode,
        temporaryProvinceCode,
        temporaryDistrictCode,
        temporaryWardCode,
      },
      _.identity,
    ) as any;

    const searchPrimaryPersonSimilarPayload = _.pickBy(
      {
        pageNumber: 1,
        pageSize: 10,
        phoneNumber,
        name,
        dateOfBirthFrom: dob,
        dateOfBirthTo: dob,
      },
      _.identity,
    ) as any;

    // tìm kiếm chính xác persons
    const searchExactlytPerson = await this.familyCoreService.searchPrimaryPersonExactly(
      searchPrimaryPersonExactlyPayload,
    );
    if (searchExactlytPerson?.items?.length) {
      // loại bỏ lcvId đang tìm kiếm ra
      const exactlyPersons = this._filterLvcId(
        this._convertDataPersonLogic(searchExactlytPerson?.items),
        getTcqgDto?.lcvId,
      );
      const personsByCheckWithNationalCode = exactlyPersons?.filter((x) => x?.nationalVaccineCode);

      if (getTcqgDto?.isCheckTcqg) {
        // nếu tạo mới mã hoặc xem thông tin profile thì phải có mã tcqg
        if (personsByCheckWithNationalCode?.length) {
          return {
            personsFound: personsByCheckWithNationalCode,
            payloadSearchPerson: searchPrimaryPersonExactlyPayload || {},
            isExactlySearch: true,
          };
        }
      } else {
        // nếu ecom hoặc tạo kh thì chính xác bình thường
        return {
          personsFound: this._convertDataPersonLogic(searchExactlytPerson?.items),
          payloadSearchPerson: searchPrimaryPersonExactlyPayload || {},
          isExactlySearch: true,
        };
      }
    }

    // không có kết quả search chính xác
    // tìm kiếm tương đối
    const searchPrimaryPersons = await this.familyCoreService.searchPrimaryPersonExactly(
      searchPrimaryPersonSimilarPayload,
    );

    if (!this.isChannelValidTCQG()) {
      if (searchPrimaryPersons?.item?.length) {
        return {
          payloadSearchPerson: searchPrimaryPersonSimilarPayload || {},
          personsFound: this._filterLvcId(
            // loại bỏ lcvId đang tìm kiếm ra
            this._uniqWithLogic(this._convertDataPersonLogic(searchPrimaryPersons?.items)),
            getTcqgDto?.lcvId,
          ),
        };
      }
      return {
        payloadSearchPerson: searchPrimaryPersonSimilarPayload || {},
        personsFound: [],
      };
    }
    const searchFromTcqg = await this._searchFromTcqg(
      {
        phoneNumber,
        name,
        dateOfBirth: dob,
        gender: `${person ? person?.gender : getTcqgDto?.gender}`,
        frequentlyProvinceCode: frequentlyProvinceCode,
        frequentlyDistrictCode: frequentlyDistrictCode,
        frequentlyWardCode: frequentlyWardCode,
      },
      employeeCode,
    );

    return {
      errorFromTCQG: searchFromTcqg?.errorFromTCQG || false,
      payloadSearchTCQG: searchFromTcqg?.payloadSearch || {},
      payloadSearchPerson: searchPrimaryPersonSimilarPayload || {},
      personsFound: this._filterLvcId(
        // loại bỏ lcvId đang tìm kiếm ra
        this._uniqWithLogic([
          ...searchFromTcqg?.personFromTCQG,
          ...this._convertDataPersonLogic(searchPrimaryPersons?.items),
        ]),
        getTcqgDto?.lcvId,
      ),
    };
  }

  _getDob(getTcqgDtoDateOfBirth, personDateOfBirth) {
    if (!getTcqgDtoDateOfBirth && !personDateOfBirth) return null;
    return String(getTcqgDtoDateOfBirth || personDateOfBirth).substring(0, 10);
    // return moment(getTcqgDtoDateOfBirth || personDateOfBirth)
    //   .utcOffset(7)
    //   .format('YYYY-MM-DD');
  }

  // loại bỏ lcvId đang tìm kiếm ra
  _filterLvcId(array, lcvId) {
    if (!lcvId) return array;
    return array.filter((x) => x.lcvId !== lcvId);
  }

  _convertDataPersonLogic(array) {
    return array.map((x) => {
      delete x.personContacts;
      delete x.personIdentifications;
      return x;
    });
  }

  _uniqWithLogic(array) {
    return _.uniqWith(array, (objA, objB) => {
      return objA?.lcvId === objB?.lcvId && objA?.nationalVaccineCode === objB?.nationalVaccineCode;
    });
  }

  async _searchFromTcqg(filter: GetTcqgDto, employeeCode: string) {
    // nếu có thông tin tcqg thì không làm gì :)
    //  1. có phone thì tìm theo phone
    //  2. không có phone, tìm theo họ tên, ngày tháng năm, địa chỉ nếu có + try cache
    //  3. call api để đồng bộ lại lịch sử tiêm

    // Nếu chỉ có 1 trong field: Ko tìm
    // Nếu có 2 trong 3 field: Tìm theo 2 field
    // Nếu có 3 field: Tìm theo 3 field, nếu ko có kết quả thì search rule combine. Nếu có kết quả thì trả luôn.
    const arrayCheck = [filter?.name, filter?.dateOfBirth, filter?.phoneNumber].filter((p) => p);
    if (arrayCheck.length === 1) {
      return {
        personFromTCQG: [],
      };
    }
    let dataFromTCQG = null;
    let errorFromTCQG = false;
    try {
      dataFromTCQG = await this._searchOnTCQG2({
        pageNumber: 1,
        pageSize: 10,
        phoneNumber: filter?.phoneNumber,
        name: filter?.name, // -> luôn có
        dateOfBirthFrom: filter?.dateOfBirth,
        dateOfBirthTo: filter?.dateOfBirth,
        gender: filter?.gender,
        shopCode: this.shopCode,
        provinceCode: filter?.frequentlyProvinceCode,
        districtCode: filter?.frequentlyDistrictCode,
        wardCode: filter?.frequentlyWardCode,
        employeeCode,
        arrayCheck,
      });
    } catch (error) {
      // if status là 500 or 401 thì mới báo lỗi
      if (error.status === HttpStatus.INTERNAL_SERVER_ERROR || error.status === HttpStatus.UNAUTHORIZED) {
        errorFromTCQG = true;
      }
    }

    return {
      personFromTCQG: dataFromTCQG?.personMapTcqg || [],
      payloadSearch: {
        payloadSearch: dataFromTCQG?.payloadSearch,
        totalCount: dataFromTCQG?.totalCount,
      },
      errorFromTCQG: errorFromTCQG ? errorFromTCQG : dataFromTCQG?.errorFromTCQG,
    };
  }

  /**
   * @TODO verify phoneNumber
   */
  async verifyPhoneNumber(phone: string) {
    const phoneInfo = await this.familyCoreService.verifyByPhone({ phone: phone });
    if (
      phoneInfo?.lcvId &&
      !OrderChannels.RSA_AFFILIATE.includes((this.req.headers?.['order-channel'] as string) || '')
    ) {
      return await this.familyCoreService.getPersonByLcvId(phoneInfo?.lcvId);
    }
    return phoneInfo;
  }

  /**
   * @TODO search family member by keyword
   */
  async searchFamilyByKeyword(payload: SearchFamilyByKeywordDto) {
    return await this.familyCoreService.searchFamilyByKeyWord(payload);
  }

  /**
   * @TODO search family member by keyword
   */
  async changeFamilyProfile(payload: ChangeFamilyMemberProfileDto, employeeCode: string) {
    const { modifiedBy, newFamilyId, oldFamilyId, personId, lcvId, personIdReload, hostLcvId } = payload;
    // FV-14891
    const getPerson = await this.familyCoreService.getPersonById(personId);
    if (getPerson && getPerson?.isHost) {
      const exception: IError = {
        code: ErrorCode.RSA_CUSTOMER_BLOCK_DELETE_HOST_PERSON,
        message: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_BLOCK_DELETE_HOST_PERSON)?.replace(
          `{lcvId}`,
          getPerson?.lcvId,
        ),
        details: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_BLOCK_DELETE_HOST_PERSON)?.replace(
          `{lcvId}`,
          getPerson?.lcvId,
        ),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
    //End FV-14891
    const res = await this.familyCoreService.changeFamilyProfile({
      newFamilyId,
      oldFamilyId,
      personId,
      personIdReload,
      hostLcvId,
      modifiedBy: employeeCode || modifiedBy,
    });

    //Check chặn trùng gia đình khi chuyển
    if (
      oldFamilyId &&
      newFamilyId &&
      !_.isEmpty(oldFamilyId) &&
      !_.isEmpty(newFamilyId) &&
      oldFamilyId === newFamilyId
    ) {
      const exception: IError = {
        code: ErrorCode.FAMILY_DUPLICATE,
        message: ErrorCode.getError(ErrorCode.FAMILY_DUPLICATE),
        details: ErrorCode.getError(ErrorCode.FAMILY_DUPLICATE),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    if (this.isChannelValidTCQG()) {
      const vaccinationCode = res?.familyProfileDetails?.find?.((x) => x?.personId === personId)?.nationalVaccineCode;
      if (vaccinationCode && !_?.isEmpty(vaccinationCode)) {
        const hostMemberLcvId = res?.familyProfileDetails?.find?.((x) => x?.isHost)?.lcvId;
        const hostPersonData = await this.familyCoreService.getPersonByLcvId(hostMemberLcvId);
        if (!hostPersonData) {
          Logger.log({
            message: `syncFamilyProfileDetailsToTcqg host person get data not found`,
          });
        }
        const person = await this.familyCoreService.getPersonByLcvId(lcvId);
        const updateUserProfilePayload: UpdateGuardianDto = {
          employeeCode: employeeCode,
          shopCode: this.shopCode,
          lcvId: person?.lcvId,
          DoiTuongId: person?.nationalVaccineId,
          ...(await this._getGuardianTcqgInfo({ familyProfileDetails: res?.familyProfileDetails, hostPersonData })),
        };
        this.tcqgIntegrationService.updateGuardian(updateUserProfilePayload);
      }
    }

    if (res && res?.familyProfileDetails?.length > 0) {
      const ageRanges = await this.regimenCoreService.getAgeRanges();
      res.familyProfileDetails = this.handleFamilyProfileDetailsWithAge(res?.familyProfileDetails, ageRanges);
    }

    return res;
  }

  async verifyAndChangeFamilyProfile(payload: VerifyAndTransferMemberDto, employeeCode: string) {
    const { otp, major, verifyMethod, phoneNumber, insideCode, application, transferMember } = payload;
    switch (verifyMethod) {
      case EnmVerifyMethod.OTP:
        await this.customerRuleService.verifyChangePhoneWithOtp({ otp, major, phoneNumber: phoneNumber });
        break;
      case EnmVerifyMethod.Staff: {
        if (
          this.req.headers?.['order-channel'] &&
          OrderChannels.RSA.includes(this.req.headers?.['order-channel'] as string)
        ) {
          await this.customerRuleService.verifyInsideSMByPermission({
            otp,
            token: this.token,
            shopCode: (this.req.headers?.['shop-code'] as string) || '',
            insideCode,
            orderChannel: (this.req.headers?.['order-channel'] as string) || '',
            functionName: 'Change Family Profile',
            arrPermissionSM: [PERMISSION.ASM_PERMISSION],
          });
        } else {
          await this.customerRuleService.verifyChangePhoneWithInsideSM({
            otp,
            token: this.token,
            shopCode: (this.req.headers?.['shop-code'] as string) || '',
            application,
            insideCode,
            orderChannel: (this.req.headers?.['order-channel'] as string) || '',
          });
        }
        break;
      }
    }
    await this.changeFamilyProfile(transferMember, employeeCode);
  }
  /**
   * @TODO lấy danh sách chức danh
   */
  async getListTitle() {
    const titleList = await this.familyCoreService.getListTitle();
    return {
      items: titleList,
    };
  }

  /**
   * @TODO lấy danh sách khách hàng nhiều lcvid
   */
  async getManyByLcvId(payload: PayloadGetManyLcvIdDto) {
    const data = await this.familyCoreService.getManyByLcvId(payload);
    return {
      items: data,
    };
  }

  /**
   * @description Tạo địa chỉ khách hàng
   * @param paras
   * @param createCustomerAddressDto
   */
  async createCustomerAddress(
    paras: CreateCustomerAddressParasDto,
    createCustomerAddressDto: CreateCustomerAddressDto | CreateCustomerAddressDto[],
  ): Promise<CreateCustomerAddressResponseDto | CreateCustomerAddressResponseDto[]> {
    if (!Array.isArray(createCustomerAddressDto)) {
      return this.customerCoreService.createCustomerAddress(paras, createCustomerAddressDto);
    }

    return Promise.all(
      createCustomerAddressDto.map((item) => this.customerCoreService.createCustomerAddress(paras, item)),
    );
  }

  /**
   * @description Lấy thông tin địa chỉ khách hàng
   * @param paras
   */
  async getCustomerAddress(paras: GetCustomerAddressDto): Promise<CreateCustomerAddressResponseDto> {
    return this.customerCoreService.getCustomerAddress(paras);
  }

  /**
   * @TODO Cập nhật địa chỉ khách hàng
   */
  async updateCustomerAddress(paras: UpdateCustomerAddressParasDto, payload: UpdateCustomerAddressDto) {
    return this.customerCoreService.updateCustomerAddress(paras, payload);
  }

  /**
   * @TODO Xóa địa chỉ khách hàng
   */
  async deleteCustomerAddress(paras: DeleteCustomerAddressParasDto) {
    await this.customerCoreService.deleteCustomerAddress(paras);
    return {
      status: true,
    };
  }

  /**
   * @TODO Lấy thông tin khách hàng theo số điện thoại
   */
  async getCustomerByPhone(getCustomerByPhoneDTO: GetCustomerByPhoneDTO) {
    return await this.familyCoreService.verifyByPhone({ phone: getCustomerByPhoneDTO.mobilePhone });
  }

  /**
   * @TODO lấy danh sách person theo orderCode
   */
  async searchPersonByOrderCode(filter: SearchPersonByOrderCodeDto) {
    const { orderCode: orderCodeFilter } = filter;
    const { items } = await this.scheduleCoreService.searchSchedule({ orderCode: orderCodeFilter });

    if (items?.length === 0)
      return {
        totalCount: 0,
        items: [],
      };

    const { lcvId, orderCode } = items?.at(0);

    const family = await this.searchCustomer({ keyword: lcvId }, '');

    // add orderCode cho lcvid để FE dựa vào highlight
    if (family?.items?.length) {
      for (const item of family?.items) {
        const findOrderCodeForLcvId = item?.lcvId === lcvId;
        if (!findOrderCodeForLcvId) {
          // case find lcvId on familyDetails
          const person = item?.familyProfileDetails?.find((x) => x?.lcvId === lcvId);
          if (person) {
            person['orderCode'] = orderCode;
          }
        } else {
          // case orderCode for isHot
          item['orderCode'] = orderCode;
        }
      }
    }

    return family;
  }

  async validateCustomerByLcvId(lcvId: string): Promise<{ isValid: boolean }> {
    const customer = await this.familyCoreService.getPersonByLcvIdV2(lcvId);

    if (!customer) {
      return {
        isValid: false,
      };
    }

    const listFieldCheckValue = [
      'phoneNumber',
      'referralSourceId',
      'name',
      'dateOfBirth',
      'frequentlyProvinceCode',
      'frequentlyProvinceName',
      'frequentlyDistrictCode',
      'frequentlyDistrictName',
      'frequentlyWardCode',
      'frequentlyWardName',
      'temporaryProvinceCode',
      'temporaryProvinceName',
      'temporaryDistrictCode',
      'temporaryDistrictName',
      'temporaryWardCode',
      'temporaryWardName',
      'ethnicCode',
      'gender',
    ];

    let isValid = true;
    for (const field of listFieldCheckValue) {
      if (customer[field] === null) {
        isValid = false;
      }
    }

    return {
      isValid,
    };
  }

  /**
   * @TODO lấy thông tin family bởi lcvid
   */
  async getFamilyByLcvId(query: GetFamilyByLcvIdDto) {
    const data = await this.familyCoreService.getFamilyByLcvId(query);
    const ageRanges = await this.regimenCoreService.getAgeRanges();

    data.customerAge = calculateTimeDifference(data?.dateOfBirth, data?.from, data?.to, ageRanges, data?.ageUnitCode);
    if (data?.familyProfileDetails?.length > 0) {
      data.familyProfileDetails = this.handleFamilyProfileDetailsWithAge(data.familyProfileDetails, ageRanges);
    }
    return data;
  }

  /**
   * @TODO xác minh số điện thoại
   */
  async verifyPhoneNumberPerson(body: VerifyPhoneNumberDto) {
    const { newPhoneNumber } = body;

    // check định dạng sdt
    // if (!validateVietnamesePhoneNumber(newPhoneNumber)) {
    //   throw new SystemException(
    //     {
    //       code: ErrorCode.RSA_VERIFY_FORMAT_PHONE_NUMBER,
    //     },
    //     HttpStatus.FORBIDDEN,
    //   );
    // }

    const { message, isAbleForUpdate } = await this.familyCoreService.verifyPhoneNumber(body);
    return {
      message,
      isAbleForUpdate,
    };
  }

  /**
   * @TODO xác nhận cập nhật số điện thoại
   */
  async updatePhoneNumberForPerson(body: VerifyUpdateChangePhoneDto, orderChannel?: string) {
    const {
      otp,
      major,
      modifiedByStaffCode,
      modifiedByStaffName,
      oldPhoneNumber,
      personId,
      verifyMethod,
      newPhoneNumber,
      insideCode,
      arrImages,
      lcvId,
      application,
    } = body;

    switch (verifyMethod) {
      case EnmVerifyMethod.OTP:
        await this.customerRuleService.verifyChangePhoneWithOtp({ otp, major, phoneNumber: oldPhoneNumber });
        break;
      case EnmVerifyMethod.Staff: {
        if (orderChannel && OrderChannels.RSA.includes(orderChannel)) {
          await this.customerRuleService.verifyInsideSMByPermission({
            otp,
            token: this.token,
            shopCode: this.shopCode,
            insideCode,
            orderChannel,
            functionName: 'Update Phone Number',
            arrPermissionSM: [PERMISSION.ASM_PERMISSION],
          });
        } else {
          await this.customerRuleService.verifyChangePhoneWithInsideSM({
            otp,
            token: this.token,
            shopCode: this.shopCode,
            application,
            insideCode,
            orderChannel,
          });
        }
        break;
      }
      case EnmVerifyMethod.Identification:
        await this.customerRuleService.verifyChangePhoneWithIdentification({
          personId,
          arrImages,
          modifiedByStaffCode,
        });
        break;
    }

    const rs = await this.familyCoreService.updatePhoneNumber({
      personId,
      oldPhoneNumber,
      newPhoneNumber,
      modifiedByStaffCode,
      modifiedByStaffName,
      verifyMethod,
    });

    if (this.isChannelValidTCQG()) {
      let person = await this.familyCoreService?.getPersonByLcvId(lcvId);
      person = {
        ...person,
        phoneNumber: newPhoneNumber,
      };

      if (person?.nationalVaccineCode && !_.isEmpty(person?.nationalVaccineCode)) {
        // Cập nhật sdt qua tcqg cho person có mã tcqg
        this.tcqgIntegrationService.updatePhone({
          DoiTuongId: person?.nationalVaccineId,
          DienThoai: newPhoneNumber,
          lcvId: person?.lcvId,
          employeeCode: modifiedByStaffCode,
          shopCode: this.shopCode,
        });

        // nếu người được cập nhật sdt là chủ hộ
        if (person?.isHost) {
          //  Lọc thành viên có mã tcqg
          const listMemberLcvIdHaveVaccineCode = person?.familyProfileDetails
            ?.filter((item) => !item?.isHost && item?.nationalVaccineCode)
            ?.map((x) => x?.lcvId);

          // Nếu có ít nhất 1 thành viên có mã tcqg
          if (listMemberLcvIdHaveVaccineCode && listMemberLcvIdHaveVaccineCode?.length) {
            const listMemberHaveVaccineCode = await this.familyCoreService.getManyByLcvId({
              lcvId: listMemberLcvIdHaveVaccineCode,
            });
            // Đồng bộ sdt người giám hộ qua tcqg cho danh sách thành viên gia đình
            const syncJobs = listMemberHaveVaccineCode?.map(async (item) => {
              const updateUserProfilePayload: UpdateGuardianDto = {
                employeeCode: modifiedByStaffCode,
                shopCode: this.shopCode,
                lcvId: item?.lcvId,
                DoiTuongId: item?.nationalVaccineId,
                ...(await this._getGuardianTcqgInfo({
                  familyProfileDetails: person?.familyProfileDetails,
                  hostPersonData: person,
                })),
              };

              return this.tcqgIntegrationService.updateGuardian(updateUserProfilePayload);
            });

            if (syncJobs && syncJobs.length) {
              Promise.all(syncJobs);
            }
          }
        }
      }
    }

    const ageRanges = await this.regimenCoreService.getAgeRanges();

    const customerAfterHandleAge = this.handleDisableDeleteButtonWithAge(rs?.personInfo, ageRanges);

    return {
      ...rs,
      personInfo: {
        ...rs?.personInfo,
        customerAge: customerAfterHandleAge?.customerAge,
        buttonHandler: customerAfterHandleAge?.buttonHandler,
      },
    };
  }

  /**
   * @TODO lấy danh sách hình ảnh
   */
  async getIdentificationPerson(personId: string) {
    const data = await this.familyCoreService.getIdentification(personId);
    return {
      items: data,
    };
  }

  /*
   * @TODO lấy thông tin profile với mã tiêm chủng quốc gia
   */
  async getPersonByNationalVaccineCode(query: SearchPersonByNationalVaccineCodeDto, employeeCode) {
    const { nationalVaccineCode } = query;
    const person = await this.familyCoreService.searchPerson({
      keyword: nationalVaccineCode,
      pageNumber: 0,
      pageSize: 1,
    });

    if (person?.items?.length === 0) {
      // search tiêm chủng quốc gia
      try {
        const searchTCQG = await this.tcqgIntegrationService.searchCustomer(
          {
            shopCode: this.shopCode,
            nationalVaccineCode,
            employeeCode,
            source: 1,
            pageNumber: 1,
            pageSize: 10,
          },
          {
            timeout: TIMEOUT_SEARCH_TCQG,
          },
        );
        // vinhdq6: Update check nếu tìm thấy 0 hoặc > 1 kết quả thì đều trả về 0
        if (searchTCQG?.items.length == 1) {
          const personMapTcqg = await this.familyLocalService.mapPersonFromTCQG(searchTCQG?.items);

          return {
            totalCount: personMapTcqg.length,
            items: personMapTcqg,
          };
        }
      } catch (error) {
        return {
          totalCount: 0,
          items: [],
        };
      }
    }

    return person;
  }

  /**
   * @TODO xóa số điện thoại
   */
  async deletePhoneForPerson(body: DeletePhoneNumberForPersonDto, orderChannel?: string) {
    const {
      otp,
      major,
      modifiedByStaffCode,
      personId,
      verifyMethod,
      phoneNumber,
      insideCode,
      arrImages,
      modifiedByStaffName,
      application,
    } = body;

    // FV-5207 [Web-RSA][Khách hàng]_Không cho phép xóa số điện thoại của người giám hộ
    // check không được xóa số điện thoại người giám hộ
    if (personId) {
      const getPerson = await this.familyCoreService.getPersonById(personId);
      // getPerson?.isHost == true (chủ hộ thì không cho xóa sdt)
      if (getPerson && getPerson?.isHost) {
        const exception: IError = {
          code: ErrorCode.RSA_CUSTOMER_BLOCK_DELETE_PHONE,
          message: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_BLOCK_DELETE_PHONE),
          details: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_BLOCK_DELETE_PHONE),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.BAD_REQUEST);
      }

      /**
       * @link https://reqs.fptshop.com.vn/browse/FV-12256
       * call family get group theo lcvid
       * check là owner thì k cho xóa sdt
       */
      const resGroupFamily = await this.familyPackageService.getGroupFamilyPackage({ lcvIds: [getPerson?.lcvId] });
      // await this.customerRuleService.checkOwnerFamilyPackage({
      //   person: getPerson,
      //   resFamily: resGroupFamily?.at(0),
      //   isDeletePhone: true,
      // });
    }
    // END FV-5207 [Web-RSA][Khách hàng]_Không cho phép xóa số điện thoại của người giám hộ

    switch (verifyMethod) {
      case EnmVerifyMethod.OTP:
        await this.customerRuleService.verifyChangePhoneWithOtp({ otp, major, phoneNumber });
        break;
      case EnmVerifyMethod.Staff: {
        if (orderChannel && OrderChannels.RSA.includes(orderChannel)) {
          await this.customerRuleService.verifyInsideSMByPermission({
            otp,
            token: this.token,
            shopCode: this.shopCode,
            insideCode,
            orderChannel,
            functionName: 'Delete Phone Number',
            arrPermissionSM: [PERMISSION.ASM_PERMISSION],
          });
        } else {
          await this.customerRuleService.verifyChangePhoneWithInsideSM({
            otp,
            token: this.token,
            shopCode: this.shopCode,
            application,
            insideCode,
            orderChannel,
          });
        }
        break;
      }
      case EnmVerifyMethod.Identification:
        await this.customerRuleService.verifyChangePhoneWithIdentification({
          personId,
          arrImages,
          modifiedByStaffCode,
        });
        break;
    }

    const rs = await this.familyCoreService.deletePhoneNumber([
      { personId, modifiedByStaffCode, modifiedByStaffName, verifyMethod },
    ]);
    const ageRanges = await this.regimenCoreService.getAgeRanges();

    const customerAfterHandleAge = this.handleDisableDeleteButtonWithAge(rs?.[0]?.personInfo, ageRanges);

    // xóa cache customer for stc
    try {
      if (phoneNumber) {
        await this.customerAppService.clearCacheCustomer({ customerPhoneList: [phoneNumber] });
      }
    } catch (error) {}
    return {
      ...rs?.[0],
      personInfo: {
        ...rs?.[0]?.personInfo,
        customerAge: customerAfterHandleAge?.customerAge,
        buttonHandler: customerAfterHandleAge?.buttonHandler,
      },
    };
  }

  /**
   * @TODO xác nhận cập nhật chủ hộ
   */
  async changeHostPerson(body: VerifyUpdateHostForPersonDto, orderChannel?: string, employeeCode?: string) {
    const {
      otp,
      major,
      insideCode,
      verifyMethod,
      phoneNumber,
      personId,
      personIdHost,
      lcvIdPrimary,
      modifiedBy,
      application,
    } = body;

    switch (verifyMethod) {
      case EnmVerifyMethod.OTP:
        await this.customerRuleService.verifyChangePhoneWithOtp({ otp, major, phoneNumber });
        break;
      case EnmVerifyMethod.Staff: {
        if (orderChannel && OrderChannels.RSA.includes(orderChannel)) {
          await this.customerRuleService.verifyInsideSMByPermission({
            otp,
            token: this.token,
            shopCode: this.shopCode,
            insideCode,
            orderChannel,
            functionName: 'Change Host Person',
            arrPermissionSM: [PERMISSION.ASM_PERMISSION],
          });
        } else {
          await this.customerRuleService.verifyChangePhoneWithInsideSM({
            otp,
            token: this.token,
            shopCode: this.shopCode,
            application,
            insideCode,
            orderChannel,
          });
        }
        break;
      }
      case EnmVerifyMethod.None:
        break;
    }

    // FV-14891
    const getFamilyProfileHostCurrent = await this.familyCoreService.getPersonById(personIdHost);
    const listPersonIdOfFamilyHostCurrent = getFamilyProfileHostCurrent?.familyProfileDetails?.map(
      (item) => item?.personId,
    );
    if (!listPersonIdOfFamilyHostCurrent?.includes(personId)) {
      const getPerson = await this.familyCoreService.getPersonById(personId);
      const exception: IError = {
        code: ErrorCode.RSA_CUSTOMER_BLOCK_UPDATE_HOST_PERSON,
        message: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_BLOCK_UPDATE_HOST_PERSON)
          ?.replace('{personChange}', `${getPerson?.lcvId}-${getPerson?.name}`)
          ?.replace('{personPrimary}', `${getFamilyProfileHostCurrent?.name}`),
        details: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_BLOCK_UPDATE_HOST_PERSON)
          ?.replace('{personChange}', `${getPerson?.lcvId}-${getPerson?.name}`)
          ?.replace('{personPrimary}', `${getFamilyProfileHostCurrent?.name}`),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
    // End FV-14891

    const result = await this.familyCoreService.updateHostForPerson({
      personId,
      personIdHost,
      lcvIdPrimary,
      modifiedBy,
    });

    if (result?.familyProfileDetails?.length > 0 && result) {
      const ageRanges = await this.regimenCoreService.getAgeRanges();
      result.customerAge = calculateTimeDifference(
        result?.dateOfBirth,
        result?.from,
        result?.to,
        ageRanges,
        result?.ageUnitCode,
      );

      result.familyProfileDetails = this.handleFamilyProfileDetailsWithAge(result?.familyProfileDetails, ageRanges);
    }

    //Nếu gọi từ ECOM sẽ không sync tcqg
    if (!this.isChannelValidTCQG()) {
      return result;
    }

    this.syncFamilyProfileDetailsToTcqg(result?.familyProfileDetails, employeeCode);

    return result;
  }

  async deleteFamilyProfile(body: DeleteFamilyProfileDto) {
    // FV-14891
    const getPerson = await this.familyCoreService.getPersonById(body.personIdRemove);
    if (getPerson && getPerson?.isHost) {
      const exception: IError = {
        code: ErrorCode.RSA_CUSTOMER_BLOCK_DELETE_HOST_PERSON,
        message: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_BLOCK_DELETE_HOST_PERSON)?.replace(
          `{lcvId}`,
          getPerson?.lcvId,
        ),
        details: ErrorCode.getError(ErrorCode.RSA_CUSTOMER_BLOCK_DELETE_HOST_PERSON)?.replace(
          `{lcvId}`,
          getPerson?.lcvId,
        ),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
    //End FV-14891
    const rs = await this.familyCoreService.deleteFamilyProfile(body);
    const ageRanges = await this.regimenCoreService.getAgeRanges();

    return { familyProfileDetails: this.handleFamilyProfileDetailsWithAge(rs?.familyProfileDetails, ageRanges) };
  }

  async inactivePerson(payload: InactivePersonPayload) {
    if (OrderChannels.RSA_AFFILIATE.includes(this.orderChannel)) {
      return {};
    }

    // docs: https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=147920423
    await this.customerRuleService.verifyOtpInactivePerson({
      otp: payload?.otp,
      insideCode: payload?.insideCode,
      token: this.token,
      shopCode: this.req.headers?.['shop-code'] as string,
      orderChannel: this.req.headers?.['order-channel'] as string,
    });

    // Step 0: Check user trong group family package thì không cho xóa
    const resFamilyPackage = await this.familyPackageService.getGroupFamilyPackage({ lcvIds: [payload?.lcvId] });
    if (resFamilyPackage?.length) {
      const exception: IError = {
        code: ErrorCode.RSA_INACTIVE_PERSON_IN_FAMILY_PACKAGE,
        message: ErrorCode.getError(ErrorCode.RSA_INACTIVE_PERSON_IN_FAMILY_PACKAGE),
        details: ErrorCode.getError(ErrorCode.RSA_INACTIVE_PERSON_IN_FAMILY_PACKAGE),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    // Nếu active person thì chỉ cần call bên family
    if (payload?.isActive) {
      const inactivePerson = await this.familyCoreService.inactivePerson(payload);
      return inactivePerson;
    }

    const getFamilyProfile = await this.familyCoreService.getPersonByLcvId(payload?.lcvId);

    /**
     * @TODO kiểm tra là chủ hộ có thành viên thì không cho inActive
     */
    if (getFamilyProfile) {
      // filter thành viên ngoại trừ chính nó
      const filterFamilyNotIsHot = getFamilyProfile?.isHost
        ? getFamilyProfile?.familyProfileDetails?.filter((i) => i?.lcvId !== getFamilyProfile?.lcvId)
        : [];
      if (getFamilyProfile?.isHost && filterFamilyNotIsHot?.length > 0) {
        const exception: IError = {
          code: ErrorCode.RSA_ACTIVE_PERSON_HAVE_FAMILY,
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }
    }

    const listSchedule = await this.scheduleCoreService.getScheduleByPersonCode({
      personCode: getFamilyProfile?.lcvId,
      status: [0],
    });

    // Step 1: Thanh toán chưa tiêm
    const listScheduleNotVaccinated = listSchedule?.items?.find(
      (schedule) => schedule?.status === 0 && schedule?.isPaid,
    );
    if (listScheduleNotVaccinated) {
      const exception: IError = {
        code: ErrorCode.RSA_INACTIVE_PERSON_NOT_VACCINATED,
        message: ErrorCode.getError(ErrorCode.RSA_INACTIVE_PERSON_NOT_VACCINATED),
        details: ErrorCode.getError(ErrorCode.RSA_INACTIVE_PERSON_NOT_VACCINATED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
    // Step 1.1: chặn inactive cho thanh toán từng phần
    const listScheduleWaittingPaid = listSchedule?.items?.find(
      (schedule) => schedule?.status === 0 && schedule?.waittingPaid,
    );
    if (listScheduleWaittingPaid) {
      const exception: IError = {
        code: ErrorCode.RSA_INACTIVE_PERSON_WAITING_PAID,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    // Step 2: Check phiếu đang xử lý trong ngày
    const getListTicket = await this.examinationCoreService.searchByLcsIds({
      fromDate: moment().utcOffset(7).format('YYYY-MM-DD'),
      toDate: moment().utcOffset(7).format('YYYY-MM-DD'),
      lCVIds: [getFamilyProfile?.lcvId],
    });
    const ticketInprogress = getListTicket?.find(
      (ticket) =>
        ticket?.status !== STATUS_TICKET.HUY &&
        ticket?.status !== STATUS_TICKET.HUY_TIEM &&
        ticket?.status !== STATUS_TICKET.DONG &&
        ticket?.status !== STATUS_TICKET.HOAN_THANH &&
        ticket?.ticketType !== 1,
    );
    if (ticketInprogress) {
      const exception: IError = {
        code: ErrorCode.RSA_INACTIVE_PERSON_JOURNEY_OPEN,
        message: ErrorCode.getError(ErrorCode.RSA_INACTIVE_PERSON_JOURNEY_OPEN)?.replace(
          `{ticketCode}`,
          ticketInprogress?.ticketCode,
        ),
        details: ErrorCode.getError(ErrorCode.RSA_INACTIVE_PERSON_JOURNEY_OPEN)?.replace(
          `{ticketCode}`,
          ticketInprogress?.ticketCode,
        ),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    // Step 4: Check có lịch sử không
    const getListInjectionScheduleByLcvId = await this.historyCoreService.getByLcvId(getFamilyProfile?.lcvId, false);
    if (getListInjectionScheduleByLcvId?.length) {
      const exception: IError = {
        code: ErrorCode.RSA_INACTIVE_PERSON_EXIST_HISTORY,
        message: ErrorCode.getError(ErrorCode.RSA_INACTIVE_PERSON_EXIST_HISTORY),
        details: ErrorCode.getError(ErrorCode.RSA_INACTIVE_PERSON_EXIST_HISTORY),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    const getOrderOfEcom = _.uniqBy(
      listSchedule?.items?.filter((schedule) => schedule?.sourceId === SourceId.Ecom && schedule?.status === 0),
      'orderCode',
    );
    const arrOrderCode: string[] = getOrderOfEcom?.map((order) => order?.orderCode);
    if (arrOrderCode?.length) {
      const { orders } = await this.omsService.getListOrderES({
        orderCode: arrOrderCode,
      });
      const arrOrderOption = orders
        ?.filter((e) => [OrderStatus.FinishDeposit].includes(e.orderStatus))
        ?.map((e) => ({ orderCode: e.orderCode, orderStatus: e.orderStatus }));
      if (arrOrderOption?.length) {
        const exception: IError = {
          code: ErrorCode.RSA_INACTIVE_ORDER,
          message: ErrorCode.getError(ErrorCode.RSA_INACTIVE_ORDER)?.replace(
            '{{orderCode}}',
            arrOrderOption?.map((e) => e.orderCode).join(', '),
          ),
          details: ErrorCode.getError(ErrorCode.RSA_INACTIVE_ORDER)?.replace(
            '{{orderCode}}',
            arrOrderOption?.map((e) => e.orderCode).join(', '),
          ),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }
    }

    // call journey get orderInfor để check đơn hoàn tất cọc
    const { items: arrOrderStatus6 } = await this.journeyCoreService.getOrderInfo({
      keyWord: payload?.lcvId,
      orderStatus: [6],
      pageNumber: 1, // BaoNT9 kêu hard
      pageSize: 1000, // BaoNT9 kêu hard
      totalSize: 1000, // BaoNT9 kêu hard
    });

    if (arrOrderStatus6?.length) {
      const exception: IError = {
        code: ErrorCode.RSA_INACTIVE_ORDER_HOAN_TAT_COC,
        message: ErrorCode.getError(ErrorCode.RSA_INACTIVE_ORDER_HOAN_TAT_COC)?.replace(
          '{orderCode}',
          arrOrderStatus6?.map((e) => e.orderCode).join(', '),
        ),
        details: ErrorCode.getError(ErrorCode.RSA_INACTIVE_ORDER_HOAN_TAT_COC)?.replace(
          '{orderCode}',
          arrOrderStatus6?.map((e) => e.orderCode).join(', '),
        ),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    // Step 4: Inactive person
    const inactivePerson = await this.familyCoreService.inactivePerson(payload);

    try {
      // step 5: Bỏ cờ theo dõi trên tcqg: nếu mapping 1-1 nationalVaccineCode với family
      if (getFamilyProfile?.nationalVaccineCode) {
        const searchPerson = await this.familyCoreService.searchPerson({
          nationalVaccineCode: getFamilyProfile?.nationalVaccineCode,
        });
        if (searchPerson?.totalCount === 1) {
          this.tcqgIntegrationService.changeFollowStatus({
            nationalVaccineCode: getFamilyProfile?.nationalVaccineCode,
            shopCode: this.shopCode,
            employeeCode: payload?.modifiedBy,
            reason: 'Inactive person',
          });
        }
      }
      // Step 6: Xóa khỏi gia đình
      await this.familyCoreService.deleteFamilyProfile({
        personIdNow: payload?.personId,
        personIdRemove: payload?.personId,
        familyIdNow: getFamilyProfile?.familyProfileId,
        personIdHost: getFamilyProfile?.familyProfileDetails?.find((familyProfileDetail) => familyProfileDetail?.isHost)
          ?.personId,
      });

      // Step 6.1: xóa personContact
      await this.familyCoreService.inactivePersonContactByPersonId({
        personId: getFamilyProfile?.id,
        modifiedBy: payload?.modifiedBy,
      });
    } catch (error) {}

    // Step 7: Check isPaid = false thì cancel lịch hẹn
    const listScheduleNotPaid = listSchedule?.items?.filter((schedule) => !schedule?.isPaid);
    const arrListScheduleNotPaid = listScheduleNotPaid?.map((schedule) => schedule?.id);
    await this.scheduleCoreService.cancelScheduleByKey({
      calendarIds: arrListScheduleNotPaid,
      note: 'Inactive person',
      cancelBy: payload?.modifiedBy,
    });

    // Step 8: Lịch hẹn từ ecom => hủy đơn ecom
    const listScheduleEcom = _.uniqBy(
      listSchedule?.items?.filter((schedule) => schedule?.sourceId === SourceId.Ecom && schedule?.status === 0),
      'orderCode',
    );

    if (listScheduleEcom?.length) {
      for (const scheduleEcom of listScheduleEcom) {
        const getOrder = await this.omsService.getOneOrder(scheduleEcom?.orderCode);
        if (getOrder?.orderStatus === OrderStatus.Confirmed) {
          await this.omsService.cancelOrder({
            orderCode: getOrder.orderCode,
            orderCancelChannel: 14,
            orderID: getOrder.orderID,
            modifiedBy: payload.modifiedBy,
            modifiedByName: payload?.modifiedByName,
            reason: 1,
            reasonCancel: 'Hồ sơ KH bị vô hiệu hóa nên cần hủy đơn ecom',
            systemDate: moment().format(),
          });
          await this.examinationCoreService.changeStatusTicket({
            ticketCode: scheduleEcom?.ticketCode,
            status: EnmStatusTicket.CLOSED,
            modifiedBy: payload.modifiedBy,
            note: 'Hồ sơ KH bị vô hiệu hóa nên cần hủy phiếu khám từ ecom',
          });
        }
      }
    }

    // xóa cache customer for stc
    try {
      if (inactivePerson) {
        await this.customerAppService.clearCacheCustomer({ customerPhoneList: [getFamilyProfile?.phoneNumber] });
      }
    } catch (error) {}

    return inactivePerson;
  }

  /**
   * get customer không trả về thông tin family
   */
  async getCustomerNotResFamily(lcvId: string) {
    const result = await this.familyCoreService.getListPrimaryPerson([lcvId]);
    const ageRanges = await this.regimenCoreService.getAgeRanges();
    const data = result?.at(0);
    result.at(0).customerAge = calculateTimeDifference(
      data?.dateOfBirth,
      data?.from,
      data?.to,
      ageRanges,
      data?.ageUnitCode,
    );

    result.at(0).pregnancy = this._checkRulePregnancy(result?.at(0)?.pregnancy);

    return result?.at(0) || null;
  }

  private _mapTitleIdByTypeFromTcqg(familyProfileDetails: any[]) {
    return familyProfileDetails?.map((i) => {
      return {
        ...i,
        ...this.getTitleIdByType(i?.type),
      };
    });
  }

  private getTitleIdByType(type: number) {
    switch (type) {
      case 1: // 'Mẹ
        return {
          titleName: 'Mẹ',
          titleId: '4a9022bc-edab-4eee-8131-998159946bee',
        };
      case 2:
        return {
          titleName: 'Bố',
          titleId: 'dfef731e-0c80-4f20-bd84-b962ee75f424',
        };
      default:
        return {
          titleName: 'Khác',
          titleId: '8d54cabe-f239-4c9a-b5de-4d9f8727ce7d',
        };
    }
  }

  /**
   * @description tạo/ cập nhật phụ nữ mang thai
   */
  async createAndUpdatePregnancy(payload: pregnancyDto) {
    const { employee_code } = jwtDecode<IAuthUser>(this.token) || { employee_code: null };
    const { details, personId, createdBy, modifiedBy } = payload;
    await this.customerRuleService.checkCreateAndUpdatePregnancy(details);

    let person: CreatePersonRes;
    // check luồng để tạo hoặc update
    const payloadUpdate: UpdatePregnancyDto[] = [];
    const payloadCreate: PregnancyDetailDto[] = [];
    const payloadTcqg: PregnantsDto[] = [];
    details?.map((i) => {
      if (i?.id) {
        payloadUpdate.push({
          ...i,
          personId: personId,
          pregnancyDate: i?.pregnancyDate,
          estimateDate: i?.estimateDate,
          modifiedBy: modifiedBy,
        });
      } else {
        payloadCreate.push({
          pregnancyNumber: i?.pregnancyNumber,
          pregnancyDate: i?.pregnancyDate,
          estimateDate: i?.estimateDate,
          status: i?.status,
        });
      }
      payloadTcqg.push({
        LanMangThai: i?.pregnancyNumber,
        NgayMangThai: i?.pregnancyDate,
        TrangThai: i?.status + 1,
        NgaySinhEmBe: i?.estimateDate,
      });
    });

    person = await this.familyCoreService.createPregnancy({ details: payloadCreate, personId, createdBy });
    if (payloadUpdate?.length > 0) {
      person = await this.familyCoreService.updatePregnancy(payloadUpdate);
    }

    if (person && person?.pregnancy?.length > 0) {
      person.pregnancy = this._checkRulePregnancy(person?.pregnancy);

      // đẩy lên tcqg
      if (this.isChannelValidTCQG()) {
        try {
          await this.tcqgIntegrationService.createdAndUpdatedPregnant({
            shopCode: this.shopCode,
            lcvId: person?.lcvId,
            employeeCode: employee_code,
            doiTuongId: person?.nationalVaccineId,
            pregnants: payloadTcqg,
          });
        } catch (error) {}
      }
    }

    return person;
  }

  /**
   * @description xóa thông tin phụ nữ mang thai
   */
  async deletePregnancy(params: DeletePregnancyDto) {
    const { employee_code } = jwtDecode<IAuthUser>(this.token) || { employee_code: null };
    // get danh sách lần khai báo mang thai của person (phục vụ cho việc lấy lần mang thai truyền lên tcqg)
    const pregnancyes = await this.familyCoreService.getPregnancy({ personId: params?.personId });

    const res = await this.familyCoreService.deletePregnancy(params);

    if (res && res?.pregnancy?.length > 0) {
      res.pregnancy = this._checkRulePregnancy(res.pregnancy);
    }

    // xóa phụ nữ mang thai lên tcqg
    if (res && pregnancyes?.length > 0 && this.isChannelValidTCQG()) {
      const findPregnancyById = pregnancyes?.find((i) => i?.id === params?.id);

      if (findPregnancyById) {
        try {
          await this.tcqgIntegrationService.deletePregnant({
            shopCode: this.shopCode,
            lcvId: res?.lcvId,
            employeeCode: employee_code,
            doiTuongId: res?.nationalVaccineId,
            lanMangThai: findPregnancyById?.pregnancyNumber,
          });
        } catch (error) {}
      }
    }

    return res;
  }

  /**
   * @description check rule >= 10 tháng tuổi
   */
  _checkRulePregnancy(pregnancy: Pregnancy[]) {
    if (!pregnancy?.length) return [];
    return pregnancy
      ?.map((i) => {
        const { months, remainingDaysInMonths } = calculateTimeDifferenceV2(new Date(i?.pregnancyDate));
        return {
          ...i,
          isValid: (months >= 10 && remainingDaysInMonths > 0) || [1, 2].includes(i?.status) ? false : true, // 10 tháng thì false ; <= 10 tháng thì true || status = 0, 1 thì fasle (1: đã sinh, 2: sảy thai)
        };
      })
      ?.sort((a, b) => a?.pregnancyNumber - b?.pregnancyNumber);
  }

  private isChannelValidTCQG() {
    return OrderChannels.RSA.includes(this.orderChannel);
  }

  private isChannelAffilate() {
    const channel = (this.req.headers?.['order-channel'] as string) || '';
    return OrderChannels.RSA_AFFILIATE.includes(channel);
  }

  async getUserPlatformActiveByLcvId(lcvId: string) {
    return await this.osrCoreService.getUserPlatformActiveByLcvId(lcvId);
  }
}
