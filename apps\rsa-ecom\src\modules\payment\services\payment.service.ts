import { Inject, Injectable } from '@nestjs/common';
import {
  getDepositDetailAmount,
  PaymentGatewayService,
  ServiceType,
  VerifyVoucherGotitLibDto,
  VerifyVoucherUrboxLibDto,
} from 'vac-nest-payment-gateway';
import { CheckBillDto } from '../dto';
import { ITEM_VACCINE_SO1, OrderChannels, RedisService, UserDto, concurrentPromise, getExpiredTime } from '@shared';
import { ALLOWED_PAYMENT_METHOD, ALLOWED_PAYMENT_METHOD_PRE_ORDER } from '../constants';
import { convertPaymentMethodToName, filterPaymentMethod } from '../utils';
import { PaymentLinkService } from './payment-link.service';
import { EcomOrderService } from '../../ecom-order/services/ecom-order.service';
import { PaymentMethod } from 'vac-nest-payment-portal';
import { SendSmsPaymentDto } from '../dto/send-sms-payment-link.dto';
import { OMSService, OrderStatus } from 'vac-nest-oms';
import { InjectRepository } from '@nestjs/typeorm';
import { PaymentMetaData } from '../entities/payment-meta-data.entity';
import { Repository } from 'typeorm';
import moment from 'moment';
import { ConfigService } from '@nestjs/config';
import { uniqBy, uniq, compact } from 'lodash';
import { GetListDepositAmountBySkuDto, OsrService } from 'vac-nest-osr';
import { GetListPaymentPayVaccineDto } from '../dto/get-list-payment-pay-vaccine.dto';
import { RsaBeV2Service } from 'vac-nest-rsa-be-v2';
import { GetPaymentMethodDto } from '../dto/get-payment-method.dto';
import { OrdersService } from 'modules/modules/modules/orders/services/orders.service';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { PIMAppService } from 'vac-nest-pim-app';
import { ITEM_CODE_HANG_HIEM } from '../enum';
import { OrderAttribute } from 'vac-nest-cart-app';
import { PaymentType } from 'vac-nest-examination';

@Injectable()
export class PaymentService {
  constructor(
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly paymentLinkService: PaymentLinkService,
    private readonly ecomOrderService: EcomOrderService,
    private readonly redisService: RedisService,
    private readonly omsService: OMSService,
    private readonly osrService: OsrService,
    private readonly rsaBeV2Service: RsaBeV2Service,
    private readonly orderService: OrdersService,

    @InjectRepository(PaymentMetaData)
    private readonly paymentMetaData: Repository<PaymentMetaData>,
    private readonly configService: ConfigService,
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly pimAppService: PIMAppService,
  ) {}

  async getListPaymentMethod(params: GetPaymentMethodDto, orderChannel?: string) {
    if (orderChannel && OrderChannels.RSA_ECOM.includes(orderChannel)) {
      const paymentMethods = await this.paymentGatewayService.getListPaymentMethod({
        serviceTypeId: ServiceType.Vaccine,
      });
      const filteredMethods = filterPaymentMethod(paymentMethods, ALLOWED_PAYMENT_METHOD);

      /**
       * Pre-order phase 2 không trả về phương thức thanh toán tại cửa hàng.
       * https://reqs.fptshop.com.vn/browse/FV-9622
       * https://reqs.fptshop.com.vn/browse/FV-9699
       */
      if (params.orderCode) {
        const { isPreOrderPhase2, order } = await this.orderService.isPreOrderPhase2(params.orderCode);

        /**
         * Nếu hàng khang hiếm thì k hiển thị thanh toán tại shop
         * 9671 = id hàng khang hiếm
         * https://reqs.fptshop.com.vn/browse/FV-13050
         */
        const skus =
          order?.details
            ?.find((item) => item.itemCode === ITEM_VACCINE_SO1)
            ?.detailAttachments?.map((i) => i?.itemCode) || [];

        let isHangHiem = false;
        let isPreOrder = false;
        if (skus?.length) {
          const { listProduct } = await this.pimAppService.getListProductBySku(uniq(skus));
          if (listProduct?.length) {
            // some có ít nhất 1 phần tử thoả mãn điều kiện thì true
            isHangHiem = listProduct?.some((i) => i?.attributeShop?.attributeOptionId === ITEM_CODE_HANG_HIEM);
            isPreOrder = listProduct?.some((i) => !!i?.isPreOrder);
          }
        }

        if (order?.orderAttribute === OrderAttribute.PRE_ORDER && order?.orderStatus === OrderStatus.FinishDeposit) {
          const arrPaymentCode = uniq(
            compact(
              order.orderPaymentCreate.map((e) => {
                if (e.paymentType === PaymentType.THU) return e.paymentCode;
              }),
            ),
          );
          const payment = await this.paymentGatewayService.getListRedis(arrPaymentCode);
          const totalPayment = payment?.reduce((acc, cur) => acc + getDepositDetailAmount(cur?.detail), 0);
          // phase 2 chưa thanh toán thì hiện thanh toán tại shop
          if (isPreOrderPhase2 && totalPayment <= 0) {
            return filteredMethods;
          } else {
            return filteredMethods.filter((method) => method.id !== PaymentMethod.PAY_AT_SHOP);
          }
        } else if (
          isPreOrderPhase2 ||
          order.orderStatus === OrderStatus.FinishDeposit || //FV-11730: Tất cả đơn hoàn tất cọc thì sẽ bỏ payment method PAY_AT_SHOP
          isHangHiem ||
          isPreOrder || //FV-15842
          order?.orderAttribute === 9 // https://reqs.frt.vn/browse/FV-18037: Ecom bỏ phương thức thanh toán PAY_AT_SHOP cho đơn gia đình
        ) {
          return filteredMethods.filter((method) => method.id !== PaymentMethod.PAY_AT_SHOP);
        }
      }

      return filteredMethods;
    }
    return await this.paymentGatewayService.getListPaymentMethod({ serviceTypeId: ServiceType.Vaccine });
  }

  async getListPaymentMethodPreOrder(params: GetListDepositAmountBySkuDto, orderChannel = '7') {
    const depositBySku = await this.osrService.getListDepositAmountBySku(params);

    if (orderChannel && OrderChannels.RSA_ECOM.includes(orderChannel)) {
      const paymentMethods = await this.paymentGatewayService.getListPaymentMethod({
        serviceTypeId: ServiceType.Vaccine,
      });

      return {
        depositBySku,
        paymentMethods: filterPaymentMethod(paymentMethods, ALLOWED_PAYMENT_METHOD_PRE_ORDER),
      };
    }

    const paymentMethods = await this.paymentGatewayService.getListPaymentMethod({
      serviceTypeId: ServiceType.Vaccine,
    });

    return {
      depositBySku,
      paymentMethods,
    };
  }

  async checkBill(checkBillDto: CheckBillDto, orderChannel?: string) {
    if (orderChannel && OrderChannels.RSA_ECOM.includes(orderChannel)) {
      return this.checkBillEcom(checkBillDto);
    }

    const { total, cash, transfer, card, voucher, cod, ewallet, point, topUp } = checkBillDto;

    const totalBill = +total || 0;
    const cashBill = +cash || 0;
    const transferBill = +transfer || 0;
    const cardBill = +card || 0;
    const voucherBill = +voucher || 0;
    const codBill = +cod || 0;
    const eWalletBill = +ewallet || 0;
    const rewardPoints = +point || 0;
    const topUpBill = +topUp || 0;

    const totalPriceMethodNoChange = transferBill + voucherBill + cardBill + topUpBill;
    const totalPriceMethodRemain = totalBill - totalPriceMethodNoChange;

    //f.sell point payment
    const pointFriendSell =
      rewardPoints - Math.floor(voucherBill / 1000) > 0 ? rewardPoints - Math.floor(voucherBill / 1000) : 0;

    //price for order deposit
    const depositPrice = Number(Math.round((totalBill * 0.3) / 1000) * 1000);

    // Total price
    if (totalPriceMethodRemain < 0) {
      return {
        total: totalPriceMethodNoChange,
        remain: 0,
        voucher: voucher,
        depositPrice: depositPrice,
        depositRemain: 0,
        point: pointFriendSell,
      };
    }

    let totalCustomerPay = cashBill + transferBill + cardBill + codBill + voucherBill + topUpBill;
    let totalCustomerPayRemain = totalCustomerPay - totalBill;
    // E-wallet with voucher
    if (eWalletBill) {
      totalCustomerPay = eWalletBill + voucherBill;
      totalCustomerPayRemain = totalCustomerPay - totalBill;
    }

    const dataRes: any = {
      // Total don't calculate with voucher
      total: totalCustomerPay,
      remain: totalCustomerPayRemain,
      voucher: voucher,
      depositPrice: depositPrice,
      depositRemain: totalCustomerPayRemain,
      point: pointFriendSell,
    };

    return dataRes;
  }

  async checkBillEcom(checkBillDto: CheckBillDto) {
    const { total, cash, transfer, card, voucher, cod, ewallet, point, topUp, payAtShop } = checkBillDto;

    const totalBill = +total || 0;
    const cashBill = +cash || 0;
    const transferBill = +transfer || 0;
    const cardBill = +card || 0;
    const voucherBill = +voucher || 0;
    const codBill = +cod || 0;
    const eWalletBill = +ewallet || 0;
    const rewardPoints = +point || 0;
    const topUpBill = +topUp || 0;
    const payAtShopBill = +payAtShop || 0;

    const totalPriceMethodNoChange = transferBill + voucherBill + cardBill + topUpBill;
    const totalPriceMethodRemain = totalBill - totalPriceMethodNoChange;

    //f.sell point payment
    const pointFriendSell =
      rewardPoints - Math.floor(voucherBill / 1000) > 0 ? rewardPoints - Math.floor(voucherBill / 1000) : 0;

    //price for order deposit
    const depositPrice = Number(Math.round((totalBill * 0.3) / 1000) * 1000);

    // Total price
    if (totalPriceMethodRemain < 0) {
      return {
        total: totalPriceMethodNoChange,
        remain: 0,
        voucher: voucher,
        depositPrice: depositPrice,
        depositRemain: 0,
        point: pointFriendSell,
      };
    }

    let totalCustomerPay = cashBill + transferBill + cardBill + codBill + voucherBill + topUpBill + payAtShopBill;
    let totalCustomerPayRemain = totalCustomerPay - totalBill;
    // E-wallet with voucher
    if (eWalletBill) {
      totalCustomerPay = eWalletBill + voucherBill;
      totalCustomerPayRemain = totalCustomerPay - totalBill;
    }

    const dataRes: any = {
      // Total don't calculate with voucher
      total: totalCustomerPay,
      remain: totalCustomerPayRemain,
      voucher: voucher,
      depositPrice: depositPrice,
      depositRemain: totalCustomerPayRemain,
      point: pointFriendSell,
      payAtShop: payAtShopBill,
    };

    return dataRes;
  }

  /**
   * @TODO lấy danh sách phương thức chi tiền
   */
  async getPaymentPayVaccine(getPaymentPayVaccineDto: GetListPaymentPayVaccineDto) {
    const { orderCode, type } = getPaymentPayVaccineDto;
    let res = await this.paymentGatewayService.getListPaymentPayVaccine();

    if (!res) {
      return {
        items: [],
      };
    }
    res?.forEach((item) => {
      if (item?.id === 1) {
        item?.detail?.forEach((e) => {
          e['disabled'] = false;
        });
      }
      if (item?.id === 2) {
        item['disabled'] = false;
      }
    });
    /**
     * @TODO Nếu kênh ecom thì trả disable hết chỉ trả ck nếu có tiền không thì chỉ có
     */
    const [getTotalTien, order] = await concurrentPromise(
      this.rsaBeV2Service.getDepositCancelDetail(orderCode),
      this.omsService.getOneOrder(orderCode),
    );
    // hủy cọc
    if (type === 1 && (order?.orderAttribute === 7 || OrderChannels.RSA_AFFILIATE.includes(order?.orderChanel))) {
      if (!getTotalTien?.['totalRefund']) {
        res?.forEach((item) => {
          if (item?.id === 1) {
            item?.detail?.forEach((e) => {
              if (e?.id === 1) {
                e['disabled'] = true;
              }
            });
          }
          if (item?.id === 5) {
            item['disabled'] = true;
          }
        });
      } else if (getTotalTien?.['totalRefund']) {
        res = res.filter((item) => item.id !== 1);
      }
    }
    // Hủy cọc và nguồn đơn đến từ web app
    if (type === 1 && OrderChannels.WEB_APP.includes(order?.orderChanel)) {
      res = res.filter((item) => item?.id === 5); // chỉ trả về phương thức chuyển khoản
    }
    // chi tiền
    if (type === 2) {
      res = res.filter((item) => item?.id === 1);
    }
    return {
      items: res,
    };
  }

  async sendVnpayLink(data: SendSmsPaymentDto, user?: UserDto): Promise<any> {
    try {
      const orderData = await this.omsService.getOneOrder(data.orderCode);
      let paymentLink = '';
      const redisKey = `PaymentLink:${convertPaymentMethodToName(PaymentMethod.VNPAY)}:${data.orderCode}`;
      const createdPaymentLink = await this.redisService.get(redisKey);
      if (createdPaymentLink) {
        paymentLink = createdPaymentLink;
      } else {
        const oldPaymentRequestCode = await this.redisService.get(redisKey + '-PaymentRequestCode');
        if (oldPaymentRequestCode) {
          // cancel payment request code cux
          this.paymentGatewayService.cancelPaymentRequestCode(oldPaymentRequestCode);
        }
        const { accountId, paymentRequestCode } = await this.paymentLinkService.getDataForPaymentLinkGenerate(
          orderData,
        );
        paymentLink = await this.paymentLinkService.genVnpayLink(
          paymentRequestCode,
          accountId,
          data,
          orderData,
          user.employee_code,
        );

        const linkExpiredInRaw = this.configService.get('REDIS_LINK_EXPIRED_IN_MINUTES');
        const paymentRequestExpiredInRaw = this.configService.get('REDIS_PAYMENT_REQUEST_CODE_EXPIRED_IN_MINUTES');
        const linkExpiredInMinutes = linkExpiredInRaw ? Number(linkExpiredInRaw) : 15;
        const paymentRequestExpiredInMinutes = paymentRequestExpiredInRaw ? Number(paymentRequestExpiredInRaw) : 30;

        await this.redisService.set(redisKey, paymentLink, 'EX', getExpiredTime('min', linkExpiredInMinutes));
        // lưu payment request cũ, để khi tạo link mới thì cancelPaymentRequest cũ đi
        await this.redisService.set(
          redisKey + '-PaymentRequestCode',
          paymentRequestCode,
          'EX',
          getExpiredTime('min', paymentRequestExpiredInMinutes),
        );
      }

      // update pttt vào ecomOrder
      const paymentMethodCurrent = (
        data?.vouchers?.length ? [PaymentMethod.VNPAY, PaymentMethod.VOUCHER] : [PaymentMethod.VNPAY]
      ) as number[];

      const vouchers = await this.processVoucherPayload(data.orderCode, data?.vouchers);
      await this.ecomOrderService.update(data.orderCode, {
        paymentMethod: paymentMethodCurrent,
        lastestPayment: {
          paymentMethods: paymentMethodCurrent,
          orderStatus: +orderData.orderStatus,
          currentPaymentRequestCode: orderData?.paymentRequestCode,
        },
        vouchers,
      });

      return await this.paymentLinkService.sendSMSPaymentLinkToCustomer({
        orderCode: orderData.orderCode,
        insideId: data.inside,
        paymentLink: paymentLink,
        phone: orderData.phone,
        paymentType: PaymentMethod.VNPAY,
        orderData: orderData,
        partnerId: data.partnerId,
      });
    } catch (error) {
      throw error;
    }
  }

  async sendAlepayLink(data: SendSmsPaymentDto, user?: UserDto): Promise<any> {
    try {
      const orderData = await this.omsService.getOneOrder(data.orderCode);

      let paymentLink = '';
      const redisKey = `CreatedPaymentLink:${convertPaymentMethodToName(PaymentMethod.ALEPAY)}:${data.orderCode}`;
      const createdPaymentLink = await this.redisService.get(redisKey);
      if (createdPaymentLink) {
        paymentLink = createdPaymentLink;
      } else {
        const oldPaymentRequestCode = await this.redisService.get(redisKey + '-PaymentRequestCode');
        if (oldPaymentRequestCode) {
          // cancel payment request code cux
          this.paymentGatewayService.cancelPaymentRequestCode(oldPaymentRequestCode);
        }

        const { accountId, paymentRequestCode } = await this.paymentLinkService.getDataForPaymentLinkGenerate(
          orderData,
        );

        paymentLink = await this.paymentLinkService.genAlepayLink(
          paymentRequestCode,
          accountId,
          data,
          orderData,
          user.employee_code,
        );

        const linkExpiredInRaw = this.configService.get('REDIS_LINK_EXPIRED_IN_MINUTES');
        const paymentRequestExpiredInRaw = this.configService.get('REDIS_PAYMENT_REQUEST_CODE_EXPIRED_IN_MINUTES');
        const linkExpiredInMinutes = linkExpiredInRaw ? Number(linkExpiredInRaw) : 15;
        const paymentRequestExpiredInMinutes = paymentRequestExpiredInRaw ? Number(paymentRequestExpiredInRaw) : 30;

        await this.redisService.set(redisKey, paymentLink, 'EX', getExpiredTime('min', linkExpiredInMinutes));
        // lưu payment request cũ, để khi tạo link mới thì cancelPaymentRequest cũ đi
        await this.redisService.set(
          redisKey + '-PaymentRequestCode',
          paymentRequestCode,
          'EX',
          getExpiredTime('min', paymentRequestExpiredInMinutes),
        );
      }

      // update pttt vào ecomOrder
      const paymentMethodCurrent = data?.vouchers?.length
        ? [PaymentMethod.ALEPAY, PaymentMethod.VOUCHER]
        : [PaymentMethod.ALEPAY];

      const vouchers = await this.processVoucherPayload(data.orderCode, data?.vouchers);
      await this.ecomOrderService.update(data.orderCode, {
        paymentMethod: paymentMethodCurrent,
        lastestPayment: {
          paymentMethods: paymentMethodCurrent,
          orderStatus: +orderData.orderStatus,
          currentPaymentRequestCode: orderData?.paymentRequestCode,
        },
        vouchers,
      });

      return await this.paymentLinkService.sendSMSPaymentLinkToCustomer({
        orderCode: orderData.orderCode,
        insideId: data.inside,
        paymentLink: paymentLink,
        phone: orderData.phone,
        paymentType: PaymentMethod.ALEPAY,
        orderData: orderData,
        partnerId: data.partnerId,
      });
    } catch (error) {
      throw error;
    }
  }

  async sendHPLLink(data: SendSmsPaymentDto): Promise<any> {
    try {
      const orderData = await this.omsService.getOneOrder(data.orderCode);

      let paymentLink = '';
      const redisKey = `CreatedPaymentLink:${convertPaymentMethodToName(PaymentMethod.HOME_PAY_LATER)}:${
        data.orderCode
      }`;
      const createdPaymentLink = await this.redisService.get(redisKey);
      if (createdPaymentLink) {
        paymentLink = createdPaymentLink;
      } else {
        const oldPaymentRequestCode = await this.redisService.get(redisKey + '-PaymentRequestCode');
        if (oldPaymentRequestCode) {
          // cancel payment request code cux
          this.paymentGatewayService.cancelPaymentRequestCode(oldPaymentRequestCode);
        }
        const { accountId, paymentRequestCode } = await this.paymentLinkService.getDataForPaymentLinkGenerate(
          orderData,
        );
        paymentLink = await this.paymentLinkService.genHPLLink(paymentRequestCode, accountId, data, orderData);
        await this.redisService.set(redisKey, paymentLink, 'EX', getExpiredTime('min', 15));
        // lưu payment request cũ, để khi tạo link mới thì cancelPaymentRequest cũ đi
        await this.redisService.set(
          redisKey + '-PaymentRequestCode',
          paymentRequestCode,
          'EX',
          getExpiredTime('min', 30),
        );
      }

      // update pttt vào ecomOrder
      const paymentMethodCurrent = (
        data?.vouchers?.length ? [PaymentMethod.HOME_PAY_LATER, PaymentMethod.VOUCHER] : [PaymentMethod.HOME_PAY_LATER]
      ) as number[];
      await this.ecomOrderService.update(data.orderCode, {
        paymentMethod: paymentMethodCurrent,
        lastestPayment: {
          paymentMethods: paymentMethodCurrent,
          orderStatus: +orderData.orderStatus,
          currentPaymentRequestCode: orderData?.paymentRequestCode,
        },
      });

      return await this.paymentLinkService.sendSMSPaymentLinkToCustomer({
        orderCode: orderData.orderCode,
        insideId: data.inside,
        paymentLink: paymentLink,
        phone: orderData.phone,
        paymentType: PaymentMethod.HOME_PAY_LATER,
        orderData: orderData,
        partnerId: data.partnerId,
      });
    } catch (error) {
      throw error;
    }
  }

  async sendZalopayLink(data: SendSmsPaymentDto, user?: UserDto): Promise<any> {
    try {
      const orderData = await this.omsService.getOneOrder(data.orderCode);

      let paymentLink = '';
      const redisKey = `CreatedPaymentLink:${convertPaymentMethodToName(PaymentMethod.ZALO_PAY)}:${data.orderCode}`;
      const createdPaymentLink = await this.redisService.get(redisKey);
      if (createdPaymentLink) {
        paymentLink = createdPaymentLink;
      } else {
        const oldPaymentRequestCode = await this.redisService.get(redisKey + '-PaymentRequestCode');
        if (oldPaymentRequestCode) {
          // cancel payment request code cux
          this.paymentGatewayService.cancelPaymentRequestCode(oldPaymentRequestCode);
        }
        const { accountId, paymentRequestCode } = await this.paymentLinkService.getDataForPaymentLinkGenerate(
          orderData,
        );
        paymentLink = await this.paymentLinkService.genZalopayLink(
          paymentRequestCode,
          accountId,
          data,
          orderData,
          user.employee_code,
        );

        const linkExpiredInRaw = this.configService.get('REDIS_LINK_EXPIRED_IN_MINUTES');
        const paymentRequestExpiredInRaw = this.configService.get('REDIS_PAYMENT_REQUEST_CODE_EXPIRED_IN_MINUTES');
        const linkExpiredInMinutes = linkExpiredInRaw ? Number(linkExpiredInRaw) : 15;
        const paymentRequestExpiredInMinutes = paymentRequestExpiredInRaw ? Number(paymentRequestExpiredInRaw) : 30;

        await this.redisService.set(redisKey, paymentLink, 'EX', getExpiredTime('min', linkExpiredInMinutes));
        // lưu payment request cũ, để khi tạo link mới thì cancelPaymentRequest cũ đi
        await this.redisService.set(
          redisKey + '-PaymentRequestCode',
          paymentRequestCode,
          'EX',
          getExpiredTime('min', paymentRequestExpiredInMinutes),
        );
      }

      // update pttt vào ecomOrder
      const paymentMethodCurrent = (
        data?.vouchers?.length ? [PaymentMethod.ZALO_PAY, PaymentMethod.VOUCHER] : [PaymentMethod.ZALO_PAY]
      ) as number[];

      const vouchers = await this.processVoucherPayload(data.orderCode, data?.vouchers);
      await this.ecomOrderService.update(data.orderCode, {
        paymentMethod: paymentMethodCurrent,
        lastestPayment: {
          paymentMethods: paymentMethodCurrent,
          orderStatus: +orderData.orderStatus,
          currentPaymentRequestCode: orderData?.paymentRequestCode,
        },
        vouchers,
      });
      return await this.paymentLinkService.sendSMSPaymentLinkToCustomer({
        orderCode: orderData.orderCode,
        insideId: data.inside,
        paymentLink: paymentLink,
        phone: orderData.phone,
        paymentType: PaymentMethod.ZALO_PAY,
        orderData: orderData,
        partnerId: data.partnerId,
      });
    } catch (error) {
      throw error;
    }
  }

  async sendMomoLink(data: SendSmsPaymentDto, user?: UserDto): Promise<any> {
    try {
      const orderData = await this.omsService.getOneOrder(data.orderCode);

      let paymentLink = '';
      const redisKey = `CreatedPaymentLink:${convertPaymentMethodToName(PaymentMethod.MOMO)}:${data.orderCode}`;

      const createdPaymentLink = await this.redisService.get(redisKey);
      if (createdPaymentLink) {
        paymentLink = createdPaymentLink;
      } else {
        const oldPaymentRequestCode = await this.redisService.get(redisKey + '-PaymentRequestCode');
        if (oldPaymentRequestCode) {
          // cancel payment request code cux
          this.paymentGatewayService.cancelPaymentRequestCode(oldPaymentRequestCode);
        }
        const { accountId, paymentRequestCode } = await this.paymentLinkService.getDataForPaymentLinkGenerate(
          orderData,
        );
        paymentLink = await this.paymentLinkService.genMomoLink(
          paymentRequestCode,
          accountId,
          data,
          orderData,
          user.employee_code,
        );

        const linkExpiredInRaw = this.configService.get('REDIS_LINK_EXPIRED_IN_MINUTES');
        const paymentRequestExpiredInRaw = this.configService.get('REDIS_PAYMENT_REQUEST_CODE_EXPIRED_IN_MINUTES');
        const linkExpiredInMinutes = linkExpiredInRaw ? Number(linkExpiredInRaw) : 15;
        const paymentRequestExpiredInMinutes = paymentRequestExpiredInRaw ? Number(paymentRequestExpiredInRaw) : 30;

        await this.redisService.set(redisKey, paymentLink, 'EX', getExpiredTime('min', linkExpiredInMinutes));
        // lưu payment request cũ, để khi tạo link mới thì cancelPaymentRequest cũ đi
        await this.redisService.set(
          redisKey + '-PaymentRequestCode',
          paymentRequestCode,
          'EX',
          getExpiredTime('min', paymentRequestExpiredInMinutes),
        );
      }

      // update pttt vào ecomOrder
      const paymentMethodCurrent = (
        data?.vouchers?.length ? [PaymentMethod.MOMO, PaymentMethod.VOUCHER] : [PaymentMethod.MOMO]
      ) as number[];
      await this.ecomOrderService.update(data.orderCode, {
        paymentMethod: paymentMethodCurrent,
        lastestPayment: {
          paymentMethods: paymentMethodCurrent,
          orderStatus: +orderData.orderStatus,
          currentPaymentRequestCode: orderData?.paymentRequestCode,
        },
      });
      return await this.paymentLinkService.sendSMSPaymentLinkToCustomer({
        orderCode: orderData.orderCode,
        insideId: data.inside,
        paymentLink: paymentLink,
        phone: orderData.phone,
        paymentType: PaymentMethod.MOMO,
        orderData: orderData,
        partnerId: data.partnerId,
      });
    } catch (error) {
      throw error;
    }
  }

  async sendTransferLink(data: SendSmsPaymentDto): Promise<any> {
    try {
      const orderData = await this.omsService.getOneOrder(data.orderCode);

      let paymentLink = '';
      const redisKey = `CreatedPaymentLink:${convertPaymentMethodToName(PaymentMethod.TRANSFER)}:${data.orderCode}`;
      const redisKeyAmount = `CreatedPaymentLink:${convertPaymentMethodToName(PaymentMethod.TRANSFER)}:${
        data.orderCode
      }:amount`;

      const createdPaymentLink = null; //await this.redisService.get(redisKey);
      if (createdPaymentLink) {
        paymentLink = createdPaymentLink;
      } else {
        paymentLink = await this.paymentLinkService.genTransferLink(orderData);
        await this.redisService.set(redisKey, paymentLink, 'EX', getExpiredTime('min', 15));
        this.redisService.set(redisKeyAmount, data?.amount, 'EX', getExpiredTime('min', 15));
      }

      // update pttt vào ecomOrder
      const paymentMethodCurrent = (
        data?.vouchers?.length ? [PaymentMethod.TRANSFER, PaymentMethod.VOUCHER] : [PaymentMethod.TRANSFER]
      ) as number[];

      const vouchers = await this.processVoucherPayload(data.orderCode, data?.vouchers);

      await this.ecomOrderService.update(data.orderCode, {
        paymentMethod: paymentMethodCurrent,
        paymentMethodDefault: paymentMethodCurrent,
        lastestPayment: {
          paymentMethods: paymentMethodCurrent,
          orderStatus: +orderData.orderStatus,
          currentPaymentRequestCode: orderData?.paymentRequestCode,
        },
        vouchers,
      });
      return await this.paymentLinkService.sendSMSPaymentLinkToCustomer({
        orderCode: orderData.orderCode,
        insideId: data.inside,
        paymentLink: paymentLink,
        phone: orderData.phone,
        paymentType: PaymentMethod.TRANSFER,
        orderData: orderData,
        partnerId: data.partnerId,
      });
    } catch (error) {
      throw error;
    }
  }

  async checkPaymentLink(orderCode: string) {
    return this.paymentLinkService.checkPaymentLink(orderCode);
  }

  async processVoucherPayload(orderCode: string, vouchers: any) {
    const payload = [];
    const ecomData = await this.ecomOrderService.getByOrderCode(orderCode);
    const currentVouchers = ecomData.vouchers;
    if (currentVouchers && Array.isArray(currentVouchers)) {
      payload.push(...currentVouchers);
    }

    if (vouchers && Array.isArray(vouchers)) {
      payload.push(...vouchers);
    }

    return uniqBy(payload, 'voucherCode');
  }

  /**
   * @TODO verifyVoucherGotit
   */
  async verifyVoucherGotit(param: VerifyVoucherGotitLibDto) {
    return this.paymentGatewayService.verifyVoucherGotit(param);
  }

  /**
   * @TODO verifyVoucherGotit
   */
  async verifyVoucherUrbox(param: VerifyVoucherUrboxLibDto) {
    const orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    return await this.paymentGatewayService.verifyVoucherUrboxV2({ ...param, channel: orderChannel });
  }
}
