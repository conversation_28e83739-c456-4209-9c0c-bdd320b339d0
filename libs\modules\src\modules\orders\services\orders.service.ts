import { ReasonsService } from '@libs/modules/reasons/services/reasons.service';
import { HttpStatus, Inject, Injectable, Logger, LoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import {
  concurrentPromise,
  ErrorCode,
  formatCurrency,
  getExpiredTime,
  getStartDate,
  getStartDateV2,
  IAuthUser,
  IError,
  isSameDate,
  ITEM_SERVICE_FEE,
  ITEM_VACCINE_SO1,
  OrderChannels,
  parseJson,
  PARTIAL_PAYMENT_REDIS_KEY,
  RedisService,
  removeVietnameseTones,
  SystemException,
  TICKET_CREATE_KEY,
} from '@shared';
import { ErrorCodeV2 } from '@shared/common/errors/error-code-v2';
import { SystemExceptionV2 } from '@shared/common/exceptions/system.exception-v2';
import { LIST_CHANNEL_ECOM, ORDER_CHANNEL } from '@shared/common/hard-code';
import { Channel, ChannelType, EnmCartType, SystemBook, TransType } from '@shared/enum';
import { calculateTimeDifference } from '@shared/utilities/caculate-age-by-dob';
import { EcomOrderService } from 'apps/rsa-ecom/src/modules/ecom-order/services/ecom-order.service';
import { isPossibleContinueBuyingByScheduleD2 } from 'apps/rsa-ecom/src/modules/orders/utils/next-action.util';
import { Request } from 'express';
import { JSONPath } from 'jsonpath-plus';
import jwtDecode from 'jwt-decode';
import * as _ from 'lodash';
import { GetDetailsByTicketCodeDTO } from 'modules/modules/modules/orders/dto/get-details-by-ticket-code.dto';
import moment from 'moment';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { Repository } from 'typeorm';
import { CartAppService, GetCartLibResponse, ItemType, OrderAttribute } from 'vac-nest-cart-app';
import { VacContractService } from 'vac-nest-contract';
import { CustomerCoreService } from 'vac-nest-customer-core';
import { PaymentMethod } from 'vac-nest-payment-portal';

import {
  AssignRoomDtoV2,
  ClinicType,
  CreateTicketDto,
  CreateTicketRes,
  EnmAssignRule,
  EnmStatusTicket,
  ExaminationCoreService,
  PaymentType,
  ScheduleRes,
  TicketDetailAdjustDto,
  TicketDetailRes,
} from 'vac-nest-examination';
import { FamilyService, GetPersonByIdRes } from 'vac-nest-family';
import { IMSService } from 'vac-nest-ims';
import { FinishBookParams, IMSBookingService } from 'vac-nest-ims-booking';
import { GetEmployeeByCodeResponse, InsideService } from 'vac-nest-inside';
import { InvoiceAppService, InvoiceDetailRes } from 'vac-nest-invoice-app';
import {
  GetJourneyByOrderOrTicketDto,
  JourneyPaymentMethod,
  JourneyService,
  MultipleJourneyDto,
  OrdersInfo,
  TRANSACTION_TYPE,
  UpdateAmountDto,
  UpdatePartialAmountDto,
} from 'vac-nest-journey';
import { LoggerService as LoggerServiceLib } from 'vac-nest-logger';
import { LoyaltyAppService } from 'vac-nest-loyalty-app';
import { System } from 'vac-nest-loyalty-rule';
import { NotificationService } from 'vac-nest-notification';
import {
  CancelOrderLibResponse,
  CreateOrderRes,
  DetailAttachment,
  DetailLib,
  EcomDisplay,
  EmployeeLib,
  EmployeeStep,
  FinishOrderLibDto,
  GetOneOrderLibResponse,
  OMSService,
  OrderPaymentCreate,
  OrderStatus,
  OrderType,
  PayloadUpdatedStatusOrderDto,
  SearchOrderESLibDto,
  SearchOrderESLibResponse,
  UpdateOrderLibDto,
} from 'vac-nest-oms';
import { UpdateStatusByAttachmentCodeDto, VacOrderInjectionService } from 'vac-nest-order-injection';
import { CheckRuleCreateUpdateCartDto, OrderRuleEngineService } from 'vac-nest-order-rule-engine';
import { OsrService } from 'vac-nest-osr';
import {
  EWalletDetailLib,
  getDepositDetailAmount,
  getDepositedAmountByMethods,
  GetPaymentHistoryESLibResponse,
  PaymentGatewayService,
  PaymentOnlineStatus,
  RefundHomePaylaterBody,
  VoucherDetail,
  VoucherPartnerId,
} from 'vac-nest-payment-gateway';
import { PIMAppService } from 'vac-nest-pim-app';
import { CheckPromotionResponse, PromotionResponse, PromotionService } from 'vac-nest-promotion';
import { RegimenItem, RegimenService, RegimenService as RegimenServiceCore } from 'vac-nest-regimen';
import { ItemScheduleByPerson, ItemUpdateManyScheduleDto, ScheduleCoreService } from 'vac-nest-schedule';
import {
  CancelVoucherByPaymentCodeDto,
  UnbookVoucherByPaymentCodeDto,
  VoucherCoreService,
} from 'vac-nest-voucher-core';
import { CartRulesService } from '../../carts/services/carts-rule.service';
import { CustomersService } from '../../customers/services/customers.service';
import { DepositCancel } from '../../deposit-cancel/entities/depositCancel.entity';
import { FilesService } from '../../files/services/files.service';
import { RegimensService } from '../../regimens/services/regimens.service';
import { SchedulesService } from '../../schedules/services/schedules.service';
import { STATUS_TICKET } from '../../ticket/constants';
import { TicketUtilsService } from '../../ticket/services/ticket-utils.service';
import { STEP_CONTRACT, ORDER_ATTRIBUTE } from '../constants';
import { CashbackOrderStatus } from '../../cashback/constants';
import {
  CalculatePaymentLateDto,
  CalculatePaymentLateRes,
  CancelOrderDto,
  ConfirmPayDepositCancelOtp,
  ContinueBuyingRes,
  ContinueBuyingTicketCodeRes,
  CreateTicketPartialPaymentDto,
  EmployeeInfoContinueBuyingDto,
  FinishPaymentExtraDto,
  GetGiftDeliveryRes,
  GetOrderEsRes,
  IndicationButtonForDoctorDto,
  IndicationButtonForDoctorRes,
  PaymentInfoForClient,
  PaymentMethodDto,
  PlaceOrderDto,
  UpdateStatusOrderPartialPaymentDto,
  UpdateStatusOrderPayloadDto,
} from '../dto';
import { ChangeShopCodeDto } from '../dto/change-shop-code.dto';
import { PushsOrderPayloadDto } from '../dto/push-order.dto';
import { BusinessOrderService } from './business-order.services';
import { OrderUtilsService } from './order-utils.services';

import { checkWarningStockFromCart } from '../helper/intex';
import { WEEKDAY } from '../../cashback/constants';

import { convertPaymentMethodToName } from '../../../../../shared/src/utilities/convert-payment-method-name';
import { RSA_ECOM_HARD_DEFAULT_SHOP_CODE } from '@shared/common/hard-code';

class DetailAttachmentFormat extends DetailAttachment {
  sku: string;
  totalWithFee: number;
  manufactor: string;
  taxonomies: string;
  unitNameSale: string;
  vaccineName: string;
  scheduleType: string;
}

@Injectable()
export class OrdersService {
  orderChannel: string;
  token: string;

  constructor(
    @InjectRepository(DepositCancel)
    private readonly depositCancelRepository: Repository<DepositCancel>,
    private readonly omsService: OMSService,
    private readonly cartAppService: CartAppService,
    private readonly orderUtilsService: OrderUtilsService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly paymentGWService: PaymentGatewayService,
    private readonly ticketUtilsService: TicketUtilsService,
    private readonly businessOrderService: BusinessOrderService,
    private readonly promotionService: PromotionService,
    private readonly regimensService: RegimensService,
    private readonly familyCoreService: FamilyService,
    private readonly regimenCoreService: RegimenServiceCore,
    private readonly voucherCoreService: VoucherCoreService,
    private readonly scheduleCoreService: ScheduleCoreService,
    private readonly imsService: IMSService,
    private readonly imsBookingService: IMSBookingService,
    private ecomOrderService: EcomOrderService,
    private vacOrderInjectionService: VacOrderInjectionService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly journeyCoreService: JourneyService,
    private readonly cartRulesService: CartRulesService,
    private readonly loyaltyAppService: LoyaltyAppService,
    private readonly scheduleService: SchedulesService,
    private readonly customerCoreService: CustomerCoreService,
    private readonly filesService: FilesService,
    private readonly pimAppService: PIMAppService,
    private readonly vacContractService: VacContractService,
    private readonly invoiceAppService: InvoiceAppService,
    private readonly redisService: RedisService,
    private readonly insideService: InsideService,
    private readonly customerService: CustomersService,
    private readonly orderRuleEngineService: OrderRuleEngineService,
    private readonly osrService: OsrService,
    protected readonly regimenService: RegimenService,
    private readonly loggerServiceLib: LoggerServiceLib,
    private readonly reasonService: ReasonsService,
    private readonly notificationService: NotificationService,
    private readonly configService: ConfigService,
  ) {
    this.orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    this.token = (this.req.headers?.authorization as string) || (this.req.headers?.Authorization as string) || '';
  }

  private async checkOpenOrderSkuExist(cart: GetCartLibResponse, excludeOrderCodes: string[] = []) {
    const customerLcvId = cart?.listCartSelected?.find((item) => item.itemType === 1)?.lcvId || null;
    if (!customerLcvId) {
      return true;
    }
    const skusInCart = _.compact(cart.listCartSelected.map((item) => item.productInfo?.sku));

    // get all of sku bought in order (status = 1)
    const { items: orders } = await this.journeyCoreService.getOrderInfoEcom({
      orderStatus: [1],
      sources: ['1', '6', '7', '8'],
      totalSize: 100,
      keyWord: customerLcvId,
    });
    const orderCodes = _.compact((orders || []).map((order) => order.orderCode));
    const orderDetails = await Promise.all(
      orderCodes
        .filter((orderCodeItem) => !excludeOrderCodes.includes(orderCodeItem))
        .map((orderCode) => this.omsService.getOneOrder(orderCode)),
    );
    const skuInOrders = [];
    for (const orderDetail of orderDetails) {
      for (const detail of orderDetail.details) {
        for (const detailAttachment of detail.detailAttachments) {
          if (detailAttachment.itemCode) {
            skuInOrders.push({
              orderCode: orderDetail.orderCode,
              itemCode: detailAttachment.itemCode,
            });
          }
        }
      }
    }

    const allSkuInOrder = skuInOrders.map((skuInOrder) => skuInOrder.itemCode);
    const existedSkus = skusInCart.filter((skuInCart) => {
      return allSkuInOrder.includes(skuInCart);
    });

    if (existedSkus.length > 0) {
      const orderSameSku = _.uniq(
        skuInOrders.filter((skuInOrder) => existedSkus.includes(skuInOrder.itemCode)).map((item) => item.orderCode),
      );

      throw new SystemExceptionV2({
        code: ErrorCodeV2.RSA_ECOM_BLOCK_OPEN_ORDER_SAME_SKU,
        message: ErrorCodeV2.getError(ErrorCodeV2.RSA_ECOM_BLOCK_OPEN_ORDER_SAME_SKU).message.replace(
          ':orderCode',
          orderSameSku.join(', '),
        ),
        details: ErrorCodeV2.getError(ErrorCodeV2.RSA_ECOM_BLOCK_OPEN_ORDER_SAME_SKU).message.replace(
          ':orderCode',
          orderSameSku.join(', '),
        ),
      });
    }

    return true;
  }

  async placeOrder(placeOrderDto: PlaceOrderDto, isCheckRuleMaxInjection?: boolean) {
    /**
     * @TODO
     *  - Lấy cart (xử lý tạo đơn)
     *  - Tạo paymentCode
     *  - Tạo đơn
     *  - Cập nhật orderCode vào cart
     *  - Tạo Cart Confirm
     */
    const orderChannel = this.req['headers']?.['order-channel'] as string;
    this.orderUtilsService.blockAdjustTicketWithAnotherVaccine({
      orderChannel,
      createTicketDto: placeOrderDto?.createTicketDto,
    });
    let getCart = await this.cartAppService.getCart(placeOrderDto);
    const arrLcvIdUserInjection: Array<string> = _.uniq(
      _.compact(
        JSONPath({
          json: getCart?.listCartSelected,
          path: '$[*]..lcvId',
        }),
      ),
    );
    if (arrLcvIdUserInjection?.length) {
      const arrOrderInjection = await this.journeyCoreService.getOrderAffiliateInfoByLcvId(
        arrLcvIdUserInjection?.at(0),
      );
      const { items } = await this.journeyCoreService.getOrderInfo({
        keyWord: arrLcvIdUserInjection?.at(0),
        orderStatus: [OrderStatus.Confirmed],
        sources: [...OrderChannels.WEB_APP],
        ecomDisplay: [String(EcomDisplay.AtOnline)],
      });
      const itemCarts = getCart?.listCartSelected?.map((item) => item?.itemCart);
      await this.orderUtilsService.checkHandlePriorityOrder(
        arrOrderInjection,
        getCart?.headerData?.orderAttribute,
        itemCarts,
        null,
        items,
      );
    }

    if (getCart?.headerData?.orderAttribute === OrderAttribute.PRE_ORDER) {
      const arrRegimenCart: string[] = _.compact(
        _.uniq(
          getCart?.listCartSelected?.map((e) => {
            if (!e.objectGroupId && _.min(e?.orderInjections) === 1) {
              return e?.regimenId;
            }
          }),
        ),
      );
      if (arrRegimenCart?.length) {
        const checkRuleDto: CheckRuleCreateUpdateCartDto = {
          DOB: null,
          items: arrRegimenCart?.map((e) => ({ regimenId: e })),
        };
        await this.cartRulesService._checkRuleCreateUpdateCartPreOrder(
          checkRuleDto,
          getCart?.listCartSelected?.map((e) => e?.lcvId) || [],
        );
      }
      await this.orderRuleEngineService.checkRulePreOrder({
        items: getCart?.listCartSelected?.map((cartItem) => ({
          sku: cartItem?.itemCart,
          quantity: cartItem?.quantity,
        })),
        lcvIds: [getCart?.listCartSelected?.at(0)?.lcvId],
      });
    }

    // check max injection
    const listCartSelectedProduct = getCart?.listCartSelected?.filter(
      (itemCart) => itemCart?.itemType === ItemType.Product,
    );
    if (listCartSelectedProduct?.length && isCheckRuleMaxInjection) {
      const lstRuleMaxInjection = await this.orderRuleEngineService.checkRuleMaxInjection({
        items: listCartSelectedProduct?.map((cartItem) => ({
          sku: cartItem?.itemCart,
          quantity: cartItem?.quantity,
          regimenId: cartItem?.regimenId,
        })),
        lcvId: listCartSelectedProduct?.at(0)?.lcvId,
      });
      const restrictMaxInjection = lstRuleMaxInjection?.items?.find((injection) => injection?.isRestrict);
      if (restrictMaxInjection) {
        throw new SystemException(
          {
            code: ErrorCode.RULE_MAX_INJECTION_DISEASE,
            message: lstRuleMaxInjection?.msg,
            details: lstRuleMaxInjection?.msg,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    if (OrderChannels.RSA_ECOM.includes(this.orderChannel)) {
      await this.checkOpenOrderSkuExist(getCart);
    }

    // Check rule stock FV-14084
    checkWarningStockFromCart(getCart, orderChannel);
    // End Check rule stock

    this.orderUtilsService.filterDupTicketSchedule(placeOrderDto?.createTicketDto);
    if (!OrderChannels.RSA_AFFILIATE.includes(orderChannel)) {
      await this.orderUtilsService.validateSellRestrictProducts(
        getCart?.listCartSelected,
        ErrorCode.RSA_SKU_SELL_RESTRICT_AT_PLACE_ORDER,
        placeOrderDto?.createTicketDto,
      );
    }

    const { cartNew, isRemoveLoyalty } = await this.orderUtilsService.checkRuleLoyaltyGreaterThanTotalOrder(
      getCart,
      placeOrderDto,
    );
    if (isRemoveLoyalty) {
      getCart = cartNew;
    }

    const { loyalty, checkPromotion } = getCart;

    /**
     * @TODO check phone number
     */
    if (!placeOrderDto.phoneNumber && !getCart?.headerData?.phoneNumber) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_PHONE_EMPTY,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    /**
     * @TODO check rule age
     */
    const arrRegimen: string[] = _.compact(
      _.uniq(
        getCart?.listCartSelected?.map((e) => {
          if (_.min(e?.orderInjections) === 1) {
            return e?.regimenId;
          }
        }),
      ),
    );
    // if (arrRegimen?.length) {
    //   const checkRuleDto: CheckRuleCreateUpdateCartDto = {
    //     DOB: null,
    //     items: arrRegimen.map((e) => ({ regimenId: e })),
    //   };
    //   await this.cartRulesService._checkRuleCreateUpdateCart(
    //     checkRuleDto,
    //     getCart?.listCartSelected.map((e) => e.lcvId),
    //   );
    // }

    const checkPromotionDto: CheckPromotionResponse = parseJson(checkPromotion || '{}');

    if (!_.isEmpty(checkPromotionDto)) {
      await this.promotionService.checkPromotion({
        ...checkPromotionDto,
        sessionId: placeOrderDto.sessionId,
        orderType: placeOrderDto.orderType.toString(),
      });
    }

    // Lấy danh sách những promotion có schemaCode
    const schemaPromotions: PromotionResponse[] = [];
    checkPromotionDto?.items?.forEach((itemPromotion) => {
      itemPromotion?.promotions?.forEach((entry) => {
        if (
          entry?.paymentMethods?.find((paymentmethod) => paymentmethod?.schemeCode) &&
          entry?.allowSelect &&
          entry?.matching &&
          entry?.applied
        ) {
          schemaPromotions.push(entry);
        }
      });
    });
    checkPromotionDto?.promotions?.forEach((entry) => {
      if (
        entry?.paymentMethods?.find((paymentmethod) => paymentmethod?.schemeCode) &&
        entry?.allowSelect &&
        entry?.matching &&
        entry?.applied
      ) {
        schemaPromotions.push(entry);
      }
    });

    const createOrderDto = await this.orderUtilsService.createUpdateOrderDto(placeOrderDto, getCart, null, true);

    //
    if (OrderChannels.RSA_ECOM.includes(orderChannel)) {
      const arrAppointmentDateDate: string[] = _.uniq(
        JSONPath({
          json: createOrderDto,
          path: '$..appointmentDate',
        })?.map((e: string) => moment(e).format('YYYY-MM-DD')),
      );
      if (
        (arrAppointmentDateDate?.length === 1 || arrAppointmentDateDate?.find((e) => e === null)) &&
        getCart?.headerData?.orderAttribute === 4
      ) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_CHAN_KHONG_MUI_TIEM_SAU,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    /**
     * https://reqs.fptshop.com.vn/browse/FV-13047
     * chặn create/update order với sp hàng khan hiếm và tạm hết hàng
     */
    if (OrderChannels.RSA_AFFILIATE.includes(orderChannel)) {
      await this.orderUtilsService.validateSellRestrictStockProducts(getCart?.listCartSelected);
    }

    const createOrder = await this.omsService.createOrder({
      ...createOrderDto,
      urlImagePriceProduct: placeOrderDto?.urlImagePriceProduct,
    });
    const { errorVoucher, voucher } = await this.orderUtilsService.generateVoucherCreateUpdateOrder(
      createOrder,
      getCart,
      placeOrderDto,
    );

    await this.cartAppService.updateOrderCode(placeOrderDto.sessionId, {
      orderCode: createOrder.orderCode,
      paymentCode: createOrder.paymentRequestCode,
    });

    await this.orderUtilsService.addCartConfirmUtil(
      placeOrderDto?.sessionId,
      createOrder,
      loyalty,
      checkPromotion,
      getCart?.listCartSelected,
      voucher,
      getCart?.calculatorPriceInfo,
    );

    if (
      OrderChannels.RSA_ECOM.includes(this.orderChannel) ||
      [ORDER_CHANNEL.APP_CART, ORDER_CHANNEL.WEB_CART].includes(placeOrderDto?.source)
    ) {
      await this.ecomOrderService.create({
        orderCode: createOrder.orderCode,
        source: placeOrderDto.source,
      });
    }
    if (placeOrderDto?.createTicketDto?.length) {
      this.redisService.set(
        `${TICKET_CREATE_KEY}:${createOrder?.orderCode}`,
        JSON.stringify(placeOrderDto?.createTicketDto),
        'EX',
        getExpiredTime('day', 10),
      );
    }

    return {
      ...createOrder,
      isRemoveLoyalty: isRemoveLoyalty,
      errorVoucher,
      voucher,
      totalVouchers: getCart.vouchers,
      voucherGenerate: getCart.voucherGenerate || null,
      schemaPromotions,
      preOrderPhaseId: String(getCart?.preOrderDepositAmount?.['phaseId']),
    };
  }

  async updatePlaceOrder(orderCode: string, placeOrderDto: PlaceOrderDto, isCheckRuleMaxInjection?: boolean) {
    let createTicketDto = placeOrderDto?.createTicketDto || [];
    const orderChannel = this.req['headers']?.['order-channel'] as string;

    /**
     * @link https://reqs.fptshop.com.vn/browse/FV-12333
     * với màn hình chi tiết thì overwrite ticket của phiếu
     */
    if (placeOrderDto?.screenType === 'DETAIL_ORDER' && OrderChannels.RSA.includes(orderChannel)) {
      const ticket = await this.examinationCoreService.getTicket({ ticketCode: createTicketDto?.at(0)?.ticketCode });
      if (ticket) {
        createTicketDto = [ticket];
        placeOrderDto.createTicketDto = [ticket];
      }
    }

    this.orderUtilsService.blockAdjustTicketWithAnotherVaccine({
      orderChannel,
      createTicketDto: createTicketDto,
    });
    /**
     * @TODO
     *  - Lấy cart (xử lý tạo đơn)
     *  - Tạo paymentCode
     *  - Cập đơn
     *  - Huỷ cart confirm
     *  - Tạo Cart Confirm
     */
    // eslint-disable-next-line prefer-const
    let [getCart, getOrder] = await concurrentPromise(
      this.cartAppService.getCart(placeOrderDto),
      this.omsService.getOneOrder(orderCode),
    );

    // check max injection
    /**
     * @link https://reqs.fptshop.com.vn/browse/FV-12095
     * nếu không phải màn hình detail lúc thu thêm thì chặn
     */
    if (placeOrderDto?.screenType !== 'DETAIL_ORDER') {
      const listCartSelectedProduct = getCart?.listCartSelected?.filter(
        (itemCart) => itemCart?.itemType === ItemType.Product,
      );
      if (listCartSelectedProduct?.length && isCheckRuleMaxInjection) {
        const lstRuleMaxInjection = await this.orderRuleEngineService.checkRuleMaxInjection({
          items: listCartSelectedProduct?.map((cartItem) => ({
            sku: cartItem?.itemCart,
            quantity: cartItem?.quantity,
            regimenId: cartItem?.regimenId,
          })),
          lcvId: listCartSelectedProduct?.at(0)?.lcvId,
        });
        const restrictMaxInjection = lstRuleMaxInjection?.items?.find((injection) => injection?.isRestrict);
        if (restrictMaxInjection) {
          throw new SystemException(
            {
              code: ErrorCode.RULE_MAX_INJECTION_DISEASE,
              message: lstRuleMaxInjection?.msg,
              details: lstRuleMaxInjection?.msg,
            },
            HttpStatus.FORBIDDEN,
          );
        }
      }
    }

    // Check rule stock FV-14084
    checkWarningStockFromCart(getCart, orderChannel);
    // End Check rule stock

    const totalPaymentAmount = getOrder?.orderPaymentCreate
      ?.filter((e) => e.paymentStatus === 4 && e.paymentType === 1)
      .reduce((prev, curr) => {
        return (prev += curr?.paymentAmount);
      }, 0);

    /**
     * @TODO hiện tại phí chênh lệch < 0 vẫn giữ message như cũ
     * thêm phí chênh lệch = 0 thì message khác => BA CF
     */
    const arrPaymentRequestCode =
      (getOrder?.orderPaymentCreate?.length && getOrder?.orderPaymentCreate?.map((i) => i?.paymentCode)) || [];
    const resPayDeposit = await this.paymentGWService.getPaymentRedis({
      paymentCodes: arrPaymentRequestCode,
    });

    const totalAllTransaction =
      (resPayDeposit?.length &&
        resPayDeposit?.reduce((acc, cur) => {
          const caculItemTras = getDepositDetailAmount(cur?.detail);
          return acc + caculItemTras;
        }, 0)) ||
      0;

    if (
      ([...OrderChannels.RSA_AFFILIATE, ...OrderChannels.WEB_APP].includes(getOrder?.orderChanel) ||
        OrderAttribute.PRE_ORDER === getOrder?.orderAttribute) &&
      // hoàn tất cọc
      getOrder?.orderStatus === OrderStatus.FinishDeposit &&
      // nó applied voucher vì vậy cần check thêm cart có âm tiền hay ko
      getCart?.calculatorPriceInfo?.remainingAmount <= 0
    ) {
      // Nếu nguồn Ecom thì chỉ adjust đơn thoy. k Adjust gì hết
      if (OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string)) {
        const dto = await this.orderUtilsService.getAdjustDto(placeOrderDto, getCart, getOrder);
        const orderAdjust = await this.omsService.updateOrder(orderCode, {
          ...dto,
          orderID: getOrder?.orderID,
          orderVersion: getOrder?.orderVersion,
          orderCode: orderCode,
          modifiedBy: placeOrderDto?.employeeCode,
          modifiedByName: placeOrderDto?.employeeName,
        });
        return orderAdjust;
      }

      const msg = !totalAllTransaction
        ? ErrorCode.getError(ErrorCode.RSA_ORDER_ADJUST_SAME_MONEY)
        : ErrorCode.getError(ErrorCode.RSA_ORDER_ADJUST_LESS_MONEY);
      const exception: IError = {
        code: ErrorCode.RSA_ORDER_ADJUST_LESS_MONEY,
        message: msg,
        details: msg,
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    if (
      getCart?.headerData?.orderAttribute === OrderAttribute.PRE_ORDER &&
      getOrder?.orderStatus === OrderStatus.Confirmed
    ) {
      const arrRegimenCart: string[] = _.compact(
        _.uniq(
          getCart?.listCartSelected?.map((e) => {
            if (!e.objectGroupId && _.min(e?.orderInjections) === 1) {
              return e?.regimenId;
            }
          }),
        ),
      );
      if (arrRegimenCart?.length) {
        const checkRuleDto: CheckRuleCreateUpdateCartDto = {
          DOB: null,
          items: arrRegimenCart?.map((e) => ({ regimenId: e })),
        };
        await this.cartRulesService._checkRuleCreateUpdateCartPreOrder(
          checkRuleDto,
          getCart?.listCartSelected?.map((e) => e?.lcvId) || [],
        );
      }
      await this.orderRuleEngineService.checkRulePreOrder({
        items: getCart?.listCartSelected?.map((cartItem) => ({
          sku: cartItem?.itemCart,
          quantity: cartItem?.quantity,
        })),
        lcvIds: [getCart?.listCartSelected?.at(0)?.lcvId],
      });
    }

    if (getOrder?.orderStatus === OrderStatus.Cancel) {
      const exception: IError = {
        code: ErrorCode.RSA_BLOCK_PLACE_WITH_CANCEL_STATUS,
        message: ErrorCode.getError(ErrorCode.RSA_BLOCK_PLACE_WITH_CANCEL_STATUS),
        details: ErrorCode.getError(ErrorCode.RSA_BLOCK_PLACE_WITH_CANCEL_STATUS),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    if (OrderChannels.RSA_ECOM.includes(this.orderChannel)) {
      const arrLcvIdUserInjection: Array<string> = _.uniq(
        _.compact(
          JSONPath({
            json: getCart?.listCartSelected,
            path: '$[*]..lcvId',
          }),
        ),
      );
      const arrOrderInjection = await this.journeyCoreService.getOrderAffiliateInfoByLcvId(
        arrLcvIdUserInjection?.at(0),
      );
      const itemCarts = getCart?.listCartSelected?.map((item) => item?.itemCart);

      const { items } = await this.journeyCoreService.getOrderInfo({
        keyWord: arrLcvIdUserInjection?.at(0),
        orderStatus: [OrderStatus.Confirmed],
        sources: [...OrderChannels.WEB_APP],
        ecomDisplay: [String(EcomDisplay.AtOnline)],
      });

      await this.orderUtilsService.checkHandlePriorityOrder(
        arrOrderInjection,
        getCart?.headerData?.orderAttribute,
        itemCarts,
        getOrder,
        items,
      );

      if (!(EcomDisplay.AtOnline === getOrder?.ecomDisplay && OrderChannels.WEB_APP.includes(getOrder?.orderChanel))) {
        await this.checkOpenOrderSkuExist(getCart, [orderCode]);
      }
    }

    this.orderUtilsService.filterDupTicketSchedule(createTicketDto);
    if (!OrderChannels.RSA_AFFILIATE.includes(orderChannel)) {
      await this.orderUtilsService.validateSellRestrictProducts(
        getCart?.listCartSelected,
        ErrorCode.RSA_SKU_SELL_RESTRICT_AT_PLACE_ORDER,
        createTicketDto,
      );
    }

    /**
     * https://reqs.fptshop.com.vn/browse/FV-13047
     * chặn create/update order với sp hàng khan hiếm và tạm hết hàng
     */
    if (OrderChannels.RSA_AFFILIATE.includes(orderChannel)) {
      await this.orderUtilsService.validateSellRestrictStockProducts(getCart?.listCartSelected);
    }

    const { cartNew, isRemoveLoyalty } = await this.orderUtilsService.checkRuleLoyaltyGreaterThanTotalOrder(
      getCart,
      placeOrderDto,
    );
    if (isRemoveLoyalty) {
      getCart = cartNew;
    }

    if (!placeOrderDto.phoneNumber && !getCart?.headerData?.phoneNumber) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_PHONE_EMPTY,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    if (!getCart?.listCartSelected?.length) {
      let exception: IError = {
        code: ErrorCode.RSA_CART_ORDER,
        message: ErrorCode.getError(ErrorCode.RSA_CART_ORDER),
        details: ErrorCode.getError(ErrorCode.RSA_CART_ORDER),
        validationErrors: null,
      };

      if (getOrder?.orderStatus === OrderStatus.Confirmed) {
        exception = {
          code: ErrorCode.RSA_CART_ORDER_ORDER_STATUS_CONFIRM,
          message: ErrorCode.getError(ErrorCode.RSA_CART_ORDER_ORDER_STATUS_CONFIRM),
          details: ErrorCode.getError(ErrorCode.RSA_CART_ORDER_ORDER_STATUS_CONFIRM),
          validationErrors: null,
        };
      }

      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    /**
     * @TODO check rule age
     */
    const arrRegimen: string[] = _.compact(
      _.uniq(
        getCart?.listCartSelected?.map((e) => {
          if (_.min(e?.orderInjections) === 1) {
            return e?.regimenId;
          }
        }),
      ),
    );
    // if (arrRegimen?.length) {
    //   const checkRuleDto: CheckRuleCreateUpdateCartDto = {
    //     DOB: null,
    //     items: arrRegimen?.map((e) => ({ regimenId: e })),
    //   };
    //   await this.cartRulesService._checkRuleCreateUpdateCart(
    //     checkRuleDto,
    //     getCart?.listCartSelected.map((e) => e.lcvId),
    //   );
    // }

    const checkPromotionDto: CheckPromotionResponse = parseJson(getCart.checkPromotion || '{}');
    if (!_.isEmpty(checkPromotionDto)) {
      await this.promotionService.checkPromotion({
        ...checkPromotionDto,
        sessionId: placeOrderDto.sessionId,
        orderType: placeOrderDto.orderType.toString(),
      });
    }

    let isStatus5 = false;
    if (createTicketDto?.at(0)?.ticketCode) {
      const ticket = await this.examinationCoreService.getTicket({
        ticketCode: createTicketDto?.at(0)?.ticketCode,
      });
      isStatus5 = !!ticket?.indications?.find((e) => e.status === 5);
    }

    const arrPaymentCode3 = _.uniq(
      _.compact(
        getOrder?.orderPaymentCreate?.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );

    let createOrderDto = null;
    // check tien chênh lệch
    if (
      ([...OrderChannels.RSA_AFFILIATE, ...OrderChannels.WEB_APP].includes(getOrder?.orderChanel) ||
        OrderAttribute.PRE_ORDER === getOrder?.orderAttribute) &&
      // hoàn tất cọc
      getOrder?.orderStatus === OrderStatus.FinishDeposit &&
      // chênh lệch tiền
      getCart?.calculatorPriceInfo?.totalBill > totalPaymentAmount &&
      // k có payment mở
      !getOrder?.orderPaymentCreate?.find((e) => e.paymentType === 1 && e.paymentStatus !== 4)
    ) {
      let arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getPaymentRedis({
        paymentCodes: arrPaymentCode3,
      });

      await this.businessOrderService.forceCancel(
        isStatus5,
        getCart,
        arrPaymentES,
        getOrder['orderPaymentCreate'],
        getOrder,
        placeOrderDto?.shopCode,
        placeOrderDto?.employeeCode,
      );
      // sau khi cancel voucher thì đi get lại lấy tổng tiền
      arrPaymentES = await this.paymentGWService.getPaymentRedis({
        paymentCodes: arrPaymentCode3,
      });

      const totalDeposited = arrPaymentES?.reduce((prev, curr) => {
        return prev + getDepositDetailAmount(curr?.detail);
      }, 0);

      createOrderDto = await this.orderUtilsService.createUpdateOrderDto(
        placeOrderDto,
        {
          ...getCart,
          calculatorPriceInfo: {
            ...getCart.calculatorPriceInfo,
            totalBill: getCart?.calculatorPriceInfo?.totalBill - totalDeposited,
          },
        },
        getOrder,
        true,
        false,
      );
    } else {
      createOrderDto = await this.orderUtilsService.createUpdateOrderDto(
        placeOrderDto,
        getCart,
        getOrder,
        false,
        true,
        createTicketDto?.at(0)?.ticketCode,
      );
    }

    if (OrderChannels.RSA_ECOM.includes(orderChannel)) {
      const arrAppointmentDateDate: string[] = _.uniq(
        JSONPath({
          json: createOrderDto,
          path: '$..appointmentDate',
        })?.map((e: string) => moment(e).format('YYYY-MM-DD')),
      );
      if (
        (arrAppointmentDateDate?.length === 1 || arrAppointmentDateDate?.find((e) => e === null)) &&
        getCart?.headerData?.orderAttribute === 4
      ) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_CHAN_KHONG_MUI_TIEM_SAU,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    const updateOrder = await this.businessOrderService.handleFlowUpdateOrder(
      getOrder,
      createOrderDto,
      getCart,
      placeOrderDto,
    );

    // @TODO Bỏ vì liên quan version của ticket
    // orderChanel ecom && ecomDisplay = 1
    // if (createTicketDto?.length && updateOrder && updateOrder?.orderChanel === '7' && updateOrder?.ecomDisplay === 1) {
    //   // call adjustTicket xét kênh ecom
    //   const updateTicketDto = await this.ticketUtilsService.createAdjustTicketDtoV2([createTicketDto?.at(0)], getOrder);
    //   await this.examinationCoreService.adjustTicket(
    //     {
    //       ticketCode: createTicketDto?.at(0)?.ticketCode,
    //     },
    //     {
    //       ...updateTicketDto?.at(0),
    //       ticketType: updateTicketDto?.at(0)?.ticketType,
    //     },
    //   );
    // }
    // đã thanh toán ví rồi thì remove schemeCode
    const orderPaymentCreate = updateOrder?.orderPaymentCreate?.map((e) => e.paymentCode);
    const arrPaymentES3 = await this.paymentGWService.getPaymentRedis({
      paymentCodes: orderPaymentCreate,
    });
    const eWalletDetail: Array<EWalletDetailLib> = JSONPath({
      path: '$.[*].detail.eWalletAll[*].eWalletDetail',
      json: arrPaymentES3,
    });
    const eWalletDetailOnlineAll: Array<EWalletDetailLib> = JSONPath({
      path: '$.[*].detail.eWalletOnlineAll[*].eWalletDetail',
      json: arrPaymentES3,
    });
    const useInWallet =
      eWalletDetail.concat(eWalletDetailOnlineAll)?.filter((e) => e.amount !== e.realAmount)?.length > 0 ? true : false;
    const schemaPromotions = [];
    // chưa thanh toán mới trả ra. Thanh toán rồi k cần trả
    if (!useInWallet || !!createOrderDto?.getTicket?.orderCodeOld) {
      // Lấy danh sách những promotion có schemaCode
      checkPromotionDto?.items?.forEach((itemPromotion) => {
        itemPromotion?.promotions?.forEach((entry) => {
          if (
            entry?.paymentMethods?.find((paymentmethod) => paymentmethod?.schemeCode) &&
            entry?.allowSelect &&
            entry?.matching &&
            entry?.applied
          ) {
            schemaPromotions.push(entry);
          }
        });
      });
      checkPromotionDto?.promotions?.forEach((entry) => {
        if (
          entry?.paymentMethods?.find((paymentmethod) => paymentmethod?.schemeCode) &&
          entry?.allowSelect &&
          entry?.matching &&
          entry?.applied
        ) {
          schemaPromotions.push(entry);
        }
      });
    }
    if (placeOrderDto?.createTicketDto?.length) {
      this.redisService.set(
        `${TICKET_CREATE_KEY}:${updateOrder?.orderCode}`,
        JSON.stringify(placeOrderDto?.createTicketDto),
        'EX',
        getExpiredTime('day', 10),
      );
    }

    return {
      ...updateOrder,
      isRemoveLoyalty,
      schemaPromotions,
      preOrderPhaseId: String(getCart?.preOrderDepositAmount?.['phaseId']),
    };
  }

  /**
   * @description API Phục vụ nút chỉ định tiêm
   *  - 3 case để xử lý. BE handle cho FE
   */
  async indicationButtonForDoctor(
    ticketCode: string,
    indicationButtonForDoctorDto: IndicationButtonForDoctorDto,
  ): Promise<IndicationButtonForDoctorRes> {
    /**
     * @TODO
     *  - Case 1: Có So1 =>  flow adjust So1
     *  - Case 2: Không có So1 và trong cart k có => flow chỉ định không adjust/create so1
     *  - Case 3: Không có So1 và trong cart có => flow tạo So1
     *  - Case mới: Đơn trả chậm
     */
    const {
      createTicketDto,
      placeOrderDto,
      createDetailMaterialDto,
      ticketVersion,
      ticketCode: ticketCodeIndication,
    } = indicationButtonForDoctorDto;

    indicationButtonForDoctorDto.createTicketDto = await this.orderUtilsService._mappingOrderInjection(createTicketDto);
    placeOrderDto.createTicketDto = indicationButtonForDoctorDto.createTicketDto;

    if (!createTicketDto?.length) {
      return null;
    }

    // gán ticket code cho create ticket Dto
    indicationButtonForDoctorDto.createTicketDto.at(0).ticketCode = ticketCode;
    // tách luồng Thanh toán chậm
    const checkStatus5 = indicationButtonForDoctorDto?.createTicketDto?.at(0)?.indications?.find((e) => e.status === 5);

    let getCart = await this.cartAppService.getCart(placeOrderDto);

    await this.orderUtilsService.validateSellRestrictProducts(
      getCart?.listCartSelected,
      ErrorCode.RSA_SKU_SELL_RESTRICT_AT_INDICATION,
      createTicketDto,
    );

    const { cartNew, isRemoveLoyalty } = await this.orderUtilsService.checkRuleLoyaltyGreaterThanTotalOrder(
      getCart,
      placeOrderDto,
    );
    if (isRemoveLoyalty) {
      getCart = cartNew;
    }

    if (!placeOrderDto.phoneNumber && !getCart?.headerData?.phoneNumber) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_PHONE_EMPTY,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    /**
     * @TODO check rule age
     */
    // const arrRegimen: string[] = _.compact(
    //   _.uniq(
    //     getCart?.listCartSelected?.map((e) => {
    //       if (_.min(e?.orderInjections) === 1) {
    //         return e?.regimenId;
    //       }
    //     }),
    //   ),
    // );
    // if (arrRegimen?.length) {
    //   const checkRuleDto: CheckRuleCreateUpdateCartDto = {
    //     DOB: null,
    //     items: arrRegimen?.map((e) => ({ regimenId: e })),
    //   };
    //   await this.cartRulesService._checkRuleCreateUpdateCart(
    //     checkRuleDto,
    //     getCart?.listCartSelected.map((e) => e.lcvId),
    //   );
    // }

    await this.businessOrderService.checkStock(createTicketDto, this.req.headers?.['shop-code'] as string);
    placeOrderDto.orderType = OrderType.OrderContract;
    let getTicket = await this.examinationCoreService.getTicket({ ticketCode });
    const ticketBeforeAdjust = { ...getTicket };

    if (checkStatus5) {
      const arrAttachmentCode = _.compact(
        _.uniq(createTicketDto?.at(0)?.indications?.map((e) => e?.orderDetailAttachmentCode)),
      );
      const { items } = await this.vacOrderInjectionService.searchByPerson({
        personCode: createTicketDto?.at(0)?.lcvId,
        skipCount: 0,
      });

      const ticketOld = items?.find((e) => arrAttachmentCode.includes(e.orderDetailAttachmentCode) && e.status === 5);
      // gán orderCodeOld vào payload
      if (!createTicketDto?.at(0)?.orderCodeOld && !createTicketDto?.at(0)?.ticketCodeOld) {
        createTicketDto.at(0).orderCodeOld = ticketOld?.orderCode || '';
        createTicketDto.at(0).ticketCodeOld = ticketOld?.ticketCode || '';
        getTicket.orderCodeOld = ticketOld?.orderCode || '';
        getTicket.ticketCodeOld = ticketOld?.ticketCode || '';
      }
    }

    // handle phiếu khám trống -> không hiển thị trên ds đơn
    const ticketOrderInfor = await this.journeyCoreService.getOrderInfoByOrderOrTicket({
      ticketCode: getTicket?.ticketCode,
    });
    const checkStatusGetTicket5 = getTicket?.indications?.find((e) => e.status === 5);
    const isNormalChangeToPartial =
      (!!checkStatus5 && !checkStatusGetTicket5) || (!ticketOrderInfor && checkStatusGetTicket5);
    const isPartialChangeToNormal =
      (!checkStatus5 && !!checkStatusGetTicket5) || (ticketOrderInfor?.orderAttribute === 4 && !checkStatus5); //  (ticketOrderInfor?.orderAttribute === 4 && !checkStatus5) => ticket từng phần (orderAttribute === 4) và mũi chỉ định k có mũi từng phần
    if ((isNormalChangeToPartial || isPartialChangeToNormal) && !getTicket?.orderCode) {
      // Hủy order info cũ
      await this.journeyCoreService.changeStatusOrderInfo({
        orderCode: null,
        ticketCode,
        modifiedBy: placeOrderDto.employeeCode,
        status: 2,
      });
      // Tạo mới order info với trường hợp từ ticket thường -> ticket đơn từng phần
      if (isNormalChangeToPartial) {
        const journeyRes = await this.journeyCoreService.getJourneyIdByTicketCode({
          ticketCode,
        });
        await this.journeyCoreService.createPartialOrderInfo({
          ticketCode,
          lcvId: createTicketDto?.[0]?.lcvId,
          shopCode: placeOrderDto?.shopCode,
          totalBill: 0,
          totalPayment: 0,
          gapAmount: 0,
          journeyId: journeyRes?.[0]?.id,
          source: '14',
          ecomDisplay: 1,
          paymentMethod: 2,
          orderAttribute: OrderAttribute.TRA_CHAM,
          orderCodeOld: createTicketDto.at(0).orderCodeOld,
          orderStatus: OrderStatus.Confirmed,
          createdBy: placeOrderDto.employeeCode,
        });
      }
    }

    // Chặn không cho update cart nếu là đơn từng phần đã thanh toán và không phải đơn mix
    if (
      getCart?.listCartSelected?.length &&
      !!checkStatus5 &&
      getTicket?.paymentType === PaymentType.PAID &&
      getTicket?.orderCodeOld
    ) {
      const exception: IError = {
        code: ErrorCode.RSA_BLOCK_UPDATA_CART,
        message: ErrorCode.getError(ErrorCode.RSA_BLOCK_UPDATA_CART),
        details: ErrorCode.getError(ErrorCode.RSA_BLOCK_UPDATA_CART),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }
    //=> chặn chỉ định không có thông tin ticket
    if (!createTicketDto?.at(0)?.schedules?.length) {
      // exception
      throw new SystemException(
        {
          code: ErrorCode.RSA_TICKET_EMPTY,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    let order: GetOneOrderLibResponse;
    if (getTicket?.orderCode) {
      order = await this.omsService.getOneOrder(getTicket.orderCode);
    }

    if (getTicket?.orderCode && !getCart?.listCartSelected?.length) {
      let exception: IError = {
        code: ErrorCode.RSA_CART_ORDER,
        message: ErrorCode.getError(ErrorCode.RSA_CART_ORDER),
        details: ErrorCode.getError(ErrorCode.RSA_CART_ORDER),
        validationErrors: null,
      };

      if (order?.orderStatus === OrderStatus.Confirmed) {
        exception = {
          code: ErrorCode.RSA_CART_ORDER_ORDER_STATUS_CONFIRM,
          message: ErrorCode.getError(ErrorCode.RSA_CART_ORDER_ORDER_STATUS_CONFIRM),
          details: ErrorCode.getError(ErrorCode.RSA_CART_ORDER_ORDER_STATUS_CONFIRM),
          validationErrors: null,
        };
      }

      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    /**
     * @TODO check rule chặn chỉ định tiêm cho phác đồ mang thai
     */
    if (createTicketDto?.at(0)?.indications?.length > 0) {
      await this.checkRuleIndicationPregnancy(createTicketDto?.at(0));
    }

    if (getTicket?.orderCode && getCart?.listCartSelected?.length) {
      const arrPaymentES3 = await this.paymentGWService.getPaymentRedis({
        paymentCodes: order?.orderPaymentCreate?.map((e) => e.paymentCode),
      });
      const eWalletDetails: Array<EWalletDetailLib> = JSONPath({
        path: '$.[*].detail.eWalletAll[*].eWalletDetail',
        json: arrPaymentES3,
      });
      const eWalletOnlineDetails: Array<EWalletDetailLib> = JSONPath({
        path: '$.[*].detail.eWalletOnlineAll[*].eWalletDetail',
        json: arrPaymentES3,
      });

      const joinArrayPayment = [
        ...eWalletDetails?.map((e) => e.typeWalletId),
        ...eWalletOnlineDetails?.map((e) => e.typeWalletId),
      ];
      // Tổng tiền thanh toán qua ví điện tử
      const totalBillInOrderPaymentEWallet =
        eWalletDetails?.reduce((acc, cur) => {
          if (cur?.amount !== cur?.realAmount) return acc + cur?.amount;
          else return acc;
        }, 0) +
        eWalletOnlineDetails?.reduce((acc, cur) => {
          if (cur?.amount !== cur?.realAmount) return acc + cur?.amount;
          else return acc;
        }, 0);

      // Kiểm tra nếu amount !== realAmount => có dùng voucher đối tác
      const isDifferenceRealAmount = [...eWalletDetails, ...eWalletOnlineDetails]?.some(
        (e) => e.amount !== e.realAmount,
      );
      if (
        joinArrayPayment?.length &&
        totalBillInOrderPaymentEWallet > getCart?.calculatorPriceInfo?.totalBill &&
        isDifferenceRealAmount
      ) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_EWALLET_RULE,
            message: ErrorCode.getError(ErrorCode.RSA_EWALLET_RULE).replace(
              '{amount}',
              formatCurrency(totalBillInOrderPaymentEWallet),
            ),
          },
          HttpStatus.FORBIDDEN,
        );
      }

      // Kiểm tra tổng tiền của voucher đối tác > total bill
      const arrVoucherProvider: number[] = [
        VoucherPartnerId.GotIT,
        VoucherPartnerId.Urbox,
        // VoucherPartnerId.Taptap,
        // VoucherPartnerId.UTop,
      ]; // Trang BA confirm là hiện tại chỉ cần got it vs UrBox

      const arrVoucherDetail: VoucherDetail[] =
        JSONPath({
          json: arrPaymentES3,
          path: '$[*]..vouchersAll[*].voucherDetail',
        })?.filter((e: VoucherDetail) => arrVoucherProvider.includes(e.voucherType)) || [];

      const totalVoucherPartner = arrVoucherDetail?.reduce((prev, curr) => {
        return prev + curr.amount;
      }, 0);

      if (totalVoucherPartner > getCart?.calculatorPriceInfo?.totalBill) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_VOUCHER_PARTNER_RULE,
            message: ErrorCode.getError(ErrorCode.RSA_VOUCHER_PARTNER_RULE),
          },
          HttpStatus.FORBIDDEN,
        );
      }

      // Enc check tổng tiền voucher

      if (+order?.orderAttribute !== +getCart?.headerData['orderAttribute']) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_CHANGE_ORDER_ATTRIBUTE,
          },
          HttpStatus.FORBIDDEN,
        );
      }
      const arrVoucherCodePayment: string[] = JSONPath({
        json: arrPaymentES3,
        path: '$[*]..voucherDetail.code',
      });

      // remove voucher trong gio
      const arrVoucherInCart: string[] = JSONPath({
        path: '$.vouchers[?(@ && @.voucherType == 1)].seriesCode',
        json: getCart,
      });

      const diff: string[] = _.difference(arrVoucherInCart, arrVoucherCodePayment);

      if (diff?.length) {
        // verify vouchers

        const verifyVoucherData = await this.voucherCoreService.verifyVoucher({
          orderCode: order.orderCode,
          phoneNumber: order.phone,
          orders: getCart.listCartSelected
            .filter((e) => e.itemType === ItemType.Product)
            .map((e) => {
              return {
                itemCode: e.itemCart,
                price: e.detailCalculatorPriceInfo.price,
                quantity: e.quantity,
                totalPriceAfterDiscount:
                  e.detailCalculatorPriceInfo.total - e.detailCalculatorPriceInfo.discountPromotion,
                totalDiscount: e.detailCalculatorPriceInfo.discountPromotion,
              };
            }),
          seriesCodes: diff,
          totalBill: order.totalBill,
          paymentCode: order.paymentRequestCode,
        });

        if (verifyVoucherData?.invalidVouchers?.length) {
          const arrVoucherError: string[] = JSONPath({
            json: verifyVoucherData,
            path: '$..errors[*]..seriesCodes[*]',
          });

          throw new SystemException(
            {
              code: ErrorCode.VOUCHER_RETURN_MONEY,
              message: ErrorCode.getError(ErrorCode.VOUCHER_RETURN_MONEY)?.replace(
                '{{voucherCodes}}',
                arrVoucherError?.join(','),
              ),
              details: ErrorCode.getError(ErrorCode.VOUCHER_RETURN_MONEY)?.replace(
                '{{voucherCodes}}',
                arrVoucherError?.join(','),
              ),
            },
            HttpStatus.FORBIDDEN,
          );
        }
      }
    }

    /**
     * check đơn đang trong trạng thái hoàn tất cọc thì không cho sửa về trả chậm
     * comment code 26/10 - lỗi chuyển đơn pre -> đơn trả chậm
     * link https://reqs.fptshop.com.vn/browse/FV-13559
     */

    const isRuleDonMix =
      order &&
      (!!checkStatus5 || getCart?.headerData?.['orderAttribute'] === 4) &&
      getTicket?.indications?.filter((e) => e?.status === 5) &&
      [7, 8].includes(+order?.orderAttribute);
    if (isRuleDonMix) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ERROR_RULE_DON_MIX,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    getTicket = await this.examinationCoreService.adjustTicket(
      {
        ticketCode: ticketCode,
      },
      {
        ...getTicket,
        status: EnmStatusTicket.CHECKED_UP,
        orderCodeOld: !checkStatus5 ? '' : getTicket?.orderCodeOld,
        ticketCodeOld: !checkStatus5 ? '' : getTicket?.ticketCodeOld,
        ticketVersion: ticketVersion,
        ticketCode: ticketCodeIndication,
        indications: createTicketDto?.at(0)?.indications,
        schedules: createTicketDto?.at(0)?.schedules,
        modifiedBy: placeOrderDto.employeeCode,
      },
    );

    // Update Thông tin phiếu khám và status thành đã khám
    // getTicket = await this.examinationCoreService.changeStatusTicket({
    //   status: EnmStatusTicket.CHECKED_UP,
    //   ticketCode: ticketCode,
    //   modifiedBy: placeOrderDto.employeeCode,
    // });

    //=> cover trường hợp tiêm lại nhưng bs méo chỉ định nữa => Return lun => Đi về
    if (!createTicketDto.at(0)?.indications?.length && !getCart?.listCartSelected?.length) {
      // regimen Close
      try {
        await this.orderUtilsService.cancelBook(placeOrderDto.shopCode, placeOrderDto.employeeCode, ticketCode);
      } catch (error) {
        Logger.error({
          fields: {
            info: '[cancelBook] handle error',
            errors: error,
          },
        });
      }
      await this.orderUtilsService.addRegimenDetailClose(createTicketDto.at(0), placeOrderDto.employeeCode);
      const adjust = await this.examinationCoreService.adjustTicket(
        { ticketCode },
        {
          ...getTicket,
          indications: [],
          status: EnmStatusTicket.DONE,
        },
      );
      return { ...adjust, paymentInfo: null, isRemoveLoyalty };
    }

    //=> Tạo thuốc hạ sốt
    if (createDetailMaterialDto?.length) {
      this.examinationCoreService.createMaterialAttachment({
        ticketCode: ticketCode,
        createdBy: placeOrderDto.employeeCode,
        details: createDetailMaterialDto,
      });
    } else {
      const materialAttachmentCurrent = getTicket?.materialAttachments
        ?.filter((entry) => entry?.status === 0)
        ?.map((material) => ({ ...material, status: 1 }));
      if (materialAttachmentCurrent?.length) {
        this.examinationCoreService.updateMaterialAttachment({
          ticketCode: ticketCode,
          modifiedBy: placeOrderDto.employeeCode,
          details: materialAttachmentCurrent,
        });
      }
    }

    //=> Get Order về lấy sp tặng =>
    //=> Xử lý cho trường hợp paymentType = PAID
    let arrGift: Partial<DetailLib>[] = [];
    if (
      getTicket?.orderCode
      // && ticketRes?.paymentType === 4
    ) {
      arrGift = cartNew?.calculatorPriceInfo?.details?.filter(
        (e) => e?.isPromotion === 'Y' && e?.isInventoryManagement === 'Y',
      );
    }

    //=> Book tồn
    if (!createTicketDto?.at(0)?.indications?.length) {
      try {
        await this.orderUtilsService.cancelBook(placeOrderDto.shopCode, placeOrderDto.employeeCode, ticketCode);
      } catch (error) {
        Logger.error({
          fields: {
            info: '[cancelBook] handle error',
            errors: error,
          },
        });
      }
      if (arrGift?.length) {
        await this.orderUtilsService.bookingInventory(placeOrderDto?.employeeCode, ticketCode, arrGift, null, '', true);
      }
    } else {
      await this.orderUtilsService.bookingInventory(placeOrderDto?.employeeCode, ticketCode, arrGift, null, '', true);
    }
    await this.cartAppService.updateOrderCode(placeOrderDto.sessionId, {
      orderCode: null,
      paymentCode: null,
      ticketCode: ticketCode,
    });

    let ticketRes: TicketDetailRes = null;

    let listGiftProduct = null;
    let isOrderNormal = false;
    switch (true) {
      // Case đơn preOrder tạo phíu khám qua bác sĩ chứ k qua thanh toán
      case order?.orderAttribute === 7 &&
        order?.orderPaymentCreate?.length === 1 &&
        order?.orderPaymentCreate?.at(0)?.paymentStatus === 1:
        const orderAfterUpdate = await this.updatePlaceOrder(order.orderCode, {
          ...placeOrderDto,
          ticketCode: ticketCode,
        });
        ticketRes = await this.businessOrderService.handleFlowIndicationPhase2CreateTicket(orderAfterUpdate, getTicket);
        break;

      // case luồng thanh toán chậm đã thanh toán
      case (!!checkStatus5 || getCart?.headerData?.['orderAttribute'] === 4) &&
        getTicket?.paymentType === PaymentType.PAID:
        ticketRes = await this.businessOrderService.handleFlowIndicationButtonCasePayLaterContinue(
          indicationButtonForDoctorDto,
          getTicket,
          getCart?.headerData,
        );
        break;
      // case luồng thanh toán chậm
      case !!checkStatus5 || getCart?.headerData?.['orderAttribute'] === 4:
        ticketRes = await this.businessOrderService.handleFlowIndicationButtonCasePayLater(
          indicationButtonForDoctorDto,
          cartNew,
          getTicket,
        );
        break;
      // Case 1:
      case !!getTicket?.orderCode:
        ticketRes = await this.businessOrderService.handleFlowIndicationButtonCase1(
          order,
          placeOrderDto,
          getCart,
          getTicket,
          createTicketDto,
          ticketBeforeAdjust,
        );
        listGiftProduct = ticketRes?.['listGiftProduct'];
        isOrderNormal = true;
        break;

      // Case 2:
      case !!!getTicket?.orderCode && getCart?.listCartSelected?.length === 0:
        ticketRes = await this.businessOrderService.handleFlowIndicationButtonCase2(
          placeOrderDto,
          ticketCode,
          createTicketDto,
        );
        isOrderNormal = true;
        break;

      // Case 3:
      case !!!getTicket?.orderCode && getCart?.listCartSelected?.length !== 0:
        const createOrderSO1 = await this.placeOrder({ ...placeOrderDto, ticketCode });
        // Tìm line sp KM có quản lý tồn kho để book tồn
        listGiftProduct = createOrderSO1?.details?.filter(
          (detailEntry) => detailEntry.isPromotion === 'Y' && detailEntry?.isInventoryManagement === 'Y',
        );
        ticketRes = await this.businessOrderService.handleFlowIndicationButtonCase3(
          createOrderSO1,
          getTicket,
          createTicketDto,
        );
        isOrderNormal = true;
        break;
    }

    // update lại luồng khi vào đơn thường
    if (isOrderNormal) {
      const updatePartialPaymentDto: UpdatePartialAmountDto = {
        modifiedBy: placeOrderDto.employeeCode,
        partialPaymentAmount: 0, // Tiền mũi tiêm dợt này
        ticketCode: getTicket?.ticketCode,
        totalBill: ticketRes?.['order']?.['totalBill'] || 0, // Tiền thanh toán hôm nay,
        orderCodeOld: '',
        orderAttribute: ticketRes?.['order']?.['orderAttribute'] || 0,
        isFinishPayment: false,
      };
      await this.journeyCoreService.updatePartialAmount(updatePartialPaymentDto);

      this.redisService.set(
        `${PARTIAL_PAYMENT_REDIS_KEY}:${ticketCode}`,
        JSON.stringify(updatePartialPaymentDto),
        'EX',
        getExpiredTime('day', 1),
      );
    }
    // update tiền chênh lệch
    if (
      ticketRes?.['order']?.['orderStatus'] === OrderStatus.FinishDeposit &&
      ticketRes?.['paymentInfo']?.remainingAmount !== 0 &&
      ticketRes?.['order']?.['orderAttribute'] !== OrderAttribute.TRA_CHAM
    ) {
      await this.journeyCoreService.updateAmount({
        gapAmount: ticketRes?.['paymentInfo']?.remainingAmount,
        ticketCode: ticketCode,
        modifiedBy: placeOrderDto.employeeCode,
        orderCode: ticketRes?.['order']?.orderCode,
        totalBill: ticketRes?.['order']?.totalBill,
      });
    }

    /**
     * @TODO PETER_20240119
     */
    // await this.inventoryService.cancelBookByTransactionCode({ transactionCode: ticketCode });
    // try {
    //   await this.orderUtilsService.cancelBook(placeOrderDto.shopCode, placeOrderDto.employeeCode, ticketCode);
    // } catch (error) {
    //   Logger.error({
    //     fields: {
    //       info: '[cancelBook] handle error',
    //       errors: error,
    //     },
    //   });
    // }
    //=> Get Order về lấy sp tặng =>
    //=> Xử lý cho trường hợp paymentType = PAID
    // let arrGift: Partial<DetailLib>[] = [];
    // if (
    //   ticketRes?.orderCode
    //   // && ticketRes?.paymentType === 4
    // ) {
    //   const getOrder = await this.omsService.getOneOrder(ticketRes?.orderCode);
    //   arrGift = getOrder?.details?.filter((e) => e?.isPromotion === 'Y' && e?.isInventoryManagement === 'Y');
    // }
    //=> Book tồn
    // await this.orderUtilsService.bookingInventory(placeOrderDto?.employeeCode, ticketCode, arrGift, null, '', true);
    //=> Insert question
    await this.examinationCoreService.insertQuestionsToTicketNote({
      personId: getTicket.personId,
      ticketCode: ticketCode,
    });

    // regimen Close
    await this.orderUtilsService.addRegimenDetailClose(createTicketDto.at(0), placeOrderDto.employeeCode);

    return { ...ticketRes, isRemoveLoyalty } as any;
  }

  /**
   * @description Tìm kiếm đơn hàng
   */
  async searchOrder(searchOrder: SearchOrderESLibDto) {
    return this.omsService.searchOrderES(searchOrder);
  }

  async searchOrderForSTC(searchOrder: SearchOrderESLibDto) {
    const { items: orders, ...otherItems } = await this.omsService.searchOrderES(searchOrder);
    const allPaymentCodes = _.uniq(
      _.compact(_.flatMap(orders, (order) => order?.orderPaymentCreate?.map((e) => e.paymentCode))),
    );
    const paymentES: GetPaymentHistoryESLibResponse[] = [];
    if (allPaymentCodes.length > 0) {
      paymentES.push(...(await this.paymentGWService.getManyPaymentHistoryES(allPaymentCodes)));
    }

    const orderResponse = [];
    for (const order of orders) {
      const orderPaymentCodes = (order?.orderPaymentCreate || [])?.map((e) => e.paymentCode);
      const totalDiscountVoucher = paymentES
        .filter((paymentItem) => orderPaymentCodes.includes(paymentItem.paymentCode))
        .reduce((acc, cur) => {
          return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
        }, 0);
      const paymentInfo = this.orderUtilsService._mapPaymentInfo(order, totalDiscountVoucher);
      orderResponse.push({
        ...order,
        totalDiscountVoucher,
        totalBillAfterVoucher: paymentInfo.totalBillAfterVoucher,
      });
    }

    return {
      ...otherItems,
      items: orderResponse,
    };
  }

  private async updatePaymentInfoFollowCart(
    paymentInfo: PaymentInfoForClient,
    getOrder: GetOneOrderLibResponse,
    totalDiscountVoucher: number,
    totalVoucherPartner: number,
  ) {
    let getCart: GetCartLibResponse;
    if (![OrderStatus.Completed, OrderStatus.FullReturn, OrderStatus.PartialReturn].includes(getOrder.orderStatus)) {
      let totalVoucher = 0;
      try {
        const cartComfirmData = await this.cartAppService.getCartConfirmByOrderCode(getOrder.orderCode);
        const sessionId = cartComfirmData?.sessionId;
        const sessionIdSplit = sessionId?.split(':');
        const personSubId = sessionIdSplit?.length > 3 ? sessionIdSplit?.at(-1) : '';
        const sessionIdBase = personSubId
          ? `${sessionIdSplit[0]}:${sessionIdSplit[1]}:${sessionIdSplit[2]}`
          : sessionId;
        getCart = personSubId
          ? await this.cartAppService.getCart(
              {
                sessionId: sessionIdBase,
                shopCode: getOrder.shopCode,
                orderType: +getOrder.orderType,
                phoneNumber: getOrder.phone,
              },
              {
                headers: {
                  'cart-type': EnmCartType.FAMILY_PACKAGE,
                  'lcv-id': personSubId,
                } as any,
              },
            )
          : await this.cartAppService.getCart({
              sessionId: cartComfirmData?.sessionId,
              shopCode: getOrder.shopCode,
              orderType: +getOrder.orderType,
              phoneNumber: getOrder.phone,
            });

        if (
          (!getCart || !getCart?.listCartSelected || getCart?.listCartSelected?.length === 0) &&
          OrderChannels.WEB_APP.includes(getOrder?.orderChanel) &&
          [OrderStatus.Confirmed, OrderStatus.FinishDeposit].includes(getOrder?.orderStatus)
        ) {
          getCart = await this.cartAppService.adjustOrder({
            orderCode: getOrder?.orderCode,
            shopCode: getOrder?.shopCode,
          });
        }

        totalVoucher = getCart?.calculatorPriceInfo?.totalVoucherPrice || 0;
      } catch (error) {
        Logger.error({
          fields: {
            info: '[getCartConfirmByOrderCode] handle error',
            errors: error,
          },
        });
      }

      if (totalVoucher) {
        paymentInfo.totalDiscountVoucher = totalDiscountVoucher ? totalDiscountVoucher : totalVoucher;
        paymentInfo.totalBillAfterVoucher -= totalVoucher - totalDiscountVoucher + totalVoucherPartner;
        paymentInfo.remainingAmount -= totalVoucher - totalDiscountVoucher + totalVoucherPartner;
      }
    }
    return { paymentInfo, getCart };
  }

  /**
   * @TODO api get order detail
   * @param orderCode string required
   * @returns
   */
  async orderDetail(orderCode: string) {
    const getOrder = await this.omsService.getOneOrder(orderCode);

    if (getOrder?.urlImagePriceProduct.length >= 0) {
      const key_Image = getOrder?.urlImagePriceProduct.map((img) => {
        return img.image;
      });

      const { links } = await this.filesService.getS3Presign({ urls: key_Image });

      let idx = 0;
      for (const item of getOrder?.urlImagePriceProduct) {
        item.url = links?.at(idx);
        ++idx;
      }
    }

    let isAbleToContinuePreOrder = false;
    let phaseId: string = '';
    if (
      getOrder?.orderAttribute === 7
      // getOrder?.orderPaymentCreate?.length === 1 &&
      // getOrder?.orderPaymentCreate?.at(0)?.paymentStatus === 1
    ) {
      const arrSku: Array<string> = JSONPath({
        json: getOrder,
        path: '$.details[*]..itemCode',
      });

      const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({
        listSku: arrSku,
      });

      const phase1 = osrDepositAmountBySku?.find((e) => e?.phaseId === 1);
      const isPhase1 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase1?.fromDate).format('YYYY-MM-DD'),
        moment(phase1?.toDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );

      if (isPhase1 && phase1) {
        phaseId = String(phase1?.phaseId);
      }

      const phase2 = osrDepositAmountBySku?.find((e) => e.phaseId === 2);
      const isPhase2 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
        moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );
      if (phase2 && isPhase2 && getOrder?.orderStatus === OrderStatus.FinishDeposit) {
        isAbleToContinuePreOrder = true;
        phaseId = String(phase2?.phaseId);
      }
    }

    if (
      getOrder?.orderPaymentCreate?.length === 1 &&
      getOrder?.orderPaymentCreate?.at(0)?.paymentStatus === 4 &&
      getOrder?.orderAttribute === OrderAttribute.PRE_ORDER &&
      OrderChannels.RSA_ECOM.includes(this.req.headers['order-channel'] as string)
    ) {
      isAbleToContinuePreOrder = true;
      phaseId = process.env.PHASE_HARD || '2';
    }

    return {
      ...getOrder,
      ...{
        serviceFeeFake: (getOrder?.serviceFee || 0) * 2,
        totalWithFee: (getOrder?.total || 0) + (getOrder?.serviceFee || 0),
        isAbleToContinuePreOrder,
        preOrderPhaseId: phaseId,
      },
    };
  }

  async getOrderOnEs(orderCode: string, ticketCode?: string) {
    /**
     * @TODO
     *  - Lấy thông tin đơn hàng trên ES
     *  - Thông tin thanh toán
     */
    //  - Chuyển về call DB vì thanh toán thêm k sync es
    const getOrder = await this.omsService.getOneOrder(orderCode);
    const arrPaymentCode = _.uniq(
      _.compact(
        getOrder.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );
    let ticket: TicketDetailRes = null;
    if (ticketCode) {
      ticket = await this.examinationCoreService.getTicket({ ticketCode });
    }
    const isGetListOrderSameIdInter = getOrder?.orderIdInter && getOrder?.orderAttribute === 9;
    const [arrPaymentES, resOrderSameIdInter]: [GetPaymentHistoryESLibResponse[], SearchOrderESLibResponse] =
      await Promise.all([
        this.paymentGWService.getPaymentRedis({
          paymentCodes: arrPaymentCode,
        }),
        isGetListOrderSameIdInter
          ? this.omsService.searchOrderDynamicES({
              maxResultCount: 20, //BA nguyennt81 confirm 20
              skipCount: 0,
              searchMatchPhraseFields: {
                orderIdInter: getOrder?.orderIdInter,
              },
            })
          : { totalCount: 0, items: [] },
      ]);
    // Handle orderIdInter
    const listOrderSameIdInter = resOrderSameIdInter?.items?.map((item) => {
      const lcvIds: string[] = JSONPath({
        path: '$.details[*].detailAttachments[*].personIdSub',
        json: item,
      })?.filter(Boolean);
      return {
        ..._.pick(item, ['orderCode', 'totalBill', 'total', 'journeyId']),
        lcvIdSub: lcvIds?.[0], //Hiện tại 1 đơn chỉ có 1 khách tiêm
      };
    });
    const arrLcvIds: string[] = _.compact(_.uniq(listOrderSameIdInter?.map((item) => item?.lcvIdSub)));
    const arrOrderCode: string[] = listOrderSameIdInter?.map((item) => item?.orderCode);
    const [listPrimaryPerson, listTicketCodeByOrderCode]: [GetPersonByIdRes[], OrdersInfo[]] = await Promise.all([
      arrLcvIds?.length ? this.familyCoreService.getListPrimaryPerson(arrLcvIds) : [],
      arrOrderCode?.length
        ? this.journeyCoreService.getStatusGiftByOrderCodes({
            orderCodes: arrOrderCode,
          })
        : [],
    ]);
    const listOrderSameIdInterMapInfo = _.sortBy(
      listOrderSameIdInter?.map((item) => {
        const findPerson = listPrimaryPerson?.find((person) => person?.lcvId === item?.lcvIdSub);
        const findTicket = listTicketCodeByOrderCode?.find((ticketItem) => ticketItem?.orderCode === item?.orderCode);
        return {
          ...item,
          phoneSub: findPerson?.phoneNumber || '',
          nameSub: findPerson?.name || '',
          ticketCode: findTicket?.ticketCode || '',
        };
      }),
      (item) => (item?.orderCode === orderCode ? 0 : 1),
    ); // sort item có order code trùng với order code hiện tại lên đầu
    // End Handle orderIdInter

    let totalDiscountVoucher = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
    }, 0);

    if (ticket) {
      let arrReferenceVoucher: Array<any> = [];
      if (getOrder?.orderAttribute === 4) {
        arrReferenceVoucher = JSONPath({
          path: `$.[*].detail.vouchersAll[*].[?(@ && @.referenceId === '${ticket?.ticketCodeOld || ticketCode}' )]`,
          json: arrPaymentES,
        });
        if (!arrReferenceVoucher?.length) {
          arrReferenceVoucher = JSONPath({
            path: `$.[*].detail.vouchersAll[*].[?(@ && @.referenceId === null )]`,
            json: arrPaymentES,
          });
        }
        totalDiscountVoucher = 0;
      } else {
        // đơn từng phần
        if (ticket?.orderCodeOld) {
          arrReferenceVoucher = JSONPath({
            path: `$.[*].detail.vouchersAll[*].[?(@ && @.referenceId === '${ticketCode}' )]`,
            json: arrPaymentES,
          });
          totalDiscountVoucher = 0;
        }
      }
      if (arrReferenceVoucher?.length) {
        totalDiscountVoucher = arrReferenceVoucher?.reduce((acc, cur) => acc + cur?.amount || 0, 0);
      }
    }
    // const getVoucherAppliedOnOrder = await this.orderUtilsService.getVoucherByOrderCode(orderCode);
    // // getSerial voucher
    // const arrSerialDeposited: string[] = _.compact(
    //   _.uniq(
    //     _.flattenDeep(
    //       arrPaymentES.map((e) => e?.detail?.vouchersAll?.map((detailEntry) => detailEntry?.voucherDetail?.code)),
    //     ),
    //   ),
    // );
    // if (getVoucherAppliedOnOrder?.validVouchers?.length) {
    //   getVoucherAppliedOnOrder.validVouchers = getVoucherAppliedOnOrder?.validVouchers?.filter((e) => {
    //     const serialFind = arrSerialDeposited?.find((serialEntry) => serialEntry === e?.seriesCode);
    //     if (serialFind) return false;
    //     return true;
    //   });
    // }

    const paymentInfo = this.orderUtilsService._mapPaymentInfo(getOrder, totalDiscountVoucher, arrPaymentES, ticket);

    const arrVoucherProvider: number[] = [
      VoucherPartnerId.GotIT,
      VoucherPartnerId.Taptap,
      VoucherPartnerId.Urbox,
      VoucherPartnerId.UTop,
    ];

    const arrVoucherDetail: VoucherDetail[] =
      JSONPath({
        json: arrPaymentES,
        path: '$[*]..vouchersAll[*].voucherDetail',
      })?.filter((e: VoucherDetail) => arrVoucherProvider.includes(e.voucherType)) || [];

    const totalVoucherPartner = arrVoucherDetail?.reduce((prev, curr) => {
      return prev + curr.amount;
    }, 0);

    let { getCart } = await this.updatePaymentInfoFollowCart(
      paymentInfo,
      getOrder,
      totalDiscountVoucher,
      totalVoucherPartner,
    );

    if (getOrder?.urlImagePriceProduct.length >= 0) {
      const key_Image = getOrder?.urlImagePriceProduct.map((img) => {
        return img.image;
      });

      const { links } = await this.filesService.getS3Presign({ urls: key_Image });

      let idx = 0;
      for (const item of getOrder?.urlImagePriceProduct) {
        item.url = links?.at(idx);
        ++idx;
      }
    }

    const totalDiscountAdjustment =
      getOrder?.details?.reduce((acc, cur) => acc + cur?.discount * cur?.quantity, 0) || 0;

    let isCompleted = false;

    if (
      getOrder.orderStatus === OrderStatus.FinishDeposit &&
      paymentInfo.remainingAmount === 0 &&
      ticket?.status >= STATUS_TICKET.DA_KHAM
    ) {
      isCompleted = true;
    }

    const paymentMethods = await this.paymentMethodDetails(getOrder);
    // const  =
    // nếu có orderCreatePayment mở
    let isAbleToContinuePreOrder = false;
    let phaseId: string = '';
    if (getOrder?.orderAttribute === 7) {
      // && getOrder?.orderPaymentCreate?.length === 1) {
      const arrSku: Array<string> = JSONPath({
        json: getOrder,
        path: '$.details[*]..itemCode',
      });

      const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({
        listSku: arrSku,
      });

      const phase2 = osrDepositAmountBySku?.find((e) => e.phaseId === 2);
      const isPhase2 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
        moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );
      if (phase2 && isPhase2 && getOrder?.orderStatus === OrderStatus.FinishDeposit) {
        isAbleToContinuePreOrder = true;
        phaseId = String(phase2?.phaseId);
      }

      const phase1 = osrDepositAmountBySku?.find((e) => e?.phaseId === 1);
      const isPhase1 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase1?.fromDate).format('YYYY-MM-DD'),
        moment(phase1?.toDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );

      if (isPhase1 && phase1 && !phaseId) {
        phaseId = String(phase1?.phaseId);
        paymentInfo.remainingAmount = 0;
      }
    }

    if (
      getOrder?.orderPaymentCreate?.length === 1 &&
      getOrder?.orderPaymentCreate?.at(0)?.paymentStatus === 4 &&
      getOrder?.orderAttribute === OrderAttribute.PRE_ORDER &&
      OrderChannels.RSA_ECOM.includes(this.req.headers['order-channel'] as string)
    ) {
      isAbleToContinuePreOrder = true;
      phaseId = process.env.PHASE_HARD || '2';
    }

    const totalDiscountAmount =
      (arrPaymentES?.length === 1 && getDepositDetailAmount(arrPaymentES?.at(0)?.detail)) || 0;
    // nếu ECOM, đơn affiliate, arrPaymentES?.length === 1 (vì lớn hơn 1 thì lúc nào cũng khác 0), remainingAmount = null và total !== totalDiscountAmount => đơn cọc 0đ => hiển thị nút xử lý đơn
    const isEcomContinueBuyingVT =
      OrderChannels.RSA_ECOM.includes(this.req.headers?.['order-channel'] as string) &&
      OrderChannels.RSA_AFFILIATE.includes(getOrder?.orderChanel) &&
      arrPaymentES?.length === 1 &&
      !arrPaymentES?.at(0)?.remainingAmount &&
      getOrder?.orderStatus === OrderStatus.FinishDeposit &&
      totalDiscountAmount === 0 &&
      !ticket;

    // check trả thêm cờ cho FE để xử lý đơn vệ tinh
    // currentShopCode, orderAttribute: 15, paymentStatus: 6, !ticket
    const isAbleToContinueDepositOrder =
      ((this.req.headers?.['order-channel'] as string) === '14' &&
        OrderChannels.RSA_AFFILIATE.includes(getOrder?.orderChanel) &&
        getOrder?.orderStatus === OrderStatus.FinishDeposit &&
        !ticket) ||
      isEcomContinueBuyingVT;

    // get schedules infos.
    const allDetailsAttachments: DetailAttachment[] = JSONPath({
      path: `$.details[*].detailAttachments[*]`,
      json: getOrder,
    });

    const dateMin = getStartDateV2(
      new Date(_.minBy(allDetailsAttachments, 'appointmentDate')?.appointmentDate),
      '+07:00',
    );

    const findStep = (stepParams: number, employeeParams: Array<Partial<EmployeeLib>>) => {
      return employeeParams.find((employeeItem) => employeeItem.step === stepParams);
    };

    // Ưu tiên step 2, 5, 1
    const employeeFound =
      findStep(EmployeeStep.EmployeeConfirmAndCollectMoney, getOrder.employees) ||
      findStep(EmployeeStep.EmployeeUpdateOrderDeposit, getOrder.employees) ||
      findStep(EmployeeStep.EmployeeCreate, getOrder.employees);

    const dateMinToAdd30Days = getStartDateV2(
      new Date(moment(`${employeeFound?.createdDate}+07:00`).add(30, 'days').format()),
      '+07:00',
    );

    if (!getCart) {
      const cartComfirmData = await this.cartAppService.getCartConfirmByOrderCode(getOrder.orderCode);
      getCart = await this.cartAppService.getCart({
        sessionId: cartComfirmData?.sessionId,
        shopCode: getOrder.shopCode,
        orderType: +getOrder.orderType,
        phoneNumber: getOrder.phone,
      });
    }

    const immediateVaccines = [];
    allDetailsAttachments.forEach((e) => {
      const dateInTimeZone = `${e.appointmentDate}+07:00`;
      if (
        isSameDate(new Date(dateInTimeZone), new Date(dateMin), '+07:00') &&
        moment(dateInTimeZone).isSameOrBefore(moment(dateMinToAdd30Days), 'D')
      ) {
        immediateVaccines.push(e);
      }
    });

    const immediateVaccineIds = immediateVaccines.map((item) => item.id);
    const subsequentVaccines = allDetailsAttachments.filter((item) => !immediateVaccineIds.includes(item.id));

    const regimensInfo = [];
    if (getCart && getCart.listCartSelected?.length) {
      regimensInfo.push(
        ...(await this.regimenService.getRegimenByIds({
          regimenIds: getCart.listCartSelected.map((cartItem) => cartItem.regimenId),
        })),
      );
    }

    const subsequentVaccinesFormat = this.orderUtilsService.formatVaccineResponse(
      getCart,
      subsequentVaccines,
      regimensInfo,
    );
    const groupedBySubsequentVaccinesFormat = _.groupBy(
      subsequentVaccinesFormat,
      (obj) => obj.sku + '|' + obj.injectionDate?.unitCode,
    );
    let handleGroupedByCombined: DetailAttachmentFormat[] = [];
    for (const [_key, value] of Object.entries(groupedBySubsequentVaccinesFormat)) {
      const findDetailAttachmentFormat = value?.[0] as DetailAttachmentFormat;
      const listDetailAttachmentFormat = value as DetailAttachmentFormat[];
      const quantity = listDetailAttachmentFormat?.reduce((acc, cur) => acc + cur?.quantity || 0, 0);

      handleGroupedByCombined = [
        ...handleGroupedByCombined,
        {
          ...findDetailAttachmentFormat,
          quantity,
          totalWithFee: Math.max(
            0,
            (findDetailAttachmentFormat['price'] -
              findDetailAttachmentFormat['discountPromotion'] +
              findDetailAttachmentFormat['serviceFee'] -
              findDetailAttachmentFormat['discount']) *
              quantity,
          ),
        },
      ];
    }

    const checkContinueSavedOrder = isPossibleContinueBuyingByScheduleD2(getOrder, this.loggerServiceLib);
    const resJourney: any = await this.journeyCoreService.getOrderInfoByOrderOrTicket({
      orderCode: getOrder.orderCode,
    });
    return {
      ...getOrder,
      originalTotal: getOrder?.total - getOrder?.serviceFee,
      totalDiscountAdjustment,
      totalDirectDiscount: getOrder?.totalDiscount - totalDiscountAdjustment,
      paymentInfo,
      ...{
        serviceFeeFake: (getOrder?.serviceFee || 0) * 2,
        totalWithFee: (getOrder?.total || 0) + (getOrder?.serviceFee || 0),
      },
      isCompleted,
      paymentMethods,
      isAbleToContinuePreOrder,
      preOrderPhaseId: phaseId,
      isAbleToContinueDepositOrder,
      immediateVaccines: this.orderUtilsService.formatVaccineResponse(getCart, immediateVaccines, regimensInfo),
      subsequentVaccines: handleGroupedByCombined,
      appointmentSchedulesOrder: this.orderUtilsService.formatVaccineResponse(
        getCart,
        allDetailsAttachments,
        regimensInfo,
      ),
      isContinueBuying: checkContinueSavedOrder?.isPossible,
      isContinueBuyingMessage: checkContinueSavedOrder?.message,
      listOrderSameIdInter: listOrderSameIdInterMapInfo,
      shopCodeCreate: resJourney?.shopCodeCreate,
      shopNameCreate: resJourney?.shopNameCreate,
    } as GetOrderEsRes;
  }

  async trackingPreOrder(orderCode: string) {
    /**
     * Để ECOM LC gọi sang lấy thông tin trả về cho storefront hiển thị trên màn hình thanh toán.
     */
    //  - Chuyển về call DB vì thanh toán thêm k sync es
    const getOrder = await this.omsService.getOneOrder(orderCode);
    const arrPaymentCode = _.uniq(
      _.compact(
        getOrder.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );
    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getManyPaymentHistoryES(
      arrPaymentCode,
    );

    const totalDiscountVoucher = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
    }, 0);

    const paymentInfo = this.orderUtilsService._mapPaymentInfo(getOrder, totalDiscountVoucher, arrPaymentES);

    const arrVoucherProvider: number[] = [
      VoucherPartnerId.GotIT,
      VoucherPartnerId.Taptap,
      VoucherPartnerId.Urbox,
      VoucherPartnerId.UTop,
    ];

    const arrVoucherDetail: VoucherDetail[] =
      JSONPath({
        json: arrPaymentES,
        path: '$[*]..vouchersAll[*].voucherDetail',
      })?.filter((e: VoucherDetail) => arrVoucherProvider.includes(e.voucherType)) || [];

    const totalVoucherPartner = arrVoucherDetail?.reduce((prev, curr) => {
      return prev + curr.amount;
    }, 0);

    await this.updatePaymentInfoFollowCart(paymentInfo, getOrder, totalDiscountVoucher, totalVoucherPartner);

    const arrSku: Array<string> = JSONPath({
      json: getOrder,
      path: '$.details[*]..itemCode',
    });
    const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({
      listSku: arrSku,
    });
    const phase2 = osrDepositAmountBySku?.find((e) => e.phaseId === 2);
    const isPhase2 = phase2
      ? moment(moment().format('YYYY-MM-DD')).isBetween(
          moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
          moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
          undefined,
          '[]',
        )
      : false;
    const phase1 = osrDepositAmountBySku?.find((e) => e?.phaseId === 1);
    const isPhase1 = phase1
      ? moment(moment().format('YYYY-MM-DD')).isBetween(
          moment(phase1?.fromDate).format('YYYY-MM-DD'),
          moment(phase1?.toDate).format('YYYY-MM-DD'),
          undefined,
          '[]',
        )
      : false;

    const ecomData = await this.ecomOrderService.getByOrderCode(getOrder.orderCode);
    const totalVoucherDiscount = (ecomData?.vouchers || []).reduce((acc, cur) => {
      return acc + cur.amount;
    }, 0);

    const totalBillAfterVouchers = getOrder.totalBill - totalVoucherDiscount;
    let totalPaidAmount = 999999999;
    let isDepositAmountPaid = false;
    const getKeyRedis = `CreatedPaymentLink:${convertPaymentMethodToName(PaymentMethod.TRANSFER)}:${orderCode}:amount`;
    const getRedis: string = (await this.redisService.get(getKeyRedis)) || '0';

    if (isPhase1) {
      isDepositAmountPaid =
        paymentInfo?.depositedAmount !== 0 ? paymentInfo?.depositedAmount >= paymentInfo?.totalDeposit : false;
      if (isDepositAmountPaid) {
        totalPaidAmount = 0;
      } else {
        totalPaidAmount = +getRedis || paymentInfo?.totalDeposit;
      }
    }

    if (isPhase2) {
      totalPaidAmount = totalBillAfterVouchers - (paymentInfo?.depositedAmount || 0);
      isDepositAmountPaid = totalPaidAmount <= paymentInfo.depositedAmount;
      if (isDepositAmountPaid) {
        totalPaidAmount = 0;
      }
    }

    if ([isPhase1, isPhase2].every((e) => e === false)) {
      throw new SystemException(
        {
          code: ErrorCode.PRE_ORDER_PHASE_ISSUES,
          message: ErrorCode.getError(ErrorCode.PRE_ORDER_PHASE_ISSUES),
          details: ErrorCode.getError(ErrorCode.PRE_ORDER_PHASE_ISSUES),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      depositedAmount: paymentInfo?.depositedAmount ?? null,
      isDepositAmountPaid,
      phaseId: isPhase2 ? 2 : isPhase1 ? 1 : null,
      totalPaidAmount,
    };
  }

  async getOrderOnEsForSTC(orderCode: string): Promise<GetOrderEsRes> {
    /**
     * @TODO
     *  - Lấy thông tin đơn hàng trên ES
     *  - Thông tin thanh toán
     */
    //  - Chuyển về call DB vì thanh toán thêm k sync es
    const getOrder = await this.omsService.getOneOrder(orderCode);
    const arrPaymentCode = _.uniq(
      _.compact(
        getOrder.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );

    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getManyPaymentHistoryES(
      arrPaymentCode,
    );

    const totalDiscountVoucher = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
    }, 0);

    const paymentInfo = this.orderUtilsService._mapPaymentInfo(getOrder, totalDiscountVoucher);
    if (getOrder?.urlImagePriceProduct.length >= 0) {
      const key_Image = getOrder?.urlImagePriceProduct.map((img) => {
        return img.image;
      });

      const { links } = await this.filesService.getS3Presign({ urls: key_Image });

      let idx = 0;
      for (const item of getOrder?.urlImagePriceProduct) {
        item.url = links?.at(idx);
        ++idx;
      }
    }

    const totalDiscountAdjustment =
      getOrder?.details?.reduce((acc, cur) => acc + cur?.discount * cur?.quantity, 0) || 0;

    let isCompleted = false;
    const ticket = await this.examinationCoreService.getTicketByOrderCode({ orderCodes: [getOrder.orderCode] });
    if (
      getOrder.orderStatus === OrderStatus.FinishDeposit &&
      paymentInfo.remainingAmount === 0 &&
      ticket?.items?.[0]?.status >= STATUS_TICKET.DA_KHAM
    ) {
      isCompleted = true;
    }

    // check contract and invoice
    let isHasInvoice = false;
    let isHasContract = false;

    const resContract = await this.vacContractService.searchContract({
      transactionCode: [orderCode],
    });
    if (resContract && resContract?.length > 0) {
      const findIndex = resContract?.findIndex((e) => e.status === STEP_CONTRACT.COMPLATED);
      if (findIndex > -1) {
        isHasContract = true;
      }
    }

    let invoiceData: InvoiceDetailRes = null;
    try {
      invoiceData = await this.invoiceAppService.getInvoiceByOrderCode(orderCode);
    } catch (error) {}
    if (invoiceData) isHasInvoice = true;

    return {
      ...getOrder,
      totalDiscountAdjustment,
      totalDirectDiscount: getOrder?.totalDiscount - totalDiscountAdjustment,
      paymentInfo,
      ...{
        serviceFeeFake: (getOrder?.serviceFee || 0) * 2,
        totalWithFee: (getOrder?.total || 0) + (getOrder?.serviceFee || 0),
      },
      isCompleted,
      isHasInvoice,
      isHasContract,
    };
  }

  async updateStatusOrder(body: UpdateStatusOrderPayloadDto) {
    const { orderCode, modifiedBy, modifiedByName } = body;
    const getOrder = await this.omsService.getOneOrder(orderCode);
    await this.omsService.updateStatusPayment(orderCode, PaymentOnlineStatus.Complete, getOrder.paymentRequestCode);

    const employeeStep5 = _.orderBy(
      getOrder?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const payload: PayloadUpdatedStatusOrderDto = {
      orderCode: orderCode,
      modifiedBy: employeeStep5?.employeeCode || modifiedBy,
      modifiedByName: employeeStep5?.employeeName || modifiedByName,
      orderStatus: OrderStatus.FinishDeposit,
      ecomDisplay: EcomDisplay.AtShop,
      orderType: getOrder.orderType,
      shopCode: this.req.headers?.['shop-code'] as string,
    };
    const data = await this.omsService.updateStatusOrderDeposit(payload);

    // get ticketCode by orderCode from redis
    // ưu tiên lấy từ ticket
    // k có lấy từ redis
    const { items } = await this.examinationCoreService.getTicketByOrderCode({ orderCodes: [orderCode] });
    if (items?.length) {
      body.ticketInfor.at(0).ticketCode = items?.at(0)?.ticketCode;
    } else {
      const ticketCodeFromRedis = (await this.redisService.get(`TICKET_CREATE:${orderCode}`)) ?? '';
      if (ticketCodeFromRedis) {
        body.ticketInfor.at(0).ticketCode = ticketCodeFromRedis;
      }
    }

    // Tạo phiếu khám
    return this.handleLogicUpdateStatusOrder(body, getOrder, true);
  }

  async handleLogicUpdateStatusOrder(
    { ticketInfor, orderCode, modifiedBy, journeyId, modifiedByName }: UpdateStatusOrderPayloadDto,
    getOrder: GetOneOrderLibResponse,
    data: boolean,
    isFullPayment: boolean = false,
    shopCode?: string,
  ) {
    if (_.isEmpty(ticketInfor)) {
      return { isSuccess: data, ticketInfor: [] };
    }
    const ticketCreateCache = await this.redisService.get<Array<CreateTicketDto>>(`${TICKET_CREATE_KEY}:${orderCode}`);
    if (ticketCreateCache?.length && OrderChannels.RSA.includes(this.orderChannel)) {
      ticketInfor.at(0).indications = ticketCreateCache?.at(0)?.indications || [];
      ticketInfor.at(0).schedules = ticketCreateCache?.at(0)?.schedules || [];
      /**
       * @DungTTT13
       * @DAT
       * @description confirm khi chỉ định rỗng ticketType = 1, có chỉ định thì ticketType = 0
       */
      ticketInfor.at(0).ticketType = !ticketCreateCache?.at(0)?.indications?.length ? 1 : 0;
    }

    this.orderUtilsService.filterDupTicketSchedule(ticketInfor);

    // check exception with ticketInfor indication rong
    let isCheckException = false;
    ticketInfor.forEach((e) => {
      if (!e?.indications?.length && !e?.schedules?.length) {
        isCheckException = true;
      }
    });
    if (isCheckException) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_EXAMINATION_TICKET_INDICATION_EMPTY,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    let arrTicket: CreateTicketRes[] = [];
    if (ticketInfor?.at(0)?.ticketCode) {
      const getTicket = await this.examinationCoreService.getTicket({ ticketCode: ticketInfor?.at(0)?.ticketCode });
      const updateTicketDto = await this.ticketUtilsService.createAdjustTicketDtoV2(ticketInfor, getOrder);
      const ticketRes = await this.examinationCoreService.adjustTicket(
        {
          ticketCode: ticketInfor?.at(0)?.ticketCode,
        },
        {
          ...getTicket,
          ...updateTicketDto.at(0),
          clinicId: null,
          clinicName: '',
          status: getTicket?.status === EnmStatusTicket.CHECKED_UP ? getTicket?.status : EnmStatusTicket.CHO_KHAM,
          ticketType: ticketInfor?.at(0)?.ticketType,
        },
      );
      arrTicket.push(ticketRes as any);
    } else {
      arrTicket = await this.ticketUtilsService.createTicketUtil(ticketInfor, journeyId);
    }

    if (OrderChannels.RSA_ECOM.includes(this.orderChannel)) {
      await Promise.all(
        arrTicket.map((ticket: CreateTicketRes) =>
          this.journeyCoreService.updateTicketCodeOrerInfo({
            orderCode,
            ticketCode: ticket.ticketCode,
            paymentMethod: JourneyPaymentMethod.Online,
          }),
        ),
      );
    }

    if (!arrTicket?.length) {
      return { isSuccess: data, ticketInfor: [] };
    }

    const linkId = await this.orderUtilsService.getIdLinkFromEcom(orderCode);
    arrTicket.forEach((e) => {
      if (!e?.indications?.length) {
        e.paymentType = PaymentType.PAID; // @todo linkId
        e.linkId = linkId;
      } else {
        e.paymentType = null;
      }
    });

    if (isFullPayment) {
      arrTicket.forEach((e) => {
        e.paymentType = PaymentType.PAID; // @todo linkId
        e.linkId = linkId;
      });
    }

    const arrAdjustDto: Array<TicketDetailAdjustDto> = [];
    for (const ticket of arrTicket) {
      // update status done and paid
      if (!ticket?.indications?.length) {
        ticket.status = EnmStatusTicket.DONE;
      }
      arrAdjustDto.push(ticket as any);
    }

    //@HOHP Tách nợ trước khi bắn PAID
    const paymentTypePaid = arrAdjustDto?.filter((e) => e?.paymentType === PaymentType.PAID);
    let updateStatusByAttachmentCodeDto: Array<UpdateStatusByAttachmentCodeDto> = [];

    if (paymentTypePaid && paymentTypePaid?.length === arrAdjustDto?.length) {
      updateStatusByAttachmentCodeDto = this.orderUtilsService.getStatusForTraTungPhan(
        getOrder,
        arrAdjustDto?.at(0),
        modifiedBy,
      );

      // book tồn
      const getOrderDetail = await this.omsService.getOneOrder(orderCode);
      const listGiftProduct = getOrderDetail?.details?.filter(
        (detailEntry) => detailEntry.isPromotion === 'Y' && detailEntry?.isInventoryManagement === 'Y',
      );
      for (const ticket of arrAdjustDto) {
        await this.orderUtilsService.bookingInventory(
          modifiedBy,
          ticket.ticketCode,
          listGiftProduct,
          shopCode,
          orderCode,
          true,
        );
        // tách nợ km
      }
      await this.orderUtilsService.debitPromotion(orderCode, arrAdjustDto?.at(0)?.ticketCode);

      const confirmPromotion = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
      await this.orderUtilsService.confirmPromotion(orderCode, confirmPromotion);
    }
    const arrReturnAdj = [];
    const arrPaymentCode: string[] = _.flattenDeep(_.uniq(getOrder?.orderPaymentCreate?.map((e) => e.paymentCode)));
    const getListPayment = await this.paymentGWService.getPaymentRedis({
      paymentCodes: arrPaymentCode,
    });
    const arrUpdatePartialPaymentDto: UpdatePartialAmountDto[] = [];
    for (const adjust of arrAdjustDto) {
      const dto: TicketDetailAdjustDto = {
        ...adjust,
        schedules: adjust?.schedules?.map((e) => {
          const orderAttachmentFind = updateStatusByAttachmentCodeDto?.find(
            (orderEntry) => orderEntry.orderDetailAttachmentCode === e?.orderDetailAttachmentCode,
          );
          if (orderAttachmentFind && orderAttachmentFind?.status === 5) {
            e['partialPaid'] = 5;
          }
          return e;
        }),
      };

      if (dto?.paymentType === PaymentType.PAID) {
        dto['paymentEmployeeCode'] = modifiedBy;
        dto['paymentEmployeeName'] = modifiedByName;
      }

      if (getOrder?.orderAttribute === 4) {
        const totalDaThanhToan2 = getListPayment?.reduce((acc, cur) => acc + getDepositDetailAmount(cur.detail), 0);
        if (totalDaThanhToan2 > 0) {
          dto['isUpdatePartialPaymentAmount'] = true;
          dto['totalBill'] = Math.max(totalDaThanhToan2);
          dto['partialPaymentAmount'] = Math.max(totalDaThanhToan2 - getOrder?.serviceFee - getOrder?.totalDeposit, 0);

          const updatePartialPaymentDto: UpdatePartialAmountDto = {
            modifiedBy: modifiedBy,
            partialPaymentAmount: dto['partialPaymentAmount'], // Tiền mũi tiêm dợt này
            ticketCode: arrAdjustDto?.at(0)?.ticketCode,
            totalBill: dto['totalBill'] || 0, // Tiền thanh toán hôm nay,
            orderCodeOld: '',
            orderAttribute: 4,
            isFinishPayment: false,
          };

          if (dto['paymentType'] === PaymentType.PAID) {
            updatePartialPaymentDto['totalPayment'] = updatePartialPaymentDto['partialPaymentAmount'];
          }

          arrUpdatePartialPaymentDto.push(updatePartialPaymentDto);

          this.redisService.set(
            `${PARTIAL_PAYMENT_REDIS_KEY}:${arrAdjustDto?.at(0)?.ticketCode}`,
            JSON.stringify(updatePartialPaymentDto),
            'EX',
            getExpiredTime('day', 1),
          );
        }
      }
      const adjTicketEx = await this.examinationCoreService.adjustTicket(
        {
          ticketCode: adjust.ticketCode,
        },
        dto as any,
      );
      arrReturnAdj.push(adjTicketEx);
    }

    const arrTicketCode = [];
    for (const ticket of arrReturnAdj) {
      if (!ticket?.indications?.length) continue;
      arrTicketCode.push(ticket?.ticketCode);
    }

    if (arrUpdatePartialPaymentDto?.length) {
      await concurrentPromise(
        ...arrUpdatePartialPaymentDto?.map((item) => this.journeyCoreService.updatePartialAmount(item)),
      );
    }

    if (!arrTicketCode?.length) return { isSuccess: data, ticketInfor: arrReturnAdj };

    const assignRoomData: AssignRoomDtoV2 = {
      ticketCodes: arrTicketCode,
      shopCode: arrReturnAdj?.at(0)?.shopCode,
      roomType: ClinicType.PK,
      rule: EnmAssignRule.ASSIGN_EXAMINATION,
      modifiedBy: arrReturnAdj?.at(0)?.createdBy,
    };
    const assignRooms = await this.examinationCoreService.assignRoomV2(assignRoomData);
    for (const ticket of assignRooms) {
      await this.examinationCoreService.adjustTicket({ ticketCode: ticket.ticketCode }, ticket);
    }

    if (updateStatusByAttachmentCodeDto?.length && getOrder?.orderChanel === '14') {
      await this.journeyCoreService.updateEmployeeOrderInfo(arrReturnAdj?.at(0).ticketCode, {
        modifiedBy: modifiedBy,
        saleEmployeeName: getOrder?.modifiedByName,
      });
    }

    // lưu ticketCode vào redis
    try {
      if (arrAdjustDto?.length)
        this.redisService.set(
          `TICKET_CREATE:${orderCode}`,
          arrAdjustDto?.at(0)?.ticketCode,
          'EX',
          getExpiredTime('day', 1),
        );
    } catch (error) {}

    return { isSuccess: data, ticketInfor: assignRooms };
  }

  /**
   * Clone ra từ handleLogicUpdateStatusOrder, xử lý riêng cho đơn từng phần.
   */
  async handleLogicUpdateStatusPartialOrder(
    { ticketInfor, orderCode, modifiedBy, journeyId, modifiedByName }: UpdateStatusOrderPayloadDto,
    getOrder: GetOneOrderLibResponse,
    data: boolean,
    isFullPayment: boolean = false,
    shopCode?: string,
  ) {
    if (_.isEmpty(ticketInfor)) {
      return { isSuccess: data, ticketInfor: [] };
    }

    this.orderUtilsService.filterDupTicketSchedule(ticketInfor);

    // check exception with ticketInfor indication rong
    let isCheckException = false;
    ticketInfor.forEach((e) => {
      if (!e?.indications?.length && !e?.schedules?.length) {
        isCheckException = true;
      }
    });
    if (isCheckException) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_EXAMINATION_TICKET_INDICATION_EMPTY,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    let arrTicket: CreateTicketRes[] = [];
    if (ticketInfor?.at(0)?.ticketCode) {
      const getTicket = await this.examinationCoreService.getTicket({ ticketCode: ticketInfor?.at(0)?.ticketCode });
      const updateTicketDto = await this.ticketUtilsService.createAdjustTicketDtoV2(ticketInfor, getOrder);
      const ticketRes = await this.examinationCoreService.adjustTicket(
        {
          ticketCode: ticketInfor?.at(0)?.ticketCode,
        },
        {
          ...getTicket,
          ...updateTicketDto.at(0),
          clinicId: null,
          clinicName: '',
          status: getTicket?.status === EnmStatusTicket.CHECKED_UP ? getTicket?.status : EnmStatusTicket.CHO_KHAM,
          ticketType: ticketInfor?.at(0)?.ticketType,
        },
      );
      arrTicket.push(ticketRes as any);
    } else {
      arrTicket = await this.ticketUtilsService.createTicketUtil(ticketInfor, journeyId);
    }

    if (OrderChannels.RSA_ECOM.includes(this.orderChannel)) {
      await Promise.all(
        arrTicket.map((ticket: CreateTicketRes) =>
          this.journeyCoreService.updateTicketCodeOrerInfo({
            orderCode,
            ticketCode: ticket.ticketCode,
            paymentMethod: JourneyPaymentMethod.Online,
          }),
        ),
      );
    }

    if (!arrTicket?.length) {
      return { isSuccess: data, ticketInfor: [] };
    }

    const linkId = await this.orderUtilsService.getIdLinkFromEcom(orderCode);
    arrTicket.forEach((e) => {
      if (!e?.indications?.length) {
        e.paymentType = PaymentType.PAID; // @todo linkId
        e.linkId = linkId;
      } else {
        e.paymentType = null;
      }
    });

    if (isFullPayment) {
      arrTicket.forEach((e) => {
        e.paymentType = PaymentType.PAID; // @todo linkId
        e.linkId = linkId;
      });
    }

    const arrAdjustDto: Array<TicketDetailAdjustDto> = [];
    for (const ticket of arrTicket) {
      // update status done and paid
      if (!ticket?.indications?.length) {
        ticket.status = EnmStatusTicket.DONE;
      }
      if (ticket?.paymentType === PaymentType.PAID) {
        ticket['paymentEmployeeCode'] = modifiedBy;
        ticket['paymentEmployeeName'] = modifiedByName;
      }
      arrAdjustDto.push(ticket as any);
    }

    //@HOHP Tách nợ trước khi bắn PAID
    const paymentTypePaid = arrAdjustDto?.filter((e) => e?.paymentType === PaymentType.PAID);
    let updateStatusByAttachmentCodeDto: Array<UpdateStatusByAttachmentCodeDto> = [];

    if (paymentTypePaid && paymentTypePaid?.length === arrAdjustDto?.length) {
      updateStatusByAttachmentCodeDto = this.orderUtilsService.getStatusForTraTungPhan(
        getOrder,
        arrAdjustDto?.at(0),
        modifiedBy,
      );

      // book tồn
      const getOrderDetail = await this.omsService.getOneOrder(orderCode);
      const listGiftProduct = getOrderDetail?.details?.filter(
        (detailEntry) => detailEntry.isPromotion === 'Y' && detailEntry?.isInventoryManagement === 'Y',
      );
      for (const ticket of arrAdjustDto) {
        await this.orderUtilsService.bookingInventory(
          modifiedBy,
          ticket.ticketCode,
          listGiftProduct,
          shopCode,
          orderCode,
          true,
        );
        // tách nợ km
      }
      await this.orderUtilsService.debitPromotion(orderCode, arrAdjustDto?.at(0)?.ticketCode);

      const confirmPromotion = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
      await this.orderUtilsService.confirmPromotion(orderCode, confirmPromotion);
    }
    const arrReturnAdj = [];
    const arrPaymentCode: string[] = _.flattenDeep(_.uniq(getOrder?.orderPaymentCreate?.map((e) => e.paymentCode)));
    const getListPayment = await this.paymentGWService.getPaymentRedis({
      paymentCodes: arrPaymentCode,
    });

    for (const adjust of arrAdjustDto) {
      const dto = {
        ...adjust,
        schedules: adjust?.schedules?.map((e) => {
          const orderAttachmentFind = updateStatusByAttachmentCodeDto?.find(
            (orderEntry) => orderEntry.orderDetailAttachmentCode === e?.orderDetailAttachmentCode,
          );
          if (orderAttachmentFind && orderAttachmentFind?.status === 5) {
            e['partialPaid'] = 5;
          }
          return e;
        }),
      };

      if (getOrder?.orderAttribute === 4) {
        const totalDaThanhToan2 = getListPayment?.reduce((acc, cur) => acc + getDepositDetailAmount(cur.detail), 0);
        if (totalDaThanhToan2 > 0) {
          dto['isUpdatePartialPaymentAmount'] = true;
          dto['totalBill'] = Math.max(totalDaThanhToan2);
          dto['partialPaymentAmount'] = Math.max(totalDaThanhToan2 - getOrder?.serviceFee - getOrder?.totalDeposit, 0);

          const updatePartialPaymentDto: UpdatePartialAmountDto = {
            modifiedBy: modifiedBy,
            partialPaymentAmount: dto['partialPaymentAmount'], // Tiền mũi tiêm dợt này
            ticketCode: arrAdjustDto?.at(0)?.ticketCode,
            totalBill: dto['totalBill'] || 0, // Tiền thanh toán hôm nay,
            orderCodeOld: '',
            orderAttribute: 4,
            isFinishPayment: false,
          };

          await this.journeyCoreService.updatePartialAmount(updatePartialPaymentDto);

          this.redisService.set(
            `${PARTIAL_PAYMENT_REDIS_KEY}:${arrAdjustDto?.at(0)?.ticketCode}`,
            JSON.stringify(updatePartialPaymentDto),
            'EX',
            getExpiredTime('day', 1),
          );
        }
      }
      const adjTicketEx = await this.examinationCoreService.adjustTicket(
        {
          ticketCode: adjust.ticketCode,
        },
        dto as any,
      );
      arrReturnAdj.push(adjTicketEx);
    }

    const arrTicketCode = [];
    for (const ticket of arrReturnAdj) {
      if (!ticket?.indications?.length) continue;
      arrTicketCode.push(ticket?.ticketCode);
    }

    if (!arrTicketCode?.length) return { isSuccess: data, ticketInfor: arrReturnAdj };

    const assignRoomData: AssignRoomDtoV2 = {
      ticketCodes: arrTicketCode,
      shopCode: arrReturnAdj?.at(0)?.shopCode,
      roomType: ClinicType.PK,
      rule: EnmAssignRule.ASSIGN_EXAMINATION,
      modifiedBy: arrReturnAdj?.at(0)?.createdBy,
    };
    const assignRooms = await this.examinationCoreService.assignRoomV2(assignRoomData);
    for (const ticket of assignRooms) {
      await this.examinationCoreService.adjustTicket({ ticketCode: ticket.ticketCode }, ticket);
    }

    if (updateStatusByAttachmentCodeDto?.length && getOrder?.orderChanel === '14') {
      await this.journeyCoreService.updateEmployeeOrderInfo(arrReturnAdj?.at(0).ticketCode, {
        modifiedBy: modifiedBy,
        saleEmployeeName: getOrder?.modifiedByName,
      });
    }

    // lưu ticketCode vào redis
    try {
      if (arrAdjustDto?.length)
        this.redisService.set(
          `TICKET_CREATE:${orderCode}`,
          arrAdjustDto?.at(0)?.ticketCode,
          'EX',
          getExpiredTime('day', 1),
        );
    } catch (error) {}

    return { isSuccess: data, ticketInfor: assignRooms };
  }

  /**
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=136558615
   * @description Hoàn tất thanh toán Thêm
   */
  async finishPaymentExtra(finishPaymentExtraDto: FinishPaymentExtraDto) {
    /**
     * @TODO
     *  - Cập nhật payment về 4 (hoàn tất thanh toán)
     *  - Update phiếu khám về chờ tiêm
     *  - Assign phòng
     */
    const { modifiedBy, ticketCode, orderCode, modifiedByName } = finishPaymentExtraDto;

    const getOrder = await this.omsService.getOneOrder(orderCode);

    if (getOrder?.orderStatus !== OrderStatus.Completed && getOrder?.orderStatus !== OrderStatus.PartialReturn) {
      await this.omsService.updateStatusPayment(orderCode, PaymentOnlineStatus.Complete, getOrder.paymentRequestCode);
      await this.orderUtilsService.updateStatusDeposit(getOrder?.orderCode, modifiedBy, modifiedByName);
    }
    let ticket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });

    /**
     * @TODO Đối với đơn hàng trả chậm
     *  - Update trạng thái vaccineOrder
     *  - Order Attribute = 4 => Lấy mũi tiêm đợt 1 trong đơn.
     *  - Update trạng thái vaccineOrder là đã thanh toán
     */
    const updateStatusByAttachmentCodeDto: Array<UpdateStatusByAttachmentCodeDto> =
      this.orderUtilsService.getStatusForTraTungPhan(getOrder, ticket, modifiedBy);

    if (updateStatusByAttachmentCodeDto?.length && getOrder?.orderChanel === '14') {
      await this.journeyCoreService.updateEmployeeOrderInfo(ticketCode, {
        modifiedBy: modifiedBy,
        saleEmployeeName: modifiedByName,
      });
    }

    if (updateStatusByAttachmentCodeDto?.length) {
      await this.vacOrderInjectionService.updateStatusByAttachmentCode(updateStatusByAttachmentCodeDto);
    }

    const linkId = await this.orderUtilsService.getIdLinkFromEcom(orderCode);
    // tách nợ km
    await this.orderUtilsService.debitPromotion(orderCode, ticketCode);
    ticket = await this.examinationCoreService.adjustTicket(
      { ticketCode },
      {
        ...ticket,
        status: ticket?.indications?.length !== 0 ? EnmStatusTicket.WAITING_INJECT : EnmStatusTicket.DONE,
        schedules: ticket?.schedules?.map((e) => {
          const orderAttachmentFind = updateStatusByAttachmentCodeDto?.find(
            (orderEntry) => orderEntry.orderDetailAttachmentCode === e?.orderDetailAttachmentCode,
          );
          if (orderAttachmentFind && orderAttachmentFind?.status === 5) {
            e['partialPaid'] = 5;
          }

          return e;
        }),
        paymentEmployeeCode: modifiedBy,
        paymentEmployeeName: modifiedByName,
        paymentType: PaymentType.PAID, // @todo linkId
        linkId,
      },
    );

    const confirmPromotion = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
    await this.orderUtilsService.confirmPromotion(orderCode, confirmPromotion);

    const assignRooms = [await this.orderUtilsService.adjustTicketAndAssignRoom(ticket, modifiedBy)];

    // Refund 1M lại cho khách
    await this.businessOrderService.refundDeposit(orderCode, ticket, modifiedBy);

    return assignRooms;
  }

  wordingErrorMessage(errorCode) {
    switch (errorCode) {
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10012:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10012);
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10013:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10013);
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10014:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10014);
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10016:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10016);
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10017:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10017);
      default:
        return '';
    }
  }

  /**
   * @TODO
   * Hủy đơn hàng
   */
  async cancelOrder(cancelOrderDto: CancelOrderDto, isException = false) {
    const { orderCode } = cancelOrderDto;

    const orderData = await this.omsService.getOneOrder(orderCode);
    // const cartConfirmData = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
    // Chặn không cho cancel đơn hàng nếu đơn có orderAttribute = 9 và huỷ từ channel 7 15 16
    const orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    if (
      orderData?.orderAttribute === 9 &&
      [...OrderChannels.RSA_ECOM, ...OrderChannels.RSA_AFFILIATE].includes(orderChannel)
    ) {
      throw new SystemException(
        {
          code: ErrorCode.AFFILLIATE_ECOM_NOT_ALLOWED_CANCEL_ORDER,
          message: ErrorCode.getError(ErrorCode.AFFILLIATE_ECOM_NOT_ALLOWED_CANCEL_ORDER),
          details: ErrorCode.getError(ErrorCode.AFFILLIATE_ECOM_NOT_ALLOWED_CANCEL_ORDER),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }
    // Chặn khong cho Hủy đơn nếu đã thanh toán. Payment check rule
    if (orderData?.orderStatus === OrderStatus.Confirmed) {
      const arrPaymentCode = _.uniq(
        _.compact(
          orderData.orderPaymentCreate.map((e) => {
            if (e.paymentType === PaymentType.THU) return e.paymentCode;
          }),
        ),
      );

      const cancelPayment = await this.paymentGWService.cancelPaymentVaccine({
        paymentCodeWithShopCodes: arrPaymentCode?.map((entry) => ({
          paymentCode: entry,
          shopCode: orderData?.shopCode,
        })),
        cancelBy: cancelOrderDto?.modifiedBy,
        fromSystem: 'BE RSA',
        phoneNumber: orderData?.phone,
        orderCode,
        referenceId: cancelOrderDto?.ticketCode || '',
      });

      const cancelPaymentFailure = cancelPayment?.processedPayments?.filter((process) => !process?.isCancelled);

      if (cancelPaymentFailure?.length) {
        const exception: IError = {
          code: cancelPaymentFailure?.at(0)?.errorCode,
          message:
            this.wordingErrorMessage(cancelPaymentFailure?.at(0)?.errorCode) || cancelPaymentFailure?.at(0)?.message,
          details:
            this.wordingErrorMessage(cancelPaymentFailure?.at(0)?.errorCode) || cancelPaymentFailure?.at(0)?.message,
          validationErrors: cancelPaymentFailure?.map((error) => ({
            message: this.wordingErrorMessage(error?.errorCode) || error?.message,
          })),
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }
    }

    if (orderData?.orderStatus !== OrderStatus.Confirmed && isException) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ORDER_STATUS_NOT_CONFIRMED,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    const { items } = await this.examinationCoreService.getTicketByOrderCode({
      orderCodes: [cancelOrderDto.orderCode],
    });
    if (items?.at(0)?.ticketCode) {
      await this.examinationCoreService.changeStatusTicket({
        ticketCode: items?.at(0)?.ticketCode,
        status: EnmStatusTicket.CLOSED,
        modifiedBy: cancelOrderDto?.modifiedBy,
      });
    }

    /**
     * @TODO Huỷ hẹn trước huỷ order vì liên quan sub
     */
    /**
     * @TODO ChiTNB bao bo
     */
    // cancel schedule by sku and lcvid
    // const payloadCancelScheduleByLcvId: {
    //   sku: string;
    //   injections: number;
    //   regimenId?: string;
    //   lcvId?: string;
    //   note?: string;
    //   updatedBy?: string;
    //   isPaid?: boolean;
    //   orderDetailAttachmentCode: string;
    // }[] = [];

    // if (items?.at(0)?.schedules?.length) {
    //   const vacOrder = await this.vacOrderInjectionService.searchByPerson({
    //     personCode: items?.at(0)?.lcvId,
    //     skipCount: 0,
    //   });
    //   // compare sku và injection của schedule với oms
    //   for (const schedule of items?.at(0)?.schedules) {
    //     if ([0, 1].includes(schedule?.status)) {
    //       // 0 => chưa thanh toán, 1: đã thanh toán
    //       const vacOrderFilter = vacOrder?.items?.filter((item) => item?.sku === schedule?.sku);
    //       const omsFind = orderData?.details
    //         ?.find((e) => e.itemCode === ITEM_VACCINE_SO1)
    //         ?.detailAttachments?.find((e) => e.orderDetailAttachmentCode === schedule?.orderDetailAttachmentCode);

    //       let isPaid = false;
    //       if (
    //         vacOrderFilter?.length === 1 &&
    //         vacOrderFilter?.at(0)?.orderInjections === schedule?.orderInjections &&
    //         orderData?.orderStatus === OrderStatus.FinishDeposit &&
    //         omsFind
    //       ) {
    //         isPaid = true;
    //       } else {
    //         isPaid = false;
    //       }

    //       payloadCancelScheduleByLcvId.push({
    //         sku: schedule?.sku,
    //         injections: schedule?.orderInjections,
    //         regimenId: schedule?.regimenId,
    //         lcvId: items?.at(0)?.lcvId,
    //         note: 'Huỷ đơn',
    //         updatedBy: cancelOrderDto?.modifiedBy,
    //         isPaid: isPaid,
    //         orderDetailAttachmentCode: schedule?.orderDetailAttachmentCode,
    //       });

    //       // schedule.regimenId = schedule?.regimenId;
    //       // schedule.note = 'Huỷ đơn';
    //       // schedule.updatedBy = cancelOrderDto?.modifiedBy;
    //       // schedule.isPaid = isPaid;
    //     }
    //   }
    //   await this.scheduleCoreService.cancelScheduleByLcvIdSku(payloadCancelScheduleByLcvId);
    // }

    // Huỷ order
    const cancelOrderData = await this.omsService.cancelOrder({
      ...cancelOrderDto,
      systemDate: moment().format(),
      orderID: orderData?.orderID,
    });

    // roll back điểm fsell khi hủy đơn
    const arrPaymentCode3 = _.uniq(
      _.compact(
        orderData.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );

    const arrPaymentES3: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getPaymentRedis({
      paymentCodes: arrPaymentCode3,
    });

    let cancelVoucherLoyaltyDto = null;
    for (const payment of arrPaymentES3) {
      if (!payment?.detail?.vouchersAll?.length) continue;
      for (const voucher of payment?.detail?.vouchersAll) {
        if (voucher?.voucherDetail?.channel?.typeCode !== ChannelType.Loyalty) continue;
        cancelVoucherLoyaltyDto = {
          system: System,
          orderCode: orderData.orderCode,
          paymentCode: payment?.paymentCode,
          phoneNumber: orderData?.phone,
          shopCode: orderData?.shopCode,
          reason: 'Huỷ voucher loyalty',
          seriesCode: [voucher?.voucherDetail?.code],
        };
      }
    }
    // cancel voucher loyalty đã deposit
    if (cancelVoucherLoyaltyDto) {
      try {
        await this.loyaltyAppService.voucherDeposit(orderData?.custCode, cancelVoucherLoyaltyDto);
      } catch (error) {
        Logger.error({
          fields: {
            info: '[CancelVoucherLoyalty] handle error',
            errors: error,
          },
        });
      }
    }

    // cancel schedule
    // await this.scheduleCoreService.cancelByOrderCode({
    //   cancelBy: cancelOrderDto.modifiedBy,
    //   orderCode: cancelOrderDto.orderCode,
    // });

    /**
     * @TODO Huỷ voucher
     */

    for (const orderPayment of orderData?.orderPaymentCreate) {
      try {
        const payloadCancelVoucher: CancelVoucherByPaymentCodeDto = {
          fromSystem: 'RSA',
          orderCode: orderCode,
          paymentCode: orderPayment?.paymentCode,
          phoneNumber: orderData?.phone,
          shopCode: orderData?.shopCode,
          reason: 'Huỷ voucher khi huỷ đơn hàng',
        };
        await this.voucherCoreService.cancelVoucherByPaymentCode(payloadCancelVoucher);
      } catch (error) {
        Logger.error({
          fields: {
            info: '[cancelVoucher] handle error',
            errors: error,
          },
        });
      }

      try {
        // cancel voucher loyalty nếu hủy đơn mà chưa thanh toán
        await this.loyaltyAppService.cancelExchangePoint(orderData?.custCode, {
          paymentCode: orderPayment?.paymentCode,
          phoneNumber: orderData?.phone,
          reason: 'Huỷ đơn hàng',
          system: 'RSA',
          orderCode: orderCode,
          shopCode: orderData?.shopCode,
        });
      } catch (error) {
        Logger.error({
          fields: {
            info: '[cancelVoucher] handle error',
            errors: error,
          },
        });
      }

      try {
        const unbookVoucherDto: UnbookVoucherByPaymentCodeDto = {
          fromSystem: 'RSA',
          phoneNumber: orderData.phone,
          orderCode: orderData.orderCode,
          paymentCode: orderPayment?.paymentCode,
          shopCode: orderData.shopCode,
        };
        await this.voucherCoreService.unbookVoucherByPaymentCode(unbookVoucherDto);
      } catch (error) {
        Logger.error({
          fields: {
            info: '[cancelVoucher] handle error',
            errors: error,
          },
        });
      }
    }

    /**
     * @TODO Remove data redis
     */
    // if (cartConfirmData?.sessionId) {
    //   await this.cartAppService.deleteKeyRedis(cartConfirmData?.sessionId);
    // }

    // Cancel book
    /**
     * @TODO PETER_20240119
     */

    await this.orderUtilsService.cancelBook(
      orderData.shopCode,
      orderData?.modifiedBy || orderData?.createdBy,
      items?.at(0)?.ticketCode,
    );

    /**
     * SEND TIN NHAN
     */
    let sendSmsError = null;
    try {
      if (cancelOrderDto.reasonId) {
        const reasonDetail = await this.reasonService.findOne(cancelOrderDto.reasonId);

        let fromSys = '';
        if (OrderChannels.RSA.includes(this.req.headers['order-channel'] as string)) {
          fromSys = 'Web RSA Vaccine';
        }

        if (OrderChannels.RSA_ECOM.includes(this.req.headers['order-channel'] as string)) {
          fromSys = 'Web RSA Ecom Vaccine';
        }

        if (reasonDetail?.isSendMessage && orderData.ecomDisplay === EcomDisplay.AtShop) {
          await this.notificationService.sendNotification({
            FromSys: fromSys,
            Sender: cancelOrderDto.modifiedBy,
            Messages: [
              {
                TemplateId: this.configService.get('CANCEL_ORDER_SMS_TEMPLATE_ID'),
                To: [orderData.phone],
                Bcc: [],
                Cc: [],
                Param: {
                  Title: {},
                  Content: {
                    orderCode: orderCode?.slice(-7),
                    customerName: removeVietnameseTones(orderData?.custName),
                    shopName: removeVietnameseTones(orderData?.shopName),
                  },
                  ContentFailOver: {},
                  ExtraProperties: {},
                },
                ImageLink: '',
                MessageLink: '',
              },
            ],
          });
        }
      }
    } catch (e) {
      sendSmsError = e.message || 'Error send sms';
    }

    return {
      ...cancelOrderData,
      sendSmsError,
    };
  }

  /**
   * @TODO
   * Hủy đơn hàng
   */
  async cancelOrderFamilyPackage(cancelOrderDto: CancelOrderDto, isException = false) {
    const { orderCode } = cancelOrderDto;

    const orderData = await this.omsService.getOneOrder(orderCode);
    // const cartConfirmData = await this.cartAppService.getCartConfirmByOrderCode(orderCode);

    // Chặn khong cho Hủy đơn nếu đã thanh toán. Payment check rule
    if (orderData?.orderStatus === OrderStatus.Confirmed) {
      const arrPaymentCode = _.uniq(
        _.compact(
          orderData.orderPaymentCreate.map((e) => {
            if (e.paymentType === PaymentType.THU) return e.paymentCode;
          }),
        ),
      );

      const cancelPayment = await this.paymentGWService.cancelPaymentVaccine({
        paymentCodeWithShopCodes: arrPaymentCode?.map((entry) => ({
          paymentCode: entry,
          shopCode: orderData?.shopCode,
        })),
        cancelBy: cancelOrderDto?.modifiedBy,
        fromSystem: 'BE RSA',
        phoneNumber: orderData?.phone,
        orderCode,
        referenceId: cancelOrderDto?.ticketCode || '',
      });

      const cancelPaymentFailure = cancelPayment?.processedPayments?.filter((process) => !process?.isCancelled);

      if (cancelPaymentFailure?.length) {
        const exception: IError = {
          code: cancelPaymentFailure?.at(0)?.errorCode,
          message:
            this.wordingErrorMessage(cancelPaymentFailure?.at(0)?.errorCode) || cancelPaymentFailure?.at(0)?.message,
          details:
            this.wordingErrorMessage(cancelPaymentFailure?.at(0)?.errorCode) || cancelPaymentFailure?.at(0)?.message,
          validationErrors: cancelPaymentFailure?.map((error) => ({
            message: this.wordingErrorMessage(error?.errorCode) || error?.message,
          })),
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }
    }

    if (orderData?.orderStatus !== OrderStatus.Confirmed && isException) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ORDER_STATUS_NOT_CONFIRMED,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    const { items } = await this.examinationCoreService.getTicketByOrderCode({
      orderCodes: [cancelOrderDto.orderCode],
    });
    if (items?.at(0)?.ticketCode) {
      await this.examinationCoreService.changeStatusTicket({
        ticketCode: items?.at(0)?.ticketCode,
        status: EnmStatusTicket.CLOSED,
        modifiedBy: cancelOrderDto?.modifiedBy,
      });
    }

    /**
     * @TODO Huỷ hẹn trước huỷ order vì liên quan sub
     */
    /**
     * @TODO ChiTNB bao bo
     */
    // cancel schedule by sku and lcvid
    // const payloadCancelScheduleByLcvId: {
    //   sku: string;
    //   injections: number;
    //   regimenId?: string;
    //   lcvId?: string;
    //   note?: string;
    //   updatedBy?: string;
    //   isPaid?: boolean;
    //   orderDetailAttachmentCode: string;
    // }[] = [];

    // if (items?.at(0)?.schedules?.length) {
    //   const vacOrder = await this.vacOrderInjectionService.searchByPerson({
    //     personCode: items?.at(0)?.lcvId,
    //     skipCount: 0,
    //   });
    //   // compare sku và injection của schedule với oms
    //   for (const schedule of items?.at(0)?.schedules) {
    //     if ([0, 1].includes(schedule?.status)) {
    //       // 0 => chưa thanh toán, 1: đã thanh toán
    //       const vacOrderFilter = vacOrder?.items?.filter((item) => item?.sku === schedule?.sku);
    //       const omsFind = orderData?.details
    //         ?.find((e) => e.itemCode === ITEM_VACCINE_SO1)
    //         ?.detailAttachments?.find((e) => e.orderDetailAttachmentCode === schedule?.orderDetailAttachmentCode);

    //       let isPaid = false;
    //       if (
    //         vacOrderFilter?.length === 1 &&
    //         vacOrderFilter?.at(0)?.orderInjections === schedule?.orderInjections &&
    //         orderData?.orderStatus === OrderStatus.FinishDeposit &&
    //         omsFind
    //       ) {
    //         isPaid = true;
    //       } else {
    //         isPaid = false;
    //       }

    //       payloadCancelScheduleByLcvId.push({
    //         sku: schedule?.sku,
    //         injections: schedule?.orderInjections,
    //         regimenId: schedule?.regimenId,
    //         lcvId: items?.at(0)?.lcvId,
    //         note: 'Huỷ đơn',
    //         updatedBy: cancelOrderDto?.modifiedBy,
    //         isPaid: isPaid,
    //         orderDetailAttachmentCode: schedule?.orderDetailAttachmentCode,
    //       });

    //       // schedule.regimenId = schedule?.regimenId;
    //       // schedule.note = 'Huỷ đơn';
    //       // schedule.updatedBy = cancelOrderDto?.modifiedBy;
    //       // schedule.isPaid = isPaid;
    //     }
    //   }
    //   await this.scheduleCoreService.cancelScheduleByLcvIdSku(payloadCancelScheduleByLcvId);
    // }

    // Huỷ order
    const cancelOrderData = await this.omsService.cancelOrder({
      ...cancelOrderDto,
      systemDate: moment().format(),
      orderID: orderData?.orderID,
    });

    // roll back điểm fsell khi hủy đơn
    const arrPaymentCode3 = _.uniq(
      _.compact(
        orderData.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );

    const arrPaymentES3: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getPaymentRedis({
      paymentCodes: arrPaymentCode3,
    });

    let cancelVoucherLoyaltyDto = null;
    for (const payment of arrPaymentES3) {
      if (!payment?.detail?.vouchersAll?.length) continue;
      for (const voucher of payment?.detail?.vouchersAll) {
        if (voucher?.voucherDetail?.channel?.typeCode !== ChannelType.Loyalty) continue;
        cancelVoucherLoyaltyDto = {
          system: System,
          orderCode: orderData.orderCode,
          paymentCode: payment?.paymentCode,
          phoneNumber: orderData?.phone,
          shopCode: orderData?.shopCode,
          reason: 'Huỷ voucher loyalty',
          seriesCode: [voucher?.voucherDetail?.code],
        };
      }
    }
    // cancel voucher loyalty đã deposit
    if (cancelVoucherLoyaltyDto) {
      try {
        await this.loyaltyAppService.voucherDeposit(orderData?.custCode, cancelVoucherLoyaltyDto);
      } catch (error) {
        Logger.error({
          fields: {
            info: '[CancelVoucherLoyalty] handle error',
            errors: error,
          },
        });
      }
    }

    // cancel schedule
    // await this.scheduleCoreService.cancelByOrderCode({
    //   cancelBy: cancelOrderDto.modifiedBy,
    //   orderCode: cancelOrderDto.orderCode,
    // });

    /**
     * @TODO Huỷ voucher
     */

    for (const orderPayment of orderData?.orderPaymentCreate) {
      try {
        const payloadCancelVoucher: CancelVoucherByPaymentCodeDto = {
          fromSystem: 'RSA',
          orderCode: orderCode,
          paymentCode: orderPayment?.paymentCode,
          phoneNumber: orderData?.phone,
          shopCode: orderData?.shopCode,
          reason: 'Huỷ voucher khi huỷ đơn hàng',
        };
        await this.voucherCoreService.cancelVoucherByPaymentCode(payloadCancelVoucher);
      } catch (error) {
        Logger.error({
          fields: {
            info: '[cancelVoucher] handle error',
            errors: error,
          },
        });
      }

      try {
        // cancel voucher loyalty nếu hủy đơn mà chưa thanh toán
        await this.loyaltyAppService.cancelExchangePoint(orderData?.custCode, {
          paymentCode: orderPayment?.paymentCode,
          phoneNumber: orderData?.phone,
          reason: 'Huỷ đơn hàng',
          system: 'RSA',
          orderCode: orderCode,
          shopCode: orderData?.shopCode,
        });
      } catch (error) {
        Logger.error({
          fields: {
            info: '[cancelVoucher] handle error',
            errors: error,
          },
        });
      }

      try {
        const unbookVoucherDto: UnbookVoucherByPaymentCodeDto = {
          fromSystem: 'RSA',
          phoneNumber: orderData.phone,
          orderCode: orderData.orderCode,
          paymentCode: orderPayment?.paymentCode,
          shopCode: orderData.shopCode,
        };
        await this.voucherCoreService.unbookVoucherByPaymentCode(unbookVoucherDto);
      } catch (error) {
        Logger.error({
          fields: {
            info: '[cancelVoucher] handle error',
            errors: error,
          },
        });
      }
    }

    /**
     * @TODO Remove data redis
     */
    // if (cartConfirmData?.sessionId) {
    //   await this.cartAppService.deleteKeyRedis(cartConfirmData?.sessionId);
    // }

    // Cancel book
    /**
     * @TODO PETER_20240119
     */

    await this.orderUtilsService.cancelBook(
      orderData.shopCode,
      orderData?.modifiedBy || orderData?.createdBy,
      items?.at(0)?.ticketCode,
    );

    /**
     * SEND TIN NHAN
     */
    let sendSmsError = null;
    try {
      if (cancelOrderDto.reasonId) {
        const reasonDetail = await this.reasonService.findOne(cancelOrderDto.reasonId);

        let fromSys = '';
        if (OrderChannels.RSA.includes(this.req.headers['order-channel'] as string)) {
          fromSys = 'Web RSA Vaccine';
        }

        if (OrderChannels.RSA_ECOM.includes(this.req.headers['order-channel'] as string)) {
          fromSys = 'Web RSA Ecom Vaccine';
        }

        if (reasonDetail?.isSendMessage && orderData.ecomDisplay === EcomDisplay.AtShop) {
          await this.notificationService.sendNotification({
            FromSys: fromSys,
            Sender: cancelOrderDto.modifiedBy,
            Messages: [
              {
                TemplateId: this.configService.get('CANCEL_ORDER_SMS_TEMPLATE_ID'),
                To: [orderData.phone],
                Bcc: [],
                Cc: [],
                Param: {
                  Title: {},
                  Content: {
                    orderCode: orderCode?.slice(-7),
                    customerName: removeVietnameseTones(orderData?.custName),
                    shopName: removeVietnameseTones(orderData?.shopName),
                  },
                  ContentFailOver: {},
                  ExtraProperties: {},
                },
                ImageLink: '',
                MessageLink: '',
              },
            ],
          });
        }
      }
    } catch (e) {
      sendSmsError = e.message || 'Error send sms';
    }

    return {
      ...cancelOrderData,
      sendSmsError,
    };
  }

  /**
   * @TODO thực hiện việc chi tiền hủy cọc
   */
  async cancelDepositPaymentWihOtpAndSM(
    payload: ConfirmPayDepositCancelOtp,
  ): Promise<CancelOrderLibResponse | GetOneOrderLibResponse> {
    const { modifiedBy, orderCode, modifiedByName, ticketCode, shopAffiliate } = payload;

    // gọi service lấy chi tiết đơn hàng
    // lấy orderId, orderCode
    const infoOrder = await this.orderDetail(orderCode);
    const getOrder = await this.omsService.getOneOrder(orderCode);

    if (getOrder?.orderStatus !== OrderStatus.FinishDeposit) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ORDER_STATUS_NOT_FINISH_DEPOSITED,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    // Người thao với đơn (hủy cọc)
    await this.omsService.employeeOrder({
      orderID: infoOrder?.orderID,
      orderCode: infoOrder?.orderCode,
      employeeCode: modifiedBy,
      employeeName: modifiedByName,
      step: EmployeeStep.EmployeeCancelDeposit,
      createdDate: new Date(),
      modifiedDate: new Date(),
      orderCancelChannel: Channel.Offline,
    });

    if (!infoOrder) return { isSuccess: false };

    const depositCancel = await this.depositCancelRepository.findOneBy({
      orderCode: infoOrder?.orderCode,
    });

    // refund
    const payWithHomePaylater: GetPaymentHistoryESLibResponse | null = _.find(
      depositCancel?.paymentHistory,
      (item: GetPaymentHistoryESLibResponse) => _.get(_.first(item?.detail?.installmentAll), 'amount', 0) > 0,
    );
    if (payWithHomePaylater) {
      // paymentRequestType với chi trả tiền cọc 3,trả hàng 1
      const body: RefundHomePaylaterBody = {
        paymentCode: payWithHomePaylater?.paymentCode,
        paymentRequestType: 3,
      };
      await this.paymentGWService.refundHomePaylater(body);
    }

    await this.orderUtilsService.createPaymentAndDepositAll(
      {
        orderCode,
        totalRefund: depositCancel?.totalRefund,
        modifiedBy,
        shopAffiliate,
      },
      false,
    );

    // gọi api hủy đơn
    const payloadCancelDeposit: CancelOrderDto = {
      modifiedBy: modifiedBy,
      orderID: infoOrder?.orderID,
      orderCode: orderCode,
      modifiedByName: modifiedByName,
      orderCancelChannel: depositCancel?.orderChanel,
      reason: 1, // default
      reasonCancel: depositCancel?.reasonNote ? depositCancel?.reasonNote : depositCancel?.reasonName,
    };

    return this.cancelOrder(payloadCancelDeposit);
  }

  /**
   * @TODO Thực hiện việc tiếp tục mua hàng
   */
  async continueBuying(
    orderCode: string,
    isEcom = false,
    employeeInfo?: EmployeeInfoContinueBuyingDto,
  ): Promise<ContinueBuyingRes> {
    /**
     * @TODO
     *    - Lấy thông tin đơn hàng
     *    - Adjust cart
     *    - vac order (lịch sử tiêm của người đó)
     */

    const { employee_code } = jwtDecode<IAuthUser>(this.token) || { employee_code: null };
    const getOrder = await this.omsService.getOneOrder(orderCode);
    // if (getOrder?.orderStatus !== OrderStatus.Confirmed && getOrder?.orderAttribute !== 7) {
    //   throw new SystemException(
    //     {
    //       code: ErrorCode.RSA_CONTINUE_BUYING_RULE,
    //     },
    //     HttpStatus.FORBIDDEN,
    //   );
    // }
    const orderChannel = (this.req.headers['order-channel'] as string) || '';
    if (
      getOrder?.orderAttribute === 9 &&
      (OrderChannels.RSA_AFFILIATE.includes(orderChannel) ||
        (OrderChannels.RSA_ECOM.includes(orderChannel) &&
          getOrder?.orderIdInter?.split(':')?.at(0) !== RSA_ECOM_HARD_DEFAULT_SHOP_CODE))
    ) {
      throw new SystemException(
        {
          code: ErrorCode.AFFILLIATE_ECOM_NOT_ALLOWED_CONTINUE_BUYING,
          message: ErrorCode.getError(ErrorCode.AFFILLIATE_ECOM_NOT_ALLOWED_CONTINUE_BUYING),
          details: ErrorCode.getError(ErrorCode.AFFILLIATE_ECOM_NOT_ALLOWED_CONTINUE_BUYING),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    const arrLCVId: string[] = _.uniq(
      JSONPath({ path: '$.details[*].detailAttachments[*].personIdSub', json: getOrder }),
    );
    // không check ưu tiên đối với đơn pre-order
    if (getOrder.orderAttribute !== ORDER_ATTRIBUTE.DON_HANG_PRE_ORDER && !isEcom) {
      // check order priority
      await this.orderUtilsService.checkOrderPriority(arrLCVId, orderCode);
    }

    // check weekday
    const today = moment().utcOffset(7).day();
    const isDayInWeek = WEEKDAY?.includes(today);

    if (!OrderChannels.RSA_AFFILIATE.includes(orderChannel) && getOrder?.orderAttribute === 8 && isDayInWeek) {
      const mobileCarrierSelected = await this.checkMobileCarrierSelection(getOrder?.phone);
      if (!mobileCarrierSelected) {
        const messageEcom = ErrorCode.getError(ErrorCode.RSA_ECOM_SAVE_MOBILE_CARRIER_RULE);
        const messageRsa = ErrorCode.getError(ErrorCode.RSA_SAVE_MOBILE_CARRIER_RULE);
        throw new SystemException(
          {
            code: ErrorCode.RSA_SAVE_MOBILE_CARRIER_RULE,
            message: OrderChannels.RSA_ECOM.includes(orderChannel) ? messageEcom : messageRsa,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    // https://reqs.fptshop.com.vn/browse/FV-10201
    // RSA nếu có phiếu khám mở thì chặn xử lý đơn đặt cọc

    // Task https://reqs.fptshop.com.vn/browse/FV-10335
    // Sub-task https://reqs.fptshop.com.vn/browse/FV-10452
    // vì check all cả đơn ecom đẩy về và đơn VT đẩy về nên xóa đi đoạn check getOrder.orderAttribute === 8 của Hải
    // nếu có phiếu và
    const resExamTicket =
      arrLCVId?.length && OrderChannels.RSA.includes(this.req.headers['order-channel'] as string)
        ? (await this.examinationCoreService.searchByLcsIds({
            lCVIds: arrLCVId,
          })) || []
        : [];
    if (OrderChannels.RSA.includes(this.req.headers['order-channel'] as string)) {
      const openTicket =
        resExamTicket?.length &&
        resExamTicket?.find(
          (e) =>
            [
              EnmStatusTicket.CHO_KHAM, // 1
              EnmStatusTicket.DANG_KHAM, // 2
              EnmStatusTicket.CHECKED_UP, // 3
              EnmStatusTicket.WAITING_INJECT, // 5
              EnmStatusTicket.INJECTING, // 6
            ].includes(e.status) && e.ticketType === 0,
        );
      if (openTicket) {
        const message = ErrorCode.getError(ErrorCode.RSA_BLOCK_CONTINUE_ORDER_EXIST_OPEN_TICKET).replace(
          '{ticket}',
          openTicket?.ticketCode,
        );
        throw new SystemException(
          {
            code: ErrorCode.RSA_BLOCK_CONTINUE_ORDER_EXIST_OPEN_TICKET,
            message: message,
            details: message,
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const arrOrderAffiliate = await this.journeyCoreService.getOrderAffiliateInfoByLcvId(arrLCVId?.at(0));
    this.orderUtilsService.checkRuleContinueBuyingForAffiliate(arrOrderAffiliate, getOrder);

    const getCart = await this.cartAppService.adjustOrder({
      orderCode: getOrder.orderCode,
      orderType: +getOrder.orderType,
      phoneNumber: getOrder.phone,
      shopCode: this.req.headers?.['shop-code'] as string,
    });

    // Get dto tạo phiếu redis
    const ticketCreateCache = await this.redisService.get<Array<CreateTicketDto>>(`${TICKET_CREATE_KEY}:${orderCode}`);

    const regimenIdCart = JSONPath({
      path: '$..schedules[*].regimenId',
      json: ticketCreateCache,
    });

    // lấy danh sách lcvids
    const lcvIds = [];
    const regimenIds = [];
    let regimens = [];
    getCart?.listCartSelected?.forEach((item) => {
      lcvIds.push(item?.lcvId);
      regimenIds.push(item?.regimenId);
    });
    const persons = (await this.familyCoreService.getManyByLcvId({ lcvId: _.compact(_.uniq(lcvIds)) })) || [];
    // update dob to cart
    await this.cartAppService.updateDob(getCart?.headerData?.sessionId, { dob: persons?.at(0)?.dateOfBirth || '' });

    if (regimenIdCart?.length) regimenIds.push(...regimenIdCart);

    if (regimenIds.length > 0) {
      regimens = await this.regimenCoreService.getRegimenByIds({
        regimenIds: _.compact(_.uniq(regimenIds)),
      });

      const arrSku = regimens?.map((e) => e?.vaccine?.sku);
      if (arrSku?.length) {
        const { listProduct } = await this.pimAppService.getListProductBySkuNoRule(arrSku);
        regimens = regimens?.map((regimen) => {
          const product = listProduct?.find((e) => e?.sku === regimen?.vaccine?.sku);
          regimen['vaccine']['isMultiDose'] = product?.isMultiDose || false;
          return regimen;
        });
      }
    }
    // Danh sách lịch sử tiêm bằng lcvID
    const regimenVaccineOrder = await this.regimensService.getRegimenVaccineOrderV2(arrLCVId);

    // call api get jounery by orderCode
    let journeyByOrderCode = await this.journeyCoreService.getJourneyByOrderCode({ orderCode: orderCode });
    const cloneJourney: Array<MultipleJourneyDto> = [];
    Logger.log(
      {
        message: `continue-buy: ${getOrder.orderCode}`,
        fields: {
          info: `continue-buy: ${getOrder.orderCode}`,
          method: `GET`,
          url: `continue-buy: ${getOrder.orderCode}`,
          bodyReq: '{}',
          queryReq: '{}',
          paramsReq: '{}',
          headers: '{}',
          dataRes: JSON.stringify([
            getStartDate(new Date(journeyByOrderCode?.createdDate), '+00:00', 'YYYY-MM-DD'),
            getStartDate(new Date(), '+00:00', 'YYYY-MM-DD'),
          ]),
        },
      },
      false,
    );

    if (
      (!isSameDate(new Date(journeyByOrderCode?.createdDate), new Date()) &&
        (getOrder.orderStatus === OrderStatus.Confirmed || getOrder.orderStatus === OrderStatus.FinishDeposit)) ||
      ([7, 8].includes(getOrder?.orderAttribute) && getOrder?.orderStatus === OrderStatus.FinishDeposit)
    ) {
      /**
       * @TODO
       * clone journey moi
       * Overrite res journeyByOrderCode
       * cap nhat journeyId vao api moi cua bao
       */
      cloneJourney.push({
        ...journeyByOrderCode,
        step: journeyByOrderCode?.journeySteps || [],
        shopCode: this.req.headers?.['shop-code'] as string,
        createdBy: employee_code || journeyByOrderCode?.createdBy || '',
      });

      const resJourney = await this.journeyCoreService.postCreatedMultipleJourney({ items: cloneJourney });
      if (resJourney?.length) {
        journeyByOrderCode = resJourney?.at(0);
      }
      // Update journeyId
      // update dob to cart
      await Promise.all([
        this.cartAppService.updateDob(getCart?.headerData?.sessionId, { journeyId: resJourney?.at(0)?.id || '' }),
        this.journeyCoreService.updateJourneyId({
          orderInforId: getOrder?.orderID,
          journeyId: resJourney?.at(0)?.id,
          modifiedBy: employee_code || getOrder?.modifiedBy || getOrder?.createdBy || '',
          orderCode,
        }),
      ]);
    } else {
      await this.journeyCoreService.updateShopCode(journeyByOrderCode?.id, {
        shopCode: this.req.headers?.['shop-code'] as string,
        modifiedBy: employee_code,
      });
    }

    const ticketCode = journeyByOrderCode?.journeySteps?.find(
      (step) => step?.transactionType === TRANSACTION_TYPE.TICKET_CODE,
    );

    // FV-16631
    if (
      OrderChannels.RSA.includes(this.req.headers['order-channel'] as string) && // chỉ check trên chanel rsa
      OrderChannels.WEB_APP.includes(getOrder?.orderChanel) //Nguồn đơn từ Web/app
    ) {
      const findTicket = resExamTicket?.find((ticket) => ticket?.orderCode === orderCode);
      if (!findTicket) {
        throw new SystemException(
          {
            code: ErrorCode.WEB_APP_ORDER_NOT_YET_COMPLETE_400,
            message: ErrorCode.getError(ErrorCode.WEB_APP_ORDER_NOT_YET_COMPLETE_400),
            details: ErrorCode.getError(ErrorCode.WEB_APP_ORDER_NOT_YET_COMPLETE_400),
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
      // Get ticket detail
      if (findTicket?.ticketType === 0) {
        throw new SystemException(
          {
            code: ErrorCode.WEB_APP_ORDER_HANDLE_BY_DOCTOR_400,
            message: ErrorCode.getError(ErrorCode.WEB_APP_ORDER_HANDLE_BY_DOCTOR_400),
            details: ErrorCode.getError(ErrorCode.WEB_APP_ORDER_HANDLE_BY_DOCTOR_400),
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    if (getOrder?.urlImagePriceProduct.length >= 0) {
      const key_Image = getOrder?.urlImagePriceProduct.map((img) => {
        return img.image;
      });

      const { links } = await this.filesService.getS3Presign({ urls: key_Image });

      let idx = 0;
      for (const item of getOrder?.urlImagePriceProduct) {
        item.url = links?.at(idx);
        ++idx;
      }
    }

    // save step xử lý đơn ecom
    try {
      if (!isEcom && !OrderChannels.RSA.includes(getOrder?.orderChanel)) {
        await this.journeyCoreService.postCreatedJourneyStep({
          journeyId: journeyByOrderCode?.id,
          personIdSub: persons?.at(0)?.lcvId,
          stepId: 20,
          stepName: 'Xử lý đơn Ecom',
          transactionNums: getOrder?.orderCode,
          orderType: getOrder?.orderType,
          createdBy: employee_code,
          transactionType: TRANSACTION_TYPE.ORDER_CODE,
        });
      }
    } catch (error) {}

    const personMain = await this.familyCoreService.getPersonByPhone(getOrder?.phone);
    let journeyData = journeyByOrderCode;

    if (personMain?.at(0)?.lcvId !== journeyByOrderCode?.personIdMain) {
      const arrData = await this.journeyCoreService.updateDetailMultipleJourney({
        modifiedBy: employee_code,
        details: [
          {
            custIdMain: personMain?.at(0)?.customerId,
            id: journeyByOrderCode?.id,
            personIdMain: personMain?.at(0)?.lcvId,
          },
        ],
      });
      journeyData = arrData?.at(0);
      await this.journeyCoreService.updateCustomerInfo({
        journeyId: journeyByOrderCode?.id,
        customerName: personMain?.at(0)?.name || '',
        phone: personMain?.at(0)?.phoneNumber || '',
        modifiedBy: employee_code,
      });
    }

    const ageRanges = await this.regimenCoreService.getAgeRanges();

    let phaseId: string = '';
    if (
      getOrder?.orderAttribute === 7 &&
      getOrder?.orderPaymentCreate?.length === 1 &&
      getOrder?.orderPaymentCreate?.at(0)?.paymentStatus === 1
    ) {
      const arrSku: Array<string> = JSONPath({
        json: getOrder,
        path: '$.details[*]..itemCode',
      });

      const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({
        listSku: arrSku,
      });

      const phase1 = osrDepositAmountBySku?.find((e) => e?.phaseId === 1);
      const isPhase1 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase1?.fromDate).format('YYYY-MM-DD'),
        moment(phase1?.toDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );

      if (isPhase1 && phase1) {
        phaseId = String(phase1?.phaseId);
      }

      const phase2 = osrDepositAmountBySku?.find((e) => e.phaseId === 2);
      const isPhase2 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
        moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );
      if (phase2 && isPhase2 && getOrder?.orderStatus === OrderStatus.FinishDeposit) {
        phaseId = String(phase2?.phaseId);
      }
    }
    if (
      ((getOrder?.orderAttribute === 8 && [...OrderChannels.RSA_AFFILIATE].includes(getOrder?.orderChanel)) ||
        [...OrderChannels.WEB_APP].includes(getOrder?.orderChanel)) &&
      employeeInfo?.handleEmployeeCode &&
      employeeInfo?.handleEmployeeName
    ) {
      const payload: PayloadUpdatedStatusOrderDto = {
        orderCode: orderCode,
        modifiedBy: employeeInfo?.handleEmployeeCode,
        modifiedByName: employeeInfo?.handleEmployeeName,
        orderStatus: OrderStatus.FinishDeposit,
        ecomDisplay: EcomDisplay.AtShop,
        orderType: getOrder.orderType,
        shopCode: getOrder?.shopCode,
      };
      await this.omsService.updateStatusOrderDeposit(payload);
    }

    return {
      order: { ...getOrder, preOrderPhaseId: phaseId } as any,
      getCart,
      vacOrder: regimenVaccineOrder,
      persons: persons.map((p) => ({
        ...p,
        customerAge: calculateTimeDifference(p?.dateOfBirth, p?.from, p?.to, ageRanges, p?.ageUnitCode),
        pregnancy: this.customerService._checkRulePregnancy(p?.pregnancy),
      })),
      regimens,
      journey: journeyData,
      ticketCode: ticketCode?.transactionNums || null,
      ticket: ticketCreateCache?.at(0),
    };
  }

  /**
   * @TODO Thực hiện việc tiếp tục mua hàng cho đơn trả chậm
   */
  async continueBuyingTicketCode(ticketCode: string): Promise<ContinueBuyingTicketCodeRes> {
    /**
     * @TODO
     *    - Lấy thông tin phiếu khám để có orderCodeOld
     *    - Lấy thông tin đơn hàng
     *    - Adjust cart
     *    - vac order (lịch sử tiêm của người đó)
     */
    const getTicket = await this.examinationCoreService.getTicket({ ticketCode });
    const getOrder = await this.omsService.getOneOrder(getTicket?.orderCodeOld);
    const getCart = await this.cartAppService.getCart({
      sessionId: getTicket?.sessionId,
      shopCode: getTicket?.shopCode,
    });

    // lấy danh sách lcvids
    // const lcvIds = [];
    const regimenIds = [];
    let regimens: RegimenItem[] = [];
    getCart?.listCartSelected?.forEach((item) => {
      // lcvIds.push(item?.lcvId);
      regimenIds.push(item?.regimenId);
    });

    const persons = (await this.familyCoreService.getManyByLcvId({ lcvId: [getTicket?.lcvId] })) || [];
    // update dob to cart
    await this.cartAppService.updateDob(getCart?.headerData?.sessionId, { dob: persons?.at(0)?.dateOfBirth || '' });

    if (regimenIds.length > 0) {
      regimens = await this.regimenCoreService.getRegimenByIds({
        regimenIds: _.compact(_.uniq(regimenIds)),
      });
      const arrSku = regimens?.map((e) => e?.vaccine?.sku);
      const { listProduct } = await this.pimAppService.getListProductBySkuNoRule(arrSku);
      regimens = regimens?.map((regimen) => {
        const product = listProduct?.find((e) => e?.sku === regimen?.vaccine?.sku);
        regimen['vaccine']['isMultiDose'] = product?.isMultiDose || false;
        return regimen;
      });
    }

    // Danh sách lịch sử tiêm bằng lcvID
    const arrLCVId: string[] = _.uniq(
      JSONPath({ path: '$.details[*].detailAttachments[*].personIdSub', json: getOrder }),
    );

    const regimenVaccineOrder = await this.regimensService.getRegimenVaccineOrderV2(arrLCVId);

    // call api get jounery by orderCode
    let journeyByOrderCode = await this.journeyCoreService.getJourneyByOrderCode({
      orderCode: getTicket?.orderCodeOld,
    });
    const cloneJourney: Array<MultipleJourneyDto> = [];
    Logger.log(
      {
        message: `continue-buy: ${getOrder.orderCode}`,
        fields: {
          info: `continue-buy: ${getOrder.orderCode}`,
          method: `GET`,
          url: `continue-buy: ${getOrder.orderCode}`,
          bodyReq: '{}',
          queryReq: '{}',
          paramsReq: '{}',
          headers: '{}',
          dataRes: JSON.stringify([
            getStartDate(new Date(journeyByOrderCode?.createdDate), '+00:00', 'YYYY-MM-DD'),
            getStartDate(new Date(), '+00:00', 'YYYY-MM-DD'),
          ]),
        },
      },
      false,
    );

    if (
      // !isSameDate(new Date(journeyByOrderCode?.createdDate), new Date()) &&
      getOrder.orderStatus === OrderStatus.Confirmed ||
      getOrder.orderStatus === OrderStatus.FinishDeposit
    ) {
      /**
       * @TODO
       * clone journey moi
       * Overrite res journeyByOrderCode
       * cap nhat journeyId vao api moi cua bao
       */
      cloneJourney.push({
        ...journeyByOrderCode,
        step: journeyByOrderCode?.journeySteps || [],
      });

      const resJourney = await this.journeyCoreService.postCreatedMultipleJourney({ items: cloneJourney });
      if (resJourney?.length) {
        journeyByOrderCode = resJourney?.at(0);
      }
      // update cart journeyId
      await Promise.all([
        this.cartAppService.updateDob(getCart?.headerData?.sessionId, { journeyId: resJourney?.at(0)?.id || '' }),
        this.journeyCoreService.updateJourneyId({
          orderInforId: getOrder?.orderID,
          journeyId: resJourney?.at(0)?.id,
          modifiedBy: getOrder?.modifiedBy || getOrder?.createdBy || '',
        }),
      ]);
    }

    if (getOrder?.urlImagePriceProduct.length >= 0) {
      const key_Image = getOrder?.urlImagePriceProduct.map((img) => {
        return img.image;
      });

      const { links } = await this.filesService.getS3Presign({ urls: key_Image });

      let idx = 0;
      for (const item of getOrder?.urlImagePriceProduct) {
        item.url = links?.at(idx);
        ++idx;
      }
    }

    const ageRanges = await this.regimenCoreService.getAgeRanges();

    return {
      order: getOrder,
      getCart,
      vacOrder: regimenVaccineOrder,
      persons: persons.map((p) => ({
        ...p,
        customerAge: calculateTimeDifference(p?.dateOfBirth, p?.from, p?.to, ageRanges, p?.ageUnitCode),
      })),
      regimens,
      journey: journeyByOrderCode,
      ticketInfo: getTicket,
      ticketCode,
    };
  }

  async pushOrder(body: PushsOrderPayloadDto) {
    const { orderCode, modifiedBy, ticketInfor } = body;
    const modifiedByName = this.req['user']?.full_name;
    const order = await this.omsService.getOneOrder(orderCode);

    this.orderUtilsService.filterDupTicketSchedule(ticketInfor);

    if (OrderChannels.RSA_AFFILIATE.includes(order.orderChanel) && order.orderStatus === OrderStatus.FinishDeposit) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_PUSH_ORDER_ERROR_001001,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_PUSH_ORDER_ERROR_001001),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_PUSH_ORDER_ERROR_001001),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    if (order.orderStatus === OrderStatus.Cancel) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_CANCELED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    const isPushOrderFirstTime = order.ecomDisplay === EcomDisplay.AtOnline;
    if (Number(order.orderAttribute) !== 7 && order.ecomDisplay === EcomDisplay.AtShop) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_PUSHED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    if (Number(order.orderAttribute) === 7 && isPushOrderFirstTime) {
      // check rule
      const detailAttachments: DetailAttachment[] = JSONPath({
        path: '$.details[*].detailAttachments[*]',
        json: order,
      });

      const lcvIds: string[] = _.compact(_.uniq(detailAttachments?.map((detail) => detail?.personIdSub)));

      await this.orderRuleEngineService.checkRulePreOrder({
        items: detailAttachments?.map((detail) => ({
          sku: detail?.itemCode,
          quantity: detailAttachments?.filter((entry) => entry?.itemCode === detail?.itemCode)?.length,
        })),
        lcvIds: [lcvIds?.at(0)],
      });

      await this.omsService.updateEcomDisplay({
        orderCode: orderCode,
        modifiedBy: modifiedBy,
        modifiedByName: modifiedByName,
        ecomDisplay: EcomDisplay.AtShop,
        shopCode: order.shopCode,
      } as any);

      // return { isSuccess: true, ticketInfor: [] };
    }

    // Tạo phiếu khám

    if (_.isEmpty(ticketInfor)) {
      return { isSuccess: false, ticketInfor: [] };
    }

    const tickets: TicketDetailRes[] | any = await this.ticketUtilsService.createTicketUtil(
      ticketInfor,
      order?.journeyId || '',
      true,
    );

    switch (OrderChannels.RSA_ECOM.includes(this.orderChannel)) {
      case true:
        tickets.forEach((ticketEntry) => {
          ticketEntry.paymentType = null;
        });
        break;

      default:
        const isFullPaymentOrder = await this.isFullPaymentOrder(order.totalBill, order.orderPaymentCreate);
        const linkId = await this.orderUtilsService.getIdLinkFromEcom(order?.orderCode);
        // Không có mũi khám => hoàn tất thanh toán
        if (!tickets[0].indications?.length && isFullPaymentOrder) {
          tickets.forEach((ticketEntry) => {
            ticketEntry.paymentType = PaymentType.PAID;
            ticketEntry.linkId = linkId;
          });
        }
        break;
    }

    const arrTicketPaymentTypePaid = tickets?.filter((e) => e?.paymentType === PaymentType.PAID);
    //@HOHP Tách nợ trước khi bắn PAID
    if (arrTicketPaymentTypePaid && arrTicketPaymentTypePaid?.length === tickets?.length) {
      // book tồn
      const getOrderDetail = await this.omsService.getOneOrder(orderCode);
      const listGiftProduct = getOrderDetail?.details?.filter(
        (detailEntry) => detailEntry.isPromotion === 'Y' && detailEntry?.isInventoryManagement === 'Y',
      );
      for (const ticket of tickets) {
        await this.orderUtilsService.bookingInventory(
          modifiedBy,
          ticket.ticketCode,
          listGiftProduct,
          order.shopCode,
          orderCode,
          true,
        );

        // tách nợ km
      }
      await this.orderUtilsService.debitPromotion(orderCode, tickets?.at(0)?.ticketCode);

      const confirmPromotion = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
      await this.orderUtilsService.confirmPromotion(orderCode, confirmPromotion);
    }

    const arrReturnTicket = [];
    for (const adjust of tickets) {
      const adjTicketEx = await this.examinationCoreService.adjustTicket(
        {
          ticketCode: adjust.ticketCode,
        },
        adjust,
      );
      arrReturnTicket.push(adjTicketEx);
    }

    await Promise.all(
      tickets.map((ticket: TicketDetailRes) =>
        this.journeyCoreService.updateTicketCodeOrerInfo({
          orderCode,
          ticketCode: ticket.ticketCode,
          paymentMethod: JourneyPaymentMethod.Offline,
        }),
      ),
    );

    const payload = {
      orderCode: orderCode,
      modifiedBy: modifiedBy,
      modifiedByName: modifiedByName,
      ecomDisplay: EcomDisplay.AtShop,
      shopCode: order.shopCode,
    };

    const data = await this.omsService.updateEcomDisplay(payload);

    if (!arrReturnTicket?.length) {
      return { isSuccess: data, ticketInfor: [] };
    }

    return { isSuccess: data, ticketInfor: arrReturnTicket };
  }

  async pushPartialOrder(body: PushsOrderPayloadDto) {
    const { orderCode, modifiedBy, ticketInfor } = body;
    const modifiedByName = this.req['user']?.full_name;
    const order = await this.omsService.getOneOrder(orderCode);

    if (order.orderStatus === OrderStatus.Cancel) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_CANCELED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
    if (order.ecomDisplay === EcomDisplay.AtShop) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_PUSHED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    // Tạo phiếu khám

    if (_.isEmpty(ticketInfor)) {
      return { isSuccess: false, ticketInfor: [] };
    }

    const tickets: TicketDetailRes[] | any = await this.ticketUtilsService.createTicketUtil(
      ticketInfor,
      order?.journeyId || '',
      true,
    );

    switch (OrderChannels.RSA_ECOM.includes(this.orderChannel)) {
      case true:
        tickets.forEach((ticketEntry) => {
          ticketEntry.paymentType = null;
        });
        break;

      default:
        const isFullPaymentOrder = await this.isFullPaymentOrder(order.totalBill, order.orderPaymentCreate);
        const linkId = await this.orderUtilsService.getIdLinkFromEcom(order?.orderCode);
        // Không có mũi khám => hoàn tất thanh toán
        if (!tickets[0].indications?.length && isFullPaymentOrder) {
          tickets.forEach((ticketEntry) => {
            ticketEntry.paymentType = PaymentType.PAID;
            ticketEntry.linkId = linkId;
          });
        }
        break;
    }

    const arrTicketPaymentTypePaid = tickets?.filter((e) => e?.paymentType === PaymentType.PAID);
    //@HOHP Tách nợ trước khi bắn PAID
    if (arrTicketPaymentTypePaid && arrTicketPaymentTypePaid?.length === tickets?.length) {
      // book tồn
      const getOrderDetail = await this.omsService.getOneOrder(orderCode);
      const listGiftProduct = getOrderDetail?.details?.filter(
        (detailEntry) => detailEntry.isPromotion === 'Y' && detailEntry?.isInventoryManagement === 'Y',
      );
      for (const ticket of tickets) {
        await this.orderUtilsService.bookingInventory(
          modifiedBy,
          ticket.ticketCode,
          listGiftProduct,
          order.shopCode,
          orderCode,
          true,
        );

        // tách nợ km
      }
      await this.orderUtilsService.debitPromotion(orderCode, tickets?.at(0)?.ticketCode);

      const confirmPromotion = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
      await this.orderUtilsService.confirmPromotion(orderCode, confirmPromotion);
    }

    const arrReturnTicket = [];
    const ticketAdjusts = tickets?.map((ticket) => {
      ticket?.schedules?.forEach((schedule) => (schedule.partialPaid = 5));
      return ticket;
    });
    for (const adjust of ticketAdjusts) {
      const adjTicketEx = await this.examinationCoreService.adjustTicket(
        {
          ticketCode: adjust.ticketCode,
        },
        adjust,
      );
      arrReturnTicket.push(adjTicketEx);
    }

    await Promise.all(
      tickets.map((ticket: TicketDetailRes) =>
        this.journeyCoreService.updateTicketCodeOrerInfo({
          orderCode,
          ticketCode: ticket.ticketCode,
          paymentMethod: JourneyPaymentMethod.Offline,
        }),
      ),
    );

    const payload = {
      orderCode: orderCode,
      modifiedBy: modifiedBy,
      modifiedByName: modifiedByName,
      ecomDisplay: EcomDisplay.AtShop,
      shopCode: order.shopCode,
    };

    const data = await this.omsService.updateEcomDisplay(payload);

    if (!arrReturnTicket?.length) {
      return { isSuccess: data, ticketInfor: [] };
    }

    return { isSuccess: data, ticketInfor: arrReturnTicket };
  }

  async debitPromotion(orderCode: string) {
    return this.orderUtilsService.debitPromotion(orderCode, null);
  }

  /**
   * Check whether payment is full for order
   */
  async isFullPaymentOrder(totalBill: number, orderPaymentCreate: Partial<OrderPaymentCreate>[]) {
    const totalPaidAmount = orderPaymentCreate.reduce((prev, curr) => {
      let paidAmount = 0;
      if (curr.paymentType === 1 && curr.paymentStatus === 4) {
        paidAmount = curr.paymentAmount;
      }
      return prev + paidAmount;
    }, 0);

    return totalPaidAmount === totalBill;
  }

  /**
   * @returns list channel ecom
   */
  async getListChannelEcom() {
    return LIST_CHANNEL_ECOM;
  }

  async getDetailsByTicketCode(ticketCode: string, getDetailsByTicketCodeDTO: GetDetailsByTicketCodeDTO) {
    const ticket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });
    if (!ticket) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_TICKET_NOT_FOUND,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_TICKET_NOT_FOUND)?.replace('{ticketCode}', ticketCode),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_TICKET_NOT_FOUND)?.replace('{ticketCode}', ticketCode),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }
    const order = await this.orderDetail(getDetailsByTicketCodeDTO.orderCode);
    if (!order) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_NOT_FOUND,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND)?.replace('{orderCode}', ticket.orderCode),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND)?.replace('{orderCode}', ticket.orderCode),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }

    const immediateVaccines = ticket.indications;
    const schedules = new Map(ticket.schedules.map((schedule) => [schedule.orderDetailAttachmentCode, schedule]));
    const subsequentVaccines = order.details.find((item) => item.itemCode === '00040035')?.detailAttachments || [];
    const arrOrderDetailAttachmentCode = subsequentVaccines.map((item) => item.orderDetailAttachmentCode);

    const filteredSubsequentVaccines = [...subsequentVaccines];
    for (const immediateVaccine of immediateVaccines) {
      const identifier = immediateVaccine.sku + immediateVaccine.unitCode + immediateVaccine.orderInjections;
      const indexToRemove = filteredSubsequentVaccines.findIndex(
        (subsequentVaccine) =>
          subsequentVaccine.itemCode + subsequentVaccine.unitCode + subsequentVaccine.orderInjection === identifier,
      );
      if (indexToRemove !== -1) {
        filteredSubsequentVaccines.splice(indexToRemove, 1);
      }
    }

    const mergedVaccines = immediateVaccines
      .map(function (immediateVaccine) {
        const item = subsequentVaccines.find(
          (subsequentVaccine) =>
            subsequentVaccine.itemCode + subsequentVaccine.unitCode ===
            immediateVaccine.sku + immediateVaccine.unitCode,
        );
        return {
          ...immediateVaccine,
          ...item,
          ...{
            quantity: 1,
            totalWithFee: Math.max(
              0,
              item?.orderDetailAttachmentCode === immediateVaccine?.orderDetailAttachmentCode
                ? (item?.price || 0) - (item?.discountPromotion || 0)
                : 0,
            ),
            totalPriceVaccine:
              item?.orderDetailAttachmentCode === immediateVaccine?.orderDetailAttachmentCode
                ? (item?.price || 0) - (item?.discountPromotion || 0)
                : 0,
          },
        };
      })
      .filter((vaccine) => arrOrderDetailAttachmentCode.includes(vaccine.orderDetailAttachmentCode));

    const mergedData: Record<string, any> = {};
    filteredSubsequentVaccines.forEach((vaccine) => {
      const key = `${vaccine.itemCode}_${vaccine.unitCode}`;
      if (!mergedData[key]) {
        mergedData[key] = {
          ...vaccine,
          quantity: 0,
          price: 0,
          serviceFee: 0,
          priceAfterFee: 0,
          totalAmount: 0,
          serviceFeePercent: 0,
          discountPromotion: 0,
          totalPriceVaccine: 0,
        };
      }
      mergedData[key].quantity += vaccine.quantity;
      mergedData[key].price += vaccine.price;
      mergedData[key].serviceFee += vaccine.serviceFee;
      mergedData[key].priceAfterFee += vaccine.priceAfterFee;
      mergedData[key].totalAmount += vaccine.totalAmount;
      mergedData[key].discountPromotion += vaccine.discountPromotion;
      mergedData[key].serviceFeePercent = Math.max(mergedData[key].serviceFeePercent, vaccine.serviceFeePercent);
    });
    const mergedSubsequentVaccines = Object.values(mergedData);
    mergedSubsequentVaccines.forEach((vaccine) => {
      vaccine.totalPriceVaccine = vaccine.price - vaccine.discountPromotion;
      vaccine.totalWithFee = Math.max(0, vaccine.price - vaccine.discountPromotion + vaccine.serviceFee);
    });
    return {
      immediateVaccines: mergedVaccines,
      subsequentVaccines: mergedSubsequentVaccines.map((mergedSubsequentVaccine) => ({
        ...schedules.get(mergedSubsequentVaccine.orderDetailAttachmentCode),
        ...mergedSubsequentVaccine,
      })),
      orderInfo: {
        ...(({ details, ...rest }) => rest)(order),
        ...{
          serviceFeeFake: (order?.serviceFee || 0) * 2,
          totalWithFee: Math.max(0, (order?.total || 0) + (order?.serviceFee || 0)),
          originalTotal: order?.total - order?.serviceFee,
        },
      },
    };
  }

  async getDetailsByTicketCodeV2(ticketCode: string, getDetailsByTicketCodeDTO: GetDetailsByTicketCodeDTO) {
    const ticket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });
    if (!ticket) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_TICKET_NOT_FOUND,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_TICKET_NOT_FOUND)?.replace('{ticketCode}', ticketCode),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_TICKET_NOT_FOUND)?.replace('{ticketCode}', ticketCode),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }
    const order = await this.orderDetail(getDetailsByTicketCodeDTO.orderCode);
    if (!order) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_NOT_FOUND,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND)?.replace('{orderCode}', ticket.orderCode),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND)?.replace('{orderCode}', ticket.orderCode),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }
    const subsequentVaccines = order.details.find((item) => item.itemCode === '00040035')?.detailAttachments || [];
    const arrOrderDetailAttachmentCode = subsequentVaccines.map((e) => e.orderDetailAttachmentCode);
    const ticketFilter: ScheduleRes[] = _.uniqBy(
      ticket?.schedules?.filter((e) => arrOrderDetailAttachmentCode.includes(e.orderDetailAttachmentCode)),
      'orderDetailAttachmentCode',
    );
    const dateMin = getStartDate(new Date(_.minBy(ticketFilter, 'appointmentDate')?.appointmentDate), '+07:00');
    const dateMinToAdd30Days = getStartDate(new Date(moment(ticket.createdDate).add(30, 'days').format()), '+07:00');
    // subsequentVaccines.forEach((e) => {
    //   const ticketFind = ticketFilter.find(
    //     (ticketEntry) => ticketEntry.orderDetailAttachmentCode === e.orderDetailAttachmentCode,
    //   );
    //   e['appointmentDate'] = ticketFind?.appointmentDate;
    // });
    const arrOrderDetailAttachmentCodeNow = [];
    ticketFilter.forEach((e) => {
      // Ngày tiêm gần nhất trong đơn && phải trước 30 ngày => Mũi tiêm đợt 1(tiêm ngay)
      Logger.log(
        `e.appointmentDate ${moment(getStartDate(new Date(e.appointmentDate), '+07:00')).format('YYYY-MM-DD')}`,
      );
      Logger.log(`dateMin ${moment(getStartDate(new Date(dateMin), '+07:00')).format('YYYY-MM-DD')}`);
      Logger.log(`dateMinToAdd30Days ${moment(dateMinToAdd30Days).format('YYYY-MM-DD')}`);

      if (
        isSameDate(e.appointmentDate, new Date(dateMin)) &&
        moment(e.appointmentDate).isSameOrBefore(moment(dateMinToAdd30Days), 'D')
      ) {
        arrOrderDetailAttachmentCodeNow.push(e.orderDetailAttachmentCode);
      }
    });

    // start map field 2024-03-06
    //totalBillAfterVoucher
    //totalDirectDiscount
    //totalDiscountVoucher
    //    totalDiscountAdjustment
    const arrPaymentCode = _.uniq(
      _.compact(
        order?.orderPaymentCreate?.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );

    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getPaymentRedis({
      paymentCodes: arrPaymentCode,
    });
    const totalDiscountVoucher = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
    }, 0);

    const totalDiscountAdjustment = order?.details?.reduce((acc, cur) => acc + cur?.discount * cur?.quantity, 0) || 0;
    // end map field 2024-03-06

    const dataReturn = {
      immediateVaccines: [],
      subsequentVaccines: [],
      appointmentSchedulesOrder: [], // sử dụng cho lịch hẹn chi tiết đơn ecom
      orderInfo: {
        ...(({ details, ...rest }) => rest)(order),
        ...{
          serviceFeeFake: (order?.serviceFee || 0) * 2,
          totalWithFee: Math.max(0, (order?.total || 0) + (order?.serviceFee || 0)),
          originalTotal: order?.total - order?.serviceFee,
        },
        totalDiscountAdjustment,
        totalDirectDiscount: order?.totalDiscount - totalDiscountAdjustment,
        totalDiscountVoucher,
        totalBillAfterVoucher: Math.max(0, order?.totalBill - totalDiscountVoucher),
      },
      partialPaymentDetails: [], // thanh toán theo đợt của chi tiết đơn trả chậm
      isFinalPayment: false,
    };

    dataReturn.immediateVaccines = ticketFilter
      .filter((e) => arrOrderDetailAttachmentCodeNow.includes(e.orderDetailAttachmentCode))
      .map((immediateVaccine) => {
        const item = subsequentVaccines.find(
          (subEntry) => subEntry.orderDetailAttachmentCode === immediateVaccine.orderDetailAttachmentCode,
        );
        return {
          ...immediateVaccine,
          ...item,
          appointmentDate: item?.appointmentDate || immediateVaccine?.appointmentDate,
          unitCode: immediateVaccine['unitCode'],
          unitName: immediateVaccine['unitName'],
          unitCodeSale: immediateVaccine['unitCodeSale'] || item['unitCode'],
          unitNameSale: immediateVaccine['unitNameSale'] || item['unitName'],
          ...{
            quantity: 1,
            totalWithFee: Math.max(
              0,
              item?.orderDetailAttachmentCode === immediateVaccine?.orderDetailAttachmentCode
                ? (item?.price || 0) - (item?.discountPromotion || 0) - (item?.discount || 0)
                : 0,
            ),
          },
        };
      });

    const mappingOrderScheduleRoundEnd = ticketFilter
      .filter((e) => !arrOrderDetailAttachmentCodeNow.includes(e.orderDetailAttachmentCode))
      .map((immediateVaccine) => {
        const item = subsequentVaccines.find(
          (subEntry) => subEntry.orderDetailAttachmentCode === immediateVaccine.orderDetailAttachmentCode,
        );
        return {
          ...immediateVaccine,
          ...item,
          appointmentDate: item?.['appointmentDate'] || immediateVaccine?.appointmentDate,
          unitCode: immediateVaccine['unitCode'],
          unitName: immediateVaccine['unitName'],
          unitCodeSale: immediateVaccine['unitCodeSale'] || item['unitCode'],
          unitNameSale: immediateVaccine['unitNameSale'] || item['unitName'],
          ...{
            quantity: 1,
            totalWithFee: Math.max(
              0,
              item?.orderDetailAttachmentCode === immediateVaccine?.orderDetailAttachmentCode
                ? (item?.price || 0) - (item?.discountPromotion || 0) - (item?.discount || 0)
                : 0,
            ),
            totalPriceVaccine:
              item?.orderDetailAttachmentCode === immediateVaccine?.orderDetailAttachmentCode
                ? (item?.price || 0) - (item?.discountPromotion || 0)
                : 0,
          },
        };
      });

    mappingOrderScheduleRoundEnd.forEach((e) => {
      const subFindIndex = dataReturn.subsequentVaccines.findIndex(
        (subsequenFind) => subsequenFind['sku'] === e.sku && subsequenFind['unitCode'] === e.unitCode,
      );
      if (subFindIndex === -1) {
        dataReturn.subsequentVaccines.push({
          ...e,
          totalWithFee: Math.max(0, e.price - e.discountPromotion + e.serviceFee - e.discount),
        });
        return;
      }

      dataReturn.subsequentVaccines[subFindIndex]['quantity'] += e.quantity;
      dataReturn.subsequentVaccines[subFindIndex]['totalWithFee'] = Math.max(
        0,
        (dataReturn.subsequentVaccines[subFindIndex]['price'] -
          dataReturn.subsequentVaccines[subFindIndex]['discountPromotion'] +
          dataReturn.subsequentVaccines[subFindIndex]['serviceFee'] -
          dataReturn.subsequentVaccines[subFindIndex]['discount']) *
          dataReturn.subsequentVaccines[subFindIndex]['quantity'],
      );
    });

    // sortbyId
    dataReturn.subsequentVaccines = _.sortBy(dataReturn.subsequentVaccines, ['id']);
    dataReturn.immediateVaccines = _.sortBy(dataReturn.immediateVaccines, ['id']);
    dataReturn.appointmentSchedulesOrder = _.sortBy(mappingOrderScheduleRoundEnd.concat(dataReturn.immediateVaccines), [
      'id',
    ]);

    dataReturn.partialPaymentDetails = await this._inforDetailOrderForPaymentLater({ order, ticket });

    const orderInfo = await this.journeyCoreService.getOrderInfoByOrderOrTicket({
      ticketCode,
      orderType: 8,
    });

    dataReturn.isFinalPayment = orderInfo?.isFinishPayment;
    /**
     * @TODO
     *  1. Group theo sku trong orderDetailAttachment
     *  2. Mũi tiêm đợt 1 là mũi đầu tiên k tính Phí
     */
    return dataReturn;
  }

  /**
   * @TODO finish order
   */
  async finishOrder(orderId: string, body: FinishOrderLibDto) {
    const getOrder = await this.omsService.getOneOrder(body?.orderCode);
    if (!getOrder) return null;

    await this.omsService.updateStatusPayment(
      getOrder?.orderCode,
      PaymentOnlineStatus.Complete,
      getOrder?.paymentRequestCode,
    );

    const finishOrderResponse = await this.omsService.finishOrder(orderId, {
      ...body,
      versionOrder: getOrder?.orderVersion,
      rewardPoints: getOrder?.rewardPoints,
    });

    /**
     * @TODO IMS
     */
    const finishBookPayload: FinishBookParams = {
      shopCode: getOrder?.shopCode,
      systemBook: SystemBook.VACCINE,
      transNum: getOrder?.orderCode,
      transType: TransType.ORDER,
      updatedBy: body.employee,
    };
    // _.forEach(_.get(getOrder, 'details', []), (element: Partial<DetailLib>) => {
    //   finishBookPayload.push({
    //     docentry: 0,
    //     docType: 1,
    //     systemType: 14,
    //     itemCode: _.get(element, 'itemCode'),
    //     whsCode: _.get(element, 'whsCode'),
    //     shopCode: _.get(getOrder, 'shopCode', ''),
    //     qty_order: _.get(element, 'quantity'),
    //     updateBy: _.get(getOrder, 'createdBy'),
    //     bookType: 1,
    //     orderCode: _.get(getOrder, 'orderCode'),
    //     unitCode: +_.get(element, 'unitCode'),
    //   });
    // });
    await this.imsBookingService.finishBook(finishBookPayload);
    return finishOrderResponse;
  }

  async changeShopCode(payload: ChangeShopCodeDto, user: any) {
    const { orderCode, shopCode, shopName } = payload;
    /**
     * @TODO Cập nhật lại đơn So1
     */
    const [orderData, tickets] = await Promise.all([
      this.omsService.getOneOrder(orderCode),
      this.examinationCoreService.getTicketByOrderCode({ orderCodes: [orderCode] }),
    ]);

    // validating before changing shop code, order, tickets and order !=== completed.
    if (!orderData || orderData?.orderStatus === OrderStatus.Completed) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_SHOP_CODE_CHANGING_ITEM_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_SHOP_CODE_CHANGING_ITEM_NOT_FOUND),
          details: '',
          validationErrors: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const ticketCode = tickets?.items[0]?.ticketCode;
    let updateSchedulePayload = [];
    let getTicket = null;

    if (ticketCode) {
      getTicket = await this.examinationCoreService.getTicket({ ticketCode });

      const { items: schedules } = await this.scheduleCoreService.getScheduleByPersonCodes({
        personCodes: [getTicket?.lcvId],
        status: [0],
      });

      // get all vaccines buyed in orders
      const subsequentVaccines =
        orderData.details.find((item) => item.itemCode === ITEM_VACCINE_SO1)?.detailAttachments || [];
      const listSKu = subsequentVaccines.map((e) => e.itemCode);

      // format payload for updating schedules
      const findTicketSchedule = (schedule: ItemScheduleByPerson) =>
        (getTicket?.schedules || []).find(
          (ticketSchedule) =>
            ticketSchedule.sku === schedule.sku &&
            listSKu.includes(schedule.sku) &&
            ticketSchedule.orderInjections === schedule.injections &&
            ticketSchedule.regimenId === schedule.regimenId &&
            ((schedule.isPaid === false && schedule.status === 0) ||
              (schedule.isPaid === true && schedule.status === 0 && schedule.orderCode === orderCode)),
        );
      updateSchedulePayload = schedules?.filter((schedule) => findTicketSchedule(schedule));

      // if (!updateSchedulePayload.length) {
      //   throw new SystemException(
      //     {
      //       code: ErrorCode.RSA_NO_SCHEDULE_TO_UPDATE,
      //       message: ErrorCode.getError(ErrorCode.RSA_NO_SCHEDULE_TO_UPDATE),
      //       details: '',
      //       validationErrors: null,
      //     },
      //     HttpStatus.BAD_REQUEST,
      //   );
      // }
    }

    const adjustOrderDto: UpdateOrderLibDto = {
      ...orderData,
      orderType: String(OrderType.OrderContract),
      details: orderData.details.map((e) => {
        const price = e?.detailAttachments?.reduce((prev, curr) => (prev += curr.price), 0);
        e.totalBill = price || 0;
        e.total = price || 0;
        e.price = price || 0;
        e.whsCode = (e?.whsCode && e?.whsCode?.replace(orderData?.shopCode, shopCode)) || e?.whsCode;
        return e;
      }),
      shipmentPlannings: orderData?.shipmentPlannings?.map((e) => ({ planningId: e.planningID })),
      modifiedBy: orderData?.modifiedBy || orderData?.createdBy,
      modifiedByName: orderData?.modifiedByName || orderData?.createdByName,
      promotions:
        orderData?.promotionGroup?.map((promotion) => {
          promotion.whsCode =
            (promotion?.whsCode && promotion?.whsCode?.replace(orderData?.shopCode, shopCode)) || promotion?.whsCode;
          return promotion;
        }) || [],
      promotionGroup:
        orderData?.promotionGroup?.map((promotion) => {
          promotion.whsCode =
            (promotion?.whsCode && promotion?.whsCode?.replace(orderData?.shopCode, shopCode)) || promotion?.whsCode;
          return promotion;
        }) || [],
      shopCode,
      shopName,
    };

    const [orderUpdate, ticketUpdated, schedulesUpdated] = await Promise.all([
      this.omsService.updateOrder(orderData?.orderCode, adjustOrderDto),
      getTicket
        ? this.examinationCoreService.adjustTicket(
            { ticketCode },
            {
              ...getTicket,
              shopCode,
              shopName,
            },
          )
        : null,
      // adjust ticket giữ không sửa, update journey thêm mới khi không có ticketCode hoặc không có schedule
      (!getTicket || !updateSchedulePayload?.length) &&
        this.journeyCoreService.updateShopCodeByOrderCode({
          modifiedBy: user?.employee_code || '',
          orderCode,
          shopCode,
          shopName,
        }),
      updateSchedulePayload?.length
        ? this.scheduleCoreService.updateManySchedule(
            updateSchedulePayload?.map(
              (schedule) =>
                ({
                  id: schedule.id,
                  shopCode,
                  shopName,
                  appointmentDate: new Date(schedule.appointmentDate),
                  updatedBy: user?.employee_code || '',
                  updatedByName: user?.full_name || '',
                  note: payload.note || '',
                  customerNote: payload.customerNote || '',
                } as ItemUpdateManyScheduleDto),
            ),
          )
        : null,
    ]);

    return {
      order: orderUpdate,
      ticket: ticketUpdated,
      schedules: schedulesUpdated,
    };
  }

  async checkRuleLoyalty(placeOrderDto: PlaceOrderDto) {
    const getCart = await this.cartAppService.getCart(placeOrderDto);
    return this.orderUtilsService.checkRuleLoyaltyGreaterThanTotalOrder(getCart, placeOrderDto);
  }

  /**
   * @TODO đơn hủy khi không có ticket
   */
  async orderCancelNotTicket(orderCode: string) {
    const getOrderInfor = await this.omsService.getOneOrder(orderCode);

    if (!getOrderInfor) return null;

    const { details } = getOrderInfor;

    let skus: string[] = [];
    details?.map((item) => {
      const itemCodes = item?.detailAttachments
        ?.filter((f) => f?.itemCode)
        ?.map((i) => {
          return i?.itemCode;
        });
      skus = [...skus, ...itemCodes];
    });

    const { listProduct } = await this.pimAppService.getListProductBySku(_.uniq(skus));
    const { data: regimens } = await this.regimenCoreService.getRegimenByListSku({
      skus: _.uniq(skus),
    });

    getOrderInfor?.details?.map(({ detailAttachments }) => {
      detailAttachments?.map((item) => {
        const product = listProduct?.find((i) => i?.sku === item?.itemCode);
        item['productInfo'] = product || null;
        item['regimenInfo'] = regimens?.find((i) => i?.vaccine?.sku === item?.itemCode) || null;
        item['priceAfterDiscountWithFee'] =
          item?.serviceFee + Math.max(0, item?.price - item?.discountPromotion - item?.discount);
        item['unitNameSale'] = item['unitNameSale'] || item?.unitName || '';
        item['unitCodeSale'] = item['unitCodeSale'] || item?.unitCode || null;
      });
    });

    getOrderInfor['totalPriceOriginal'] = Number(getOrderInfor?.total) - (Number(getOrderInfor?.serviceFee) || 0);
    return getOrderInfor;
  }

  /**
   * @description xử lý tab thanh toán theo đợt cho đơn trả chậm
   */
  private async _inforDetailOrderForPaymentLater(payload: { order?: CreateOrderRes; ticket?: TicketDetailRes }) {
    const { order, ticket } = payload;
    Logger.log(`==================== DETAIL ORDER ATTRIBUTE 4 ====================`);
    let mergeOrderWithTicket = [];
    if (order?.orderAttribute !== 4) return mergeOrderWithTicket; // orderAttribute = 4 ,,, đơn trả chậm

    const subsequentVaccines =
      order.details.find((item) => item.itemCode === ITEM_VACCINE_SO1)?.detailAttachments || [];
    if (!subsequentVaccines?.length) return mergeOrderWithTicket;

    const arrAttachmentCodeInOrder = subsequentVaccines.map((e) => e.orderDetailAttachmentCode);

    const orderDetailAttachmentCodes =
      ticket?.indications?.filter((f) => f?.orderDetailAttachmentCode)?.map((i) => i?.orderDetailAttachmentCode) || [];

    // truong792 hợp không có indication
    // thì lấy trong schedules
    if (!orderDetailAttachmentCodes?.length) {
      const scheduleFilter = ticket?.schedules
        ?.filter((e) => arrAttachmentCodeInOrder.includes(e.orderDetailAttachmentCode))
        .map((e) => ({ ...e, formatDate: moment(e.appointmentDate).utcOffset(7).format('YYYY-MM-DD') }));
      const minDate = _.minBy(scheduleFilter, 'formatDate')?.formatDate;
      const arrAttachmentCode = scheduleFilter
        ?.filter((e) => e.formatDate === minDate)
        .map((e) => e.orderDetailAttachmentCode);
      orderDetailAttachmentCodes.push(...arrAttachmentCode);
    }

    Logger.log(`subsequentVaccines: ${JSON.stringify(subsequentVaccines)}`);

    // xử lý vụ đơn ảo SO
    const virtualOrders = await this.omsService.searchOrderForVaccine({ orderCode: order?.orderCode });
    const virtualOrdersAttribute5 = virtualOrders?.orders?.filter((i) => i?.orderAttribute === 5); // filter đơn trả chậm từ oms với orderAttribute === 5
    // orderDatailAttachment từ danh sách đơn ảo từ oms
    const virtualOrderDetailAttachment: Array<DetailAttachment> = JSONPath({
      json: virtualOrdersAttribute5,
      path: '$[*].details[*].detailAttachments[*]',
    });

    Logger.log(`virtualOrderDetailAttachment: ${JSON.stringify(virtualOrderDetailAttachment)}`);

    mergeOrderWithTicket = subsequentVaccines?.map((item) => {
      const itemTicket = ticket?.schedules?.find(
        (e) => e?.sku === item?.itemCode && e?.orderInjections === item?.orderInjection,
      );

      return {
        ...itemTicket,
        ...item,
        appointmentDate: itemTicket?.['appointmentDate'] || item?.appointmentDate,
        isNow: orderDetailAttachmentCodes.includes(item?.orderDetailAttachmentCode) ? true : false,
        totalWithFee: Math.max(
          0,
          (item['price'] - item['discountPromotion'] - item['discount']) * item['quantity'], // thành tiền
        ),
        totalDeposit: 0, // tiền cọc sản phẩm
        totalService: 0, // tiền phí (cọc + phí lưu trữ)
        totalPaymentPartial: 0, // tổng tiền từng đợt
      };
    });

    if (!mergeOrderWithTicket?.length) return [];

    const orderHasDepositAndServiceFee = virtualOrdersAttribute5?.find(
      (i) => i?.details?.find((f) => f?.itemCode === ITEM_SERVICE_FEE) || i?.totalDeposit !== 0, // loại bỏ (i?.totalDeposit !== 0 && i?.serviceFee !== 0)
    );

    // item phí lưu trữ
    let serviceFee = 0;
    if (orderHasDepositAndServiceFee) {
      serviceFee =
        orderHasDepositAndServiceFee?.details?.find((i) => i?.itemCode === ITEM_SERVICE_FEE)?.totalBill ||
        orderHasDepositAndServiceFee?.serviceFee;
    }

    mergeOrderWithTicket = _.sortBy(mergeOrderWithTicket, ['appointmentDate']);
    let idx = 0;
    mergeOrderWithTicket = mergeOrderWithTicket.map((element, index) => {
      const itemSameOrderAttachment = virtualOrderDetailAttachment?.find(
        (i) => i?.orderAttachmentCodeBase === element?.orderDetailAttachmentCode,
      ); // lấy orderCode ảo từ oms map vào mergeOrderWithTicket

      let totalService = 0;
      let totalDeposit = 0;
      let totalServiceAll = 0;
      // nếu có orderCode ảo thì cộng deposit cho item đầu tiên
      if (virtualOrderDetailAttachment?.length) {
        // nếu cùng OrderAttachment ảo và có deposit với serviceFee thì gán lại giá trị đúng với orderCode ảo
        if (
          itemSameOrderAttachment &&
          orderHasDepositAndServiceFee &&
          itemSameOrderAttachment?.orderCode === orderHasDepositAndServiceFee?.orderCode &&
          idx === 0
        ) {
          totalService = +(orderHasDepositAndServiceFee?.totalDeposit + serviceFee) || 0;
          totalDeposit = +orderHasDepositAndServiceFee?.totalDeposit || 0;
          totalServiceAll = +serviceFee;
          ++idx;
        }
      } else {
        if (index === 0) {
          totalService = +order?.totalDeposit + order?.serviceFee;
          totalDeposit = +order?.totalDeposit;
          totalServiceAll = +order?.serviceFee;
        }
      }
      const getCaiDon = virtualOrdersAttribute5.find((e) => e.orderCode === itemSameOrderAttachment?.orderCode);

      return {
        ...element,
        totalService, // tổng phí
        totalServiceAll, // tổng phí
        totalDeposit,
        totalPaymentPartial: Math.max(0, element?.totalWithFee + totalService), // tổng tiền thanh toán từng đợt
        formatDate: moment(getCaiDon?.createdDate || element.appointmentDate)
          .utcOffset(7)
          .format('YYYY-MM-DD'),
        virtualOrderCode: itemSameOrderAttachment?.orderCode || '', // mã đơn hàng ảo
      };
    });
    Logger.log(`mergeOrderWithTicket: ${JSON.stringify(mergeOrderWithTicket)}`);

    const arrFc = (orderCode, value, field: 'totalService' | 'totalServiceFee' | 'totalDeposit') => {
      let amount = 0;
      if (orderHasDepositAndServiceFee && orderHasDepositAndServiceFee?.orderCode === orderCode) {
        if (field === 'totalService') amount = serviceFee + orderHasDepositAndServiceFee?.totalDeposit;

        if (field === 'totalServiceFee') amount = serviceFee;

        if (field === 'totalDeposit') amount = orderHasDepositAndServiceFee?.totalDeposit;
      } else if (!orderHasDepositAndServiceFee) {
        amount = value?.reduce((acc, cur) => acc + cur?.[field === 'totalServiceFee' ? 'totalServiceAll' : field], 0);
      }

      return amount;
    };

    Logger.log(`===== GROUP BY DATE =====`);

    let groupWithDate = _.chain(mergeOrderWithTicket)
      .groupBy((e) => `${e.formatDate}_${e.virtualOrderCode}`)
      ?.map((value, key: string, index) => ({
        key: key,
        date: key.split('_').at(0),
        isNow: value?.some((e) => e?.isNow),
        isPaid: value?.some((e) => e?.virtualOrderCode), // trạng thái thanh toán
        totalService: arrFc(key.split('_').at(1), value, 'totalService'),
        totalServiceFee: arrFc(key.split('_').at(1), value, 'totalServiceFee'),
        totalDeposit: arrFc(key.split('_').at(1), value, 'totalDeposit'),
        totalPaymentPartial: value?.reduce((acc, cur) => acc + cur?.totalPaymentPartial, 0),
        listVaccine: value,
      }))
      .value();
    Logger.log(`groupWithDate: ${JSON.stringify(groupWithDate)}`);
    Logger.log(`==================== END DETAIL ORDER ATTRIBUTE 4 ====================`);

    // sort by data and isPaid
    groupWithDate = _.orderBy(groupWithDate, ['date', 'isPaid', 'totalServiceFee'], ['asc', 'desc', 'desc']);

    return groupWithDate;
  }

  /**
   * @description tính tiền thanh toán chậm và tiền phải trả
   */
  async getCalculatePaymentLate(body: CalculatePaymentLateDto) {
    const { listOrderDetailAttachmentCode, estimatePrice = 0 } = body;

    const result: CalculatePaymentLateRes = {
      totalAmount: 0, // tiền phải trả
      totalAmountLate: 0, // tiền trả chậm
    };

    if (!listOrderDetailAttachmentCode?.length) return { ...result, totalAmount: estimatePrice };
    Logger.log(`==================== TINH TIEN LAN 2 ====================`);
    // lấy ra những OrderDetailAttachmentCode có status = 5
    const orderDetailAttachmentCodes5 = listOrderDetailAttachmentCode
      ?.filter((f) => f?.status === 5 || f?.status === 3 || f?.status === 2)
      ?.map((i) => i?.orderDetailAttachmentCode);

    Logger.log(`orderDetailAttachmentCodes5: ${JSON.stringify(orderDetailAttachmentCodes5)}`);

    if (!orderDetailAttachmentCodes5?.length) return { ...result, totalAmount: estimatePrice };

    // call oms lấy đơn
    let { orders } = await this.omsService.getOrderByOrderAttachmentCode({
      listAttachmentCode: orderDetailAttachmentCodes5,
    });
    orders = orders?.filter((i) => i?.orderAttribute === 4); // filter đơn trả chậm
    Logger.log(`orders: ${JSON.stringify(orders)}`);
    if (!orders?.length) return { ...result, totalAmount: estimatePrice };

    const orderDetailAttachment: Array<DetailAttachment> = JSONPath({
      json: orders,
      path: '$[*].details[*].detailAttachments[*]',
    });
    Logger.log(`orders: ${JSON.stringify(orderDetailAttachment)}`);

    const fillOrderDetailAttachmentMatchPayload = orderDetailAttachment?.filter((i) =>
      orderDetailAttachmentCodes5.includes(i?.orderDetailAttachmentCode),
    );
    Logger.log(`fillOrderDetailAttachmentMatchPayload: ${JSON.stringify(fillOrderDetailAttachmentMatchPayload)}`);

    result.totalAmountLate =
      fillOrderDetailAttachmentMatchPayload?.reduce(
        (acc, cur) => acc + Math.max(cur.price - cur.discountPromotion - cur.discount),
        0,
      ) || 0; // tiền thanh toán chậm
    result.totalAmount = result.totalAmountLate + estimatePrice; // tiền phải trả
    Logger.log(`==================== END TINH TIEN LAN 2 ====================`);
    return result;
  }

  /**
   * @description lấy phương thức thanh toán
   */
  async paymentMethodDetails(order?: GetOneOrderLibResponse) {
    const arrPaymentCode = order?.orderPaymentCreate?.map((p) => p?.paymentCode);
    if (!arrPaymentCode?.length) return [];

    // call qua es-history để lấy thông tin thanh toán
    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getPaymentRedis({
      paymentCodes: _.uniq(arrPaymentCode),
    });

    if (!arrPaymentES?.length) return [];

    let data: PaymentMethodDto[] = [];

    arrPaymentES?.map((i) => {
      const { cash, transfersAll, eWalletAll, vouchersAll, eWalletOnlineAll, installmentAll, cardsAll } = i?.detail;

      // Tiền mặt
      if (cash?.length) {
        cash?.map((item) => {
          const result = this._genDatePaymentMethod({
            type: i?.type,
            dateOfPayment: item?.createdDate,
            amount: item?.amount,
            paymentMethod: 'Tiền mặt',
            employeeCode: item?.createdBy,
          });
          data?.push(result);
        });
      }

      // Chuyển khoản
      if (transfersAll?.length) {
        transfersAll?.map((item) => {
          const result = this._genDatePaymentMethod({
            type: i?.type,
            dateOfPayment: item?.transferDetail?.createdDate,
            amount: item?.transferDetail?.amount,
            paymentMethod: 'Chuyển khoản',
            description: item?.transferDetail?.bankName,
            employeeCode: item?.transaction?.createdBy,
            url: item?.transferDetail?.image || '',
          });
          data?.push(result);
        });
      }

      // Ví điện tử RSA
      if (eWalletAll?.length) {
        eWalletAll?.map((item) => {
          const result = this._genDatePaymentMethod({
            type: i?.type,
            dateOfPayment: item?.eWalletDetail?.createdDate,
            amount: item?.eWalletDetail?.amount,
            paymentMethod: 'Ví điện tử',
            description: item?.eWalletDetail?.transactionVendor,
            employeeCode: item?.transaction?.createdBy,
          });
          data?.push(result);
        });
      }

      // VOUCHER
      if (vouchersAll?.length) {
        vouchersAll?.map((item) => {
          const result = this._genDatePaymentMethod({
            type: i?.type,
            dateOfPayment: item?.voucherDetail?.createdDate,
            amount: item?.voucherDetail?.amount,
            paymentMethod: 'Voucher',
            description: item?.voucherDetail?.name,
            employeeCode: item?.transaction?.createdBy,
          });
          data?.push(result);
        });
      }

      // Ví điện tử cho ecom
      if (eWalletOnlineAll?.length) {
        eWalletOnlineAll?.map((item) => {
          const result = this._genDatePaymentMethod({
            type: i?.type,
            dateOfPayment: item?.eWalletDetail?.createdDate,
            amount: item?.eWalletDetail?.amount,
            paymentMethod: 'Cổng thanh toán',
            description: item?.eWalletDetail?.transactionVendor,
            employeeCode: item?.transaction?.createdBy,
          });
          data?.push(result);
        });
      }

      // Payoo
      if (cardsAll?.length) {
        cardsAll?.map((item) => {
          const result = this._genDatePaymentMethod({
            type: i?.type,
            dateOfPayment: item?.cardsDetail?.createdDate,
            amount: item?.cardsDetail?.amount,
            paymentMethod: 'Thanh toán thẻ',
            description: item?.cardsDetail?.bankName,
            employeeCode: item?.transaction?.createdBy,
          });
          data?.push(result);
        });
      }

      // HomepayLatter
      if (installmentAll?.length) {
        installmentAll?.map((item) => {
          const result = this._genDatePaymentMethod({
            type: i?.type,
            dateOfPayment: item?.createdDate,
            amount: item?.amount,
            paymentMethod: 'HomePaylater',
            description: '',
            employeeCode: item?.createdBy,
          });
          data?.push(result);
        });
      }
    });

    const employeeCodes = data?.map((i) => i?.employeeCode);

    let infoEmployees: GetEmployeeByCodeResponse[] = [];
    if (employeeCodes?.length > 0) {
      infoEmployees = await this.insideService.getEmployeeByCode({ listEmployeeCode: _.uniq(employeeCodes) });
    }

    data = data?.map((i) => {
      return {
        ...i,
        employeeName:
          i?.employeeCode === '001'
            ? 'system'
            : infoEmployees?.find((f) => f?.employeeCode === i?.employeeCode)?.employeeName,
      };
    });

    return _.sortBy(data, 'dateOfPayment');
  }

  private _genDatePaymentMethod(payload: {
    type?: number;
    dateOfPayment?: string;
    amount?: number;
    paymentMethod?:
      | 'Tiền mặt'
      | 'Chuyển khoản'
      | 'Voucher'
      | 'Ví điện tử'
      | 'HomePaylater'
      | 'Thanh toán thẻ'
      | 'Cổng thanh toán';
    description?: string;
    employeeName?: string;
    employeeCode?: string;
    url?: string;
  }) {
    const {
      type,
      dateOfPayment,
      amount = 0,
      paymentMethod = 'Ví điện tử',
      description = '',
      employeeCode,
      employeeName,
      url = '',
    } = payload;
    return {
      type,
      dateOfPayment,
      amount,
      paymentMethod,
      description,
      employeeName,
      employeeCode,
      url,
    };
  }

  /*
   * @description check rule chặn chỉ định tiêm cho phụ nữ mang thai
   */
  async checkRuleIndicationPregnancy(createTicketDto: CreateTicketDto) {
    const arrRegimen = createTicketDto?.indications?.map((i) => {
      return i?.regimenId;
    });

    const customer = await this.familyCoreService.getListPrimaryPerson([createTicketDto?.lcvId]);
    // if (customer && !customer?.at(0)?.pregnancy?.length) return true;

    const pregnancys = this.customerService._checkRulePregnancy(customer?.at(0)?.pregnancy);

    // call qua regiment
    const regimens = await this.regimenCoreService.getRegimenByIds({ regimenIds: _.uniq(arrRegimen) });
    createTicketDto?.indications?.forEach((item) => {
      const regimen = regimens?.find((r) => r?.id === item?.regimenId);
      // nếu các lần khai báo có tồn tại isValid false thì chặn không cho chỉ định
      const isValid = pregnancys?.every((i) => i?.isValid === false); // true => tất cả isValid trong pregnancys đều là false; false => trong pregnancys có tồn tại isValid là true
      if (regimen?.isPregnantRegimen && isValid) {
        const message = ErrorCode.getError(ErrorCode.RSA_CONTRAINDICATION_PREGNANT_WOMEN)
          ?.replace('{vaccineName}', regimen?.vaccine?.name)
          ?.replace('{regimenName}', regimen?.scheduleType);
        throw new SystemException(
          {
            code: ErrorCode.RSA_CONTRAINDICATION_PREGNANT_WOMEN,
            message: message,
            details: message,
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    });
  }

  /**
   * @TODO lấy thanh tin danh sách quà
   */
  async getGiftByOrderCode(param: GetJourneyByOrderOrTicketDto) {
    const result: GetGiftDeliveryRes = {
      giftDeliveryPerson: '',
      giftDeliveryStatus: 0,
      giftDeliveryTime: '',
      arrGift: [],
    };
    if (param?.orderCode) {
      const { giftDeliveryPerson, giftDeliveryStatus, giftDeliveryTime, paymentMethod } =
        await this.journeyCoreService.getOrderInfoByOrderOrTicket(param);
      const getOrder = await this.omsService.getOneOrder(param?.orderCode);
      const isOrderEcom = OrderChannels.RSA_ECOM.includes(getOrder?.orderChanel) && paymentMethod === 1;
      result.arrGift = isOrderEcom
        ? getOrder?.details?.filter((e) => e?.isPromotion === 'Y' && e?.isInventoryManagement === 'Y')
        : [];
      (result.giftDeliveryPerson = giftDeliveryPerson),
        (result.giftDeliveryStatus = giftDeliveryStatus),
        (result.giftDeliveryTime = giftDeliveryTime);
    }

    return result;
  }

  /**
   * @description Danh sách quà chưa giao với khách hàng có đơn từ ecom
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=155513619
   */
  async getGiftForOrderEcomService(lcvId: string) {
    const outPut: {
      items: {
        arrGift: Partial<DetailLib>[];
        ticketCode?: string;
        orderCode: string;
        journeyId: string;
        giftDeliveryPerson?: string;
        giftDeliveryTime?: Date | string;
        giftDeliveryStatus?: number;
      }[];
    } = {
      items: [],
    };

    // lấy danh sách orderCode từ orderInfor
    const { items } = await this.journeyCoreService.getOrderInfo({
      keyWord: lcvId,
      orderStatus: [4],
      sources: ['1', '7'],
    });
    if (!items?.length) return outPut;

    // lấy danh sách đơn từ oms
    const orderCodes = items?.filter((i) => i?.orderCode && i?.paymentMethod === 1)?.map((i) => i?.orderCode);
    if (!orderCodes?.length) return outPut;

    const { orders } = await this.omsService.getListOrderES({
      orderCode: _.uniq(orderCodes),
    });
    if (!orders?.length) return outPut;

    // // filter danh sách đơn với orderChanel = 7, 1 => của ecom
    const orderForEcoms = orders?.filter((o) => OrderChannels.RSA_ECOM.includes(o?.orderChanel));
    if (!orderForEcoms?.length) return outPut;

    // lấy danh sách quà
    const lstStatusGift = await this.journeyCoreService.getStatusGiftByOrderCodes({
      orderCodes: _.uniq(orderForEcoms?.map((i) => i?.orderCode)),
    });
    if (!lstStatusGift?.length) return outPut;
    // filter quà với trạng thái chưa giao
    const filterStatus0 = lstStatusGift?.filter((i) => i?.giftDeliveryStatus === 0);
    if (!filterStatus0?.length) return outPut;

    // loop order từ order của ecom ròi map với order của quà có status = 0
    for (const order of orderForEcoms) {
      const findOrder = filterStatus0?.find((i) => i?.orderCode === order?.orderCode);
      if (!findOrder) continue;
      // fillter quà
      const promotions = order?.details?.filter(
        (e) => e?.isPromotion === 'Y' && findOrder?.orderCode === order?.orderCode && e?.isInventoryManagement === 'Y',
      );
      if (promotions?.length) {
        outPut.items.push({
          arrGift: promotions,
          ticketCode: findOrder?.ticketCode,
          orderCode: order?.orderCode,
          journeyId: findOrder?.journeyId,
          giftDeliveryStatus: findOrder?.giftDeliveryStatus,
          giftDeliveryPerson: findOrder?.giftDeliveryPerson,
          giftDeliveryTime: findOrder?.giftDeliveryTime,
        });
      }
    }

    return outPut;
  }

  async createTicketPartialPayment(body: CreateTicketPartialPaymentDto) {
    let ticketInfo = {};
    const getCart =
      body?.createTicketDto?.shopCode && body?.createTicketDto?.sessionId
        ? await this.cartAppService.getCart({
            shopCode: body?.createTicketDto?.shopCode || '',
            sessionId: body?.createTicketDto?.sessionId || '',
          })
        : undefined;
    if (body?.createTicketDto?.ticketCode) {
      ticketInfo = await this.examinationCoreService.adjustTicket({ ticketCode: body?.createTicketDto?.ticketCode }, {
        ...body?.createTicketDto,
        journeyId: getCart?.headerData?.journeyId || '',
        orderCode: null,
      } as any);
    } else {
      const createTicket = await this.examinationCoreService.createTicket([
        { ...body?.createTicketDto, journeyId: getCart?.headerData?.journeyId || '', orderCode: null },
      ]);
      ticketInfo = createTicket?.at(0);
    }
    const orderInfo = await this.omsService.getOneOrder(body?.orderCodeOld);
    return { orderInfo, ticketInfo };
  }

  async updateStatusOrderPartialPayment(body: UpdateStatusOrderPartialPaymentDto) {
    const { orderCode, modifiedBy, modifiedByName, ticketCode } = body;
    const getOrder = await this.omsService.getOneOrder(orderCode);

    let ticket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });
    /**
     * @TODO Đối với đơn hàng trả chậm
     *  - Update trạng thái vaccineOrder
     *  - Order Attribute = 4 => Lấy mũi tiêm đợt 1 trong đơn.
     *  - Update trạng thái vaccineOrder là đã thanh toán
     */
    const updateStatusByAttachmentCodeDto: Array<UpdateStatusByAttachmentCodeDto> =
      this.orderUtilsService.getStatusForTraTungPhan(getOrder, ticket, modifiedBy);

    if (updateStatusByAttachmentCodeDto?.length && getOrder?.orderChanel === '14') {
      await this.journeyCoreService.updateEmployeeOrderInfo(ticketCode, {
        modifiedBy: modifiedBy,
        saleEmployeeName: modifiedByName,
      });
    }

    if (updateStatusByAttachmentCodeDto?.length) {
      await this.vacOrderInjectionService.updateStatusByAttachmentCode(updateStatusByAttachmentCodeDto);
    }
    ticket = await this.examinationCoreService.adjustTicket(
      { ticketCode },
      {
        ...ticket,
        status: ticket?.indications?.length !== 0 ? EnmStatusTicket.CHO_KHAM : EnmStatusTicket.DONE,
        schedules: ticket?.schedules?.map((e) => {
          const orderAttachmentFind = updateStatusByAttachmentCodeDto?.find(
            (orderEntry) => orderEntry.orderDetailAttachmentCode === e?.orderDetailAttachmentCode,
          );
          if (orderAttachmentFind && orderAttachmentFind?.status === 5) {
            e['partialPaid'] = 5;
          }

          return e;
        }),
        ticketType: 0,
      },
    );

    // Refund 1M lại cho khách
    await this.businessOrderService.refundDeposit(orderCode, ticket, modifiedBy);

    const arrPaymentCode: string[] = _.flattenDeep(_.uniq(getOrder?.orderPaymentCreate?.map((e) => e.paymentCode)));
    const getListPayment = await this.paymentGWService.getListRedis(arrPaymentCode);

    const totalDaThanhToan2 = getListPayment?.reduce((acc, cur) => acc + getDepositDetailAmount(cur.detail), 0);
    if (totalDaThanhToan2 > 0) {
      const ticketIndicationStatus5 = ticket?.indications?.filter((e) => e.status === 5);
      const { total } = await this.businessOrderService.getPriceStatus5(ticketIndicationStatus5);

      const updatePartialPaymentDto: UpdatePartialAmountDto = {
        modifiedBy: modifiedBy,
        partialPaymentAmount: total, // Tiền mũi tiêm dợt này
        ticketCode: ticketCode,
        totalBill: total || 0, // Tiền thanh toán hôm nay,
        orderCodeOld: ticket?.orderCodeOld,
        orderAttribute: 4,
        isFinishPayment: getListPayment?.at(0)?.isPayment,
      };

      await this.journeyCoreService.updatePartialAmount(updatePartialPaymentDto);

      this.redisService.set(
        `${PARTIAL_PAYMENT_REDIS_KEY}:${ticketCode}`,
        JSON.stringify(updatePartialPaymentDto),
        'EX',
        getExpiredTime('day', 1),
      );
    }

    const assignRoomData: AssignRoomDtoV2 = {
      ticketCodes: [ticket?.ticketCode],
      shopCode: ticket?.shopCode,
      roomType: ClinicType.PK,
      rule: EnmAssignRule.ASSIGN_EXAMINATION,
      modifiedBy: modifiedBy,
    };

    const assignRooms = await this.examinationCoreService.assignRoomV2(assignRoomData);
    const arrTicket: TicketDetailRes[] = [];
    for (const room of assignRooms) {
      const ticketRes = await this.examinationCoreService.adjustTicket(
        { ticketCode: room.ticketCode },
        {
          ...room,
          paymentType: PaymentType.PAID,
          paymentEmployeeCode: modifiedBy,
          paymentEmployeeName: modifiedByName,
        },
      );
      arrTicket.push(ticketRes);
    }

    return { ticketInfor: arrTicket, isSuccess: true };
  }

  /**
   * Updates the status of an OMS order deposit.
   */
  async updateStatusOmsOrderDeposit(payload: PayloadUpdatedStatusOrderDto, orderFetched?: GetOneOrderLibResponse) {
    const { orderCode, modifiedBy, modifiedByName } = payload;
    let getOrder: GetOneOrderLibResponse;

    if (!orderFetched) {
      getOrder = await this.omsService.getOneOrder(orderCode);
    } else {
      getOrder = orderFetched;
    }

    if (getOrder?.orderAttribute === OrderAttribute.PRE_ORDER) {
      const detailAttachments: DetailAttachment[] = JSONPath({
        path: '$.details[*].detailAttachments[*]',
        json: getOrder,
      });

      const lcvIds: string[] = _.compact(_.uniq(detailAttachments?.map((detail) => detail?.personIdSub)));

      await this.orderRuleEngineService.checkRulePreOrder({
        items: detailAttachments?.map((detail) => ({
          sku: detail?.itemCode,
          quantity: detailAttachments?.filter((entry) => entry?.itemCode === detail?.itemCode)?.length,
        })),
        lcvIds: [lcvIds?.at(0)],
      });
    }

    if (!getOrder) {
      throw new SystemException(
        {
          code: ErrorCode.NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.NOT_FOUND),
          details: '',
          validationErrors: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }

    await this.omsService.getOneOrder(orderCode);
    // await this.omsService.updateStatusPayment(orderCode, PaymentOnlineStatus.Complete, getOrder.paymentRequestCode);

    const employeeStep5 = _.orderBy(
      getOrder?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const payloadUpdatedStatusOrderDto: PayloadUpdatedStatusOrderDto = {
      orderCode: orderCode,
      modifiedBy: employeeStep5?.employeeCode || modifiedBy,
      modifiedByName: employeeStep5?.employeeName || modifiedByName,
      orderStatus: OrderStatus.FinishDeposit,
      ecomDisplay: EcomDisplay.AtShop,
      orderType: getOrder.orderType,
      shopCode: this.req.headers?.['shop-code'] as string,
    };
    const payloadUpdateAmount: UpdateAmountDto = {
      orderCode: orderCode,
      modifiedBy: employeeStep5?.employeeCode || modifiedBy,
      totalBill: getOrder?.totalBill,
      totalPayment: getOrder?.totalDeposit,
      gapAmount: Math.max(0, getOrder?.totalBill - getOrder?.totalDeposit),
      ecomDisplay: EcomDisplay.AtShop,
      orderStatus: OrderStatus.FinishDeposit,
    };

    const [data] = await concurrentPromise(
      this.omsService.updateStatusOrderDeposit(payloadUpdatedStatusOrderDto),
      this.journeyCoreService.updateAmount(payloadUpdateAmount),
    );

    return data;
  }

  async isPreOrderPhase2(orderCode: string) {
    const getOrder = await this.omsService.getOneOrder(orderCode);
    if (getOrder?.orderAttribute !== OrderAttribute.PRE_ORDER) {
      return {
        isPreOrderPhase2: false,
        order: getOrder,
      };
    }
    const arrSku: Array<string> = JSONPath({
      json: getOrder,
      path: '$.details[*]..itemCode',
    });

    const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({
      listSku: arrSku,
    });

    const phase2 = osrDepositAmountBySku?.find((e) => e.phaseId === 2);
    if (!phase2) {
      return {
        isPreOrderPhase2: false,
        order: getOrder,
      };
    }

    return {
      isPreOrderPhase2: moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
        moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      ),
      order: getOrder,
    };
  }

  async getOrderDepositByLcvId(lcvId?: string) {
    const orders = await this.journeyCoreService.getOrderAffiliateInfoByLcvId(lcvId);
    const setSKU = new Set<string>();

    let detailPreOrders =
      (orders?.length &&
        orders.map((item) => {
          const details = (item.details || []).filter((detail) => detail.isPromotion !== 'Y');
          const allDetailAttachments = details?.flatMap((detail) => detail?.detailAttachments);
          const skuInfo = details?.at(0)?.detailAttachments?.at(0);
          const skuItemCode = skuInfo?.itemCode;
          const skuItemName = skuInfo?.itemName;
          setSKU.add(skuItemCode);

          const groupBySKU = _.groupBy(allDetailAttachments, 'itemCode');
          const skuAndTotalQuantity = _.mapValues(groupBySKU, (attachmentByItem) =>
            _.sumBy(attachmentByItem, 'quantity'),
          );

          const totalDeposit =
            (item?.orderPaymentCreate?.length &&
              item?.orderPaymentCreate
                ?.filter((i) => i?.paymentStatus === 4 && i?.paymentType === 1)
                ?.reduce((accumulator, i) => {
                  return (accumulator += i?.paymentAmount);
                }, 0)) ||
            0;

          return {
            ...item,
            skuItemCode,
            skuItemName,
            totalQuantity: skuAndTotalQuantity[skuItemCode] || item.totalQuantity,
            totalDeposit,
          };
        })) ||
      [];

    const regimens = [];
    const listItemCodes = _.uniq(
      detailPreOrders?.map((item) => {
        return item?.skuItemCode;
      }),
    );

    if (listItemCodes.length) {
      regimens.push(...(await this.regimenCoreService.getListDiseaseGroupBySku(listItemCodes)));
    }

    detailPreOrders = detailPreOrders?.map((item) => {
      const diseaseGroupInfo = regimens?.find((regimen) => regimen?.sku === item?.skuItemCode);
      return {
        ...item,
        diseaseGroupInfo: diseaseGroupInfo,
        nameDisplay: `${diseaseGroupInfo?.diseaseGroupName || ''} - ${diseaseGroupInfo?.skuName || ''}`,
        isAbleToContinueDepositOrder: OrderChannels.RSA.includes(this.orderChannel) && !item?.ticketCode,
      };
    });
    return {
      orders: detailPreOrders || [],
    };
  }

  async getOrderForPaymentChecking(orderCode: string) {
    const getOrder = await this.omsService.getOneOrder(orderCode);
    const arrPaymentCode = _.uniq(
      _.compact(
        getOrder.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );
    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getManyPaymentHistoryES(
      arrPaymentCode,
    );

    const totalDiscountVoucher = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
    }, 0);

    const paymentInfo = this.orderUtilsService._mapPaymentInfo(getOrder, totalDiscountVoucher, arrPaymentES);

    const arrVoucherProvider: number[] = [
      VoucherPartnerId.GotIT,
      VoucherPartnerId.Taptap,
      VoucherPartnerId.Urbox,
      VoucherPartnerId.UTop,
    ];

    const arrVoucherDetail: VoucherDetail[] =
      JSONPath({
        json: arrPaymentES,
        path: '$[*]..vouchersAll[*].voucherDetail',
      })?.filter((e: VoucherDetail) => arrVoucherProvider.includes(e.voucherType)) || [];

    const totalVoucherPartner = arrVoucherDetail?.reduce((prev, curr) => {
      return prev + curr.amount;
    }, 0);

    await this.updatePaymentInfoFollowCart(paymentInfo, getOrder, totalDiscountVoucher, totalVoucherPartner);

    return {
      ...getOrder,
      paymentInfo,
    };
  }

  async checkRuleCreateOrder(lcvId: string) {
    const [{ items: listOrderEcomNotCompleted }, { items: listOrderAffCompleted }] = await Promise.all([
      // Lấy danh sách đơn Ecom chưa thanh toán
      this.journeyCoreService.getOrderInfo({
        keyWord: lcvId,
        orderStatus: [OrderStatus.Confirmed],
        sources: OrderChannels.RSA_ECOM,
      }),
      // Lấy danh sách đơn vệ tinh ở trạng thái hoàn tất cọc
      this.journeyCoreService.getOrderInfo({
        keyWord: lcvId,
        orderStatus: [OrderStatus.FinishDeposit],
        sources: OrderChannels.RSA_AFFILIATE,
      }),
    ]);

    const listOrderEcomNotCompletedAtShop = listOrderEcomNotCompleted?.filter(
      (item) => item?.ecomDisplay === EcomDisplay.AtShop,
    );

    if (listOrderEcomNotCompletedAtShop?.length && listOrderAffCompleted?.length) {
      //FV-13572
      const listEcomOrderCode = listOrderEcomNotCompletedAtShop?.map((itemOrder) => itemOrder?.orderCode);
      return {
        allow: false,
        message:
          'Lưu ý: Đơn cọc vệ tinh phải được ưu tiên xử lý trước. Nếu đang xử lý đơn ECOM ở trên, vui lòng hoàn tất hoặc hủy đơn để có thể xử lý đơn cọc vệ tinh',
        listOrderCodeEcom: listEcomOrderCode,
      };
    }

    return {
      allow: true,
    };
  }

  private async checkMobileCarrierSelection(phoneNumber: string) {
    const receipt = await this.osrService.getListCashBackByPhone([phoneNumber]);
    if (receipt?.length) return true;
    const cashbackSuggestOrders = await this.osrService.searchCashbackSuggestByPhoneNumber([phoneNumber]);
    const order = cashbackSuggestOrders?.find(
      (item) => item?.status === CashbackOrderStatus.DON_HANG_DANG_XU_LY && item?.isCashBack,
    );
    if (!order) return true;
    if (!order?.brandUniqueId) return false;
    return true;
  }
}
