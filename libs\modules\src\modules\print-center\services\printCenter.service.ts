import { Inject, Injectable } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import moment from 'moment';
import {
  CASHBACK_KEY,
  concurrentPromise,
  GUID_EMPTY,
  RedisService,
  translateVietnameseAccents,
  convertNumberToWordsEn,
} from '@shared';
import { EnmStatusTicket, ExaminationCoreService, PaymentType } from 'vac-nest-examination';
import { FamilyProfileDetails, FamilyService, GetPersonByIdRes } from 'vac-nest-family';
import { VacHistoryService } from 'vac-nest-history';
import { InsideService } from 'vac-nest-inside';
import {
  PRINT_TYPE,
  PrintCenterService as PrintCenterCoreService,
  PrintCenterRes,
  PrintHistoryDto as PrintHistoryCoreDto,
  PrintMedicalCardDto,
  PrinterType,
  VaccineBarcodeDto,
  VaccineHistory,
} from 'vac-nest-print-center';
import { SHOP_PHONE, TemplateType } from '../constants';
import {
  AppendixDto,
  AttachBillReturnHomeDto,
  BillAffiliateDto,
  CustomerDto,
  CustomerFPDto,
  DosageItem,
  GetPrinterDto,
  GetPrinterResponse,
  GetServiceContractDto,
  InvoiceDto,
  MedicalCardDto,
  MedicalCardRes,
  PriceListDto,
  PrintBillDataRes,
  PrintDataScheduleInjectionDto,
  PrintHistoryDto,
  PrintInvoiceFamilyPackageForZaloDto,
  PrintReportRebiesDto,
  PrintReportUsedDto,
  PrintServiceContractDto,
  PrintServiceContractFPDto,
  PrintVaccinationConfirmDto,
  PrinterInfo,
  ProductData,
  ScheduleInjectionDto,
  ServiceContractFamilyPackageDto,
  VaccineBarcodePayloadDto,
  ZaloInvoiceDto,
} from '../dto';
import {
  calculatorAge,
  calculatorAgeEN,
  convertNumberToVietnamese,
  getFullAddress,
  getFullAddressCertificate,
  getPaymentMethod,
  masterDataUnitName,
} from '../functions';
import { PrintUtilsService } from './print-utils.services';
import { EmployeeStep, OMSService, OrderStatus } from 'vac-nest-oms';
import { ReportCoreService } from 'vac-nest-report';
import { ReturnHomeService } from 'vac-nest-return-home';
import { convertNumberToMinusVietnamese } from '../functions/totalWordReturnHome';
import { OrderInjectionCommonRes, OrderInjectionRes, VacOrderInjectionService } from 'vac-nest-order-injection';
import _ from 'lodash';
import { JSONPath } from 'jsonpath-plus';
import { RegimenItem, RegimenService } from 'vac-nest-regimen';
import { ContractStatus, SearchContractRes, VacContractService } from 'vac-nest-contract';
import { SchedulesUtilsService } from '../../schedules/services/schedules-utils.service';
import { OrderAttribute } from 'vac-nest-cart-app';
import { JourneyService, OrdersInfo } from 'vac-nest-journey';
import { DSMSService, Province, District, Ward } from 'vac-nest-dsms';
import {
  getDepositedAmountByMethods,
  GetPaymentHistoryESLibResponse,
  PaymentGatewayService,
} from 'vac-nest-payment-gateway';
import { GetCashBackByListAttCodeResponse } from 'vac-nest-osr/dist/dto/get-list-cash-back.dto';
import { CashbackSuggestRes, OsrService } from 'vac-nest-osr';
import { GetListProductBySkuRes, PIMAppService } from 'vac-nest-pim-app';
import { LcPrintCenterService as LcPrintCenterLibService } from 'lc-nest-print-center';
import { ReasonCodeAffiliate, TemplateAffiliate } from '@shared/enum/templateAffiliate';
import { plainToInstance } from 'class-transformer';
import { FamilyPackageService } from 'vac-nest-family-package';
import { CamundaNotificationApiService } from '@frt/nestjs-api';

@Injectable()
export class PrintCenterService {
  shopCode: string;
  constructor(
    private readonly printCenterService: PrintCenterCoreService,
    private readonly printUtilsService: PrintUtilsService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly insideService: InsideService,
    private readonly familyService: FamilyService,
    private readonly historyService: VacHistoryService,
    private readonly reportCoreService: ReportCoreService,
    private readonly returnHomeService: ReturnHomeService,
    private readonly omsService: OMSService,
    private readonly vacOrderInjectionService: VacOrderInjectionService,
    private readonly regimenService: RegimenService,
    private readonly contractService: VacContractService,
    private readonly journeyService: JourneyService,
    private readonly schedulesUtilsService: SchedulesUtilsService,
    private readonly dsmsService: DSMSService,

    private readonly lcPrintCenter: LcPrintCenterLibService,
    private readonly pimCoreService: PIMAppService,
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly osrService: OsrService,
    private readonly redisService: RedisService,
    private readonly familyPackageService: FamilyPackageService,
    private readonly camundaNotificationApiService: CamundaNotificationApiService,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
  }

  async getPrinter(getPrinterDto: GetPrinterDto) {
    const listPrinter = await this.printCenterService.getPrinter(getPrinterDto);

    const listPrinterMapping: PrinterInfo[] = listPrinter?.map((printer) => ({
      id: printer.id,
      macID: printer?.mac_address,
      macName: printer?.display_name,
      printerType: printer?.printer_type,
    }));

    const printerInfo: GetPrinterResponse = {
      listPrinterAll: listPrinterMapping || [],
      listPrinterA4: listPrinterMapping?.filter((printer) => printer?.printerType === PrinterType.PrinterA4) || [],
      listPrinterBill: listPrinterMapping?.filter((printer) => printer?.printerType === PrinterType.PrinterBill) || [],
    };

    return printerInfo;
  }

  async vaccineBarcode(vaccineBarcodePayloadDto: VaccineBarcodePayloadDto) {
    const vaccineBarcodeDto: VaccineBarcodeDto = {
      printType: PRINT_TYPE.VACCINE_BARCODE,
      printData: {
        customerId: vaccineBarcodePayloadDto?.customerId || '',
        customerName: vaccineBarcodePayloadDto?.customerName || '',
        shopCode: this.shopCode || '',
        lcvId: vaccineBarcodePayloadDto?.lcvId || '',
        dateOfBirth: vaccineBarcodePayloadDto?.dateOfBirth || '',
        nationalVaccineCode: vaccineBarcodePayloadDto?.nationalVaccineCode,
      },
      printerInfo: {
        macID: vaccineBarcodePayloadDto?.macID || '',
        macName: vaccineBarcodePayloadDto?.macName || '',
        ignorePrint: vaccineBarcodePayloadDto?.ignorePrint || false,
      },
      ignorePrint: vaccineBarcodePayloadDto?.ignorePrint || false,
    };

    vaccineBarcodeDto.printData['type'] = vaccineBarcodePayloadDto?.type;

    return await this.printCenterService.vaccineBarcode(vaccineBarcodeDto);
  }

  async printInvoice(invoiceDto: InvoiceDto) {
    return this.printUtilsService.printInvoice(
      {
        ...invoiceDto,
        printType: PRINT_TYPE.VACCINE_INVOICE,
      },
      this.shopCode,
      true,
    );
  }

  async sendZaloInvoice(zaloInvoiceDto: ZaloInvoiceDto) {
    const { isManualSendBill } = zaloInvoiceDto;
    const bill = await this.printUtilsService.printInvoice(
      {
        ...zaloInvoiceDto,
        printerInfo: {
          ignorePrint: true,
          macID: '',
          macName: '',
        },
        printType: PRINT_TYPE.VACCINE_INVOICE,
        totalPriceReturn: 0,
        totalPriceCustomerPay: 0,
      },
      this.shopCode,
      true,
    );

    if (!isManualSendBill) {
      return bill;
    } else {
      this.camundaNotificationApiService.sendNotificationBillZalo({
        isManualSendBill: true,
        orderCode: zaloInvoiceDto?.orderCode,
        url: bill?.imageUrl,
      });
      return bill;
    }
  }

  async printListAttachBill(invoiceDto: InvoiceDto) {
    return this.printUtilsService.printInvoice(
      {
        ...invoiceDto,
        printType: PRINT_TYPE.VACCINE_LIST_ATTACH_BILL,
      },
      this.shopCode,
      false,
    );
  }

  async printMedicalCard(medicalCardDto: MedicalCardDto): Promise<PrintCenterRes> {
    const { vaccineTicketId, employeeCode, page } = medicalCardDto;
    const ticketData = await this.examinationCoreService.getManyTicketDB({
      ticketCodes: [vaccineTicketId],
    });
    const ticketInfor = ticketData?.at(0);

    //filter theo shop code
    const listEmployeeInfor = await this.insideService.getListShopByEmployee(employeeCode);
    const employeeInfor = listEmployeeInfor?.shops?.find((shop) => shop?.shopCode === ticketInfor?.shopCode);

    let customerInfor: GetPersonByIdRes = null;
    try {
      if (ticketInfor?.personId) {
        customerInfor = await this.familyService.getFamilyByLcvId({ lcvId: ticketInfor?.lcvId });
      } else customerInfor = null;
    } catch (error) {
      customerInfor = null;
    }

    const hostFamily: FamilyProfileDetails = customerInfor?.familyProfileDetails?.find((profile) => profile?.isHost);

    const dateOfBirth = moment(customerInfor?.dateOfBirth)?.utcOffset(7).format('DD/MM/YYYY');

    const healthCheck = await this.printUtilsService.mappingBiosignal(ticketInfor.healthChecks);

    const {
      custAddress,
      custPermanentAddress,
      locationAndTime,
      indications,
      lstNotes,
      trackingTime,
      examinedTimeText,
    } = await this.printUtilsService.getAddressMultiLanguage(
      customerInfor,
      medicalCardDto?.templateType,
      ticketInfor,
      ticketInfor?.healthCheckQuestions,
      employeeInfor,
    );

    const materialAttachments = ticketInfor.materialAttachments?.filter((material) => material?.status === 0);

    if (medicalCardDto?.templateType === TemplateType.EN && materialAttachments?.length) {
      materialAttachments.at(0).unitName = masterDataUnitName(
        materialAttachments?.at(0)?.unitName,
        materialAttachments?.at(0)?.quantity,
      );
    }

    const printData: MedicalCardRes = {
      ignorePrint: medicalCardDto?.printerInfo?.ignorePrint,
      printType: PRINT_TYPE.MEDICAL_CARD,
      printerInfo: medicalCardDto?.printerInfo,
      printData: {
        shopCode: ticketInfor?.shopCode || '',
        shopName: employeeInfor?.shopName ?? employeeInfor?.addressBill ?? '',
        storageName: `${ticketInfor?.shopCode}/${vaccineTicketId}`,
        vaccineTicketId: vaccineTicketId,
        customerInfo: {
          customerName: customerInfor?.name ?? '',
          customerPhone: customerInfor?.phoneNumber || hostFamily?.phoneNumber || '',
          gender: customerInfor?.gender || 0,
          age:
            medicalCardDto?.templateType === TemplateType.EN
              ? calculatorAgeEN(customerInfor?.dateOfBirth)
              : calculatorAge(customerInfor?.dateOfBirth),
          isChild:
            moment(moment().utcOffset(7).format('YYYY-MM-DD')).diff(
              moment(customerInfor?.dateOfBirth).utcOffset(7).format('YYYY-MM-DD'),
              'months',
            ) < 1,
          dob: dateOfBirth,
          custAddress: custAddress,
          custPermanentAddress: custPermanentAddress,
          guardianName: customerInfor?.phoneNumber ? '' : hostFamily?.name || '',
          healthInfo: healthCheck,
        },
        medicalCardDetail: {
          medicalExamDoctor: ticketInfor?.doctorName || '',
          injectingNursingCode: ticketInfor?.injectingNursingCode,
          injectingNursingName: ticketInfor?.injectingNursingName,
          trackingTime,
          locationAndTime: locationAndTime,
          examinedTime: examinedTimeText,
        },
        healthCheckQuestions: ticketInfor?.healthCheckQuestions,
        indications,
        conclusion: ticketInfor?.conclusion || '',
        lcvId: ticketInfor.lcvId,
        isPaid:
          ticketInfor?.paymentType === PaymentType.PAID ||
          !!(!ticketInfor?.orderCode && !ticketInfor?.orderCodeOld && ticketInfor?.indications?.length),
        templateType: medicalCardDto.templateType,
        page: page || 1,
        materialAttachments,
        notes: lstNotes,
      },
    };

    return await this.printCenterService.printMedicalCard(printData);
  }

  async printHistory(printHistoryDto: PrintHistoryDto) {
    const { printData } = printHistoryDto;
    let vaccineHistory: VaccineHistory[] = [];
    if (printData?.customerId === GUID_EMPTY) {
      const vaccineHistoryRes = await this.historyService.getByPerson({ personId: printData?.customerId });
      vaccineHistory = vaccineHistoryRes?.map((history) => ({
        vaccinatedDate: history?.vaccinatedDate?.toString() || '',
        disease: history?.disease || '',
        vaccineName: history?.vaccineName || '',
        injection: history?.injection || 0,
        locationName: history?.locationName || '',
        sku: history?.sku,
      }));
    } else {
      const historyByLcvIdRes = await this.historyService.getByLcvId(printData.lcvId);
      vaccineHistory = historyByLcvIdRes?.map((history) => ({
        vaccinatedDate: history?.vaccinatedDate?.toString() || '',
        disease: history?.disease || '',
        vaccineName: history?.vaccineName || '',
        injection: history?.injection || 0,
        locationName: history?.locationName || '',
        sku: history?.sku,
      }));
    }

    vaccineHistory = this.schedulesUtilsService.calculateAndOverwriteInjection(vaccineHistory);
    //filter theo shop code
    const listEmployeeInfor = await this.insideService.getListShopByEmployee(printData?.employeeCode);
    const employeeInfor = listEmployeeInfor?.shops?.find((shop) => shop?.shopCode === printData?.shopCode);

    const printHistory: PrintHistoryCoreDto = {
      ignorePrint: printHistoryDto?.printerInfo.ignorePrint,
      printType: PRINT_TYPE.HISTORY,
      printerInfo: printHistoryDto?.printerInfo,
      printData: {
        shopCode: printData?.shopCode,
        shopName: employeeInfor?.shopName || '',
        shopAddress: employeeInfor?.uAddress || '',
        shopPhone: SHOP_PHONE,
        customerId: printData?.customerId || '',
        customerName: printData?.customerName || '',
        customerPhone: printData?.customerPhone || '',
        gender: printData?.gender || 0,
        dateOfBirth: moment(printData?.dateOfBirth).format('DD/MM/YYYY') || '',
        lcvId: printData?.lcvId || '',
        frequentlyAddress: getFullAddress(
          {
            frequentlyAddress: printData?.frequentlyAddress,
            frequentlyWardName: printData?.frequentlyWardName,
            frequentlyDistrictName: printData?.frequentlyDistrictName,
            frequentlyProvinceName: printData?.frequentlyProvinceName,
          },
          true,
        ),
        nationalVaccineCode: printData.nationalVaccineCode || '',
        vaccineHistory: vaccineHistory,
      },
    };

    return this.printCenterService.printHistory(printHistory);
  }

  async printReportUsed(printReportUsedDto: PrintReportUsedDto) {
    const listReport = await this.reportCoreService.getReportUsedVaccine(printReportUsedDto);
    return this.printCenterService.printReportUsed({
      printType: PRINT_TYPE.REPORT_USED,
      printerInfo: printReportUsedDto.printerInfo,
      ignorePrint: false,
      printData: { listReport, shopCode: this.shopCode },
    });
  }

  async printReportRabies(printReportUsedDto: PrintReportRebiesDto) {
    const listReport = await this.reportCoreService.getReportRabiesVaccine(printReportUsedDto);
    return this.printCenterService.printReportRabies({
      printType: PRINT_TYPE.REPORT_RABIES,
      printerInfo: printReportUsedDto.printerInfo,
      ignorePrint: false,
      printData: { listReport, shopCode: this.shopCode },
    });
  }

  async printReportRabiesByCust(printReportUsedDto: PrintReportRebiesDto) {
    const listReport = await this.reportCoreService.getReportRabiesMonitoringVaccine(printReportUsedDto);
    return this.printCenterService.printReportRabiesByCust({
      printType: PRINT_TYPE.REPORT_RABIES_BY_CUST,
      printerInfo: printReportUsedDto.printerInfo,
      ignorePrint: false,
      printData: { listReport, shopCode: this.shopCode },
    });
  }

  async printAttachBillReturn(attachBillReturnHomeDto: AttachBillReturnHomeDto) {
    const attachBillInfo = await this.returnHomeService.getAttachBillInfo({
      orderReturnId: attachBillReturnHomeDto?.orderReturnId,
    });

    return this.printCenterService.printListAttachReturn({
      printType: PRINT_TYPE.LIST_ATTACH_RETURN,
      printerInfo: attachBillReturnHomeDto.printerInfo,
      ignorePrint: false,
      printData: {
        ...attachBillInfo,
        shopCode: this.shopCode,
        totalPaid: -attachBillInfo?.totalPaid,
        totalPaidWord: convertNumberToMinusVietnamese(attachBillInfo?.totalPaid),
      },
    });
  }

  async printPriceList(priceListDto: PriceListDto) {
    return this.printCenterService.printPriceList({
      printType: PRINT_TYPE.PRICE_LIST,
      printerInfo: priceListDto.printerInfo,
      ignorePrint: false,
      printData: { ...priceListDto.printData, shopCode: this.shopCode },
    });
  }

  async printServiceContract(getServiceContractDto: GetServiceContractDto) {
    // docs: https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=138858536
    // Nếu xác định muốn in thì gửi key, còn không có key thì sẽ lưu s3 bản nháp nhà mình

    let searchContract: SearchContractRes[] = [];
    try {
      if (!getServiceContractDto?.isForceCreateDraft) {
        searchContract = await this.contractService.searchContract({
          transactionCode: [getServiceContractDto?.orderCode],
        });
      }
    } catch (error) {}

    if (
      searchContract?.at(0)?.status !== ContractStatus.Rejected &&
      searchContract?.at(0)?.status !== ContractStatus.Voided &&
      searchContract?.at(0)?.status !== ContractStatus.DueDate &&
      searchContract?.at(0)?.partner?.keyImage
    ) {
      const printServiceContract = await this.printCenterService.printServiceContract({
        ignorePrint: getServiceContractDto?.printerInfo?.ignorePrint,
        printType: PRINT_TYPE.VACCINE_CONTRACT,
        printerInfo: getServiceContractDto?.printerInfo,
        printData: {
          shopCode: this.shopCode,
          totalPage: getServiceContractDto?.totalPage,
          storageName: searchContract?.at(0)?.partner?.keyImage,
        },
      });
      return { ...printServiceContract, contractDetail: searchContract?.at(0) };
    }

    const getOrder = await this.omsService.getOneOrder(getServiceContractDto?.orderCode);

    let orderInjectionData: OrderInjectionCommonRes = null;
    try {
      orderInjectionData = await this.vacOrderInjectionService.searchByOrderCode({
        orderCode: getServiceContractDto?.orderCode,
      });
    } catch (error) {
      orderInjectionData = null;
    }

    let customerInfo: GetPersonByIdRes = null;
    try {
      if (orderInjectionData?.orderInjectionDetails?.at(0)?.orderInjections?.at(0)?.personId) {
        customerInfo = await this.familyService.getPersonByLcvId(
          orderInjectionData?.orderInjectionDetails?.at(0)?.orderInjections?.at(0)?.personId,
        );
      } else customerInfo = null;
    } catch (error) {
      customerInfo = null;
    }

    // Get all address info concurrently
    // customerInfo
    const { address: addressEN, nationality: nationalityEN } = await this.printUtilsService.getAddressEN(
      customerInfo,
      true,
    );

    // customerBuyer
    const customerBuyer = await this.familyService?.getPersonByLcvId(getServiceContractDto?.lcvIdBuyer);

    const { address: addressBuyerEN, nationality: nationalityBuyerEN } = await this.printUtilsService.getAddressEN(
      customerBuyer,
      true,
    );

    const listAppointmentDate: string[] =
      JSONPath({
        path: '$.orderInjectionDetails[*].orderInjections[*].appointmentDate',
        json: orderInjectionData,
      }) || [];

    const getTicket = await this.examinationCoreService.getTicket({ ticketCode: getServiceContractDto?.ticketCode });

    const arrSku = _.compact(
      _.uniq(
        JSONPath({
          path: '$.details[*].detailAttachments[*].itemCode',
          json: getOrder,
        }),
      ),
    );

    // Get product info from PIM
    let lstProduct: GetListProductBySkuRes = null;
    try {
      lstProduct = await this.pimCoreService.getListProductBySku(arrSku);
    } catch (error) {
      lstProduct = null;
    }

    // Add regimen mapping for disease name translations
    const arrRegimen: string[] = _.uniq(JSONPath({ path: '$[*].regimenId', json: getTicket?.schedules }));
    let listRegimen: RegimenItem[] = [];
    try {
      listRegimen = await this.regimenService.getRegimenByIds({ regimenIds: arrRegimen });
    } catch (error) {}

    const productDatas: ProductData[] = getOrder?.details
      ?.filter((detail) => detail?.isPromotion === 'N')
      ?.map((item) => ({
        dosageItems: _.uniqBy(
          item?.detailAttachments?.map((detail) => {
            const productDataIndication = getTicket?.indications?.find(
              (indication) => indication?.sku === detail?.itemCode,
            );
            const productFind = lstProduct?.listProduct?.find((product) => product?.sku === detail?.itemCode);
            const productDataSchedule = getTicket?.schedules?.find((schedule) => schedule?.sku === detail?.itemCode);
            const regimenFind = listRegimen?.find((regimen) => regimen?.id === productDataSchedule?.regimenId);

            const currentVaccine = item?.detailAttachments?.filter((vaccine) => vaccine?.itemCode === detail?.itemCode);

            return {
              itemCode: detail?.itemCode || '',
              itemName: detail?.itemName || '',
              manufactor: productDataIndication?.manufactor || productDataSchedule?.manufactor || '',
              manufactorEN: productFind?.manufactorEng || '',
              diseaseName: productDataIndication?.taxonomies || productDataSchedule?.taxonomies || '',
              diseaseNameEN: regimenFind?.diseaseGroup?.engName || '',
              quantity: currentVaccine?.length,
            };
          }),
          'itemCode',
        ),
      }));

    const listProduct =
      JSONPath({
        path: '$[*].dosageItems[*]',
        json: productDatas,
      }) || [];

    let getJourney: OrdersInfo = {};
    if (getServiceContractDto?.orderCode) {
      getJourney = await this.journeyService.getOrderInfoByOrderOrTicket({
        orderCode: getServiceContractDto?.orderCode,
        orderType: 8,
      });
    }

    const printServiceContract: PrintServiceContractDto = {
      shopCode: this.shopCode,
      totalPage: getServiceContractDto?.totalPage,
      orderCode: getServiceContractDto?.orderCode,
      orderDetail: {
        orderCodeShort: getServiceContractDto?.orderCode?.slice(-7),
        productDatas: listProduct,
        totalBill: getOrder?.totalBill || 0,
        orderAttribute: getOrder?.orderAttribute || 0,
        totalDeposit: getOrder?.totalDeposit || 0,
        totalDepositWord: convertNumberToVietnamese(getOrder?.totalDeposit),
        totalDepositWordEN: convertNumberToWordsEn(getOrder?.totalDeposit),
        isPartialPayment: getJourney?.partialPaymentAmount > 0 && !getJourney?.orderCodeOld,
        totalBillPartialPayment: getJourney?.totalBill || 0,
        totalBillPartialPaymentWord: convertNumberToVietnamese(getJourney?.totalBill || 0),
        totalBillPartialPaymentWordEN: convertNumberToWordsEn(getJourney?.totalBill || 0),
        partialPaymentAmount: getJourney?.partialPaymentAmount || 0,
        partialPaymentAmountWord: convertNumberToVietnamese(getJourney?.partialPaymentAmount || 0),
        partialPaymentAmountWordEN: convertNumberToWordsEn(getJourney?.partialPaymentAmount || 0),
      },
      serviceUserInfo: {
        customerInfo: {
          name: customerInfo?.name || '',
          gender: customerInfo?.gender || 0,
          address: customerInfo ? getFullAddress(customerInfo, true) : '',
          addressEN: addressEN || '',
          dateOfBirth: customerInfo?.dateOfBirth,
          email: customerInfo?.email || '',
          ethnicName: customerInfo?.ethnicName || '',
          identityCard: customerInfo?.identityCard || '',
          phoneNumber: customerInfo?.phoneNumber || '',
          nationality: customerInfo?.nationalityName || '',
          nationalityEN: nationalityEN || '',
        },
        buyerInfo: {
          name: customerBuyer?.name || '',
          gender: customerBuyer?.gender || 0,
          address: customerBuyer ? getFullAddress(customerBuyer, true) : '',
          addressEN: addressBuyerEN || '',
          dateOfBirth: customerBuyer?.dateOfBirth,
          email: customerBuyer?.email || '',
          ethnicName: customerBuyer?.ethnicName || '',
          identityCard: customerBuyer?.identityCard || '',
          phoneNumber: customerBuyer?.phoneNumber || '',
          nationality: customerBuyer?.nationalityName || '',
          nationalityEN: nationalityBuyerEN || '',
        },
      },
      serviceProviderInfo: {
        name: 'CÔNG TY CỔ PHẦN DƯỢC PHẨM FPT LONG CHÂU',
        address: 'Số 379-381 Hai Bà Trưng, Phường Võ Thị Sáu, Quận 3, TP. Hồ Chí Minh',
        businessCode: '**********',
        phoneNumber: '***********',
        gender: 0,
        representName: 'Lê Tuấn Anh',
        position: 'Giám đốc vận hành Vaccine',
        serviceContractNum: 'số 126/2024/UQ-LC ngày 08/5/2024 của Tổng Giám đốc Công ty',
      },
      paymentInfo: {
        bankUsername: 'Công ty Cổ Phần Dược Phẩm FPT Long Châu',
        bankNum: '************',
        bankName: 'Ngân hàng TMCP Công Thương Việt Nam (VietinBank) chi nhánh Tân Định',
      },
      serviceProviderInfoEN: {
        name: 'FPT LONG CHAU PHARMA JOINT STOCK COMPANY',
        address: '379-381 Hai Ba Trung Street, Vo Thi Sau Ward, District 3, Ho Chi Minh City',
        businessCode: '**********',
        phoneNumber: '***********',
        gender: 0,
        representName: 'Le Tuan Anh',
        position: 'Operating Director of Vaccine',
        serviceContractNum: 'No. 126/2024/UQ-LC dated 08/5/2024 issued by Chief Executive Officer',
      },
      paymentInfoEN: {
        bankUsername: 'Cong ty co phan Duoc Pham FPT Long Chau',
        bankNum: '************',
        bankName: 'Vietnam Joint Stock Commercial Bank for Industry and Trade - Tan Dinh Branch',
      },
      expiredServiceContract: moment(listAppointmentDate.sort()?.at(-1)).add(3, 'M').utcOffset(7).format(),
      createdDate: moment().utcOffset(7).format(),
      createdBy: getServiceContractDto?.createdBy || '',
      createdByName: getServiceContractDto?.createdByName || '',
    };

    const printContract = await this.printCenterService.printServiceContract({
      ignorePrint: getServiceContractDto?.printerInfo?.ignorePrint,
      printType: PRINT_TYPE.VACCINE_CONTRACT,
      printerInfo: getServiceContractDto?.printerInfo,
      printData: printServiceContract,
    });

    return { ...printContract, contractDetail: searchContract?.at(0) };
  }

  async printVaccinationConfirm(printVaccinationConfirmDto: PrintVaccinationConfirmDto) {
    // docs: https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=*********

    if (printVaccinationConfirmDto?.storageName) {
      return this.printCenterService.printVaccinationConfirm({
        ignorePrint: printVaccinationConfirmDto?.printerInfo?.ignorePrint,
        printType: PRINT_TYPE.VACCINATION_CONFIRM,
        printerInfo: printVaccinationConfirmDto?.printerInfo,
        printData: {
          shopCode: this.shopCode,
          storageName: printVaccinationConfirmDto?.storageName,
        },
      });
    }
    // Mapping translate DiseaseGroupName từ regimen
    const arrDiseaseGroup: string[] = _.uniq(
      JSONPath({ path: '$[*].diseaseGroupId', json: printVaccinationConfirmDto?.history }),
    );

    const listDiseaseGroup = await this.regimenService.getManyDiseaseGroupTranslate(arrDiseaseGroup);

    printVaccinationConfirmDto?.history?.forEach((item) => {
      const currentItem = listDiseaseGroup?.find(
        (diseaseGroup) => diseaseGroup?.diseaseGroupId === item?.diseaseGroupId,
      );
      item['diseaseNameEn'] = currentItem?.engName || '';
      item['diseaseNameVi'] = currentItem?.viName || '';
    });

    const customerInfo = await this.familyService.getFamilyByLcvId({ lcvId: printVaccinationConfirmDto?.lcvId });

    const printData = {
      customerInfo: {
        name: customerInfo?.name || '',
        gender: customerInfo?.gender || 0,
        dateOfBirth: customerInfo?.dateOfBirth,
        identityCard: customerInfo?.identityCard,
        placeOfBirth: getFullAddressCertificate(customerInfo, true),
      },
      listHistory: printVaccinationConfirmDto?.history,
      shopCode: this.shopCode,
      lcvId: printVaccinationConfirmDto?.lcvId,
    };

    return this.printCenterService.printVaccinationConfirm({
      ignorePrint: printVaccinationConfirmDto?.printerInfo?.ignorePrint,
      printType: PRINT_TYPE.VACCINATION_CONFIRM,
      printerInfo: printVaccinationConfirmDto?.printerInfo,
      printData: printData,
    });
  }

  async printSchedulesInjection(payload: ScheduleInjectionDto) {
    // Thông tin person
    const getFamily = await this.familyService.getPersonByLcvId(payload?.lcvId);
    const personHost = getFamily?.familyProfileDetails?.find((detail) => detail?.isHost)?.phoneNumber;

    //filter theo shop code
    const listEmployeeInfor = await this.insideService.getListShopByEmployee(payload?.employeeCode);
    const employeeInfor = listEmployeeInfor?.shops?.find((shop) => shop?.shopCode === this.shopCode);

    const listSchedule = {};

    for (const schedule of payload?.schedules) {
      const key = moment(schedule.appointmentDate).utcOffset(7).format('DD/MM/YYYY');
      if (listSchedule[key]?.length) {
        listSchedule[key]?.push(schedule);
      } else {
        listSchedule[key] = [schedule];
      }
    }

    const printData: PrintDataScheduleInjectionDto = {
      shopCode: this.shopCode,
      shopAddress: employeeInfor?.uAddress,
      shopName: employeeInfor?.shopName,
      lcvId: payload?.lcvId,
      customerInfo: {
        name: getFamily?.name,
        phoneNumber: getFamily?.phoneNumber ? getFamily?.phoneNumber : personHost,
        dateOfBirth: getFamily?.dateOfBirth,
        address: getFullAddress(getFamily, true),
        age: calculatorAge(getFamily?.dateOfBirth),
        nationalVaccineCode: getFamily?.nationalVaccineCode,
      },
      schedules: listSchedule,
    };

    return this.printCenterService.printScheduleInjection({
      ignorePrint: payload?.printerInfo?.ignorePrint,
      printType: PRINT_TYPE.SCHEDULES_INJECTION,
      printerInfo: payload?.printerInfo,
      printData: printData,
    });
  }

  /**
   *
   * @param payload: orderCode, shopCode, totalPriceReturn, totalPriceCustomerPay
   * Lấy thông tin ORDER
   *    - Lấy thông tin step2, step5
   *    - Thời gian trên bill là thời gian thao tác step5=> step2 => createdDate
   * Lấy thông tin INSIDE theo mã inside thứ tự ưu tiên ở trên
   * Lấy thông tin product từ PIM
   * Tính lại quantity, price, totalAmount
   * Lấy thông tin PAYMENT theo payment THU
   *    - Lấy phương thức thanh toán
   *    - Tính discountVoucher
   * Get cashBack OSR theo sđt nếu có thì không in cashBack
   * Nếu không có cashBack ở trên thì gọi tiếp OSR theo orderDetailAttachmentCode
   * @returns url, imageUrl
   */

  async lcBill(payload: BillAffiliateDto) {
    const { printerInfo, orderCode, shopCode, totalPriceReturn, totalPriceCustomerPay, isRePrint } = payload;
    const orderInfo = await this.omsService.getOneOrder(orderCode);

    const storageName = orderInfo?.shopCode + '/' + orderInfo?.orderCode + '_' + orderInfo?.orderStatus;

    if (isRePrint) {
      const printData: PrintBillDataRes = {
        ignorePrint: true,
        printType: PRINT_TYPE.LC_INVOICE,
        printerInfo: { ...printerInfo, ignorePrint: true },
        printData: {
          isRePrint: isRePrint,
          shopCode: shopCode,
          orderCode: orderInfo?.orderCode,
          orderStatus: orderInfo?.orderStatus,
          storageName,
        },
      };
      const printLcInvoice = await this.printCenterService.printLcInvoice(printData);
      if (!!printLcInvoice?.url) {
        if (printerInfo?.ignorePrint) {
          return printLcInvoice;
        } else {
          return await this.lcPrintCenter.lcBill({
            url: printLcInvoice?.url,
            printerInfo: printerInfo,
            shopCode,
          });
        }
      }
    }

    const { groupCode, groupName } = await this.printUtilsService.getGroupInfoFromOrder(orderInfo);

    const employeeCodeStep2 = _.orderBy(
      orderInfo?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeConfirmAndCollectMoney),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const employeeInfoStep5 = _.orderBy(
      orderInfo?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const orderCreatedAt =
      employeeInfoStep5?.createdDate || employeeCodeStep2?.createdDate || orderInfo?.createdDate || '';

    //filter theo shop code
    const employeeCode = employeeInfoStep5?.employeeCode || employeeCodeStep2?.employeeCode || orderInfo?.createdBy;
    const listEmployeeInfo = await this.insideService.getEmployeeLC({ EmployeeCode: employeeCode });
    const employeeInfo = listEmployeeInfo?.shops?.find((shop) => shop?.shopCode === shopCode);

    const arrSku = _.compact(
      _.uniq(
        JSONPath({
          path: '$.details[*].detailAttachments[*].itemCode',
          json: orderInfo,
        }),
      ),
    );
    const lstProduct = await this.pimCoreService.getListProductBySku(arrSku);

    // Tính lại quantity, price, totalAmount, sau đó sort theo itemCode
    const productDatas: ProductData[] = orderInfo?.details
      ?.filter((detail) => !(detail?.isPromotion === 'Y' && detail?.isInventoryManagement === 'N'))
      ?.map((item) => ({
        isVaccineCombo: !!item.detailAttachments.find((detail) => detail?.serviceFee !== 0),
        itemCode: item?.itemCode ?? '',
        itemName: item?.itemName ?? '',
        measureUnit: item.unitName ?? '',
        discount: item?.discount ?? 0,
        discountType: item?.discountType ?? '',
        discountPromotion: item?.discountPromotion ?? 0,
        totalPrice: item?.price * item?.quantity,
        totalDisCount: item?.discount * item?.quantity,
        isExpiredDate: item?.isExpiredDate,
        isPromotion: item?.isPromotion,
        quantity: item?.quantity ?? 0,
        price: item?.price ?? 0,
        total: item?.total ?? 0,
        totalVaccineCombo: item?.price * item.quantity,
        haveAttachments: !!item?.detailAttachments?.length,
        dosageItems: _.uniqBy(
          item?.detailAttachments?.map((detail) => {
            const productFind = lstProduct?.listProduct?.find((product) => product?.sku === detail?.itemCode);

            const currentVaccine = item?.detailAttachments?.filter((vaccine) => vaccine?.itemCode === detail?.itemCode);
            const totalDiscount = currentVaccine?.reduce(
              (accumulator, currentValue) => accumulator + currentValue.discountPromotion,
              0,
            );
            const totalDiscountAdjustment = currentVaccine?.reduce(
              (accumulator, currentValue) => accumulator + currentValue.discount,
              0,
            );

            const isThisSkuRestrict = process.env.SKU_RESTRICTION?.split(',').includes(detail?.itemCode);

            return {
              itemCode: detail?.itemCode || '',
              itemName: detail?.itemName ?? '',
              manufactor: productFind?.manufactor || '',
              quantity: currentVaccine?.length,
              price: Math.max(
                0,
                (detail?.price * currentVaccine?.length - totalDiscount - totalDiscountAdjustment) /
                  currentVaccine?.length,
              ),
              serviceFee: detail?.serviceFee ?? 0,
              totalAmount: Math.max(
                0,
                detail?.price * currentVaccine?.length - totalDiscount - totalDiscountAdjustment,
              ),
              isAffiliateSkuRestriction: isThisSkuRestrict,
              affiliateRestrictionMsg: isThisSkuRestrict ? process.env.BILL_MESSAGE_RESTRICTION : '',
            };
          }),
          'itemCode',
        ),
      }));

    // Lấy thông tin payment
    const arrPaymentCode = _.uniq(
      _.compact(
        orderInfo.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );

    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGatewayService.getPaymentRedis({
      paymentCodes: arrPaymentCode,
    });

    const paymentData = arrPaymentES?.find((payment) => payment?.paymentRequestCode === orderInfo?.paymentRequestCode);

    const voucherDiscount = arrPaymentES?.reduce((acc, cur) => {
      return acc + getDepositedAmountByMethods(cur?.detail, ['vouchersAll']) || 0;
    }, 0);

    const getCashBack = await this.redisService.get<CashbackSuggestRes>(`${CASHBACK_KEY}:${orderCode}`);

    const totalPaymentPaid = orderInfo?.orderPaymentCreate
      ?.filter(
        (paymentCreate) =>
          paymentCreate?.paymentStatus === PaymentType.PAID && paymentCreate?.paymentType === PaymentType.THU,
      )
      ?.reduce((acc, cur) => {
        return acc + cur?.paymentAmount;
      }, 0);

    const printData: PrintBillDataRes = {
      ignorePrint: true,
      printType: PRINT_TYPE.LC_INVOICE,
      printerInfo: { ...printerInfo, ignorePrint: true },
      printData: {
        isRePrint: false,
        shopCode: shopCode,
        storageName,
        groupCode: groupCode || '',
        groupName: groupName || '',
        templateType: getCashBack?.suggestAmount
          ? TemplateAffiliate.Cashback
          : getCashBack?.reasonCode === ReasonCodeAffiliate.SkuReject
          ? TemplateAffiliate.PMH
          : TemplateAffiliate.Point,
        employeeData: {
          employeeCode: employeeInfo?.employeeCode ?? '',
          employeeName: employeeInfo?.employeeName ?? '',
        },
        moneyData: {
          totalBill: orderInfo?.totalBill ?? 0,
          finalPrice: orderInfo?.total ?? 0,
          finalPriceDiscount: orderInfo?.totalDiscount ?? 0,
          totalPricePay: Math.max(0, orderInfo?.totalBill - voucherDiscount || 0),
          voucherPrice: voucherDiscount || 0,
          totalPriceReturn: totalPriceReturn || 0,
          totalPriceCustomerPay: totalPriceCustomerPay || 0,
          paymentMethod: getPaymentMethod(paymentData?.detail),
          serviceFee: orderInfo?.serviceFee,
          totalWithFee: orderInfo?.totalBill ?? 0,
          totalDiscountAndVoucher: orderInfo?.totalDiscount + voucherDiscount,
          // Tiền cọc
          totalDeposit: totalPaymentPaid,
          totalNeedPay: totalPaymentPaid ? 0 : orderInfo?.totalBill - voucherDiscount - totalPaymentPaid,
        },
        orderSummaryData: {
          invoiceCode: orderCode,
          orderCode,
          shopName: orderInfo?.shopName,
          shopCode: orderInfo?.shopCode,
          shopAddress: employeeInfo?.uAddress ?? '',
          orderCreatedAt: orderCreatedAt,
          orderCodeShorted: orderInfo?.orderCode,
          storageName,
          customerPhone: orderInfo?.phone || '',
          customerName: orderInfo?.custName || `Khách Lẻ ${orderInfo?.shopName}`,
        },
        productDatas: productDatas?.filter((product) => product?.isPromotion === 'N'),
        promotionDatas: productDatas?.filter((product) => product?.isPromotion === 'Y'),
        cashBackMoney: getCashBack?.suggestAmount || 0,
        cashBackPoint: +process.env.CASH_BACK_POINT_AFFILIATE,
        cashBackPMH: +process.env.CASH_BACK_PMH_AFFILIATE,
      },
    };

    const printRes = await this.printCenterService.printLcInvoice(printData);

    if (printerInfo?.ignorePrint) {
      return printRes;
    } else {
      return await this.lcPrintCenter.lcBill({
        url: printRes?.url,
        printerInfo: printerInfo,
        shopCode,
      });
    }
  }

  async printInvoiceMany(invoiceDto: InvoiceDto[]) {
    const promisePrintInvoices = [];
    invoiceDto?.forEach((invoice) => {
      promisePrintInvoices.push(
        this.printUtilsService.printInvoice(
          {
            ...invoice,
            printType: PRINT_TYPE.VACCINE_INVOICE,
          },
          this.shopCode,
          true,
        ),
      );
    });

    return concurrentPromise(...promisePrintInvoices);
  }

  /**
   * B1: Check có hợp đồng chưa, có thì lấy S3 ra / Force tạo mới (Với những TH muốn làm lại HD)
   * B2: Lấy DS đơn theo orderIdInter
   * B3: Lấy chi tiết persons theo lcvIds từ ds đơn và lcvId người mua
   * B4: Lấy thông tin nhóm gia đình
   * B5: Lấy thông tin lịch hẹn từ vacOrder
   * B6: Lấy thông tin prouct từ Pim
   * B7: Tổng hợp data và tính toán xử lý dữ liệu cho hợp lý
   * B8: Gửi thông tin qua print để in/xem HD
   * B9: Trả thông tin file HD và thông tin HD nếu có
   */

  async printServiceContractFamilyPackage(getServiceContractDto: ServiceContractFamilyPackageDto) {
    const { orderIdInter, isForceCreateDraft, lcvIdBuyer } = getServiceContractDto;

    // Chi tiết thông tin HD
    let searchContract: SearchContractRes[] = [];
    try {
      if (!isForceCreateDraft) {
        searchContract = await this.contractService.searchContract({
          transactionCode: [orderIdInter],
        });
      }
    } catch (error) {}

    // Xử lý file S3 (in/xem)
    if (
      searchContract?.at(0)?.status !== ContractStatus.Rejected &&
      searchContract?.at(0)?.status !== ContractStatus.Voided &&
      searchContract?.at(0)?.status !== ContractStatus.DueDate &&
      searchContract?.at(0)?.partner?.keyImage
    ) {
      const printServiceContract = await this.printCenterService.printServiceContractFP({
        ignorePrint: getServiceContractDto?.printerInfo?.ignorePrint,
        printType: PRINT_TYPE.VACCINE_CONTRACT_FAMILY_PACKAGE,
        printerInfo: getServiceContractDto?.printerInfo,
        printData: {
          shopCode: this.shopCode,
          totalPage: getServiceContractDto?.totalPage,
          storageName: searchContract?.at(0)?.partner?.keyImage,
        },
      });
      return { ...printServiceContract, contractDetail: searchContract?.at(0) };
    }

    // Danh sách đơn theo orderIdInter
    const lstOrderInfo = await this.omsService.searchOrderDynamicES({
      maxResultCount: 20, //BA nguyennt81 confirm 20
      skipCount: 0,
      searchMatchPhraseFields: {
        orderIdInter,
      },
    });

    const arrLcvId = _.compact(
      _.uniq(
        JSONPath({
          path: '$.items[*].details[*].detailAttachments[*].personIdSub',
          json: lstOrderInfo,
        })?.concat([lcvIdBuyer]),
      ),
    );

    // Thông tin gia đình
    const persons = await this.familyService.getManyByLcvId({ lcvId: arrLcvId });
    const customerBuyer = persons?.find((person) => person.lcvId === lcvIdBuyer);
    const groupFP = await this.familyPackageService.getGroupFamilyPackage({
      lcvIds: [arrLcvId?.at(0)],
    });

    // Thông tin addressEN và nationalityEN
    const { address: addressBuyerEN, nationality: nationalityBuyerEN } = await this.printUtilsService.getAddressEN(
      customerBuyer,
      true,
    );

    const promiseAddressPerson = [];
    for (const person of persons) {
      promiseAddressPerson.push(await this.printUtilsService.getAddressEN(person, true));
    }
    const addressPersonENs = await Promise.all(promiseAddressPerson);

    // Lây thông tin Vac Order
    const promiseSearchByOrders = [];
    lstOrderInfo?.items?.forEach((order) => {
      promiseSearchByOrders.push(
        this.vacOrderInjectionService.searchByOrderCode({
          orderCode: order?.orderCode,
        }),
      );
    });
    const orderInjectionDatas = await Promise.all([...promiseSearchByOrders]);

    const listAppointmentDate: string[] =
      JSONPath({
        path: '$[*].orderInjectionDetails[*].orderInjections[*].appointmentDate',
        json: orderInjectionDatas,
      }) || [];

    // Lấy thông tin product từ PIM
    const arrSku = _.compact(
      _.uniq(
        JSONPath({
          path: '$.items[*].details[*].detailAttachments[*].itemCode',
          json: lstOrderInfo,
        }),
      ),
    );

    const arrOrderDetailAttachmentCode: string[] =
      JSONPath({
        path: '$.items[*].details[*].detailAttachments[*].orderDetailAttachmentCode',
        json: lstOrderInfo,
      }) || [];

    const arrOrderInjections: OrderInjectionRes[] = _.uniq(
      JSONPath({
        path: '$[*].orderInjectionDetails[*].orderInjections[*]',
        json: orderInjectionDatas,
      }),
    );

    const arrRegimen = [];
    arrOrderInjections?.forEach((orderInjection) => {
      if (
        arrOrderDetailAttachmentCode?.includes(orderInjection?.orderDetailAttachmentCode) &&
        !arrRegimen.includes(orderInjection?.regimenId)
      ) {
        arrRegimen.push(orderInjection?.regimenId);
      }
    });

    const listRegimen = await this.regimenService.getRegimenByIds({ regimenIds: arrRegimen });

    const lstProduct = await this.pimCoreService.getListProductBySku(arrSku);

    // Xử lý data phụ lục
    const appendices: AppendixDto[] = [];
    lstOrderInfo?.items?.forEach((order) => {
      let dosageItems: DosageItem[] = [];
      order?.details
        ?.filter((detail) => detail?.isPromotion === 'N')
        ?.forEach((item) => {
          // Chi tiết số mũi đã mua
          dosageItems = _.uniqBy(
            item?.detailAttachments?.map((detail) => {
              const productFind = lstProduct?.listProduct?.find((e) => e?.sku === detail?.itemCode);
              const currentVaccine = item?.detailAttachments?.filter(
                (vaccine) => vaccine?.itemCode === detail?.itemCode,
              );

              const regimenId = arrOrderInjections.find(
                (oi) => oi?.orderDetailAttachmentCode === detail?.orderDetailAttachmentCode,
              )?.regimenId;
              const regimenFind = listRegimen?.find((r) => r?.id === regimenId);

              return {
                itemCode: detail?.itemCode || '',
                itemName: detail?.itemName || '',
                manufactor: productFind?.manufactor || '',
                manufactorEN: productFind?.manufactorEng || '',
                diseaseName: productFind?.categories?.find((categorie) => categorie?.level === 2).categoryName || '', // NguyenNT81 confirm
                diseaseNameEN: regimenFind?.diseaseGroup?.engName || '',
                quantity: currentVaccine?.length,
              };
            }),
            'itemCode',
          );
        });

      // Thông tin người tiêm
      const personIdSub = _.compact(
        _.uniq(
          JSONPath({
            path: '$.details[*].detailAttachments[*].personIdSub',
            json: order,
          }),
        ),
      );
      const personFind = persons?.find((person) => person?.lcvId === personIdSub?.at(0));
      const addressFind = addressPersonENs?.find((address) => address?.lcvId === personIdSub?.at(0));
      const { address: addressPersonEN, nationality: nationalityPersonEN } = addressFind || {
        addressPersonEN: '',
        nationalityPersonEN: '',
      };

      const customerInfo = plainToInstance(
        CustomerDto,
        { ...personFind, address: getFullAddress(personFind, true), nationality: personFind?.nationalityName },
        {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        },
      );
      customerInfo['addressEN'] = addressPersonEN || '';
      customerInfo['nationalityEN'] = nationalityPersonEN || '';

      // Hạn phụ lục từng người
      const vacOrderFind = orderInjectionDatas?.find(
        (orderInjection) => orderInjection?.orderCode === order?.orderCode,
      );
      const listAppointmentDateItem: string[] =
        JSONPath({
          path: '$.orderInjectionDetails[*].orderInjections[*].appointmentDate',
          json: vacOrderFind,
        }) || [];
      const expiredServiceContract = moment(listAppointmentDateItem.sort()?.at(-1)).add(3, 'M').utcOffset(7).format();

      appendices.push({
        dosageItems,
        customerInfo,
        expiredServiceContract,
        totalQuantity: dosageItems?.reduce((acc, cur) => acc + cur?.quantity, 0),
      });
    });

    // Tính tiền tổng đơn nhóm
    const totalBillFP = lstOrderInfo?.items?.reduce((acc, cur) => acc + cur?.totalBill, 0);

    const printServiceContract: PrintServiceContractFPDto = {
      shopCode: this.shopCode,
      totalPage: getServiceContractDto?.totalPage,
      orderIdInter,
      orderDetail: {
        orderCodeShort: orderIdInter?.slice(-7),
        appendices,
        totalBill: totalBillFP,
        totalBillWord: convertNumberToVietnamese(totalBillFP),
        totalBillWordEN: convertNumberToWordsEn(totalBillFP),
      },
      buyerInfo: plainToInstance(
        CustomerFPDto,
        {
          ...customerBuyer,
          address: getFullAddress(customerBuyer, true),
          addressEN: addressBuyerEN || '',
          nationality: customerBuyer?.nationalityName,
          nationalityEN: nationalityBuyerEN || '',
          groupCode: groupFP?.at(0)?.code,
          groupRankingName: groupFP?.at(0)?.groupRankingName,
          groupRankingNameEN: groupFP?.at(0)?.groupRankingEnglishName || '',
        },
        {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        },
      ),
      serviceProviderInfo: {
        name: 'CÔNG TY CỔ PHẦN DƯỢC PHẨM FPT LONG CHÂU',
        address: 'Số 379-381 Hai Bà Trưng, Phường Võ Thị Sáu, Quận 3, TP. Hồ Chí Minh',
        businessCode: '**********',
        phoneNumber: '***********',
        gender: 0,
        representName: 'Lê Tuấn Anh',
        position: 'Giám đốc vận hành Vaccine',
        serviceContractNum: 'số 126/2024/UQ-LC ngày 08/5/2024 của Tổng Giám đốc Công ty',
      },
      paymentInfo: {
        bankUsername: 'Công ty Cổ Phần Dược Phẩm FPT Long Châu',
        bankNum: '************',
        bankName: 'Ngân hàng TMCP Công Thương Việt Nam (VietinBank) chi nhánh Tân Định',
      },
      serviceProviderInfoEN: {
        name: 'FPT LONG CHAU PHARMA JOINT STOCK COMPANY',
        address: '379-381 Hai Ba Trung Street, Vo Thi Sau Ward, District 3, Ho Chi Minh City',
        businessCode: '**********',
        phoneNumber: '***********',
        gender: 0,
        representName: 'Le Tuan Anh',
        position: 'Operating Director of Vaccine',
        serviceContractNum: 'No. 126/2024/UQ-LC dated 08/5/2024 issued by Chief Executive Officer',
      },
      paymentInfoEN: {
        bankUsername: 'Cong ty co phan Duoc Pham FPT Long Chau',
        bankNum: '************',
        bankName: 'Vietnam Joint Stock Commercial Bank for Industry and Trade - Tan Dinh Branch',
      },
      expiredServiceContract: moment(listAppointmentDate.sort()?.at(-1)).add(3, 'M').utcOffset(7).format(), // Hạn phụ lục của hợp đồng
      createdDate: moment().utcOffset(7).format(),
      createdBy: getServiceContractDto?.createdBy || '',
      createdByName: getServiceContractDto?.createdByName || '',
    };

    const printContract = await this.printCenterService.printServiceContractFP({
      ignorePrint: getServiceContractDto?.printerInfo?.ignorePrint,
      printType: PRINT_TYPE.VACCINE_CONTRACT_FAMILY_PACKAGE,
      printerInfo: getServiceContractDto?.printerInfo,
      printData: printServiceContract,
    });

    return { ...printContract, contractDetail: searchContract?.at(0) };
  }

  async printInvoiceFamilyPackageForZalo(body: PrintInvoiceFamilyPackageForZaloDto) {
    const { isManualSendBill, orderCode } = body;
    const bill = await this.printUtilsService.printInvoiceFamilyPackage({
      ...body,
      printerInfo: {
        ignorePrint: true,
        macID: '',
        macName: '',
      },
      totalPriceReturn: 0,
      totalPriceCustomerPay: 0,
    });

    if (!isManualSendBill) {
      return bill;
    } else {
      this.camundaNotificationApiService.sendNotificationBillZalo({
        isManualSendBill: true,
        orderCode: orderCode,
        url: bill?.imageUrl,
      });
      return bill;
    }
  }
}
