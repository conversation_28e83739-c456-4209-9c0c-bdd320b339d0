export enum POTENTIAL_CUSTOMER_REQUEST_STATUS {
  PENDING = 1, // <PERSON><PERSON><PERSON> xử lý (1)
  APPOINTMENT_SUCCESS = 2, // Đã Hẹn Thành Công (2)
  REFERENCE_1 = 3, // Tham <PERSON>o Lần 1 (3)
  REFERENCE_2 = 7, // Tham Khảo Lần 2 (7)
  NO_NEED = 4, // Không Có Nhu Cầu (3)
  CANT_CONTACT = 5, // Không Nghe Máy/Sai SĐT (5)
  INJECTED = 6, // Đã tiêm (6)
}

export enum NOTI_REGISTER_CHANEL_NAME {
  RSA_WEB = 'RSA_WEB',
  RSA_ECOM_WEB = 'RSA_ECOM_WEB',
}
export const generateAddressId = (registerChannelName: NOTI_REGISTER_CHANEL_NAME, employeeCode: string) =>
  `${registerChannelName}_${employeeCode}`;

export const SHOP_CODE_ECOM_VACCINE = '50001';
