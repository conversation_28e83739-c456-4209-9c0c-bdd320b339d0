import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, Version } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, CurrentUser, CustomHeaders, generalSchema, Public } from '@shared';
import { TicketDetailRes } from 'vac-nest-examination';
import { CreateHistoryOptionDto, VaccineHistoryDetailDto, VacHistoryService } from 'vac-nest-history';
import { CreateNotificationLibResponseDTO } from 'vac-nest-notification';
import { SearchOrderESLibResponse } from 'vac-nest-oms';
import { VacOrderInjectionService } from 'vac-nest-order-injection';
import {
  CheckRuleHostPhoneCustomerDto,
  CheckRuleScheduleConsultantDto,
  CheckRuleScheduleConsultantRes,
} from 'vac-nest-order-rule-engine';
import {
  CancelScheduleByKeyDto,
  CancelScheduleByKeyResponse,
  CreateScheduleResponse,
  ScheduleByShopCodeRes,
  ScheduleItem,
  SearchByShopDto,
  SearchScheduleByShopCodeDto,
} from 'vac-nest-schedule';
import {
  CheckScheduleDto,
  GetInjectionSchedule,
  ScheduleEngineAppService,
  SuggestInjectionSchedulesDto,
  SuggestScheduleDto,
} from 'vac-nest-schedule-engine-app';
import { LoadVaccineBookDto, VaccineBookRes } from 'vac-nest-vaccine-book';
import { UpdateManySchedule } from '../../customers/dto';
import {
  CheckRuleDupScheduleRes,
  CreateHistoryOptionRes,
  CreateScheduleBodyDto,
  GetListScheduleHistoryDto,
  GetListScheduleHistoryRes,
  InjectionHistoryDto,
  ItemReplaceSkuDto,
  ItemReplaceSkuRes,
  ListHistoryScheduleInjectionRes,
  OrderInjectionPurchaseRes,
  PayloadInjectionHistoryDto,
  PayloadInjectionScheduleDto,
  ReqDeleteHistoryDto,
  ScheduleInjectionFamilyRes,
  SearchByShopRes,
  SearchVaccineHistoryRes,
  SyncHistoryByDetailId,
  SyncHistoryTCQG,
  UpdateHistoryOptionDto,
} from '../dto';
import { SearchHistoryOrderOmsDto, SearchOmsRes } from '../dto/search-oms.dto';
import { SendMessageSunAndRainDto } from '../dto/send-message-sun-and-rain.dto';
import { SchedulesService } from '../services/schedules.service';
import { SchedulesRuleService } from '../services/schedule-rule.service';
import { UpdateScheduleEcomEarlyD2StatusDto } from 'vac-nest-schedule/dist/dto/update-schedule-ecom-early-d2-status.dto';
import { GetScheduleEcomEarlyD2ConvertDto } from '../dto/get-schedule-ecom-early.dto';
import { GetSaveOrderScheduleQuery } from '../dto/get-save-order-schedules.dto';

@Controller({ path: 'schedules', version: '1' })
@ApiTags('Schedule')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(
  ClassResponse,
  SuggestInjectionSchedulesDto,
  GetInjectionSchedule,
  ScheduleInjectionFamilyRes,
  SearchVaccineHistoryRes,
  SearchOmsRes,
  CreateHistoryOptionRes,
  SearchOrderESLibResponse,
  ScheduleByShopCodeRes,
  OrderInjectionPurchaseRes,
  VaccineBookRes,
  CancelScheduleByKeyResponse,
  ListHistoryScheduleInjectionRes,
  CreateScheduleResponse,
  SyncHistoryByDetailId,
  ItemReplaceSkuRes,
  CheckRuleScheduleConsultantRes,
  CheckRuleDupScheduleRes,
  SearchByShopRes,
  CreateNotificationLibResponseDTO,
  TicketDetailRes,
  VaccineHistoryDetailDto,
  GetListScheduleHistoryRes,
)
@CustomHeaders()
export class SchedulesController {
  constructor(
    private readonly scheduleEngineAppService: ScheduleEngineAppService,
    private readonly schedulesService: SchedulesService,
    private readonly vacHistoryService: VacHistoryService,
    private readonly vacOrderInjectionService: VacOrderInjectionService,
    private readonly schedulesRuleService: SchedulesRuleService,
  ) {}

  @Public()
  @Post('suggest')
  @ApiOperation({
    summary: 'Suggest lịch tiêm còn trống',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin danh sách phác đồ',
    schema: generalSchema(SuggestInjectionSchedulesDto, 'object'),
  })
  suggestInjectionSchedule(@Body() suggestInjectionSchedulesDto: SuggestInjectionSchedulesDto) {
    return this.schedulesService.suggestInjectionSchedule(suggestInjectionSchedulesDto);
  }

  @Public()
  @Post('suggest')
  @ApiOperation({
    summary: 'Suggest lịch tiêm còn trống',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin danh sách phác đồ',
    schema: generalSchema(SuggestScheduleDto, 'object'),
  })
  @Version('2')
  suggest(@Body() suggestScheduleDto: SuggestScheduleDto) {
    return this.scheduleEngineAppService.suggestSchedule(suggestScheduleDto);
  }

  @Public()
  @Post('validate')
  @ApiOperation({
    summary: 'validate lịch tiêm còn trống | dành cho chọn lại ngày',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin danh sách phác đồ',
    schema: generalSchema(SuggestInjectionSchedulesDto, 'object'),
  })
  validateInjectionSchedule(@Body() suggestInjectionSchedulesDto: SuggestInjectionSchedulesDto) {
    return this.scheduleEngineAppService.validateInjectionSchedule(suggestInjectionSchedulesDto);
  }

  @Public()
  @Post('validate')
  @ApiOperation({
    summary: 'validate lịch tiêm còn trống | dành cho chọn lại ngày',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin danh sách phác đồ',
    schema: generalSchema(SuggestScheduleDto, 'object'),
  })
  @Version('2')
  validate(@Body() suggestScheduleDto: SuggestScheduleDto) {
    return this.scheduleEngineAppService.validateSchedule(suggestScheduleDto);
  }

  // START lịch hẹn
  /**
   * @TODO lấy danh sách lịch hẹn
   */
  @Public()
  @Get('get-injection-schedules')
  @ApiOperation({
    summary: 'lấy danh sách lịch hẹn',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách lịch hẹn',
    schema: generalSchema(ScheduleItem, 'array'),
  })
  async injectionSchedule(@Query('lcvId') lcvId: string) {
    return this.schedulesService.getSchedule(lcvId);
  }

  /**
   * @TODO cập nhật lịch hẹn
   */
  @Post('update-many-schedule')
  @ApiOperation({
    summary: 'Cập nhật nhiều lịch hẹn',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin cập nhật',
    schema: generalSchema(ScheduleItem, 'array'),
  })
  async updateManySchedule(@Body() body: UpdateManySchedule, @CurrentUser() user) {
    return this.schedulesService.updateManySchedule(body, user);
  }

  /*
   * @TODO tạo lịch hẹn bằng tay
   */
  @Public()
  @Post('manually')
  @ApiOperation({
    summary: 'Tạo lịch hẹn bằng tay',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin lịch hẹn',
    schema: generalSchema(ScheduleItem, 'object'),
  })
  async createScheduleManually(@Body() body: CreateScheduleBodyDto) {
    return this.schedulesService.createScheduleManually(body);
  }

  @Public()
  @Post('cancel-schedule-by-key')
  @ApiOperation({
    summary: 'Huỷ lịch bằng ids',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(CancelScheduleByKeyResponse, 'object'),
  })
  cancelScheduleByKey(@Body() cancelScheduleByKeyDto: CancelScheduleByKeyDto) {
    return this.schedulesService.cancelScheduleByKey(cancelScheduleByKeyDto);
  }
  // END lịch hẹn

  /**
   * @TODO Đồng bộ lịch sử lên tiêm chủng quốc gia
   */
  @Public()
  @Post('sync-tcqg')
  @ApiOperation({
    summary: 'lấy danh sách lịch tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách lịch tiêm',
    schema: generalSchema(GetInjectionSchedule, 'object'),
  })
  async syncHistoryTCQG(@Body() body: SyncHistoryTCQG) {
    return this.schedulesService.syncHistoryTCQG(body);
  }

  /**
   * @TODO Tra cứu lịch sử tiêm
   */
  @Public()
  @Get('search-injection-history')
  @ApiOperation({
    summary: 'Tra cứu lịch sử tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách tra cứu lịch sử tiêm',
    schema: generalSchema(SearchVaccineHistoryRes, 'object'),
  })
  async injectionHistory(@Query() query: PayloadInjectionHistoryDto) {
    return this.schedulesService.searchInjectionHistory(query);
  }

  /**
   * @TODO Tra cứu lịch hẹn tiêm
   */
  @Public()
  @Get('search-injection-appointment-schedule')
  @ApiOperation({
    summary: 'Tra cứu lịch hẹn tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách tra cứu lịch hẹn tiêm',
    schema: generalSchema(SearchVaccineHistoryRes, 'object'),
  })
  async searchInjectionSchedule(@Query() query: PayloadInjectionScheduleDto) {
    return this.schedulesService.searchInjectionSchedule(query);
  }

  /**
   * @TODO Tra cứu lịch sử đơn
   */
  @Public()
  @Post('search-history-order')
  @ApiOperation({
    summary: 'Tra cứu lịch sử đơn',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách tra cứu lịch sử đơn',
    schema: generalSchema(SearchOrderESLibResponse, 'object'),
  })
  async searchHistoryOrder(@Body() body: SearchHistoryOrderOmsDto) {
    return this.schedulesService.searchHistoryOrder(body);
  }

  /**
   * @TODO lấy danh sách lịch hẹn ở màn tư vấn theo shop
   */
  @Public()
  @Post('search-schedule-by-shop')
  @ApiOperation({
    summary: 'Lấy danh sách lịch hẹn theo shop',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách lịch hẹn theo shop',
    schema: generalSchema(ScheduleByShopCodeRes, 'object'),
  })
  async searchScheduleByShop(@Body() body: SearchScheduleByShopCodeDto) {
    return this.schedulesService.searchScheduleByShop(body);
  }

  /*
   * @TODO Tra cứu lịch sử đơn
   */
  @Public()
  @Post('history-option')
  @ApiOperation({
    summary: 'Tạo lịch sử ngoài hệ thống',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách tra cứu lịch sử đơn',
    schema: generalSchema(ListHistoryScheduleInjectionRes, 'object'),
  })
  async createHistoryOption(@Body() body: CreateHistoryOptionDto) {
    return this.schedulesService.createHistoryOption(body);
  }

  /**
   * @TODO lấy danh sách lịch sử tiêm
   */
  @Public()
  @Get('list-injection-history')
  @ApiOperation({
    summary: 'Lấy danh sách lịch sử tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách tra cứu lịch sử tiêm',
    schema: generalSchema(ListHistoryScheduleInjectionRes, 'object'),
  })
  async getInjectionHistory(@Query() params: InjectionHistoryDto) {
    return this.schedulesService.getInjectionHistory(params);
  }

  /**
   * @TODO Sync danh sách lịch sử tiêm
   */
  @Post('sync-injection-history')
  @ApiOperation({
    summary: 'Lấy danh sách lịch sử tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách tra cứu lịch sử tiêm',
    schema: generalSchema(ListHistoryScheduleInjectionRes, 'object'),
  })
  async syncInjectionHistory(@Body() body: SyncHistoryTCQG) {
    return this.schedulesService.syncInjectionHistory(body);
  }

  /**
   * @TODO cung cấp api chỉ để trigger sync lịch sử tiêm và luôn trả về true
   */
  @Post('trigger-sync-history')
  @ApiOperation({
    summary: 'cung cấp api chỉ để trigger sync lịch sử tiêm và luôn trả về true',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trạng thái đồng bộ lịch sử tiêm',
    schema: generalSchema(ListHistoryScheduleInjectionRes, 'object'),
  })
  async triggerSyncHistory(@Body() body: SyncHistoryTCQG) {
    return this.schedulesService.triggerSyncHistory(body);
  }
  /*
   * @TODO Cập nhật lịch sử ngoài hệ thống
   */
  @Public()
  @Put('history-option')
  @ApiOperation({
    summary: 'Cập nhật lịch sử  sử ngoài hệ thống',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Chi tiết lịch sử',
    schema: generalSchema(ListHistoryScheduleInjectionRes, 'object'),
  })
  async updateHistory(@Body() body: UpdateHistoryOptionDto) {
    return this.schedulesService.updateScheduleHistory(body);
  }

  /*
   * @TODO Xóa lịch sử ngoài hệ thống
   */
  @Public()
  @Delete('history-option/:id/:lcvId')
  @ApiOperation({
    summary: 'Xóa lịch sử tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: '',
    schema: generalSchema({}, 'array'),
  })
  async deleteHistory(@Param('id') historyDetailId: string, @Param('lcvId') lvcId: string) {
    return this.schedulesService.deleteHistory(historyDetailId, lvcId);
  }

  /*
   * @TODO Tra cứu lịch sử đơn
   */
  @Public()
  @Get('purchase-history:lcvId')
  @ApiOperation({
    summary: 'Lấy thông tin lịch sử mua hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách lịch sử mua',
    schema: generalSchema(OrderInjectionPurchaseRes, 'array'),
  })
  async getPurchaseHistory(@Param('lcvId') lcvId: string) {
    return this.schedulesService.getPurchaseHistory(lcvId);
  }

  /*
   * @TODO sổ tiêm chủng
   */
  @Public()
  @Get('vaccination-book')
  @ApiOperation({
    summary: 'Sổ tiêm chủng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin sổ tiêm chủng',
    schema: generalSchema(VaccineBookRes, 'object'),
  })
  async getVaccinationBook(@Query() query: LoadVaccineBookDto) {
    return this.schedulesService.getVaccineBook(query);
  }

  @Public()
  @Post('sync-tcqg-by-history-detail-ids')
  @ApiOperation({
    summary: 'sync tcqg',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(SyncHistoryByDetailId, 'object'),
  })
  syncTcqgByHistoryDetail(@Body() body: string[]) {
    return this.schedulesService.syncTcqgHistoryDetail(body);
  }

  @Public()
  @Post('replace-person-of-skus')
  @ApiOperation({
    summary: 'Chuyển mũi tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(ItemReplaceSkuRes, 'object'),
  })
  replacePersonOfSkus(@Body() body: ItemReplaceSkuDto) {
    return this.schedulesService.replacePersonOfSkus(body);
  }

  @Public()
  @Post('check-dup-schedule')
  @ApiOperation({
    summary: 'Kiểm tra ngày hẹn tiêm trùng nhau khi nhấn nút tư vấn',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(CheckRuleDupScheduleRes, 'object'),
  })
  checkDupSchedule(@Body() body: CheckRuleScheduleConsultantDto) {
    return this.schedulesService.checkDupSchedule(body);
  }

  @Public()
  @Post('search-by-shop')
  @ApiOperation({
    summary: 'Lấy lịch hẹn theo shop',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(SearchByShopRes, 'object'),
  })
  searchByshop(@Body() body: SearchByShopDto) {
    return this.schedulesService.searchByShop(body);
  }

  @Public()
  @Post('send-message-voucher')
  @ApiOperation({
    summary: 'Lấy lịch hẹn theo shop',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(CreateNotificationLibResponseDTO, 'object'),
  })
  sendMessageSunAndRain(@Body() body: SendMessageSunAndRainDto) {
    return this.schedulesService.sendMessageSunAndRain(body);
  }

  @Public()
  @Post('check')
  @ApiOperation({
    summary: 'Chặn all lịch hẹn',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách rule lịch hẹn',
    schema: generalSchema(SuggestScheduleDto, 'object'),
  })
  @Version('2')
  check(@Body() checkScheduleDto: CheckScheduleDto) {
    return this.scheduleEngineAppService.checkSchedule(checkScheduleDto);
  }

  /**
   * @TODO lấy danh sách lịch hẹn trễ + lịch sử hôm nay cho call center ecom
   */
  @Public()
  @Get('get-schedules-call-center')
  @ApiOperation({
    summary: 'lấy danh sách lịch hẹn + lịch sử',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách lịch hẹn',
    schema: generalSchema(ScheduleItem, 'array'),
  })
  async getSchedulesCallCenter(@Query() params: InjectionHistoryDto) {
    return this.schedulesService.getSchedulesCallCenter(params);
  }

  /**
   * @TODO lấy danh sách lịch sử theo ticket bởi orderDetailAttachmentCode
   */
  @Public()
  @Post('get-schedules-by-order-detail-attachment-code')
  @ApiOperation({
    summary: 'Lấy danh sách lịch sử cùng ticketCode',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách lịch sử',
    schema: generalSchema(GetListScheduleHistoryRes, 'object'),
  })
  async getSchedulesByOrderDetailAttachmentCodes(@Body() body: GetListScheduleHistoryDto) {
    return this.schedulesService.getListHistoryByDetailAttachmentCode(body);
  }

  @Get('check-rule-host-phone')
  @ApiResponse({
    type: Boolean,
    status: HttpStatus.OK,
  })
  async checkRuleHostPhone(@Query() query: CheckRuleHostPhoneCustomerDto) {
    return this.schedulesRuleService.checkHostPhone(query.lcvId);
  }

  @Public()
  @Post('elasticsearch/search-schedule-ecom-early-d2')
  @ApiOperation({
    summary: 'Tìm danh sách lịch hẹn sớm d-2',
  })
  @HttpCode(HttpStatus.OK)
  async getScheduleEcomEarlyD2(@Body() getScheduleEcomEarlyD2Dto: GetScheduleEcomEarlyD2ConvertDto) {
    return this.schedulesService.getScheduleEcomEarlyD2(getScheduleEcomEarlyD2Dto);
  }

  @Post('ecom-schedule-reminder-early/update-early-remind-d2-status')
  @ApiOperation({
    summary: 'update kết quả xử lý lịch hẹn d-2',
  })
  @HttpCode(HttpStatus.OK)
  updateScheduleEcomEarlyD2Status(@Body() updateScheduleEcomEarlyD2StatusDto: UpdateScheduleEcomEarlyD2StatusDto) {
    return this.schedulesService.updateScheduleEcomEarlyD2Status(updateScheduleEcomEarlyD2StatusDto);
  }

  @Get('ecom-schedule-reminder-early/all-status')
  @ApiOperation({
    summary: 'Lấy masterdata kết quả xử lý',
  })
  @HttpCode(HttpStatus.OK)
  async getAllStatusScheduleRemindEarly() {
    return this.schedulesService.getAllStatusScheduleRemindEarly();
  }

  @Public()
  @Get('save-order-schedules')
  @ApiOperation({
    summary: 'lấy danh sách lịch hẹn đơn chờ ECOM',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách lịch hẹn đơn chờ ECOM',
  })
  async getSaveOrderSchedules(@Query() queries: GetSaveOrderScheduleQuery) {
    return this.schedulesService.getSaveOrderSchedules(queries);
  }

  /*
   * @TODO Xóa lịch sử tiêm xác nhận theo otp
   */
  @Public()
  @Post('delete-schedule-history-tcqg')
  @ApiOperation({
    summary: 'Xóa lịch sử tiêm nguồn tcqg xác nhận bởi SM/AMS/8733',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: '',
    schema: generalSchema({}, 'array'),
  })
  async deleteHistoryByOtp(@Body() reqDeleteHistoryDto: ReqDeleteHistoryDto) {
    return this.schedulesService.deleteHistoryByOtp(reqDeleteHistoryDto);
  }
}
