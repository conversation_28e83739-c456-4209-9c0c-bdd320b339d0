import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { SearchContractRes } from 'vac-nest-contract';
import { PrintCenterRes, PrinterInfoDto } from 'vac-nest-print-center';

export class Product {
  @ApiProperty()
  @Expose()
  itemCode?: string;

  @ApiProperty()
  @Expose()
  itemName?: string;

  @ApiProperty()
  @Expose()
  manufactor?: string;

  @ApiProperty()
  @Expose()
  diseaseName?: string;

  @ApiProperty()
  @Expose()
  quantity?: string;
}

export class OrderDetailDto {
  @ApiProperty()
  @Expose()
  orderCodeShort: string;

  @ApiProperty({ type: Product })
  @Expose()
  productDatas: Product[];

  @ApiProperty()
  @Expose()
  totalBill?: number;

  @ApiProperty()
  @Expose()
  orderAttribute?: number;

  @ApiProperty()
  @Expose()
  totalDeposit?: number;

  @ApiProperty()
  @Expose()
  totalDepositWord?: string;

  @ApiProperty()
  @Expose()
  totalDepositWordEN?: string;

  @ApiProperty()
  @Expose()
  isPartialPayment?: boolean;

  @ApiProperty()
  @Expose()
  totalBillPartialPayment?: number;

  @ApiProperty()
  @Expose()
  totalBillPartialPaymentWord?: string;

  @ApiProperty()
  @Expose()
  totalBillPartialPaymentWordEN?: string;

  @ApiProperty()
  @Expose()
  partialPaymentAmount?: number;

  @ApiProperty()
  @Expose()
  partialPaymentAmountWord?: string;

  @ApiProperty()
  @Expose()
  partialPaymentAmountWordEN?: string;
}

export class ServiceProviderInfoDto {
  @ApiProperty()
  @Expose()
  name?: string;

  @ApiProperty()
  @Expose()
  address?: string;

  @ApiProperty()
  @Expose()
  businessCode?: string;

  @ApiProperty()
  @Expose()
  phoneNumber?: string;

  @ApiProperty()
  @Expose()
  gender?: number;

  @ApiProperty()
  @Expose()
  representName?: string;

  @ApiProperty()
  @Expose()
  position?: string;

  @ApiProperty()
  @Expose()
  serviceContractNum?: string;
}

export class CustomerDto {
  @ApiProperty()
  @Expose()
  name?: string;

  @ApiProperty()
  @Expose()
  dateOfBirth?: Date;

  @ApiProperty()
  @Expose()
  gender?: number;

  @ApiProperty()
  @Expose()
  ethnicName?: string;

  @ApiProperty()
  @Expose()
  phoneNumber?: string;

  @ApiProperty()
  @Expose()
  email?: string;

  @ApiProperty()
  @Expose()
  address?: string;

  @ApiProperty()
  @Expose()
  addressEN?: string;

  @ApiProperty()
  @Expose()
  identityCard?: string;

  @ApiProperty()
  @Expose()
  nationality?: string;

  @ApiProperty()
  @Expose()
  nationalityEN?: string;
}

export class ServiceUserInfoDto {
  @ApiProperty({ type: CustomerDto })
  @Expose()
  customerInfo?: CustomerDto;

  @ApiProperty({ type: CustomerDto })
  @Expose()
  buyerInfo?: CustomerDto;
}

export class PaymentInfo {
  @ApiProperty()
  @Expose()
  bankUsername?: string;

  @ApiProperty()
  @Expose()
  bankNum?: string;

  @ApiProperty()
  @Expose()
  bankName?: string;
}

export class PrintServiceContractDto {
  @ApiProperty()
  @Expose()
  shopCode?: string;

  @ApiProperty()
  @Expose()
  totalPage?: number;

  @ApiProperty()
  @Expose()
  orderCode?: string;

  @ApiProperty({ type: OrderDetailDto })
  @Expose()
  @IsOptional()
  orderDetail?: OrderDetailDto;

  @ApiProperty({ type: ServiceProviderInfoDto })
  @Expose()
  @IsOptional()
  serviceProviderInfo?: ServiceProviderInfoDto;

  @ApiProperty()
  @Expose()
  serviceProviderInfoEN?: ServiceProviderInfoDto;

  @ApiProperty({ type: ServiceUserInfoDto })
  @Expose()
  @IsOptional()
  serviceUserInfo?: ServiceUserInfoDto;

  @ApiProperty({ type: PaymentInfo })
  @Expose()
  @IsOptional()
  paymentInfo?: PaymentInfo;

  @ApiProperty({ type: PaymentInfo })
  @Expose()
  @IsOptional()
  paymentInfoEN?: PaymentInfo;

  @ApiProperty()
  @Expose()
  expiredServiceContract?: string;

  @ApiProperty()
  @Expose()
  createdDate?: string;

  @ApiProperty()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  createdByName?: string;
}

export class PrinServiceContractDto {
  @ApiProperty({ type: PrinterInfoDto })
  @IsOptional()
  @Expose()
  printerInfo: PrinterInfoDto;

  @ApiProperty({ type: PrintServiceContractDto })
  @IsOptional()
  @Expose()
  printData: PrintServiceContractDto;
}

export class GetServiceContractDto {
  @ApiProperty({ type: PrinterInfoDto })
  @IsOptional()
  @Expose()
  printerInfo: PrinterInfoDto;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @Expose()
  orderCode: string;

  @ApiProperty()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  createdByName?: string;

  @ApiProperty()
  @Expose()
  isForceCreateDraft?: boolean;

  @ApiProperty()
  @Expose()
  totalPage?: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @Expose()
  ticketCode: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @Expose()
  lcvIdBuyer: string;
}

export class PrintServiceContractRes extends PrintCenterRes {
  @ApiProperty()
  @Expose()
  contractDetail: SearchContractRes;
}
