import {
  Controller,
  Get,
  Param,
  HttpCode,
  HttpStatus,
  Post,
  Body,
  Put,
  Req,
  Version,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiHeaders,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import {
  ClassErrorResponse,
  CustomHeaders,
  Public,
  ErrorCode,
  SystemException,
  generalSchema,
  BlockAdjustOrderInterceptor,
} from '@shared';
import { GetOrderEsRes } from './dto/get-order-es.dto';
import { ContinueBuyingRes } from './dto/continue-buying.dto';
import {
  UpdateStatusOrderPayloadDto,
  UpdateStatusOrderPayloadV2Dto,
  UpdateStatusOrderResponse,
} from './dto/update-status-order.dto';
import { CreateOrderRes } from 'vac-nest-oms';
import { EmployeeInfoContinueBuyingDto, PlaceOrderDto } from 'modules/modules/modules/orders/dto';
import { GetDetailsByTicketCodeDTO } from 'modules/modules/modules/orders/dto/get-details-by-ticket-code.dto';
import { PushsOrderPayloadDto } from 'modules/modules/modules/orders/dto/push-order.dto';
import { SaveEcomOrderEarlyDto } from 'vac-nest-schedule/dist/dto/save-ecom-reminder-early.dto';
import { SaveOrderDto } from './dto/save-order.dto';

@Controller({ path: 'orders', version: '1' })
@ApiTags('Order')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(GetOrderEsRes, ContinueBuyingRes)
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@CustomHeaders()
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Get('es/:orderCode')
  @ApiOperation({
    summary: 'Thông tin chi tiết đơn hàng trên ES',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin đơn hàng trên ES',
    schema: generalSchema(GetOrderEsRes, 'object'),
  })
  @Public()
  getOrderOnEs(@Param('orderCode') orderCode: string, @Query('ticketCode') ticketCode?: string) {
    return this.ordersService.getOrderOnEs(orderCode, ticketCode);
  }

  @Get(':orderCode')
  @ApiOperation({
    summary: 'Thông tin chi tiết đơn hàng trong db',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin đơn hàng trong db',
    schema: generalSchema(GetOrderEsRes, 'object'),
  })
  orderDetail(@Param('orderCode') orderCode: string) {
    return this.ordersService.orderDetail(orderCode);
  }

  @Get('continue-buying/:orderCode')
  @ApiOperation({
    summary: 'Tiếp tục đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả hủy đơn hàng',
    schema: generalSchema(ContinueBuyingRes, 'object'),
  })
  @UseInterceptors(BlockAdjustOrderInterceptor)
  continueBuying(@Param('orderCode') orderCode: string, @Query() payload: EmployeeInfoContinueBuyingDto) {
    return this.ordersService.continueBuying(orderCode, payload);
  }

  @Post('update-status-order-deposit')
  @ApiOperation({
    summary: 'Cập nhật trạng thái đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Cập nhật trạng thái đơn hàng',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  updatedStatusOrderDeposit(@Body() body: UpdateStatusOrderPayloadDto) {
    return this.ordersService.finishOrder(body);
  }

  @Post('push-order')
  @ApiOperation({
    summary: 'Cập nhật ecomDisplay = 1',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Cập nhật ecomDisplay = 1',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  pushOrder(@Body() body: PushsOrderPayloadDto) {
    return this.ordersService.pushOrder(body);
  }

  @Post('push-partial-order')
  @ApiOperation({
    summary: 'Đẩy đơn từng phần thanh toán tại shop',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Đẩy đơn từng phần thanh toán tại shop',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  pushPartialOrder(@Body() body: PushsOrderPayloadDto) {
    return this.ordersService.pushPartialOrder(body);
  }

  @Post('place')
  @ApiOperation({
    summary: 'Tạo đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(CreateOrderRes, 'object'),
  })
  placeOrder(@Body() placeOrder: PlaceOrderDto) {
    return this.ordersService.placeOrder(placeOrder);
  }

  @Put('place/:orderCode')
  @ApiOperation({
    summary: 'Cập nhật đơn hàng dành cho lễ tân',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(CreateOrderRes, 'object'),
  })
  updatePlaceOrder(@Param('orderCode') orderCode: string, @Body() placeOrder: PlaceOrderDto) {
    return this.ordersService.updatePlaceOrder(orderCode, placeOrder);
  }

  @Post('save-order-deposit-online')
  @ApiOperation({
    summary: 'Lưu đơn hàng thanh toán online',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(Boolean, 'boolean'),
  })
  saveOrderDepositOnline(@Body() dataBody: UpdateStatusOrderPayloadDto) {
    return this.ordersService.saveOrderDepositOnline(dataBody);
  }

  @Get('check-status-push-order/:orderCode')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái đẩy đơn',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(Boolean, 'boolean'),
  })
  checkStatusPushOrder(@Param('orderCode') orderCode: string) {
    return this.ordersService.checkStatusPushOrder(orderCode);
  }

  @Get('check-next-action/:orderCode')
  @ApiOperation({
    summary: 'Kiểm tra hành động tiếp theo của đơn',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'SAVE_ORDER or PUSH_ORDER',
  })
  checkNextActionOrder(@Param('orderCode') orderCode: string) {
    return this.ordersService.checkNextActionOrder(orderCode);
  }

  @ApiHeaders([
    {
      name: 'api-key',
      description: 'API Key',
      schema: {
        type: 'string',
        default: '1234556678899001232',
      },
    },
  ])
  @Public()
  @Post('update-status-order-deposit-v2') // backend - for - frontend
  @ApiOperation({
    summary: 'Đẩy đơn giành cho store-font đối với đơn hàng thanh toán online.',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Đẩy đơn hàng thanh toán online.',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  updatedStatusOrderDepositV2(@Body() body: UpdateStatusOrderPayloadV2Dto) {
    return this.ordersService.finishOrderV2(body);
  }

  @Public() // Giành riêng cho đơn từng phần.
  @Post('update-status-order-deposit-v3')
  @ApiOperation({
    summary: 'Thanh toán online giành riêng cho đơn từng phần.',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Đẩy đơn hàng thanh toán online.',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  updatedStatusOrderDepositV3(@Body() body: UpdateStatusOrderPayloadV2Dto) {
    return this.ordersService.finishOrderV3(body);
  }

  /**
   * @TODO xem chi tiết theo ticket code
   */
  @Get('tickets/:ticketCode')
  @Version('2')
  @ApiOperation({
    summary: 'Xem chi tiết theo ticket code',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Xem chi tiết theo ticket code',
  })
  async getDetailsByTicketCodeV2(
    @Param('ticketCode') ticketCode: string,
    @Query() getDetailsByTicketCodeDTO: GetDetailsByTicketCodeDTO,
  ) {
    return this.ordersService.getDetailsByTicketCodeV2(ticketCode, getDetailsByTicketCodeDTO);
  }

  /**
   * @TODO xem chi tiết theo ticket code for STC
   */
  @Get('for-stc/tickets/:ticketCode')
  @Version('2')
  @ApiOperation({
    summary: 'Xem chi tiết theo ticket code cho STC',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Xem chi tiết theo ticket code cho STC',
  })
  @Public()
  async getDetailsByTicketCodeV2ForStc(
    @Param('ticketCode') ticketCode: string,
    @Query() getDetailsByTicketCodeDTO: GetDetailsByTicketCodeDTO,
  ) {
    return this.ordersService.getDetailsByTicketCodeV2(ticketCode, getDetailsByTicketCodeDTO);
  }

  /**
   * @TODO xem chi tiết theo ticket code for STC
   */
  @Post('save-ecom-order-early')
  @Version('2')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Lưu Đơn',
  })
  @Public()
  async saveEcomOrderEarly(@Body() payload: SaveOrderDto) {
    return this.ordersService.saveEcomOrderEarly(payload);
  }
}
