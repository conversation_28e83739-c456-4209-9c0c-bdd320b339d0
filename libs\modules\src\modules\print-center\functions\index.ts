/* eslint-disable no-var */
import moment from 'moment';
import { DetailLib } from 'vac-nest-payment-gateway';
const defaultNumbers = ' hai ba bốn năm sáu bảy tám chín';

const chuHangDonVi = ('1 một' + defaultNumbers).split(' ');
const chuHangChuc = ('lẻ mười' + defaultNumbers).split(' ');
const chuHangTram = ('không một' + defaultNumbers).split(' ');

const convertBlockThree = (number) => {
  if (number == '000') return '';
  var _a = number + ''; //Convert biến 'number' thành kiểu string

  //Kiểm tra độ dài của khối
  switch (_a.length) {
    case 0:
      return '';
    case 1:
      return chuHangDonVi[_a];
    case 2:
      return convertBlockTwo(_a);
    case 3:
      let chuc_dv = '';
      if (_a.slice(1, 3) != '00') {
        chuc_dv = convertBlockTwo(_a.slice(1, 3));
      }
      var tram = chuHangTram[_a[0]] + ' trăm';
      return tram + ' ' + chuc_dv;
  }
};

const convertBlockTwo = (number) => {
  var dv = chuHangDonVi[number[1]];
  var chuc = chuHangChuc[number[0]];
  var append = '';

  // Nếu chữ số hàng đơn vị là 5
  if (number[0] > 0 && number[1] == 5) {
    dv = 'lăm';
  }

  // Nếu số hàng chục lớn hơn 1
  if (number[0] > 1) {
    append = ' mươi';

    if (number[1] == 1) {
      dv = ' mốt';
    }
  }

  return chuc + '' + append + ' ' + dv;
};

const dvBlock = '1 nghìn triệu tỷ'.split(' ');

export const convertNumberToVietnamese = (number) => {
  var str = parseInt(number) + '';
  var i = 0;
  var arr = [];
  var index = str.length;
  var result = [];
  var rsString = '';

  if (index == 0 || str == 'NaN' || number === 0) {
    return '';
  }

  // Chia chuỗi số thành một mảng từng khối có 3 chữ số
  while (index >= 0) {
    arr.push(str.substring(index, Math.max(index - 3, 0)));
    index -= 3;
  }

  // Lặp từng khối trong mảng trên và convert từng khối đấy ra chữ Việt Nam
  for (i = arr.length - 1; i >= 0; i--) {
    if (arr[i] != '' && arr[i] != '000') {
      result.push(convertBlockThree(arr[i]));

      // Thêm đuôi của mỗi khối
      if (dvBlock[i]) {
        result.push(dvBlock[i]);
      }
    }
  }

  // Join mảng kết quả lại thành chuỗi string
  rsString = result.join(' ');

  rsString = (rsString.replace(/[0-9]/g, '').replace(/ /g, ' ').replace(/ $/, '') + ' đồng').trim();

  // Trả về kết quả kèm xóa những ký tự thừa
  return rsString.charAt(0).toUpperCase() + rsString.slice(1);
};

export const getFullAddress = (customerInfor, isFrequently = false) => {
  let fullAddress = '';
  if (isFrequently) {
    if (customerInfor?.frequentlyAddress) fullAddress = customerInfor?.frequentlyAddress + ' - ';
    if (customerInfor?.frequentlyWardName) fullAddress = fullAddress + customerInfor?.frequentlyWardName + ' - ';
    if (customerInfor?.frequentlyDistrictName)
      fullAddress = fullAddress + customerInfor?.frequentlyDistrictName + ' - ';
    if (customerInfor?.frequentlyProvinceName) fullAddress = fullAddress + customerInfor?.frequentlyProvinceName;
  } else {
    if (customerInfor?.temporaryAddress) fullAddress = customerInfor?.temporaryAddress + ' - ';
    if (customerInfor?.temporaryWardName) fullAddress = fullAddress + customerInfor?.temporaryWardName + ' - ';
    if (customerInfor?.temporaryDistrictName) fullAddress = fullAddress + customerInfor?.temporaryDistrictName + ' - ';
    if (customerInfor?.temporaryProvinceName) fullAddress = fullAddress + customerInfor?.temporaryProvinceName;
  }
  return fullAddress;
};

export const getFullAddressCertificate = (customerInfor, isFrequently = false) => {
  let fullAddress = '';
  if (isFrequently) {
    if (customerInfor?.frequentlyAddress && customerInfor?.frequentlyAddress !== 'Không xác định')
      fullAddress = customerInfor?.frequentlyAddress + ', ';
    if (customerInfor?.frequentlyWardName) fullAddress = fullAddress + customerInfor?.frequentlyWardName + ', ';
    if (customerInfor?.frequentlyDistrictName) fullAddress = fullAddress + customerInfor?.frequentlyDistrictName + ', ';
    if (customerInfor?.frequentlyProvinceName) fullAddress = fullAddress + customerInfor?.frequentlyProvinceName;
  } else {
    if (customerInfor?.temporaryAddress && customerInfor?.temporaryAddress !== 'Không xác định')
      fullAddress = customerInfor?.temporaryAddress + ', ';
    if (customerInfor?.temporaryWardName) fullAddress = fullAddress + customerInfor?.temporaryWardName + ', ';
    if (customerInfor?.temporaryDistrictName) fullAddress = fullAddress + customerInfor?.temporaryDistrictName + ', ';
    if (customerInfor?.temporaryProvinceName) fullAddress = fullAddress + customerInfor?.temporaryProvinceName;
  }
  return fullAddress;
};

export const getPaymentMethod = (detail: DetailLib) => {
  const { cash = [], cardsAll = [], vouchersAll = [], eWalletAll = [], transfersAll = [] } = detail || {};

  const resuft = [];
  if (cash?.length) {
    resuft.push('Tiền mặt');
  }
  if (cardsAll?.length) {
    resuft.push('Thanh toán thẻ');
  }
  if (vouchersAll?.length) {
    resuft.push('Voucher');
  }
  if (transfersAll?.length) {
    resuft.push('Chuyển khoản');
  }
  if (eWalletAll?.length) {
    resuft.push('Ví điện tử');
  }
  return resuft?.join(', ');
};

export const calculatorAge = (dob) => {
  const currentDate = moment(moment().utcOffset(7).format('YYYY-MM-DD'));
  const dateOfBirth = moment(dob).utcOffset(7).format('YYYY-MM-DD');

  const year = currentDate.diff(dateOfBirth, 'years');
  const month = currentDate.diff(dateOfBirth, 'months');
  if (year > 0) return `${year} Tuổi ${month % 12} Tháng`;
  if (month >= 6) return `${month} Tháng`;
  const weeks = currentDate.diff(dateOfBirth, 'weeks');
  if (weeks > 0) return `${weeks} Tuần`;
  const days = currentDate.diff(dateOfBirth, 'days');
  return `${days} Ngày`;
};

export const calculatorAgeEN = (dob) => {
  const currentDate = moment(moment().utcOffset(7).format('YYYY-MM-DD'));
  const dateOfBirth = moment(dob).utcOffset(7).format('YYYY-MM-DD');

  const year = currentDate.diff(dateOfBirth, 'years');
  const month = currentDate.diff(dateOfBirth, 'months');
  const textMonth = month % 12 > 1 ? 'Months' : 'Month';
  if (year > 1) return `${year} Years ${month % 12} ${textMonth}`;
  if (year === 1) return `${year} Year ${month % 12} ${textMonth}`;
  if (month >= 6) return `${month} Months`;
  const weeks = currentDate.diff(dateOfBirth, 'weeks');
  if (weeks > 1) return `${weeks} Weeks`;
  if (weeks === 1) return `${weeks} Week`;
  const days = currentDate.diff(dateOfBirth, 'days');
  if (days <= 1) return `${days} Day`;
  return `${days} Days`;
};

// docs :https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=150566256
export const masterDataInjectionAndRoute = (dataVN) => {
  switch (dataVN) {
    // Vị trí tiêm
    case 'Tay trái':
      return 'L. deltoid';
    case 'Tay phải':
      return 'R. deltoid';
    case 'Đùi trái':
      return 'L. thigh';
    case 'Đùi phải':
      return 'R. thigh';
    // Đường dùng
    case 'Tiêm dưới da':
      return 'Subcutaneously';
    case 'Tiêm trong da':
      return 'Intradermal';
    case 'Tiêm bắp':
      return 'Intramuscular';
    case 'Uống':
      return 'Orally';
    default:
      if (dataVN) {
        let injectionAndRoute = dataVN;
        const case1 = dataVN?.includes('Tay trái');
        const case2 = dataVN?.includes('Tay phải');
        const case3 = dataVN?.includes('Đùi trái');
        const case4 = dataVN?.includes('Đùi phải');
        const case5 = dataVN?.includes('Tiêm dưới da');
        const case6 = dataVN?.includes('Tiêm trong da');
        const case7 = dataVN?.includes('Tiêm bắp');
        const case8 = dataVN?.includes('Uống');
        if (case1) injectionAndRoute = injectionAndRoute?.replace('Tay trái', 'L. deltoid');
        if (case2) injectionAndRoute = injectionAndRoute?.replace('Tay phải', 'R. deltoid');
        if (case3) injectionAndRoute = injectionAndRoute?.replace('Đùi trái', 'L. thigh');
        if (case4) injectionAndRoute = injectionAndRoute?.replace('Đùi phải', 'R. thigh');
        if (case5) injectionAndRoute = injectionAndRoute?.replace('Tiêm dưới da', 'Subcutaneously');
        if (case6) injectionAndRoute = injectionAndRoute?.replace('Tiêm trong da', 'Intradermal');
        if (case7) injectionAndRoute = injectionAndRoute?.replace('Tiêm bắp', 'Intramuscular');
        if (case8) injectionAndRoute = injectionAndRoute?.replace('Uống', 'Orally');

        return injectionAndRoute;
      }
      return '';
  }
};

export const masterDataUnitName = (unitName, quantity) => {
  switch (unitName) {
    case 'Gói':
      if (quantity > 1) return 'Packages';
      return 'Package';
    case 'Viên sủi':
      if (quantity > 1) return 'Effervescent tablets';
      return 'Effervescent tablet';
    case 'Viên':
      if (quantity > 1) return 'Tablets';
      return 'Tablet';

    default:
      return '';
  }
};
