import { Injectable } from '@nestjs/common';
import { GetManyMonitorDto, MonitorCoreService } from 'vac-nest-monitor';
import { CreateMonitorRes } from '../dto';
import * as _ from 'lodash';

@Injectable()
export class CommonService {
  constructor(private readonly monitorCoreService: MonitorCoreService) {}

  /**
   * @TODO lấy danh sách phản ứng sau tiêm
   */
  async getMonitor() {
    const infoMonitor = await this.monitorCoreService.monitorGetMany();

    if (!infoMonitor) return [];

    let arrMapMonitor = [];

    // lấy danh sách các monitor parent
    const listParent = infoMonitor?.filter((monitor) => monitor?.isParent === true) || [];

    listParent?.map((item) => {
      const children = infoMonitor?.filter((x) => x?.isParent === false && x?.monitorType === item?.monitorType);
      if (!children) {
        arrMapMonitor.push(item);
      } else {
        const itemChildren = [];
        children?.map((i) => {
          const itemChildrenMonitorDetail = {
            id: i?.id,
            label: i?.monitorDetail,
          };
          itemChildren.push(itemChildrenMonitorDetail);
        });

        const test = {
          id: item?.id,
          monitorType: item?.monitorType,
          monitorTypeName: item?.monitorTypeName,
          isRequireNote: item?.isRequireNote,
          children: itemChildren,
        };

        arrMapMonitor.push(test);
      }
    });

    arrMapMonitor = _.orderBy(arrMapMonitor, ['monitorType'], ['asc']);

    return {
      items: arrMapMonitor,
    };
  }

  /**
   * @TODO tạo health monitor
   */
  async createHealthMonitor(body: CreateMonitorRes) {
    const { arrMonitor, ticketCode } = body;
    const payloadCreate = arrMonitor?.map((monitor) => {
      return {
        ...monitor,
        ticketCode: ticketCode,
      };
    });

    const isSuccess = await this.monitorCoreService.createManyHealthMonitor(payloadCreate);
    return {
      isSuccess: isSuccess,
    };
  }

  /**
   * @TODO lấy kết quả phản ứng sau tiêm
   */
  async healthMonitorGetManyService(query: GetManyMonitorDto) {
    const { ticketCode } = query;

    // gọi api monitor
    const data = await this.monitorCoreService.healthMonitorGetMany({ ticketCode: ticketCode });

    if (!data)
      return {
        items: [],
      };

    const resultMonitor =
      data?.map((monitor) => {
        return {
          ticketCode: monitor?.ticketCode,
          note: monitor?.note,
          monitorTypeName: monitor?.details?.at(0)?.monitorTypeName,
          details: monitor?.details?.map((detail) => {
            return detail?.context;
          }),
        };
      }) || [];

    return {
      items: resultMonitor,
    };
  }
}
