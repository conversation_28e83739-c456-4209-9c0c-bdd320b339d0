import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Post, Query, Req } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';

import { ClassErrorResponse, generalSchema, Public } from '@shared';
import { SearchTicketDto, TicketAlwaysIndicationRes } from 'vac-nest-examination';
import { GetDetailedGeneralFundReportsDto, RabiesReportAppendixOneDto, UsedVaccineReportDto } from 'vac-nest-report';
import {
  DailyVaccineReportDTO,
  DailyVaccinSumReportDTO,
  DeleteCacheShopHubDto,
  ExcelInventoryDto,
  GetVacExcelReportDTO,
  GetXntReportDto,
  InjectionStatisticsDto,
  SearchTicketExcelDto,
} from '../dto';
import { ReportsService } from '../services/reports.service';

import { GetInventoryProductItemByShopSkuBody, GetListStockMedicAtShopDto as GetStockBySkuBody } from 'vac-nest-ims';
import {
  GetStockBySkuAndProductItemResponse,
  GetStockBySkuResponse,
  GetStockHistoryBySkuResponse,
} from '../../stocks/dto';
import { GetVac6In1VATReportDto } from '../dto/get-vac-6in1-vat.dto';
@Controller({ path: 'reports', version: '1' })
@ApiTags('Reports')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(TicketAlwaysIndicationRes)
export class ReportsController {
  constructor(private readonly reportService: ReportsService) {}
  @Public()
  @Get('rabies-report-excel')
  @ApiOperation({
    summary: 'Export excel file',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Excel file generated successfully',
  })
  async getExcelReport(@Query() payload: GetVacExcelReportDTO) {
    return this.reportService.getExcelReport(payload);
  }

  @Public()
  @Get('info-vaccin-use-daily')
  @ApiOperation({
    summary: 'get daily vaccin',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'get used vaccin for daily',
  })
  async getDailyVaccineReport(@Query() payload: DailyVaccineReportDTO) {
    return this.reportService.getDailyVaccineReport(payload);
  }

  @Public()
  @Get('info-sku-vaccin-daily-sum')
  @ApiOperation({
    summary: 'get sum of daily vaccin',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'get sum of vaccin for daily',
  })
  async getDailyVaccineSumReport(@Query() payload: DailyVaccinSumReportDTO) {
    return this.reportService.getDailySumVaccineReport(payload);
  }

  @Public()
  @Post('rabies-report-excel-appendix-one')
  @ApiOperation({
    summary: 'Export excel file',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Excel file generated successfully',
  })
  async getExcelReportAppendixOne(@Body() body: RabiesReportAppendixOneDto) {
    return this.reportService.getExcelReportAppendixOne(body);
  }

  /**
   * @TODO Báo cáo phản ứng sau tiêm
   */
  @Public()
  @Post('symptom-after-injection')
  @ApiOperation({
    summary: 'Báo cáo phản ứng sau tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'File báo cáo',
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  async symptomAfterInjection(@Body() filter: SearchTicketExcelDto) {
    return this.reportService.symptomAfterInjection(filter);
  }

  @Post('report-used')
  @ApiOperation({
    summary: 'Xuất file Excel báo cáo sử dụng vaccine',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin báo cáo',
  })
  printReportUsed(@Body() body: UsedVaccineReportDto) {
    return this.reportService.getExcelReportUsed(body);
  }

  @Public()
  @Post('search-monitor-after-injection')
  @ApiOkResponse({
    description: 'Lấy thông tin theo dõi sau tiêm',
    schema: generalSchema(TicketAlwaysIndicationRes, 'object'),
  })
  @HttpCode(HttpStatus.OK)
  async getMonitorAfterInjection(@Body() searchTicketDto: SearchTicketDto) {
    return this.reportService.getMonitorAfterInjection(searchTicketDto);
  }

  @Post('inventory')
  @ApiOperation({
    summary: 'báo cáo Excel xuất nhập tồn',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin báo cáo',
  })
  getExcelInventory(@Body() body: ExcelInventoryDto) {
    return this.reportService.getExcelInventory(body);
  }

  // START FOR REPORT SỞ Y TẾ
  @Post('stock/get-list-stock-medic-at-shop-and-hub')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy tồn kho bằng sku và shopcode theo shop và hub',
  })
  @ApiOkResponse({
    description: 'Lấy thông tin tồn kho của các sản phẩm tại shop và hub',
    schema: generalSchema(GetStockBySkuResponse, 'object'),
  })
  async getStockBySkuWithShopHub(@Body() body: GetStockBySkuBody): Promise<GetStockBySkuResponse> {
    return this.reportService.getStockBySkuWithShopHub(body);
  }

  // @Post('stock/product-item')
  @Post('stock/get-inventory-by-sku-at-shop-and-hub')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy tồn kho, lodate shop và hub bằng sku và shopcode',
  })
  @ApiOkResponse({
    description: '	',
    schema: generalSchema(GetStockBySkuAndProductItemResponse, 'object'),
  })
  async getStockBySkuAndProductItem(
    @Body() body: GetInventoryProductItemByShopSkuBody,
  ): Promise<GetStockBySkuAndProductItemResponse> {
    return this.reportService.getStockBySkuAndProductItem(body);
  }

  @Delete('stock/clear-cache-shop-hub')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete key shophub',
  })
  @ApiOkResponse({
    description: 'Input shopCodes, if null clear all shopCodes',
  })
  async clearCacheShopHub(@Body() body: DeleteCacheShopHubDto) {
    return this.reportService.clearCacheShopHub(body);
  }

  @Post('stock/history/get-by-sku')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy lịch sử giao dịch bằng sku',
  })
  @ApiOkResponse({
    description: 'Lấy lịch sử giao dịch bằng sku',
    schema: generalSchema(GetStockHistoryBySkuResponse, 'object'),
  })
  async getStockHistoryBySku(@Body() body: GetXntReportDto): Promise<GetStockHistoryBySkuResponse> {
    return this.reportService.getStockHistoryBySku(body);
  }
  // END FOR REPORT SỞ Y TẾ

  @Post('injection-statistics')
  @ApiOperation({
    summary: 'Báo cáo thống kê số liệu tiêm',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin báo cáo',
  })
  getExcelInjectionStatistics(@Body() body: InjectionStatisticsDto) {
    return this.reportService.getExcelInjectionStatistics(body);
  }

  @Post('vac-6in1-vat-report')
  @ApiOperation({
    summary: 'Export excel file',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Excel file generated successfully',
    schema: generalSchema(GetVac6In1VATReportDto, 'object'),
  })
  async getVAT6In1VacReport(@Body() payload: GetVac6In1VATReportDto, @Req() req: any) {
    return this.reportService.getVac6In1VATExcelReport(payload, req?.user?.employee_code);
  }

  @Post('bao_cao_quy/thong_tin_tong_hop_chi_tiet')
  @ApiOperation({
    summary: 'Thông tin chi tiết báo có quỹ',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin chi tiết báo có quỹ',
    schema: generalSchema(GetVac6In1VATReportDto, 'object'),
  })
  async getDetailedGeneralFundReports(@Body() payload: GetDetailedGeneralFundReportsDto) {
    return this.reportService.getDetailedGeneralFundReports(payload);
  }
}
