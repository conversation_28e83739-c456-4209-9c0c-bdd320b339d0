import { Injectable } from '@nestjs/common';
import { DSMSService } from 'vac-nest-dsms';
import { SHOP_TYPE } from '../constants';
import { plainToInstance } from 'class-transformer';
import { Shop } from '../dto';

@Injectable()
export class AdministrativeService {
  constructor(private readonly dsmsService: DSMSService) {}

  async findAllShop() {
    const listShop = await this.dsmsService.getListShop({ shopType: SHOP_TYPE });
    const shops = listShop?.shops?.map((item) =>
      plainToInstance(Shop, item, {
        excludeExtraneousValues: true,
        exposeUnsetFields: true,
      }),
    );
    return shops ? shops?.sort((a, b) => a?.code?.localeCompare(b?.code)) : [];
  }
}
