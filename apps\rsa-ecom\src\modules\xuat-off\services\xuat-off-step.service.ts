import { Injectable, NotFoundException } from '@nestjs/common';
import { UserDto } from '@shared';
import { XuatOffStatus } from '../constants';
import { UpdateXuatOffStepInfoDto } from '../dto';
import { CreateXuatOffStepInfoDto } from '../dto/xuat-off-step';
import { XuatOffEntity, XuatOffStepInfoEntity } from '../entities';
import { XuatOffRepository, XuatOffStepInfoRepository } from '../repository';

@Injectable()
export class XuatOffStepInfoService {
  constructor(
    private readonly xuatOffStepInfoRepository: XuatOffStepInfoRepository,
    private readonly xuatOffRepository: XuatOffRepository,
  ) {}

  assignXuatOffStep(xuatOff: XuatOffEntity, user: UserDto, step: XuatOffStatus, notes?: string) {
    const newStep = new XuatOffStepInfoEntity({
      xoCode: xuatOff?.xoCode,
      employeeCode: user?.employee_code,
      employeeName: user?.full_name,
      step,
      notes,
    });
    xuatOff.steps = [...xuatOff.steps, newStep];
  }

  // Create
  async create(payload: CreateXuatOffStepInfoDto, user: UserDto): Promise<XuatOffEntity> {
    const xuatOff = await this.xuatOffRepository.mustGetXuatOffByXOCode(payload?.xoCode, { steps: true });

    this.assignXuatOffStep(xuatOff, user, payload?.step, payload?.notes);
    await this.xuatOffRepository.save(xuatOff);
    return xuatOff;
  }

  // Read
  async findAll(): Promise<XuatOffStepInfoEntity[]> {
    return this.xuatOffStepInfoRepository.find({}, {});
  }

  async findOneById(id: string): Promise<XuatOffStepInfoEntity> {
    const step = await this.xuatOffStepInfoRepository.findOne({ id });
    if (!step) {
      throw new NotFoundException(`Step with ID ${id} not found`);
    }
    return step;
  }

  // Update
  async update(id: string, updateData: UpdateXuatOffStepInfoDto): Promise<XuatOffStepInfoEntity> {
    const step = await this.findOneById(id);
    Object.assign(step, updateData);
    return await this.xuatOffStepInfoRepository.create(step);
  }

  async remove(id: string): Promise<void> {
    const step = await this.findOneById(id);
    await this.xuatOffStepInfoRepository.remove(step);
  }
}
