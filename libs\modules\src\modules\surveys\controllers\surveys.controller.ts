import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query, Req } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiExtraModels,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, generalSchema, OrderChannels, Public } from '@shared';
import { GetListConsultantResultForTCQGRes, GetListSurveyForTCQGRes, SearchSurveyTCQGByQueryRes } from 'vac-nest-osr';
import {
  AssignSurveyDto,
  AssignUnpaidSurveysDto,
  CreateSurveyDto,
  GetListSurveyByEsDtoV2,
  RSAIntegrationService,
  UpdateSurveyDto,
} from 'vac-nest-rsa-integration';
import { GetUnpaidCustomerDto } from 'vac-nest-rsa-integration/dist/dto/unpaid-survey-customer.dto';
import { UpdateUnpaidSurveyDto } from 'vac-nest-rsa-integration/dist/dto/update-unpaid-survey.dto';
import { AutoGenerateService } from '../../../../../../apps/rsa-ecom/src/modules/auto-generate/auto-generate.service';
import { ContactAgainDto } from '../dto/contact-again.dto';
import { GetListConsultantResultConvertDto } from '../dto/get-list-consultant-result-unpaid.dto';
import { GetUnpaidDetailDto } from '../dto/get-unpaid-detail.dto';
import { AssignSurveysConvertDto, PayloadSearchSurveyTCQGByQueryDto } from '../dto/survey.dto';
import { SurveysService } from '../services/surveys.service';

import {
  AssignManySurveysDto,
  UpdatedSurveyCustomerTcqgDto,
  UpdatedSurveyCustomerTcqgRes,
} from '../dto/survey-tcqg.dto';
import { SendNotiDto } from '../dto/send-noti-survey.dto';

@Controller({ path: 'surveys', version: '1' })
@ApiTags('Survey')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(ClassResponse, GetListSurveyForTCQGRes, UpdatedSurveyCustomerTcqgRes)
export class SurveysController {
  constructor(
    private readonly rsaIntegrationService: RSAIntegrationService,
    private readonly autoGenerateService: AutoGenerateService,
    private readonly surveysService: SurveysService,
  ) {}

  @Post()
  @Public()
  @ApiOperation({
    summary: 'Tạo mới một survey',
  })
  @HttpCode(HttpStatus.OK)
  @ApiHeader({
    name: 'order-channel',
    required: true,
  })
  async create(@Body() createSurveyDto: CreateSurveyDto, @Req() req: any) {
    let sortIndex = undefined;
    if (OrderChannels.RSA_ECOM.includes(req.headers['order-channel'])) {
      sortIndex = await this.autoGenerateService.createSurveyIndex();
    }
    return this.rsaIntegrationService.createSurvey({
      ...createSurveyDto,
      sortIndex,
    });
  }

  @Put()
  @Public()
  @ApiOperation({
    summary: 'Cập nhật thông tin survey',
  })
  @HttpCode(HttpStatus.OK)
  // @ApiOkResponse({
  //   schema: generalSchema(, 'object'),
  // })
  update(@Body() updateSurveyDto: UpdateSurveyDto) {
    return this.surveysService.updateSurvey(updateSurveyDto);
  }

  @Put('assign')
  @Public()
  @ApiOperation({
    summary: 'Phân công survey',
  })
  @HttpCode(HttpStatus.OK)
  assign(@Body() updateSurveyDto: AssignSurveyDto) {
    // return this.rsaIntegrationService.assignSurvey(updateSurveyDto);
    return this.surveysService.assign(updateSurveyDto);
  }

  @Put('unpaid-survey-customer/assign')
  @Public()
  @ApiOperation({
    summary: 'Phân công survey',
  })
  @HttpCode(HttpStatus.OK)
  assignUnPaidSurvey(@Body() updateSurveyDto: AssignUnpaidSurveysDto) {
    // return this.rsaIntegrationService.assignSurvey(updateSurveyDto);
    return this.surveysService.assignUnPaidSurvey(updateSurveyDto);
  }

  @Put('assign-many')
  @Public()
  @ApiOperation({
    summary: 'Phân công nhiều survey',
  })
  @HttpCode(HttpStatus.OK)
  @ApiBody({
    type: [AssignSurveysConvertDto], //AssignSurveysDto --> AssignSurveysConvertDto thêm feild phone number
  })
  assignMany(@Body() body: AssignSurveysConvertDto[]) {
    return this.surveysService.assignManySurveys(body);
  }

  @Get('get-by-id/:id')
  @Public()
  @ApiOperation({
    summary: 'Get survey by id',
  })
  @HttpCode(HttpStatus.OK)
  // @ApiOkResponse({
  //   schema: generalSchema(, 'object'),
  // })
  getById(@Param('id') id: string) {
    return this.rsaIntegrationService.getSurveyById(id);
  }

  @Get()
  @Public()
  @ApiOperation({
    summary: 'Lấy danh sách survey theo điều kiện tìm kiếm',
  })
  @HttpCode(HttpStatus.OK)
  // @ApiOkResponse({
  //   schema: generalSchema(, 'object'),
  // })
  getListSurvey(@Query() getListSurveyByEsDto: GetListSurveyByEsDtoV2, @Req() req: any) {
    return this.rsaIntegrationService.getListSurveyByEsV2(getListSurveyByEsDto, {
      headers: {
        'order-channel': req.headers['order-channel'],
      },
    });
  }

  @Get('master-data/hour-range')
  @Public()
  @ApiOperation({
    summary: 'Lấy danh sách filter theo khoảng giờ.',
  })
  @HttpCode(HttpStatus.OK)
  getSurveyHourRange() {
    return this.rsaIntegrationService.getSurveyHourRange();
  }

  @Get('master-data/contact-channel')
  @Public()
  @ApiOperation({
    summary: 'Lấy danh sách kênh tư vấn',
  })
  @HttpCode(HttpStatus.OK)
  getContactChannel() {
    return this.rsaIntegrationService.getContactChannel();
  }

  @Get('/job-remind')
  @Public()
  @ApiOperation({
    summary: 'Nhắc gọi nhỡ',
  })
  @HttpCode(HttpStatus.OK)
  jobRemind() {
    return this.surveysService.jobRemind();
  }

  @Get('util/sort-index')
  @Public()
  @ApiOperation({
    summary: 'Tạo mới một chỉ số thứ tự.',
  })
  @HttpCode(HttpStatus.OK)
  getSortIndex() {
    return this.autoGenerateService.createSurveyIndex();
  }

  @Get('vaccine-categorization/get-all-redis')
  @Public()
  @ApiOperation({
    summary: 'Lấy danh sách vaccine-categorization',
  })
  @HttpCode(HttpStatus.OK)
  getListVaccineCategorization() {
    return this.rsaIntegrationService.getListVaccineCategorization();
  }

  @Get('consultant-result-type/get-by-survey-id')
  @Public()
  @ApiOperation({})
  @HttpCode(HttpStatus.OK)
  async getConsultantResultBySurveyId(@Query('surveyId') surveyId: string) {
    const res = await this.rsaIntegrationService.getKQTVByServeyId(surveyId);
    return res?.filter((item) => item?.code !== 11); //FV-17850
  }

  @Get('consultant-handling')
  @Public()
  @ApiOperation({})
  @HttpCode(HttpStatus.OK)
  getConsultantHandling() {
    return this.rsaIntegrationService.getConsultantHandling();
  }
  @Get('vaccine-osr/consultant-result-type/get-all-redis')
  @Public()
  @ApiOperation({
    summary: 'Lấy masterdata kết quả xử lý',
  })
  @HttpCode(HttpStatus.OK)
  getListKQTV() {
    return this.rsaIntegrationService.getListKQTV();
  }

  @Post('notification/contact-again')
  @Public()
  @ApiOperation({
    summary: 'Thông báo nhắc liên hệ lại',
  })
  @HttpCode(HttpStatus.OK)
  async contactAgainNotification(@Body() body: ContactAgainDto) {
    return this.surveysService.contactAgainNotification(body);
  }

  @Post('notification/unpaid-survey-customer/assign')
  @Public()
  @ApiOperation({
    summary: 'Thông báo nhắc liên hệ lại',
  })
  @HttpCode(HttpStatus.OK)
  async assignUnpaidSurveyNotification(@Body() body: ContactAgainDto) {
    return this.surveysService.assignUnpaidSurveyNotification(body);
  }

  @Post('unpaid-survey-customer/search-async')
  @Public()
  @ApiOperation({
    summary: 'Lấy danh sách khách hàng unpaid',
  })
  @HttpCode(HttpStatus.OK)
  async getUnpaidCustomer(@Body() getUnpaidCustomerDto: GetUnpaidCustomerDto) {
    return this.surveysService.getUnpaidCustomer(getUnpaidCustomerDto);
  }

  @Get('unpaid-survey-customer/detail')
  @Public()
  @ApiOperation({
    summary: 'Lấy chi tiết survey unpaid',
  })
  @HttpCode(HttpStatus.OK)
  async getUnpaidCustomerDetail(@Query() getUnpaidDetailDto: GetUnpaidDetailDto) {
    return this.surveysService.getUnpaidCustomerDetail(getUnpaidDetailDto);
  }

  @Put('unpaid-survey-customer/update')
  @Public()
  @ApiOperation({
    summary: 'Cập nhật thông tin survey unpaid',
  })
  @HttpCode(HttpStatus.OK)
  async updateUnpaidCustomerById(@Body() updateUnpaidSurveyDto: UpdateUnpaidSurveyDto) {
    return this.surveysService.updateUnpaidCustomerById(updateUnpaidSurveyDto);
  }

  @Get('unpaid-survey-customer/consultant-result-type')
  @Public()
  @ApiOperation({
    summary: 'get consultant result by survey id and source',
  })
  @HttpCode(HttpStatus.OK)
  async getKQTVBySurveyIdAndSource(@Query() getListConsultantResultDto: GetListConsultantResultConvertDto) {
    const res = (
      await this.rsaIntegrationService.getKQTVBySurveyIdAndSource({
        ...getListConsultantResultDto,
        source: '3',
      })
    ).data;

    return res?.filter((item) => item?.code !== 11); //FV-17850
  }
  @Post('search-survey-tcqg-by-query')
  @Public()
  @ApiOperation({
    summary: 'Danh sách khách hàng mới nguồn TCQG',
  })
  @ApiOkResponse({
    schema: generalSchema(SearchSurveyTCQGByQueryRes, 'object'),
  })
  @HttpCode(HttpStatus.OK)
  searchSurveyTCQG(@Body() payload: PayloadSearchSurveyTCQGByQueryDto) {
    return this.surveysService.searchSurveyTCQGByQuery(payload);
  }

  @Get('consultant-result-type/get-by-survey-id-for-tcqg')
  @Public()
  @ApiOperation({
    summary: 'Lấy danh sách kết quả tư vấn (Nguồn KH TCQG)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin kết quả tư vấn',
    schema: generalSchema(GetListConsultantResultForTCQGRes, 'array'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  getListKQTCFoTCQG(@Query('surveyId') surveyId: string) {
    return this.surveysService.getListKQTVForTCQG(surveyId);
  }

  @Get('customer-tcqg/es-tcqg-survey/get-by-id')
  @Public()
  @ApiOperation({
    summary: 'Lấy chi tiết phiếu tư vấn (Nguồn KH TCQG)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin chi tiết phiếu',
    schema: generalSchema(UpdatedSurveyCustomerTcqgRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  getDetailSurveyTcqgById(@Query('id') id: string) {
    return this.surveysService.getDetailSurveyTcqgById(id);
  }

  @Put('customer-tcqg/updated-survey-customer-tcqg')
  @Public()
  @ApiOperation({
    summary: 'Cập nhật phiếu tư vấn (Nguồn KH TCQG)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin cập nhật',
    schema: generalSchema(UpdatedSurveyCustomerTcqgRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  updateSurveyTcqg(@Body() body: UpdatedSurveyCustomerTcqgDto) {
    return this.surveysService.updatedSurveyTcqgById(body);
  }

  @Put('customer-tcqg/updated-assign-survey')
  @Public()
  @ApiOperation({
    summary: 'Phân công tay / nhận phân công (Nguồn KH TCQG)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin cập nhật',
    // schema: generalSchema(UpdatedSurveyCustomerTcqgRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  @ApiBody({
    type: [AssignManySurveysDto],
  })
  updateAssignSurvey(@Body() body: AssignManySurveysDto[]) {
    return this.surveysService.updateAssignSurvey(body);
  }

  @Post('send-notification')
  @Public()
  @ApiOperation({
    summary: 'Gửi notification cho Nhân viên tư vấn',
  })
  @HttpCode(HttpStatus.OK)
  sendNotficationAssignSurvey(@Body() body: SendNotiDto) {
    // return this.rsaIntegrationService.assignSurvey(updateSurveyDto);
    return this.surveysService.sendNotficationAssignSurvey(body);
  }
}
