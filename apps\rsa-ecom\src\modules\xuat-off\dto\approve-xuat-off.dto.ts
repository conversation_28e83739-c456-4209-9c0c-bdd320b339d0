import { ApiProperty } from '@nestjs/swagger';
import { ArrayNotEmpty, IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { XuatOffStatus } from '../constants';
import { Type } from 'class-transformer';

export class ApproveXuatOffDto {
  @ApiProperty({
    required: true,
    type: String,
    isArray: true,
  })
  @IsString({ each: true })
  detailIds: string[];

  @ApiProperty({
    required: true,
    type: String,
  })
  @IsString()
  xoId: string;
}
export class ApproveMultipleXuatOffDto {
  @ApiProperty({
    required: true,
    type: [ApproveXuatOffDto],
    description: 'Danh sách các phiếu xuất off cần duyệt',
  })
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ApproveXuatOffDto)
  items: ApproveXuatOffDto[];

  @ApiProperty({
    required: false,
    type: Number,
    enum: [XuatOffStatus.ApprovedByLeaderOrManager, XuatOffStatus.ApprovedByNVVH],
  })
  @IsEnum([XuatOffStatus.ApprovedByLeaderOrManager, XuatOffStatus.ApprovedByNVVH])
  approveStep?: XuatOffStatus;

  @ApiProperty({
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
