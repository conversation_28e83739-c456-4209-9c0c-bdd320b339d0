import { HttpException, HttpStatus, Inject, Injectable, LoggerService } from '@nestjs/common';
import {
  AssignOnlineOrderRequestDTO,
  AssignScheduleRequestDTO,
  CheckQuotaPreOrderDto,
  CreateNewOnlineOrderRequestDto,
  CreateScheduleRequestDTO,
  GetManyOrdersBody,
  GetScheduleRequestDTO,
  GetScheduleRequestPreOrderDTO,
  ProcessingOutOfQuotaRequestDTO,
  UpdateScheduleRequestDTO,
} from '../dtos/request';
import { getCurrentStartDateTimeZoneV2, getDateTimeZone } from '@shared/utilities/function-time-zone';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, In, Repository } from 'typeorm';
import { UploadFile } from '@shared/utilities/upload-file';
import { ElasticSearchService } from '@shared/modules/elastic-search/elastic-search.service';
import { ScheduleRequest } from '../entities/schedule-request.entity';
import { plainToInstance } from 'class-transformer';
import { STATUS_SCHEDULE_REQUEST } from '@shared/enum';
import {
  ErrorCode,
  IAuthUser,
  IError,
  RedisService,
  SystemException,
  checkStringIncludes,
  concurrentPromise,
  parseDateTimeZone,
  removeVietnameseTones,
  sleep,
} from '@shared';
import { AssignmentRemoteService, AssignTypesEnum } from 'ict-nest-assign-job';
import { InsideService } from 'vac-nest-inside';
import { EmployeeScheduling } from '../../employee-scheduling/entities/employee-scheduling.entity';
import { SHIFT } from '../../employee-scheduling/constants/employee-scheduling.constants';
import { Employee } from '../../employee/entities/employee.entity';
import { UserCheckInStatus } from '../../employee/enum';
import {
  CUSTOMER_TYPE,
  ORDER_CHANNEL_SCHEDULE,
  REDIS_UPDATE_SCHEDULE_REQUEST,
  SCHEDULE_REQUEST_TYPE,
} from '../constants/schedule-request.constants';
import { SubjectInjection } from '../entities/subject-injections.entity';
import { Item } from '../entities/items.entity';
import { ComboDetail } from '../entities/item-combo-details.entity';
import { EmployeeService } from '../../employee/employee.service';
import { getCurrentShift, isAutoAssign, isNightShift } from '../utils';
import { NotificationService } from 'modules/modules/modules/notification';
import { EmployeeJobTitleCodes } from '@shared/constants/employee-title-code.constant';
import { formatNotificationPayload, generateAddressId } from 'modules/modules/modules/notification/notification.utils';
import { NOTI_REGISTER_CHANEL_NAME } from 'modules/modules/modules/notification/notification.enum';
import {
  CreateScheduleAppointmentTemplate,
  CreateScheduleOnlineRequestTemplate,
  CreateSchedulePreOrderOutOfQuotaTemplate,
  CreateSchedulePreOrderTemplate,
  CreateScheduleRequestTemplate,
} from 'modules/modules/modules/notification/templates';
import { ScheduleRequestsUtilService } from './schedule-requests-util.service';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { OsrService } from 'vac-nest-osr';
import _ from 'lodash';
import { AffiliateCoreService } from 'vac-nest-affiliate';
import { LandingType } from '../enums/schedule-requests.enum';
import { ScheduleRequestType } from '../enums/schedule-request-type.enum';
import { OrderRuleEngineService } from 'vac-nest-order-rule-engine';
import { FamilyService } from 'vac-nest-family';
import { RegimenService } from 'vac-nest-regimen';
import { CreatePreOrderRequestDTO } from '../dtos/request/create-pre-order-request.dto';
import { SearchByLcvIdAndPhoneDto } from '../dtos/request/search-by-lcvid-or-phone.dto';
import moment from 'moment';
import { calculateTimeDifference } from 'vac-commons';
import { Sale } from '../../employee-scheduling/entities/sale.entity';
import { ASSIGN_ROLE } from '../../employee-scheduling/enum/group-sale.enum';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import Redis from 'ioredis';
import { JSONPath } from 'jsonpath-plus';
import { Product, VacSearchService } from 'vac-nest-search';
import { MasterDataResponseDto } from '../dtos/response/master-data.response.dto';
import { GetMasterDataCampaignType } from '../dtos/request/get-master-data-campaign-type.dto';
import { CmsApiService } from '@frt/nestjs-api';
import { ExaminationCoreService } from 'vac-nest-examination';
import { DetailAttachment, OMSService } from 'vac-nest-oms';
import { VacCoreAssignJobService } from 'vac-nest-assign-job';
import { JourneyService } from 'vac-nest-journey';
import { Shift } from 'vac-nest-assign-job/dist/assign-job.enum';

@Injectable()
export class ScheduleRequestsService {
  private uploadFile: UploadFile;

  constructor(
    @InjectRedis(process.env.REDIS_CONNECTION)
    private readonly redis: Redis,
    @InjectRepository(ScheduleRequest)
    private readonly scheduleRequestRepository: Repository<ScheduleRequest>,
    @InjectRepository(EmployeeScheduling)
    private readonly employeeSchedulingRepository: Repository<EmployeeScheduling>,
    @InjectRepository(SubjectInjection)
    private readonly subjectInjectionRepository: Repository<SubjectInjection>,
    @InjectRepository(Item)
    private readonly itemRepository: Repository<Item>,
    @InjectRepository(ComboDetail)
    private readonly comboDetailRepository: Repository<ComboDetail>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    private readonly elasticSearchService: ElasticSearchService,
    private readonly assignmentRemoteService: AssignmentRemoteService,
    private readonly insideService: InsideService,
    private readonly entityManager: EntityManager,
    private readonly employeeService: EmployeeService,
    private readonly notificationService: NotificationService,
    private readonly orderRuleEngineService: OrderRuleEngineService,
    private readonly familyService: FamilyService,
    private readonly redisService: RedisService,
    private readonly scheduleRequestsUtilService: ScheduleRequestsUtilService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    private readonly osrService: OsrService,
    private readonly affiliateService: AffiliateCoreService,
    protected readonly regimenService: RegimenService,
    @InjectRepository(Sale)
    private readonly saleRepository: Repository<Sale>,
    private readonly vacSearchService: VacSearchService,
    private readonly cmsApiService: CmsApiService,
    private readonly omsService: OMSService,
    private readonly vacCoreAssignJobService: VacCoreAssignJobService,
    private readonly journeyService: JourneyService,
    private readonly examinationCoreApiService: ExaminationCoreService,
  ) {
    this.uploadFile = new UploadFile();
  }

  // Xóa nhân viên khỏi hàng đợi thường
  async unAssignUserFromQueue(insideCode: string, nightShift?: boolean) {
    await this.assignmentRemoteService.popRedis({
      sub: {
        insideId: insideCode,
        shifts: nightShift === undefined ? [SHIFT.DAY, SHIFT.NIGHT] : [nightShift ? SHIFT.NIGHT : SHIFT.DAY],
      },
      type: AssignTypesEnum.user,
    });
  }

  // Xóa nhân viên khỏi hàng đợi pre-order
  async unAssignUserFromQueuePreOrder(insideCode: string, nightShift?: boolean) {
    // await this.assignmentRemoteService.popRedisPreOrder({
    //   sub: {
    //     insideId: insideCode,
    //     shifts: nightShift === undefined ? [SHIFT.DAY, SHIFT.NIGHT] : [nightShift ? SHIFT.NIGHT : SHIFT.DAY],
    //   },
    //   type: AssignTypesEnum.user,
    // });

    //FV-17755: Gom Hàng đợi pre-order vào hàng đợi thường
    await this.unAssignUserFromQueue(insideCode, nightShift);
  }

  // Xóa đơn hàng khỏi hàng đợi thường
  async unAssignOrderFromQueue(orderCode: string) {
    await this.assignmentRemoteService.popRedis({
      sub: {
        orderCode: orderCode,
        shifts: [SHIFT.DAY, SHIFT.NIGHT],
      },
      type: AssignTypesEnum.ecomOrder,
    });
  }

  // Xóa đơn hàng khỏi hàng đợi pre-order
  async unAssignOrderFromQueuePreOrder(orderCode: string) {
    // await this.assignmentRemoteService.popRedisPreOrder({
    //   sub: {
    //     orderCode: orderCode,
    //     shifts: [SHIFT.DAY, SHIFT.NIGHT],
    //   },
    //   type: AssignTypesEnum.ecomOrder,
    // });

    //FV-17755: Gom Hàng đợi pre-order vào hàng đợi thường
    await this.unAssignOrderFromQueue(orderCode);
  }

  // Thêm đơn hàng vào hàng đợi thường
  async assignOrderFromQueue({ orderCode, createdDate }, highPriority = true) {
    await this.assignmentRemoteService.pushRedis({
      sub: {
        orderCode: orderCode || '',
        createdAt: createdDate || getDateTimeZone(),
      },
      type: AssignTypesEnum.ecomOrder,
      isNightShift: isNightShift(createdDate),
      highPriority,
    });
  }

  // Thêm đơn hàng khỏi hàng đợi pre-order
  async assignOrderFromQueuePreOrder({ orderCode, createdDate }, highPriority = true) {
    // await this.assignmentRemoteService.pushRedisPreOrder({
    //   sub: {
    //     orderCode: orderCode || '',
    //     createdAt: createdDate || getDateTimeZone(),
    //   },
    //   type: AssignTypesEnum.ecomOrder,
    //   isNightShift: isNightShift(createdDate),
    //   highPriority,
    // });

    //FV-17755: Gom Hàng đợi pre-order vào hàng đợi thường
    await this.assignOrderFromQueue({ orderCode, createdDate }, highPriority);
  }

  // Thêm nhân viên vào hàng đợi thường
  async assignUserToQueue(insideCode: string, inputShifts?: number[], highPriority = true) {
    if (!(await this.isEmployeeOnline(insideCode))) {
      return;
    }
    const employeeSchedules = await this.employeeSchedulingRepository.find({
      where: {
        insideCode: insideCode,
        startDate: getCurrentStartDateTimeZoneV2('YYYY-MM-DD'),
      },
    });

    if (inputShifts) {
      inputShifts = inputShifts.filter((shift) => employeeSchedules.map((item) => item.shift).includes(shift));
      if (!inputShifts.length) {
        return;
      }
    }

    let nightShift: undefined | boolean = undefined;
    if (inputShifts) {
      switch (inputShifts.length) {
        case 1:
          nightShift = inputShifts[0] === SHIFT.DAY ? false : true;
          break;
        case 2:
          nightShift = undefined;
          break;
        default:
          return;
      }
    }

    await this.unAssignUserFromQueue(insideCode, nightShift); // if undefined => unassign all shifts

    if (employeeSchedules.length) {
      await this.assignmentRemoteService.pushRedis({
        sub: {
          insideId: insideCode,
          shifts: inputShifts || employeeSchedules.map((item) => item.shift),
        },
        type: AssignTypesEnum.user,
        highPriority,
      });
    }
  }

  //  Thêm nhân viên vào hàng đợi pre-order
  async assignUserToQueuePreOrder(insideCode: string, inputShifts?: number[], highPriority = true) {
    // if (!(await this.isEmployeePreOrderOnline(insideCode))) {
    //   return;
    // }
    // const employeeSchedules = await this.employeeSchedulingRepository.find({
    //   where: {
    //     insideCode: insideCode,
    //     startDate: getCurrentStartDateTimeZoneV2('YYYY-MM-DD'),
    //   },
    // });

    // if (inputShifts) {
    //   inputShifts = inputShifts.filter((shift) => employeeSchedules.map((item) => item.shift).includes(shift));
    //   if (!inputShifts.length) {
    //     return;
    //   }
    // }

    // let nightShift: undefined | boolean = undefined;
    // if (inputShifts) {
    //   switch (inputShifts.length) {
    //     case 1:
    //       nightShift = inputShifts[0] === SHIFT.DAY ? false : true;
    //       break;
    //     case 2:
    //       nightShift = undefined;
    //       break;
    //     default:
    //       return;
    //   }
    // }

    // await this.unAssignUserFromQueuePreOrder(insideCode, nightShift); // if undefined => unassign all shifts

    // if (employeeSchedules.length) {
    //   await this.assignmentRemoteService.pushRedisPreOrder({
    //     sub: {
    //       insideId: insideCode,
    //       shifts: inputShifts || employeeSchedules.map((item) => item.shift),
    //     },
    //     type: AssignTypesEnum.user,
    //     highPriority,
    //   });
    // }

    //FV-17755: Gom Hàng đợi pre-order vào hàng đợi thường
    await this.assignUserToQueue(insideCode, inputShifts, highPriority);
  }

  async verifyAndAssignUserToQueue(status: number, insideCode: string, shifts?: number[], highPriority = true) {
    if (status !== STATUS_SCHEDULE_REQUEST.NEW) {
      await this.assignUserToQueue(insideCode, shifts, highPriority);
    }
  }

  async verifyAndAssignUserToQueuePreOrder(status: number, insideCode: string, shifts?: number[], highPriority = true) {
    if (status !== STATUS_SCHEDULE_REQUEST.NEW) {
      await this.assignUserToQueuePreOrder(insideCode, shifts, highPriority);
    }
  }

  verifyStatusForUpdate(currentStatus: number, expectStatus: number): boolean {
    if (currentStatus === expectStatus) {
      return true;
    }
    switch (expectStatus) {
      case STATUS_SCHEDULE_REQUEST.PROCESSED:
        return currentStatus === STATUS_SCHEDULE_REQUEST.NOT_CONTACT || currentStatus === STATUS_SCHEDULE_REQUEST.NEW;
      case STATUS_SCHEDULE_REQUEST.NOT_CONTACT:
        return currentStatus === STATUS_SCHEDULE_REQUEST.NEW || currentStatus === STATUS_SCHEDULE_REQUEST.PROCESSED;
      case STATUS_SCHEDULE_REQUEST.CANCELED:
        return (
          currentStatus === STATUS_SCHEDULE_REQUEST.NOT_CONTACT ||
          currentStatus === STATUS_SCHEDULE_REQUEST.PROCESSED ||
          currentStatus === STATUS_SCHEDULE_REQUEST.NEW
        );
      case STATUS_SCHEDULE_REQUEST.SUCCESS:
        return (
          currentStatus === STATUS_SCHEDULE_REQUEST.NOT_CONTACT ||
          currentStatus === STATUS_SCHEDULE_REQUEST.PROCESSED ||
          currentStatus === STATUS_SCHEDULE_REQUEST.NEW
        );
      default:
        return false;
    }
  }

  async filter(query: GetScheduleRequestDTO, type: ScheduleRequestType): Promise<any> {
    query.status = typeof query?.status === 'string' ? [query?.status] : query?.status;
    query.insideId = typeof query?.insideId === 'string' ? [query?.insideId] : query?.insideId;
    query.orderChannel = typeof query?.orderChannel === 'string' ? [query?.orderChannel] : query?.orderChannel;
    query.fromDate = query?.fromDate ? query?.fromDate.split('T')[0] + 'T00:00:00' : null;
    query.toDate = query?.toDate ? query?.toDate.split('T')[0] + 'T23:59:59' : null;
    query.landingType = typeof query?.landingType === 'string' ? [query?.landingType] : query?.landingType;

    const results = await this.elasticSearchService.searchScheduleRequest(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      query,
      type,
    );

    return {
      totalCount: results.totalCount,
      items: results.items.map((item) => plainToInstance(ScheduleRequest, item)),
    };
  }

  async filterPreOrder(query: GetScheduleRequestPreOrderDTO): Promise<any> {
    if (query.type) {
      query.type = Array.isArray(query.type) ? query.type : [query.type];
    }
    query.status = typeof query?.status === 'string' ? [query?.status] : query?.status;
    query.insideId = typeof query?.insideId === 'string' ? [query?.insideId] : query?.insideId;
    query.orderChannel = typeof query?.orderChannel === 'string' ? [query?.orderChannel] : query?.orderChannel;
    query.fromDate = query?.fromDate ? query?.fromDate.split('T')[0] + 'T00:00:00' : null;
    query.toDate = query?.toDate ? query?.toDate.split('T')[0] + 'T23:59:59' : null;
    query.landingType = typeof query?.landingType === 'string' ? [query?.landingType] : query?.landingType;

    const results = await this.elasticSearchService.searchScheduleRequestPreOrder(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      query,
    );

    return {
      totalCount: results.totalCount,
      items: results.items.map((item) => plainToInstance(ScheduleRequest, item)),
    };
  }

  async getById(uuid: string): Promise<any> {
    const scheduleRequest = (await this.elasticSearchService.getScheduleRequestByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
    )) as any;
    if (!scheduleRequest) {
      return null;
    }
    const affiliateData = await this.getAffiliateResolverProfile((scheduleRequest as ScheduleRequest).idLink);

    if (scheduleRequest.isPreOrder) {
      const subjectInjections = (scheduleRequest as any)?.subjectInjections;
      if (subjectInjections?.length) {
        // Trả về address detail để lấy đầu địa chỉ: số nhà - đường
        scheduleRequest.subjectInjections = subjectInjections?.map((item) => ({
          ...item,
          address: item?.address ?? this._getDetailAddress(item?.addressOfSubjectsInjection),
        }));
      }
      if (subjectInjections?.[0]?.items?.[0]?.itemCode) {
        const resRegimen = await this.regimenService.searchRegimenByAgeAndGender({
          keyword: subjectInjections?.[0]?.items?.[0]?.itemCode,
          birthday: scheduleRequest?.dateOfBirth || scheduleRequest?.dateOfBirthOfSubjectsInjection,
          gender: scheduleRequest?.gender,
          page: 1,
          size: 10,
        } as any);
        scheduleRequest.subjectInjections[0].items[0].regimenInfo = resRegimen?.data?.[0];
      }

      if (
        subjectInjections?.[0] &&
        // !subjectInjections?.[0]?.phoneOfSubjectsInjection &&
        !subjectInjections?.[0]?.lcvId
      ) {
        try {
          const personInfo = await this.familyService.searchPrimaryPersonExactly({
            pageNumber: 1,
            pageSize: 100,
            name: subjectInjections?.[0]?.nameOfSubjectsInjection,
            temporaryDistrictCode: subjectInjections?.[0]?.districtCode,
            temporaryProvinceCode: subjectInjections?.[0]?.provinceCode,
            temporaryWardCode: subjectInjections?.[0]?.wardCode,
            frequentlyDistrictCode: subjectInjections?.[0]?.districtCode,
            frequentlyProvinceCode: subjectInjections?.[0]?.provinceCode,
            frequentlyWardCode: subjectInjections?.[0]?.wardCode,
            dateOfBirthFrom: moment
              .tz(subjectInjections?.[0]?.dateOfBirthOfSubjectsInjection, 'Asia/Ho_Chi_Minh')
              .startOf('days')
              .toISOString(),
            dateOfBirthTo: moment
              .tz(subjectInjections?.[0]?.dateOfBirthOfSubjectsInjection, 'Asia/Ho_Chi_Minh')
              .endOf('days')
              .toISOString(),
          });

          let personSearchs = [];
          for (const item of personInfo?.items) {
            personSearchs.push({
              name: item?.name,
              lcvId: item?.lcvId,
              dateOfBirth: item.dateOfBirth,
              temporaryDistrictCode: item?.temporaryDistrictCode,
              temporaryProvinceCode: item?.temporaryProvinceCode,
              temporaryWardCode: item?.temporaryWardCode,
              frequentlyDistrictCode: item?.frequentlyDistrictCode,
              frequentlyProvinceCode: item?.frequentlyProvinceCode,
              frequentlyWardCode: item?.frequentlyWardCode,
            });
            // const family = item?.familyProfileDetails?.map((e) => {
            //   return {
            //     name: e?.name,
            //     lcvId: e?.lcvId,
            //     dateOfBirth: e?.dateOfBirth,
            //   };
            // });
            // personSearchs.push(...(family || []));
          }

          // search
          // # priority 1 - exactly
          // # priority 2 - same length, no check vn tone
          // # priority 3 - other
          const subjectsInjection = subjectInjections?.[0];
          const nameOfSubjectsInjection = subjectsInjection?.nameOfSubjectsInjection?.trim();
          for (const item of personSearchs) {
            const isMatchedWithKeyword = checkStringIncludes(nameOfSubjectsInjection, [item?.name]);
            if (!isMatchedWithKeyword) continue;
            const isMatchDateOfBirth = moment
              .tz(String(item.dateOfBirth), 'Asia/Ho_Chi_Minh')
              .startOf('days')
              .isSame(moment.tz(subjectsInjection?.dateOfBirthOfSubjectsInjection, 'Asia/Ho_Chi_Minh').startOf('days'));
            const isMatchAddress =
              (item.frequentlyProvinceCode === subjectsInjection?.provinceCode &&
                item.frequentlyDistrictCode === subjectsInjection?.districtCode &&
                item.frequentlyWardCode === subjectsInjection?.wardCode) ||
              (item.temporaryProvinceCode === subjectsInjection?.provinceCode &&
                item.temporaryDistrictCode === subjectsInjection?.districtCode &&
                item.temporaryWardCode === subjectsInjection?.wardCode);

            if (isMatchDateOfBirth && isMatchAddress) {
              if (item?.name?.toLocaleLowerCase()?.trim() === nameOfSubjectsInjection?.toLocaleLowerCase()) {
                item.priority = 1;
              } else if (
                removeVietnameseTones(item?.name)?.toLocaleLowerCase()?.trim() ===
                removeVietnameseTones(nameOfSubjectsInjection?.toLocaleLowerCase())
              ) {
                item.priority = 2;
              } else {
                item.priority = 3;
              }
            }
          }
          personSearchs = _.uniqBy(
            personSearchs?.filter((e) => e.priority).sort((a, b) => a.priority - b.priority),
            'lcvId',
          );
          scheduleRequest.subjectInjections[0].personSearchs = personSearchs;
          scheduleRequest.subjectInjections[0].lcvId = personSearchs?.[0]?.lcvId;

          if (personSearchs?.length <= 0 && subjectInjections?.[0]?.phoneOfSubjectsInjection) {
            const resGetPerson = await this.familyService.getPersonByPhone(
              subjectInjections?.[0]?.phoneOfSubjectsInjection,
            );
            scheduleRequest.subjectInjections[0].lcvId = resGetPerson?.[0]?.lcvId;
          }
        } catch (error) {}
      }
      if (subjectInjections?.[0]?.dateOfBirthOfSubjectsInjection) {
        scheduleRequest.subjectInjections[0].customerAge = calculateTimeDifference(
          new Date(moment.tz(subjectInjections?.[0]?.dateOfBirthOfSubjectsInjection, 'Asia/Ho_Chi_Minh').toISOString()),
          null,
          null,
          null,
          null,
        );
      }
    } else {
      const subjectInjections = scheduleRequest?.subjectInjections || [];
      if (subjectInjections.length) {
        // Chuẩn bị dto và lưu trữ vị trí trong mảng
        const dtoRequests = [];
        const itemPositions = [];

        subjectInjections.forEach((subjectInjection, subjectIndex) => {
          const items = subjectInjection?.items || [];
          items.forEach((item, itemIndex) => {
            const isValidInjection =
              item?.itemCode &&
              subjectInjection?.dateOfBirthOfSubjectsInjection &&
              (subjectInjection?.genderOfSubjectsInjection === 0 || subjectInjection?.genderOfSubjectsInjection);

            // Lưu vị trí của item hiện tại
            itemPositions.push({ subjectIndex, itemIndex });

            // Chuẩn bị dto cho request
            dtoRequests.push(
              isValidInjection
                ? this.regimenService.searchRegimenByAgeAndGender({
                    keyword: item?.itemCode,
                    birthday: subjectInjection?.dateOfBirthOfSubjectsInjection,
                    gender: subjectInjection?.genderOfSubjectsInjection,
                    page: 1,
                    size: 10,
                  })
                : Promise.resolve(null),
            );
          });
        });

        // Thực hiện tất cả requests một lần
        const resRegimenAllSettled = await Promise.allSettled(dtoRequests);

        // Gán kết quả trực tiếp vào vị trí tương ứng
        resRegimenAllSettled.forEach((result, index) => {
          const { subjectIndex, itemIndex } = itemPositions[index];
          scheduleRequest.subjectInjections[subjectIndex].items[itemIndex].regimenInfo =
            result?.status === 'fulfilled' && result?.value ? result.value?.data?.[0] : undefined;
        });
      }
    }

    // Case get journey id
    if (scheduleRequest?.type === SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST) {
      const journey = await this.journeyService.getJourneyByOrderCode({ orderCode: scheduleRequest?.orderCode });
      if (journey) {
        scheduleRequest.journeyId = journey?.id;
      }
      try {
        const { items: examinations } = await this.examinationCoreApiService.getTicketByOrderCode({
          orderCodes: scheduleRequest?.orderCode,
        });

        scheduleRequest.ticketCode = examinations[0]?.ticketCode || null;
      } catch (_error) {
        scheduleRequest.ticketCode = null;
      }
    }

    const itemCodes = _.compact(
      _.uniq(JSONPath({ path: '$.subjectInjections[*].items[*].itemCode', json: scheduleRequest })),
    );
    if (itemCodes?.length) {
      const productDetails: Product[] = await this.vacSearchService.getProductBySkus({ skus: itemCodes });
      const multiDose = await this.osrService.getVaccineShotByListSku({
        listSku: itemCodes,
      });
      scheduleRequest?.subjectInjections?.forEach((sub) => {
        sub?.items?.forEach((item) => {
          const productDetail = productDetails.find((e) => e?.sku === item?.itemCode);
          if (item?.unitCode && productDetail) {
            const measureDetail = productDetail?.measures?.find(
              (e) => e?.measureUnitCode?.toString() === item?.unitCode?.toString(),
            );
            if (measureDetail && !measureDetail?.isSellDefault) {
              item['multiDose'] = (multiDose || []).filter((e) => e?.sku === item?.itemCode);
            }
          }
        });
      });
    }
    return { ...scheduleRequest, affiliateData };
  }

  private _getDetailAddress(input: string) {
    if (!input) return;
    // Split the string into an array by commas
    let items = input?.split(',');

    if (items?.length > 4) {
      // If there are more than 4 items, remove the last 3 items
      items = items?.slice(0, -3);
      // Join the remaining items back into a string without commas
      return items?.join(',');
    } else if (items?.length === 4) {
      // If there are exactly 4 items, take the first item
      return items?.[0];
    }
    return '';
  }

  async getByOrderCode(orderCode: string): Promise<any> {
    const results = await this.elasticSearchService.searchScheduleRequest(process.env.ELA_INDEX_SCHEDULE_REQUEST, {
      orderCode,
    });
    if (!results.items.length) {
      return null;
    }
    const affiliateData = await this.getAffiliateResolverProfile((results.items.at(0) as ScheduleRequest).idLink);

    return {
      ...(results.items.at(0) as any),
      affiliateData,
    };
  }

  async getManyOrders(body: GetManyOrdersBody): Promise<any> {
    const { orderCodes } = body;
    const results = await this.elasticSearchService.getManyOrders(process.env.ELA_INDEX_SCHEDULE_REQUEST, orderCodes);
    return {
      total: results.total,
      items: results.items,
    };
  }

  private async getAffiliateResolverProfile(idLink?: string | null) {
    let affiliate_info = null;
    try {
      if (idLink) {
        affiliate_info = await this.affiliateService.getResolverProfile(idLink);
      }
    } catch (e) {
      affiliate_info = null;
    }

    return affiliate_info;
  }

  async create(createRequestOrderDTO: CreateScheduleRequestDTO): Promise<any> {
    const { gender, note, customerNote, sourceId, needAdvice, user, idLink, landingType } = createRequestOrderDTO;

    const dataInsert = {
      ...createRequestOrderDTO,
      gender: gender ? +gender : 0,
      note: note || '',
      customerNote: customerNote || '',
      sourceId: sourceId ? +sourceId : 2,
      status: STATUS_SCHEDULE_REQUEST.NEW,
      needAdvice: needAdvice ? needAdvice : false,
      updatedBy: user.employee_code ? user.employee_code : '',
      createdBy: user.employee_code ? user.employee_code : '',
      createdDate: createRequestOrderDTO.createdDate || getDateTimeZone(),
      updatedDate: createRequestOrderDTO.createdDate || getDateTimeZone(),
      user: undefined,
      landingType: idLink ? LandingType.AFFILIATE : landingType,
      isPreOrder: false, // hard not pre order
      isOutOfQuota: false, // hard not pre order and out of quota
    };
    try {
      const results = await this.scheduleRequestRepository.save(plainToInstance(ScheduleRequest, dataInsert));
      if (results.type === SCHEDULE_REQUEST_TYPE.APPOINTMENT_SCHEDULING && results.id) {
        await this.entityManager.transaction(async (transactionalEntityManager: EntityManager) => {
          const subjectInjections = (dataInsert?.subjectInjections || []).map((subjectInjection) => {
            return {
              ...subjectInjection,
              ...{
                scheduleRequestId: results.id,
              },
            };
          });

          const savedSubjectInjections = await transactionalEntityManager.save(
            subjectInjections.map((subjectInjection) =>
              transactionalEntityManager.create(SubjectInjection, plainToInstance(SubjectInjection, subjectInjection)),
            ),
          );

          // Extract generated ids from savedSubjectInjections
          const subjectInjectionIds = savedSubjectInjections.map((subjectInjection) => subjectInjection.id);

          // Flatten items array and save items with mapped subjectInjectionId
          const itemsToSave = dataInsert?.subjectInjections.flatMap((subjectInjection, index) => {
            const subjectInjectionId = subjectInjectionIds[index];
            return subjectInjection.items.map((item) => ({ ...item, subjectInjectionId }));
          });

          const savedItems = await transactionalEntityManager.save(
            itemsToSave.map((itemToSave) => transactionalEntityManager.create(Item, plainToInstance(Item, itemToSave))),
          );

          // Extract generated ids from savedItems
          const itemIds = savedItems.map((item) => item.id);

          // Flatten comboDetails array and save comboDetails with mapped itemId
          const comboDetailsToSave = itemsToSave.flatMap((item, index) => {
            const itemId = itemIds[index];
            return item.comboDetails.map((comboDetail) => ({ ...comboDetail, itemId }));
          });

          await transactionalEntityManager.save(
            comboDetailsToSave.map((comboDetailToSave) =>
              transactionalEntityManager.create(ComboDetail, plainToInstance(ComboDetail, comboDetailToSave)),
            ),
          );
        });
      }
      await this.elasticSearchService.indexDocument(process.env.ELA_INDEX_SCHEDULE_REQUEST, results, results.uuid);

      createRequestOrderDTO?.type !== 5 && //Loại yêu cầu đơn hàng có queue riêng
        (await this.assignOrderFromQueue(
          {
            orderCode: results.uuid,
            createdDate: results.createdDate,
          },
          false,
        ));

      try {
        await this.sendNotification(results);
      } catch (errors) {
        let logRes = '{}';
        try {
          logRes = JSON.stringify(results);
        } catch (errorsChild) {
          logRes = '';
        }
        await this.logger.log(
          {
            message: `notification - error: ${results.id} - ${results.uuid}`,
            fields: {
              info: `notification - error: ${results.id} - ${results.uuid}`,
              method: `GET`,
              url: `notification - error: ${results.id} - ${results.uuid}`,
              bodyReq: '{}',
              queryReq: '{}',
              paramsReq: '{}',
              headers: '{}',
              dataRes: `${logRes}`,
            },
          },
          false,
        );
      }

      try {
        if (
          createRequestOrderDTO.type === SCHEDULE_REQUEST_TYPE.SCHEDULE_REQUEST &&
          createRequestOrderDTO.customerType === CUSTOMER_TYPE.FROM_FPT
        ) {
          if (createRequestOrderDTO.email) {
            await this.osrService.verifyEmail({
              emails: [createRequestOrderDTO.email],
            });
          }
          await this.osrService.saveFpterInfo([
            {
              registrantName: createRequestOrderDTO.customerName,
              registrantPhoneNumber: createRequestOrderDTO.phoneNumber,
              email: createRequestOrderDTO.email,
              vaccineeName: createRequestOrderDTO.nameOfSubjectsInjection,
              vaccineePhoneNumber: createRequestOrderDTO.phoneOfSubjectsInjection,
              dateOfBirth: createRequestOrderDTO.dateOfBirthOfSubjectsInjection,
              // whomToVaccinate: 0, // 0 - tiem cho toi, 1 - tiem cho ng than // ko co thong tin
              shopCode: createRequestOrderDTO.shopCode,
              shopName: createRequestOrderDTO.shopName,
              groupId: createRequestOrderDTO.disease?.[0]?.diseaseGroupId ?? '',
              groupName: createRequestOrderDTO.disease?.[0]?.diseaseGroupName ?? '',
              appointmentDate: createRequestOrderDTO.appointmentDate,
              timeFrame: createRequestOrderDTO.vaccinationTime,
              registrantDateOfBirth: createRequestOrderDTO.dateOfBirth,
            },
          ]);
        }
      } catch (error) {}

      return results;
    } catch (err) {
      throw new HttpException(err, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async checkQuotaLimitaion(payload: CheckQuotaPreOrderDto) {
    const { sku, isSubjectInjection, subjectInjections } = payload;
    if (!isSubjectInjection) {
      // kiểm tra quota khi tiêm cho bản thân
      return await this._checkRulePreOrderByCustomerInfo(payload);
    }

    // kiểm tra quota khi tiêm cho người thân
    return await this._checkRulePreOrderByCustomerInfo({
      name: subjectInjections?.[0]?.nameOfSubjectsInjection,
      sku,
      dateOfBirth: subjectInjections?.[0]?.dateOfBirthOfSubjectsInjection,
      phoneNumber: subjectInjections?.[0]?.phoneOfSubjectsInjection,
      wardCode: subjectInjections?.[0]?.wardCode,
      districtCode: subjectInjections?.[0]?.districtCode,
      provinceCode: subjectInjections?.[0]?.provinceCode,
    });
  }

  /**
   * Lấy lcvId khách hàng theo tên, ngày sinh, địa chỉ
   * Kiểm tra xem khách hàng đã đến giới đơn đặt chưa => trả lỗi
   * @param checkPerson
   */
  private async _checkRulePreOrderByCustomerInfo(checkPerson: CheckQuotaPreOrderDto) {
    //4 trường wardCode, provinceCode, districtCode, isSubjectInjection chỉ để phục vụ get lcvid check quota limit không lưu DB
    const { name, dateOfBirth, wardCode, provinceCode, districtCode, sku, phoneNumber } = checkPerson;

    //Check quota person theo sdt
    const result = await this.familyService.getPersonByPhone(phoneNumber);
    let person = result?.[0];
    if (result?.length > 1) {
      person = result?.find((x) => x?.dateOfBirth === dateOfBirth && x?.name === name);
    }
    if (person) {
      return await this.orderRuleEngineService.checkRulePreOrder({
        items: [{ sku, quantity: 1 }],
        lcvIds: [person?.lcvId],
      });
    }

    // Nếu không tìm được ngươi theo sdt sẽ check tiếp theo tên, ngày sinh, địa chỉ
    const personPayload = {
      name: name,
      dateOfBirthFrom: String(dateOfBirth).substring(0, 10),
      dateOfBirthTo: String(dateOfBirth).substring(0, 10),
      frequentlyWardCode: wardCode,
      frequentlyDistrictCode: districtCode,
      frequentlyProvinceCode: provinceCode,
      temporaryWardCode: wardCode,
      temporaryDistrictCode: districtCode,
      temporaryProvinceCode: provinceCode,
    };

    //Tìm kiếm thông tin người tiêm để tìm lcvid
    const persons = await this.familyService.searchPrimaryPersonExactly({
      pageNumber: 1,
      pageSize: 10,
      ...personPayload,
    });

    if (persons?.items?.length && persons?.items?.[0]?.lcvId) {
      // Truyền lcvId để tìm limit chặn pre-order vượt muốc cho phép
      await this.orderRuleEngineService.checkRulePreOrder({
        items: [{ sku, quantity: 1 }],
        lcvIds: [persons?.items?.[0]?.lcvId],
      });
    }
  }

  /**
   * @param createRequestOrderDTO
   * @returns
   */
  async createPreOrder(createRequestOrderDTO: CreatePreOrderRequestDTO): Promise<any> {
    const {
      customerName,
      gender,
      note,
      customerNote,
      sourceId,
      needAdvice,
      user,
      idLink,
      landingType,
      personId,
      wardCode,
      provinceCode,
      districtCode,
      dateOfBirth,
      isSubjectInjection,
      phoneNumber,
      isOutOfQuota,
    } = createRequestOrderDTO;

    const dataInsert = {
      ...createRequestOrderDTO,
      gender: gender ? +gender : null,
      note: note || '',
      customerNote: customerNote || '',
      sourceId: sourceId ? +sourceId : 2,
      status: STATUS_SCHEDULE_REQUEST.NEW,
      needAdvice: needAdvice ? needAdvice : false,
      updatedBy: user.employee_code ? user.employee_code : '',
      createdBy: user.employee_code ? user.employee_code : '',
      createdDate: createRequestOrderDTO.createdDate || getDateTimeZone(),
      updatedDate: createRequestOrderDTO.createdDate || getDateTimeZone(),
      user: undefined,
      landingType: idLink ? LandingType.AFFILIATE : landingType,
      isPreOrder: true, // hard pre order
      type: isOutOfQuota ? SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA : SCHEDULE_REQUEST_TYPE.PRE_ORDER,
      unsignedName: removeVietnameseTones(customerName)?.toLocaleLowerCase()?.trim(),
    };
    try {
      if (!isOutOfQuota) {
        const sku = dataInsert?.subjectInjections?.[0]?.items?.[0]?.itemCode;
        //Check rule pre-order hết quota thì báo lỗi
        if (sku) {
          if (!isSubjectInjection) {
            //Tiêm cho bản thân
            //Check rule theo personId cho chính chủ nếu tiêm cho chính chủ có personId
            if (personId) {
              const person = await this.familyService.getPersonById(personId);
              await this.orderRuleEngineService.checkRulePreOrder({
                items: [{ sku, quantity: 1 }],
                lcvIds: [person?.lcvId],
              });
            } else {
              //Check rule bằng tìm kiếm lcvId theo thông tin sdt, tên, ngày sinh, địa chỉ trên hệ thống
              await this._checkRulePreOrderByCustomerInfo({
                name: customerName,
                dateOfBirth,
                wardCode,
                provinceCode,
                districtCode,
                phoneNumber,
                sku,
              });
            }
          } else {
            //Tiêm cho người thân
            //Check rule theo lcvId cho người thân nếu tiêm cho ng thân có lcvId
            if (dataInsert?.subjectInjections?.[0]?.lcvId) {
              await this.orderRuleEngineService.checkRulePreOrder({
                items: [{ sku, quantity: 1 }],
                lcvIds: [dataInsert?.subjectInjections?.[0]?.lcvId],
              });
            } else {
              //Check rule bằng tìm kiếm lcvId theo thông tin sdt, tên, ngày sinh, địa chỉ trên hệ thống
              await this._checkRulePreOrderByCustomerInfo({
                name: dataInsert?.subjectInjections?.[0]?.nameOfSubjectsInjection,
                sku,
                dateOfBirth: dataInsert?.subjectInjections?.[0]?.dateOfBirthOfSubjectsInjection,
                phoneNumber: dataInsert?.subjectInjections?.[0]?.phoneOfSubjectsInjection,
                wardCode: dataInsert?.subjectInjections?.[0]?.wardCode,
                districtCode: dataInsert?.subjectInjections?.[0]?.districtCode,
                provinceCode: dataInsert?.subjectInjections?.[0]?.provinceCode,
              });
            }
          }
        }
        //End check
      }
      //End check

      dataInsert.subjectInjections = dataInsert?.subjectInjections?.map((e) => {
        return {
          ...e,
          unsignedName: removeVietnameseTones(e?.nameOfSubjectsInjection)?.toLocaleLowerCase()?.trim(),
        };
      });
      const results = await this.scheduleRequestRepository.save(plainToInstance(ScheduleRequest, dataInsert));
      if (results.id) {
        await this.entityManager.transaction(async (transactionalEntityManager: EntityManager) => {
          const subjectInjections = (dataInsert?.subjectInjections || []).map((subjectInjection) => {
            return {
              ...subjectInjection,
              ...{
                scheduleRequestId: results.id,
              },
            };
          });

          const _savedSubjectInjections = await transactionalEntityManager.save(
            subjectInjections.map((subjectInjection) =>
              transactionalEntityManager.create(SubjectInjection, plainToInstance(SubjectInjection, subjectInjection)),
            ),
          );

          // Extract generated ids from savedSubjectInjections
          // const subjectInjectionIds = savedSubjectInjections.map((subjectInjection) => subjectInjection.id);

          // Flatten items array and save items with mapped subjectInjectionId
          // const itemsToSave = dataInsert?.subjectInjections.flatMap((subjectInjection, index) => {
          //   const subjectInjectionId = subjectInjectionIds[index];
          //   return subjectInjection.items.map((item) => ({ ...item, subjectInjectionId }));
          // });

          // const savedItems = await transactionalEntityManager.save(
          //   itemsToSave.map((itemToSave) => transactionalEntityManager.create(Item, plainToInstance(Item, itemToSave))),
          // );

          // Extract generated ids from savedItems
          // const itemIds = savedItems.map((item) => item.id);

          // Flatten comboDetails array and save comboDetails with mapped itemId
          // const comboDetailsToSave = itemsToSave.flatMap((item, index) => {
          //   const itemId = itemIds[index];
          //   return item.comboDetails.map((comboDetail) => ({ ...comboDetail, itemId }));
          // });

          // await transactionalEntityManager.save(
          //   comboDetailsToSave.map((comboDetailToSave) =>
          //     transactionalEntityManager.create(ComboDetail, plainToInstance(ComboDetail, comboDetailToSave)),
          //   ),
          // );
        });
      }
      await this.elasticSearchService.indexDocument(process.env.ELA_INDEX_SCHEDULE_REQUEST, results, results.uuid);

      if (!isOutOfQuota) {
        await this.assignOrderFromQueuePreOrder(
          {
            orderCode: results.uuid,
            createdDate: results.createdDate,
          },
          false,
        );

        try {
          await this.sendNotification(results);
        } catch (errors) {
          let logRes = '{}';
          try {
            logRes = JSON.stringify(results);
          } catch (errorsChild) {
            logRes = '';
          }
          await this.logger.log(
            {
              message: `notification - error: ${results.id} - ${results.uuid}`,
              fields: {
                info: `notification - error: ${results.id} - ${results.uuid}`,
                method: `GET`,
                url: `notification - error: ${results.id} - ${results.uuid}`,
                bodyReq: '{}',
                queryReq: '{}',
                paramsReq: '{}',
                headers: '{}',
                dataRes: `${logRes}`,
              },
            },
            false,
          );
        }

        try {
          if (
            createRequestOrderDTO.type === SCHEDULE_REQUEST_TYPE.SCHEDULE_REQUEST &&
            createRequestOrderDTO.customerType === CUSTOMER_TYPE.FROM_FPT
          ) {
            if (createRequestOrderDTO.email) {
              await this.osrService.verifyEmail({
                emails: [createRequestOrderDTO.email],
              });
            }
            await this.osrService.saveFpterInfo([
              {
                registrantName: createRequestOrderDTO.customerName,
                registrantPhoneNumber: createRequestOrderDTO.phoneNumber,
                email: createRequestOrderDTO.email,
                vaccineeName: createRequestOrderDTO.nameOfSubjectsInjection,
                vaccineePhoneNumber: createRequestOrderDTO.phoneOfSubjectsInjection,
                dateOfBirth: createRequestOrderDTO.dateOfBirthOfSubjectsInjection,
                // whomToVaccinate: 0, // 0 - tiem cho toi, 1 - tiem cho ng than // ko co thong tin
                shopCode: createRequestOrderDTO.shopCode,
                shopName: createRequestOrderDTO.shopName,
                groupId: createRequestOrderDTO.disease?.[0]?.diseaseGroupId ?? '',
                groupName: createRequestOrderDTO.disease?.[0]?.diseaseGroupName ?? '',
                appointmentDate: createRequestOrderDTO.appointmentDate,
                timeFrame: createRequestOrderDTO.vaccinationTime,
                registrantDateOfBirth: String(createRequestOrderDTO.dateOfBirth),
              },
            ]);
          }
        } catch (error) {}
      }
      return results;
    } catch (err) {
      throw new HttpException(err, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async update(uuid: string, user: IAuthUser, body: UpdateScheduleRequestDTO): Promise<any> {
    if (body.status && body.status === STATUS_SCHEDULE_REQUEST.CANCELED) {
      return this.cancel(uuid, user, body, ScheduleRequestType.Default);
    }
    const data = (await this.elasticSearchService.getScheduleRequestByUUIDAlias(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
    )) as any;

    const existingScheduleRequest: ScheduleRequest = data?._source;
    if (!existingScheduleRequest || (existingScheduleRequest as ScheduleRequest)?.isPreOrder === true) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }

    // Switch to case type = 5 if needed
    if (existingScheduleRequest?.type === SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST) {
      return this.updateForOnlineOrderRequest(existingScheduleRequest, body, user, uuid, data);
    }

    if (
      body.status !== null &&
      body.status !== undefined &&
      !this.verifyStatusForUpdate(existingScheduleRequest['status'], body.status)
    ) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
    // Update the fields with the values from the request body
    Object.assign(existingScheduleRequest, {
      ...body,
      ...{ status: body.status || existingScheduleRequest['status'], updatedBy: user?.employee_code || '' },
    });
    const updatedScheduleRequest = await this.scheduleRequestRepository.save(
      plainToInstance(ScheduleRequest, existingScheduleRequest),
    );
    await this.elasticSearchService.updateDocumentByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
      updatedScheduleRequest,
      data?._index,
    );
    await this.verifyAndAssignUserToQueue(
      body.status,
      existingScheduleRequest['insideCode'] || '',
      [getCurrentShift(new Date(existingScheduleRequest['createdDate']))],
      false,
    );

    try {
      if (body.status && body.status !== STATUS_SCHEDULE_REQUEST.NEW) {
        this.unAssignOrderFromQueue(uuid);
      }
    } catch (error) {}

    return updatedScheduleRequest;
  }

  /**
   * Clone from update method, only handle logic for pre-order
   */
  async updateForPreOrder(uuid: string, user: IAuthUser, body: UpdateScheduleRequestDTO): Promise<any> {
    const existingScheduleRequest = (await this.elasticSearchService.getScheduleRequestByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
    )) as ScheduleRequest;

    if (
      body.status &&
      body.status === STATUS_SCHEDULE_REQUEST.CANCELED &&
      existingScheduleRequest?.type !== SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA
    ) {
      return this.cancel(uuid, user, body, ScheduleRequestType.PreOrder);
    }

    // block request
    if (
      !(await this.redis.set(`${REDIS_UPDATE_SCHEDULE_REQUEST}${uuid}`, Date.now(), 'PX', 300, 'NX')) &&
      existingScheduleRequest.type === SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA
    ) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    if (existingScheduleRequest?.isPreOrder !== true) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }

    if (
      body.status !== null &&
      body.status !== undefined &&
      !this.verifyStatusForUpdate(existingScheduleRequest['status'], body.status)
    ) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    // loại 4 - hết quota thì tự động assign
    let updateDetail: any = {
      status: body.status || existingScheduleRequest['status'],
      updatedBy: user?.employee_code || '',
    };
    if (existingScheduleRequest?.type === SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA) {
      const resInside = await this.insideService.getLeaderEmployeeByCode({
        listEmployeeCode: [user.employee_code],
      });
      const isLeader = resInside?.length > 0 ? true : false;

      // check shift
      if (!isLeader) {
        // check already assign
        if (!existingScheduleRequest?.insideCode) {
          updateDetail = {
            ...updateDetail,
            insideCode: user.employee_code,
            insideName: user.full_name,
            assignedDate: existingScheduleRequest?.assignedDate || new Date(),
          };
        } else if (existingScheduleRequest?.insideCode && existingScheduleRequest?.insideCode !== user.employee_code) {
          const exception: IError = {
            code: ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST,
            message: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
            details: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
            validationErrors: null,
          };
          throw new SystemException(exception, HttpStatus.BAD_REQUEST);
        }
      }

      if (isLeader) {
        const LEADER_VALID_STATUS = [
          STATUS_SCHEDULE_REQUEST.NEW,
          STATUS_SCHEDULE_REQUEST.PROCESSED,
          STATUS_SCHEDULE_REQUEST.NOT_CONTACT,
        ];
        updateDetail = {
          ...updateDetail,
          insideCode: user.employee_code,
          insideName: user.full_name,
          assignedDate: existingScheduleRequest?.assignedDate || new Date(),
        };

        if (
          existingScheduleRequest?.insideCode &&
          existingScheduleRequest?.insideCode !== user.employee_code &&
          LEADER_VALID_STATUS.includes(existingScheduleRequest?.status)
        ) {
          updateDetail.assignedDate = new Date();
        }

        if (
          existingScheduleRequest?.insideCode &&
          existingScheduleRequest?.insideCode !== user.employee_code &&
          !LEADER_VALID_STATUS.includes(existingScheduleRequest?.status)
        ) {
          const exception: IError = {
            code: ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST,
            message: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
            details: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
            validationErrors: null,
          };
          throw new SystemException(exception, HttpStatus.BAD_REQUEST);
        }
      }

      const { shifts, err: errShift } = await this.employeeService.getShiftAndCheckPossibleAssign(
        user.employee_code,
        true,
        ScheduleRequestType.PreOrder,
        isLeader ? ErrorCode.RSA_ECOM_ASSIGN_PRE_ORDER : ErrorCode.RSA_ECOM_ASSIGN,
      );
      const targetShift = getCurrentShift(new Date(existingScheduleRequest['createdDate']));
      if (!shifts || !shifts.length || !shifts.includes(targetShift)) {
        throw new SystemException(
          errShift || { code: ErrorCode.RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT },
          HttpStatus.BAD_REQUEST,
        );
      }
    }
    // Update the fields with the values from the request body
    Object.assign(existingScheduleRequest, {
      ...body,
      ...updateDetail,
    });

    const updatedScheduleRequest = await this.scheduleRequestRepository.save(
      plainToInstance(ScheduleRequest, existingScheduleRequest),
    );
    await this.elasticSearchService.updateDocumentByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
      updatedScheduleRequest,
    );

    if (existingScheduleRequest?.type === SCHEDULE_REQUEST_TYPE.PRE_ORDER) {
      await this.verifyAndAssignUserToQueuePreOrder(
        body.status,
        existingScheduleRequest['insideCode'] || '',
        [getCurrentShift(new Date(existingScheduleRequest['createdDate']))],
        false,
      );

      try {
        if (body.status && body.status !== STATUS_SCHEDULE_REQUEST.NEW) {
          this.unAssignOrderFromQueuePreOrder(uuid);
        }
      } catch (error) {}
    }

    return updatedScheduleRequest;
  }

  async cancel(
    uuid: string,
    user: IAuthUser,
    body: UpdateScheduleRequestDTO,
    scheduleRequestType: ScheduleRequestType,
  ): Promise<any> {
    const data: any = (await this.elasticSearchService.getScheduleRequestByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
    )) as any;

    const existingScheduleRequest: ScheduleRequest = data;
    if (!existingScheduleRequest) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }
    if (!this.verifyStatusForUpdate(existingScheduleRequest['status'], STATUS_SCHEDULE_REQUEST.CANCELED)) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
    // Update the fields with the values from the request body
    Object.assign(existingScheduleRequest, {
      ...body,
      ...{
        status: STATUS_SCHEDULE_REQUEST.CANCELED,
        cancelDate: getDateTimeZone(),
        cancelBy: user?.employee_code || '',
        updatedBy: user?.employee_code || '',
      },
    });
    const updatedScheduleRequest = await this.scheduleRequestRepository.save(
      plainToInstance(ScheduleRequest, existingScheduleRequest),
    );
    await this.elasticSearchService.updateDocumentByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
      updatedScheduleRequest,
      data?._index,
    );

    if (existingScheduleRequest?.type === SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST) {
      // Handle case type = 5
      const shiftRaw = existingScheduleRequest?.insideCode
        ? (await this.employeeService.getShiftRaw(existingScheduleRequest?.insideCode))?.shiftRaw || []
        : [];
      await Promise.all([
        shiftRaw?.includes(Shift.Day) &&
          this.vacCoreAssignJobService.addQueueOnlineOrderAssignment({
            type: AssignTypesEnum.user,
            sub: {
              listInsideId: [existingScheduleRequest.insideCode],
            },
          }),
        shiftRaw?.includes(Shift.Night) &&
          this.vacCoreAssignJobService.addQueueOnlineOrderAssignment({
            type: AssignTypesEnum.user,
            sub: {
              listInsideId: [existingScheduleRequest.insideCode],
            },
            isNightShift: true,
          }),
      ]);
    } else {
      // old process
      switch (scheduleRequestType) {
        case ScheduleRequestType.Default:
          await this.verifyAndAssignUserToQueue(
            STATUS_SCHEDULE_REQUEST.CANCELED,
            existingScheduleRequest['insideCode'] || '',
            [getCurrentShift(new Date(existingScheduleRequest['createdDate']))],
            false,
          );
          break;

        case ScheduleRequestType.PreOrder:
          if (existingScheduleRequest.type === SCHEDULE_REQUEST_TYPE.PRE_ORDER) {
            await this.verifyAndAssignUserToQueuePreOrder(
              STATUS_SCHEDULE_REQUEST.CANCELED,
              existingScheduleRequest['insideCode'] || '',
              [getCurrentShift(new Date(existingScheduleRequest['createdDate']))],
              false,
            );
          }
          break;
        default:
          break;
      }
    }

    return updatedScheduleRequest;
  }

  async assign(uuid: string, insideCode: string, user: IAuthUser, body: AssignScheduleRequestDTO): Promise<any> {
    const data = (await this.elasticSearchService.getScheduleRequestByUUIDAlias(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
    )) as any;

    const existingScheduleRequest: ScheduleRequest = data?._source;

    if (existingScheduleRequest['status'] !== STATUS_SCHEDULE_REQUEST.NEW) {
      if (isAutoAssign(body)) {
        // phân tự động, add user back to queue
        await this.unAssignUserFromQueue(
          insideCode,
          getCurrentShift(new Date(existingScheduleRequest['createdDate'])) === SHIFT.NIGHT,
        );
        await this.assignUserToQueue(insideCode, [getCurrentShift(new Date(existingScheduleRequest['createdDate']))]);
      }
      throw new SystemException({ code: ErrorCode.RSA_ECOM_REQUEST_INVALID_ASSIGN }, HttpStatus.BAD_REQUEST);
    }

    if (!existingScheduleRequest) {
      await this.unAssignOrderFromQueue(uuid);
      throw new SystemException({ code: ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST }, HttpStatus.NOT_FOUND);
    }

    const currentInsideId = (existingScheduleRequest as any).insideCode || '';

    if (isAutoAssign(body) && !(await this.isEmployeeOnline(insideCode))) {
      // nếu user không online => remove all user from queue[cả ngày và đêm] phân công tự động nếu có.
      this.unAssignUserFromQueue(insideCode).then();
      if (!currentInsideId) {
        // nếu yêu cầu này chưa được phân công => remove all old queue, add to new queue phân công tự động
        await this.unAssignOrderFromQueue(uuid);
        await this.assignOrderFromQueue({
          orderCode: (existingScheduleRequest as any).uuid,
          createdDate: (existingScheduleRequest as any).createdDate,
        });
      }

      throw new SystemException({ code: ErrorCode.RSA_ECOM_EMPLOYEE_NOT_ONLINE }, HttpStatus.NOT_ACCEPTABLE);
    }

    // first check if employee has any pending schedule request
    const { items: employeePendingRequetsRaw } = await this.elasticSearchService.searchScheduleRequest(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      {
        insideId: [insideCode],
        status: [STATUS_SCHEDULE_REQUEST.NEW.toString()],
      },
    );

    const employeePendingRequets = employeePendingRequetsRaw.filter(
      (item: any) => item?.type !== SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST,
    );

    await this.logAssignedScheduleRequest(uuid, employeePendingRequets as ScheduleRequest[]);

    // get uniq shift already assign
    const shiftsAlreadyAssigned = _.uniq(
      employeePendingRequets.map((item) => {
        return getCurrentShift(new Date(item['createdDate']));
      }),
    );

    // shift already assign === 2 => unassign user, reassign order
    if (isAutoAssign(body) && shiftsAlreadyAssigned.length === 2) {
      // case phan cong tu dong.
      // remove user from all queues, add assign request to queue
      await this.assignOrderFromQueue({
        orderCode: (existingScheduleRequest as any).uuid,
        createdDate: (existingScheduleRequest as any).createdDate,
      });
      await this.unAssignUserFromQueue(insideCode);

      // throw error that shows the employee has pending schedule request along with current request shifts
      const assignScheduleShift = getCurrentShift(new Date((existingScheduleRequest as any).createdDate));
      const existShiftScheduleRequest =
        employeePendingRequets.find(
          (item: any) => getCurrentShift(new Date(item.createdDate)) === assignScheduleShift,
        ) || employeePendingRequets[0];

      const message = ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING).replace(
        '{id}',
        String((existShiftScheduleRequest as any)?.id),
      );
      throw new HttpException(
        {
          code: ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING,
          message,
          details: message,
        },
        HttpStatus.CONFLICT,
      );
    }

    // if employees has not been scheduled, throw error at this line
    const { shifts, err: errShift } = await this.employeeService.getShiftAndCheckPossibleAssign(
      insideCode,
      !isAutoAssign(body),
      ScheduleRequestType.Default,
    );
    if (!shifts || !shifts.length) {
      // nhân viên phân công 1 ca, có 1 ca trùng ca đang xử lý, ví dụ, nhân viên được phân công ca ngày, và đang xử lý ca ngày. => shifts sẽ return []
      // và trả lỗi ở biến errShift
      // trong trường hợp nhân viên được phân công 2 ca, bị trùng cả 2 ca, thì shifts sẽ return [] và throw error còn lại.
      await this.unAssignUserFromQueue(insideCode);
      if (!currentInsideId) {
        // nếu yêu cầu này chưa được phân công => remove all old queue, add to new queue auto assigns.
        await this.unAssignOrderFromQueue(uuid);
        await this.assignOrderFromQueue({
          orderCode: (existingScheduleRequest as any).uuid,
          createdDate: (existingScheduleRequest as any).createdDate,
        });
      }
      throw new SystemException(
        errShift || { code: ErrorCode.RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT },
        HttpStatus.BAD_REQUEST,
      );
    }

    // check if employee is in the list of sales
    const { status: resStatus, data: resData } = await this.insideService.employeeListSales({
      ListEmployeeCode: insideCode,
    });
    if (resStatus != HttpStatus.OK) {
      this.unAssignUserFromQueue(insideCode).then();
      if (!currentInsideId) {
        await this.unAssignOrderFromQueue(uuid);
        await this.assignOrderFromQueue({
          orderCode: (existingScheduleRequest as any).uuid,
          createdDate: (existingScheduleRequest as any).createdDate,
        });
      }
      throw new SystemException({ code: ErrorCode.RSA_ECOM_INSIDE_ERROR }, HttpStatus.NOT_FOUND);
    }

    // first check if employee has any pending schedule request
    let isValidAssign = true;

    if (isAutoAssign(body)) {
      // shift already assign === 1 => unassign user, reassign order
      if (shiftsAlreadyAssigned.length === 1) {
        const targetShift = getCurrentShift(new Date(existingScheduleRequest['createdDate']));
        if (!shifts.includes(targetShift)) {
          // nếu request của ca mới không nằm trong danh sách ca checkin của nhân viên thì không cho phép assign
          isValidAssign = false;
        }
        const userShiftsAssignedAlready = getCurrentShift(new Date((employeePendingRequets[0] as any).createdDate));
        if (targetShift === userShiftsAssignedAlready) {
          // nếu ca mới trùng với ca đã assign thì không cho phép assign
          isValidAssign = false;
        }
        // remove user from queue
        await this.unAssignUserFromQueue(insideCode, userShiftsAssignedAlready === SHIFT.DAY ? false : true);
      }

      if (!isValidAssign) {
        // only for auto assign
        if (!currentInsideId) {
          await this.unAssignOrderFromQueue(uuid);
          await this.assignOrderFromQueue({
            orderCode: (existingScheduleRequest as any).uuid,
            createdDate: (existingScheduleRequest as any).createdDate,
          });
        }
        const message = ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING).replace(
          '{id}',
          String((employeePendingRequets[0] as any)?.id),
        );
        throw new HttpException(
          {
            code: ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING,
            message,
            details: message,
          },
          HttpStatus.CONFLICT,
        );
      }
    }

    if (!isAutoAssign(body)) {
      const targetShift = getCurrentShift(new Date(existingScheduleRequest['createdDate']));
      if (!shifts.includes(targetShift)) {
        // nếu request của ca mới không nằm trong danh sách ca checkin của nhân viên thì không cho phép assign
        // only for auto assign
        throw new SystemException({ code: ErrorCode.RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT }, HttpStatus.BAD_REQUEST);
      }
    }

    // Update the fields with the values from the request body
    Object.assign(existingScheduleRequest, {
      ...body,
      ...{
        // status: STATUS_SCHEDULE_REQUEST.PROCESSED,
        insideCode: insideCode,
        insideName: body.employeeName || resData.length > 0 ? resData[0].employeeName : '',
        assignedDate: getDateTimeZone(),
        updatedBy: user?.employee_code || '',
      },
    });
    const updatedScheduleRequest = await this.scheduleRequestRepository.save(
      plainToInstance(ScheduleRequest, existingScheduleRequest),
    );
    await this.elasticSearchService.updateDocumentByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
      updatedScheduleRequest,
      data?._index,
    );
    await Promise.all([
      this.unAssignOrderFromQueue(uuid),
      this.unAssignUserFromQueue(insideCode, isNightShift(updatedScheduleRequest.createdDate)),
      currentInsideId &&
        this.assignUserToQueue(currentInsideId, [getCurrentShift(new Date(updatedScheduleRequest.createdDate))]),
    ]);

    try {
      await this.sendNotification(updatedScheduleRequest, insideCode);
    } catch (errors) {}

    return updatedScheduleRequest;
  }

  async assignForPreOrder(
    uuid: string,
    insideCode: string,
    user: IAuthUser,
    body: AssignScheduleRequestDTO,
  ): Promise<any> {
    const sale = await this.saleRepository.findOneBy({
      inside: insideCode,
    });
    if (sale && sale?.assignRole !== ASSIGN_ROLE.PREORDER) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_PRE_ORDER_ASSIGN,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_PRE_ORDER_ASSIGN),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_PRE_ORDER_ASSIGN),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    const existingScheduleRequest: any = await this.elasticSearchService.getScheduleRequestByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
    );

    if (existingScheduleRequest['status'] !== STATUS_SCHEDULE_REQUEST.NEW) {
      if (isAutoAssign(body)) {
        // phân tự động, add user back to queue
        await this.unAssignUserFromQueuePreOrder(
          insideCode,
          getCurrentShift(new Date(existingScheduleRequest['createdDate'])) === SHIFT.NIGHT,
        );
        await this.assignUserToQueuePreOrder(insideCode, [
          getCurrentShift(new Date(existingScheduleRequest['createdDate'])),
        ]);
      }
      throw new SystemException({ code: ErrorCode.RSA_ECOM_REQUEST_INVALID_ASSIGN }, HttpStatus.BAD_REQUEST);
    }

    if (!existingScheduleRequest) {
      await this.unAssignOrderFromQueuePreOrder(uuid);
      throw new SystemException({ code: ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST }, HttpStatus.NOT_FOUND);
    }

    const currentInsideId = (existingScheduleRequest as any).insideCode || '';

    if (isAutoAssign(body) && !(await this.isEmployeePreOrderOnline(insideCode))) {
      // nếu user không online => remove all user from queue[cả ngày và đêm] phân công tự động nếu có.
      this.unAssignUserFromQueuePreOrder(insideCode).then();
      if (!currentInsideId) {
        // nếu yêu cầu này chưa được phân công => remove all old queue, add to new queue phân công tự động
        await this.unAssignOrderFromQueuePreOrder(uuid);
        await this.assignOrderFromQueuePreOrder({
          orderCode: (existingScheduleRequest as any).uuid,
          createdDate: (existingScheduleRequest as any).createdDate,
        });
      }

      throw new SystemException({ code: ErrorCode.RSA_ECOM_EMPLOYEE_NOT_ONLINE }, HttpStatus.NOT_ACCEPTABLE);
    }

    // first check if employee has any pending schedule request
    const { items: employeePendingRequets } = await this.elasticSearchService.searchScheduleRequest(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      {
        insideId: [insideCode],
        status: [STATUS_SCHEDULE_REQUEST.NEW.toString()],
      },
      ScheduleRequestType.PreOrder,
    );

    await this.logAssignedScheduleRequest(uuid, employeePendingRequets as ScheduleRequest[]);

    // get uniq shift already assign
    const shiftsAlreadyAssigned = _.uniq(
      employeePendingRequets.map((item) => {
        return getCurrentShift(new Date(item['createdDate']));
      }),
    );

    // shift already assign === 2 => unassign user, reassign order
    if (isAutoAssign(body) && shiftsAlreadyAssigned.length === 2) {
      // case phan cong tu dong.
      // remove user from all queues, add assign request to queue
      await this.assignOrderFromQueuePreOrder({
        orderCode: (existingScheduleRequest as any).uuid,
        createdDate: (existingScheduleRequest as any).createdDate,
      });
      await this.unAssignUserFromQueuePreOrder(insideCode);

      // throw error that shows the employee has pending schedule request along with current request shifts
      const assignScheduleShift = getCurrentShift(new Date((existingScheduleRequest as any).createdDate));
      const existShiftScheduleRequest =
        employeePendingRequets.find(
          (item: any) => getCurrentShift(new Date(item.createdDate)) === assignScheduleShift,
        ) || employeePendingRequets[0];

      const message = ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING).replace(
        '{id}',
        String((existShiftScheduleRequest as any)?.id),
      );
      throw new HttpException(
        {
          code: ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING,
          message,
          details: message,
        },
        HttpStatus.CONFLICT,
      );
    }

    // if employees has not been scheduled, throw error at this line
    const { shifts, err: errShift } = await this.employeeService.getShiftAndCheckPossibleAssign(
      insideCode,
      !isAutoAssign(body),
      ScheduleRequestType.PreOrder,
      ErrorCode.RSA_ECOM_ASSIGN_PRE_ORDER,
    );
    if (!shifts || !shifts.length) {
      // nhân viên phân công 1 ca, có 1 ca trùng ca đang xử lý, ví dụ, nhân viên được phân công ca ngày, và đang xử lý ca ngày. => shifts sẽ return []
      // và trả lỗi ở biến errShift
      // trong trường hợp nhân viên được phân công 2 ca, bị trùng cả 2 ca, thì shifts sẽ return [] và throw error còn lại.
      await this.unAssignUserFromQueuePreOrder(insideCode);
      if (!currentInsideId && existingScheduleRequest?.type !== SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA) {
        // nếu yêu cầu này chưa được phân công => remove all old queue, add to new queue auto assigns.
        await this.unAssignOrderFromQueuePreOrder(uuid);
        await this.assignOrderFromQueuePreOrder({
          orderCode: (existingScheduleRequest as any).uuid,
          createdDate: (existingScheduleRequest as any).createdDate,
        });
      }
      throw new SystemException(
        errShift || { code: ErrorCode.RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT },
        HttpStatus.BAD_REQUEST,
      );
    }

    // check if employee is in the list of sales
    const { status: resStatus, data: resData } = await this.insideService.employeeListSales({
      ListEmployeeCode: insideCode,
    });
    if (resStatus != HttpStatus.OK) {
      this.unAssignUserFromQueuePreOrder(insideCode).then();
      if (!currentInsideId && existingScheduleRequest?.type !== SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA) {
        await this.unAssignOrderFromQueuePreOrder(uuid);
        await this.assignOrderFromQueuePreOrder({
          orderCode: (existingScheduleRequest as any).uuid,
          createdDate: (existingScheduleRequest as any).createdDate,
        });
      }
      throw new SystemException({ code: ErrorCode.RSA_ECOM_INSIDE_ERROR }, HttpStatus.NOT_FOUND);
    }

    // first check if employee has any pending schedule request
    let isValidAssign = true;

    if (isAutoAssign(body)) {
      // shift already assign === 1 => unassign user, reassign order
      if (shiftsAlreadyAssigned.length === 1) {
        const targetShift = getCurrentShift(new Date(existingScheduleRequest['createdDate']));
        if (!shifts.includes(targetShift)) {
          // nếu request của ca mới không nằm trong danh sách ca checkin của nhân viên thì không cho phép assign
          isValidAssign = false;
        }
        const userShiftsAssignedAlready = getCurrentShift(new Date((employeePendingRequets[0] as any).createdDate));
        if (targetShift === userShiftsAssignedAlready) {
          // nếu ca mới trùng với ca đã assign thì không cho phép assign
          isValidAssign = false;
        }
        // remove user from queue
        await this.unAssignUserFromQueuePreOrder(insideCode, userShiftsAssignedAlready === SHIFT.DAY ? false : true);
      }

      if (!isValidAssign) {
        // only for auto assign
        if (!currentInsideId) {
          await this.unAssignOrderFromQueuePreOrder(uuid);
          await this.assignOrderFromQueuePreOrder({
            orderCode: (existingScheduleRequest as any).uuid,
            createdDate: (existingScheduleRequest as any).createdDate,
          });
        }
        const message = ErrorCode.getError(ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING).replace(
          '{id}',
          String((employeePendingRequets[0] as any)?.id),
        );
        throw new HttpException(
          {
            code: ErrorCode.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING,
            message,
            details: message,
          },
          HttpStatus.CONFLICT,
        );
      }
    }

    if (!isAutoAssign(body)) {
      const targetShift = getCurrentShift(new Date(existingScheduleRequest['createdDate']));
      if (!shifts.includes(targetShift)) {
        // nếu request của ca mới không nằm trong danh sách ca checkin của nhân viên thì không cho phép assign
        // only for auto assign
        throw new SystemException({ code: ErrorCode.RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT }, HttpStatus.BAD_REQUEST);
      }
    }

    // Update the fields with the values from the request body
    Object.assign(existingScheduleRequest, {
      ...body,
      ...{
        // status: STATUS_SCHEDULE_REQUEST.PROCESSED,
        insideCode: insideCode,
        insideName: body.employeeName || resData.length > 0 ? resData[0].employeeName : '',
        assignedDate: getDateTimeZone(),
        updatedBy: user?.employee_code || '',
      },
    });
    const updatedScheduleRequest = await this.scheduleRequestRepository.save(
      plainToInstance(ScheduleRequest, existingScheduleRequest),
    );
    await this.elasticSearchService.updateDocumentByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
      updatedScheduleRequest,
    );
    if (existingScheduleRequest?.type !== SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA) {
      await Promise.all([
        this.unAssignOrderFromQueuePreOrder(uuid),
        this.unAssignUserFromQueuePreOrder(insideCode, isNightShift(updatedScheduleRequest.createdDate)),
        currentInsideId &&
          this.assignUserToQueuePreOrder(currentInsideId, [
            getCurrentShift(new Date(updatedScheduleRequest.createdDate)),
          ]),
      ]);
    }

    try {
      if (existingScheduleRequest?.type !== SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA) {
        await this.sendNotification(updatedScheduleRequest, insideCode);
      }
    } catch (errors) {}

    return updatedScheduleRequest;
  }

  async processingOutOfQuotaRequest(payload: ProcessingOutOfQuotaRequestDTO, user: IAuthUser): Promise<any> {
    const sale = await this.saleRepository.findOneBy({
      inside: payload.employeeCode,
    });
    if (sale && sale?.assignRole !== ASSIGN_ROLE.PREORDER) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_PRE_ORDER_ASSIGN,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_PRE_ORDER_ASSIGN),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_PRE_ORDER_ASSIGN),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    const existingScheduleRequest: any = await this.elasticSearchService.getScheduleRequestByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      payload.uuid,
    );

    // if (existingScheduleRequest['status'] !== STATUS_SCHEDULE_REQUEST.NEW) {
    //   throw new SystemException({ code: ErrorCode.RSA_ECOM_REQUEST_INVALID_ASSIGN }, HttpStatus.BAD_REQUEST);
    // }

    if (!existingScheduleRequest) {
      throw new SystemException({ code: ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST }, HttpStatus.NOT_FOUND);
    }

    if (
      !(await this.redis.set(`${REDIS_UPDATE_SCHEDULE_REQUEST}${payload.uuid}`, Date.now(), 'PX', 300, 'NX')) &&
      existingScheduleRequest.type === SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA
    ) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    const currentInsideId = (existingScheduleRequest as any).insideCode || '';

    const resInside = await this.insideService.getLeaderEmployeeByCode({
      listEmployeeCode: [user.employee_code],
    });
    const isLeader = resInside?.length > 0 ? true : false;

    // check shift
    const assignInfo = {
      insideCode: payload.employeeCode,
      insideName: payload.employeeName || '',
      assignedDate: existingScheduleRequest?.assignedDate || getDateTimeZone(),
      updatedBy: user?.employee_code || '',
    };

    if ([STATUS_SCHEDULE_REQUEST.SUCCESS, STATUS_SCHEDULE_REQUEST.CANCELED].includes(existingScheduleRequest?.status)) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_REQUEST_INVALID_ASSIGN_V2,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_REQUEST_INVALID_ASSIGN_V2),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_REQUEST_INVALID_ASSIGN_V2),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    if (!isLeader) {
      // check already assign
      if (currentInsideId && currentInsideId !== user.employee_code) {
        const exception: IError = {
          code: ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.BAD_REQUEST);
      }
    }

    if (isLeader) {
      const LEADER_VALID_STATUS = [
        STATUS_SCHEDULE_REQUEST.NEW,
        STATUS_SCHEDULE_REQUEST.PROCESSED,
        STATUS_SCHEDULE_REQUEST.NOT_CONTACT,
      ];

      if (
        LEADER_VALID_STATUS.includes(existingScheduleRequest?.status) &&
        currentInsideId &&
        currentInsideId !== user.employee_code
      ) {
        assignInfo.assignedDate = getDateTimeZone();
      }

      if (!LEADER_VALID_STATUS.includes(existingScheduleRequest?.status)) {
        const exception: IError = {
          code: ErrorCode.RSA_ECOM_REQUEST_INVALID_ASSIGN_V3,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_REQUEST_INVALID_ASSIGN_V3),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_REQUEST_INVALID_ASSIGN_V3),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.BAD_REQUEST);
      }
    }

    // if employees has not been scheduled, throw error at this line
    const { shifts, err: errShift } = await this.employeeService.getShiftAndCheckPossibleAssign(
      payload.employeeCode,
      true,
      ScheduleRequestType.PreOrder,
      isLeader ? ErrorCode.RSA_ECOM_ASSIGN_PRE_ORDER : ErrorCode.RSA_ECOM_ASSIGN,
    );
    if (!shifts || !shifts.length) {
      throw new SystemException(
        errShift || { code: ErrorCode.RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT },
        HttpStatus.BAD_REQUEST,
      );
    }

    // check if employee is in the list of sales
    const { status: resStatus, data: resData } = await this.insideService.employeeListSales({
      ListEmployeeCode: payload.employeeCode,
    });
    if (resStatus != HttpStatus.OK) {
      throw new SystemException({ code: ErrorCode.RSA_ECOM_INSIDE_ERROR }, HttpStatus.NOT_FOUND);
    }

    // first check if employee has any pending schedule request

    const targetShift = getCurrentShift(new Date(existingScheduleRequest['createdDate']));
    if (!shifts.includes(targetShift)) {
      // nếu request của ca mới không nằm trong danh sách ca checkin của nhân viên thì không cho phép assign
      // only for auto assign
      throw new SystemException({ code: ErrorCode.RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT }, HttpStatus.BAD_REQUEST);
    }

    // Update the fields with the values from the request body
    Object.assign(existingScheduleRequest, {
      ...assignInfo,
    });
    const updatedScheduleRequest = await this.scheduleRequestRepository.save(
      plainToInstance(ScheduleRequest, existingScheduleRequest),
    );
    await this.elasticSearchService.updateDocumentByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      payload.uuid,
      updatedScheduleRequest,
    );

    await sleep(900);

    return updatedScheduleRequest;
  }

  async syncES(uuid: string) {
    const document = await this.scheduleRequestRepository.findOne({ where: { uuid: uuid } });
    if (!document) {
      throw new SystemException(
        {
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST),
          code: ErrorCode.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST,
          validationErrors: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    if (document.type === SCHEDULE_REQUEST_TYPE.SCHEDULE_REQUEST) {
      return this.elasticSearchService.updateDocumentByUUID(process.env.ELA_INDEX_SCHEDULE_REQUEST, uuid, document);
    } else if (document.type === SCHEDULE_REQUEST_TYPE.APPOINTMENT_SCHEDULING) {
      const subjectInjections = await this.subjectInjectionRepository.find({
        where: { scheduleRequestId: document.id },
      });
      const items = await this.itemRepository.find({
        where: {
          subjectInjectionId: In(subjectInjections.map((subjectInjection) => subjectInjection.id)),
        },
      });
      const comboDetails = await this.comboDetailRepository.find({
        where: {
          itemId: In(items.map((item) => item.id)),
        },
      });
      return this.elasticSearchService.updateDocumentByUUID(process.env.ELA_INDEX_SCHEDULE_REQUEST, uuid, {
        ...document,
        ...{
          subjectInjections: subjectInjections.map((subjectInjection) => ({
            ...subjectInjection,
            ...{
              items: items
                .filter((item) => item.subjectInjectionId === subjectInjection.id)
                .map((item) => ({
                  ...item,
                  ...{
                    comboDetails: comboDetails.filter((comboDetail) => comboDetail.itemId === item.id),
                  },
                })),
            },
          })),
        },
      });
    }
    return;
  }

  async isEmployeeOnline(employeeCode: string) {
    return this.employeeRepository.exist({
      where: {
        employeeCode,
        online: UserCheckInStatus.Online,
      },
    });
  }

  async isEmployeePreOrderOnline(employeeCode: string) {
    return this.employeeRepository.exist({
      where: {
        employeeCode,
        onlinePreOrder: UserCheckInStatus.Online,
      },
    });
  }

  /**
   * Kiểm tra xem thời điểm hiện tại có nằm trong khoảng từ 8:00 đến 22:00 không
   * @param date - Thời điểm cần kiểm tra (mặc định là thời điểm hiện tại)
   * @returns true nếu trong khoảng 8:00-22:00, false nếu ngoài khoảng
   */
  private isWithinBusinessHours(date?: string): boolean {
    const vietnamTime = date ? moment(date).utcOffset('+07:00') : moment().utcOffset('+07:00');

    const hour = vietnamTime.hour();

    // Lấy giờ bắt đầu và kết thúc từ env, mặc định là 8:00-22:00
    const startHour = parseInt(process.env.BUSINESS_START_HOUR || '8', 10);
    const endHour = parseInt(process.env.BUSINESS_END_HOUR || '22', 10);

    // Kiểm tra trong khoảng giờ làm việc (startHour <= hour < endHour)
    return hour >= startHour && hour < endHour;
  }

  private async sendNotification(scheduleRequest: ScheduleRequest, assignId?: string) {
    const notificationAddresses = [];
    if (!assignId) {
      // case: create schedule request => send notification to all managers and sale leaders
      const employeesSendTo = await this.scheduleRequestsUtilService.getEmployeeListSales([
        ...EmployeeJobTitleCodes.Manager,
        ...EmployeeJobTitleCodes.EcomSaleLeader,
      ]);

      notificationAddresses.push(
        ...employeesSendTo.map((item) => generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, item.employeeCode)),
      );
    } else {
      // case: assign schedule request => send notification to assignee
      notificationAddresses.push(generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, assignId));
    }

    let template;
    switch (scheduleRequest.type) {
      case SCHEDULE_REQUEST_TYPE.SCHEDULE_REQUEST:
        template = CreateScheduleRequestTemplate;
        break;
      case SCHEDULE_REQUEST_TYPE.APPOINTMENT_SCHEDULING:
        template = CreateScheduleAppointmentTemplate;
        break;
      case SCHEDULE_REQUEST_TYPE.PRE_ORDER:
        template = CreateSchedulePreOrderTemplate;
        break;
      case SCHEDULE_REQUEST_TYPE.PRE_ORDER_OUT_OF_QUOTA:
        template = CreateSchedulePreOrderOutOfQuotaTemplate;
        break;
      case SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST:
        template = CreateScheduleOnlineRequestTemplate;
        break;
    }

    await this.notificationService.sendNotifications(
      [
        formatNotificationPayload({
          To: notificationAddresses,
          Cc: [],
          Bcc: [],
          template: template,
          replaceParams: {
            content: {
              id: scheduleRequest.id,
              timezone: parseDateTimeZone(new Date(), '+07:00', 'hh:mm dd/mm/yyyy'),
            },
            messageLink: {
              // messageLink: scheduleRequest.isPreOrder
              //   ? `request-pre-orders?redirect=${scheduleRequest.uuid}`
              //   : `request-orders?redirect=${scheduleRequest.uuid}`,
              messageLink: `request-orders?redirect=${scheduleRequest.uuid}`,
            },
            extraProperties: {
              code:
                scheduleRequest.type === SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST
                  ? scheduleRequest.orderCode
                  : scheduleRequest.id,
            },
          },
        }),
      ],
      true,
    );

    return true;
  }

  async logAssignedScheduleRequest(assignedScheduleRequestId: string, newScheduleRequests: ScheduleRequest[]) {
    try {
      const dataRes = {
        employeeScheduleRequestNew: newScheduleRequests,
      };
      await this.logger.log(
        {
          message: `assigned to uuid: ${assignedScheduleRequestId}`,
          fields: {
            info: `Tat ca yeu cau nhan vien dang xu ly: ${newScheduleRequests.length}`,
            method: `GET`,
            url: `assigned to uuid: ${assignedScheduleRequestId}`,
            bodyReq: `{}`,
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: `${JSON.stringify(dataRes)}`,
          },
        },
        false,
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  async searchByLcvIdOrPhone(query: SearchByLcvIdAndPhoneDto): Promise<any> {
    let results = await this.elasticSearchService.searchScheduleRequestByLcvIdOrPhone(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      query,
    );

    if (results?.items?.length <= 0) {
      if (query.phone) {
        results = await this.elasticSearchService.searchScheduleRequestByLcvIdOrPhone(
          process.env.ELA_INDEX_SCHEDULE_REQUEST,
          {
            keyword: query.phone,
            page: query.page,
            pageSize: query.pageSize,
            isPreOrder: query.isPreOrder,
          },
        );
      }
    }

    results.items.forEach((e: any) => {
      if (e?.subjectInjections?.[0]?.dateOfBirthOfSubjectsInjection)
        e.subjectInjections[0].customerAge = calculateTimeDifference(
          new Date(moment.tz(e.subjectInjections[0]?.dateOfBirthOfSubjectsInjection, 'Asia/Ho_Chi_Minh').toISOString()),
          null,
          null,
          null,
          null,
        );
    });

    return {
      totalCount: results.totalCount,
      items: results.items,
      error: results?.error,
    };
  }

  async getMasterDataCampaignType(query: GetMasterDataCampaignType): Promise<MasterDataResponseDto> {
    const listDocumentId = query.landingDocId || [];
    const campaignTypeRes = await this.cmsApiService.getCampaignTypes(listDocumentId);
    return {
      campaignTypes: campaignTypeRes?.items?.map((item) => ({
        id: item?.code,
        name: item?.displayName,
        documentId: item?.documentId,
      })),
    };
  }

  async getMasterDataLandingType(): Promise<MasterDataResponseDto> {
    const landingTypeRes = await this.cmsApiService.getLandingTypes();
    return {
      landingTypes: landingTypeRes?.items?.map((item) => ({
        id: item?.code,
        name: item?.displayName,
        documentId: item?.documentId,
      })),
    };
  }

  async getMasterDataCustomerType(): Promise<MasterDataResponseDto> {
    const customerTypeRes = await this.cmsApiService.getCustomerTypes();
    return {
      customerType: customerTypeRes?.items?.map((item) => ({
        id: item?.code,
        name: item?.displayName,
        documentId: item?.documentId,
      })),
    };
  }

  async createOnlineOrderRequest(body: CreateNewOnlineOrderRequestDto) {
    const { orderCode } = body;
    const results = await this.elasticSearchService.searchScheduleRequest(process.env.ELA_INDEX_SCHEDULE_REQUEST, {
      orderCode,
      type: SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST,
    });

    if (results?.items?.length) {
      return results.items[0];
    }

    const order = await this.omsService.getOneOrder(orderCode);
    // Thông tin người tiêm
    const personIdSub: string[] = _.compact(
      _.uniq(
        JSONPath({
          path: '$.details[*].detailAttachments[*].personIdSub',
          json: order,
        }),
      ),
    );

    // List detailAttachments
    const detailAttachments: DetailAttachment[] = JSONPath({
      path: '$.details[*].detailAttachments[*]',
      json: order,
    });

    // Lấy ra appointmentDate mới nhất từ detailAttachments
    const latestAppointmentDate = detailAttachments
      .filter((item) => item?.appointmentDate) // Lọc ra các item có appointmentDate
      .map((item) => item.appointmentDate) // Lấy ra appointmentDate
      .sort((a, b) => moment(a).diff(moment(b)))?.[0]; // Sắp xếp giảm dần theo thời gian và lấy ra cái mới nhất

    const [getPersonByPhone, getManyByLcvId] = await Promise.all([
      this.familyService.getPersonByPhone(order?.phone),
      this.familyService.getListPrimaryPerson(personIdSub),
    ]);

    const persionInSubId = personIdSub?.[0] || '';
    const findPerson = getManyByLcvId?.find((person) => person.lcvId === persionInSubId);
    const items = [];
    for (const itemdetailAttachment of detailAttachments) {
      const isAlreadyExist = items.find(
        (x) => x?.itemCode === itemdetailAttachment?.itemCode && x?.unitCode === itemdetailAttachment?.unitCode,
      );
      if (isAlreadyExist) continue; // Skip if item already exists
      const listItemFilter = detailAttachments?.filter(
        (x) => x?.itemCode === itemdetailAttachment?.itemCode && x?.unitCode === itemdetailAttachment?.unitCode,
      );
      items.push({
        itemCode: itemdetailAttachment?.itemCode,
        itemName: itemdetailAttachment?.itemName,
        unitCode: itemdetailAttachment?.unitCode,
        unitName: itemdetailAttachment?.unitName,
        price: `${itemdetailAttachment?.price}`,
        priceAfterDiscount: `${itemdetailAttachment?.price}`,
        quantity: `${listItemFilter?.length || 1}`,
      });
    }
    const subjectInjections = [
      {
        nameOfSubjectsInjection: findPerson?.name,
        phoneOfSubjectsInjection: findPerson?.phoneNumber || '',
        genderOfSubjectsInjection: findPerson?.gender,
        dateOfBirthOfSubjectsInjection: `${findPerson?.dateOfBirth}`,
        lcvId: findPerson?.lcvId,
        items,
      },
    ];

    const payloadScheduleRequest = {
      orderCode,
      customerNote: order?.note || '',
      customerId: order?.custCode,
      customerName: order?.custName,
      dateOfBirth: `${getPersonByPhone?.[0]?.dateOfBirth}`,
      gender: getPersonByPhone?.[0]?.gender,
      phoneNumber: order?.phone,
      personId: getPersonByPhone?.[0]?.id,
      type: SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST, //Loại yêu cầu đơn hàng
      customerType: CUSTOMER_TYPE.FROM_OUT_SIDE,
      user: {},
      shopCode: order?.shopCode || '',
      shopName: order?.shopName || '',
      orderChannel: Number(order?.orderChanel) || 7,
      appointmentDate: latestAppointmentDate, // Thêm appointmentDate mới nhất vào payload
      subjectInjections,
    } as any;
    const resSchedule = await this.create(payloadScheduleRequest);
    try {
      await this.vacCoreAssignJobService.addQueueOnlineOrderAssignment({
        type: AssignTypesEnum.ecomOrder,
        sub: {
          listOrderCode: [resSchedule?.uuid],
        },
        isNightShift: this.isWithinBusinessHours() ? false : true,
      });
    } catch (error) {
      await this.logger.log(
        {
          message: `add queue - error: ${JSON.stringify(error)}`,
          fields: {
            info: `add queue - error: ${JSON.stringify(error)}`,
            method: `GET`,
            url: `add queue - error: ${JSON.stringify(error)}`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify(error),
          },
        },
        false,
      );
    }

    return resSchedule;
  }

  async assignForOnlineOrderRequest(
    uuid: string,
    insideCode: string,
    empCodeCurrent: string,
    isAuto: boolean = false,
  ): Promise<any> {
    // const today = moment().utcOffset(7).format('YYYY-MM-DD');
    const data = (await this.elasticSearchService.getScheduleRequestByUUIDAlias(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
    )) as any;
    const existingScheduleRequest: ScheduleRequest = data?._source;
    const insideBefore = existingScheduleRequest?.insideCode || '';
    const createdDate = existingScheduleRequest?.createdDate;
    const shiftOrder = this.isWithinBusinessHours(createdDate) ? Shift.Day : Shift.Night;
    const shiftRawOfCurrentInside = insideCode
      ? (await this.employeeService.getShiftRaw(insideCode))?.shiftRaw || []
      : [];
    if (!isAuto) {
      // Check phân công ca trong trường hợp phân công tay
      await this.employeeService.getShiftAndCheckPossibleAssign(
        insideCode,
        true,
        ScheduleRequestType.Default,
        null,
        true,
      );

      if (!shiftRawOfCurrentInside?.includes(shiftOrder)) {
        throw new SystemException(
          {
            code: ErrorCode.ORDER_SHIFT_NOT_MATCH_IN_USER_SHIFT,
            message: ErrorCode.getError(ErrorCode.ORDER_SHIFT_NOT_MATCH_IN_USER_SHIFT),
          },
          HttpStatus.NOT_FOUND,
        );
      }
    }

    const { status: resStatus, data: resData } = await this.insideService.employeeListSales({
      ListEmployeeCode: insideCode,
    });

    if (resStatus != HttpStatus.OK) {
      throw new SystemException(
        {
          code: ErrorCode.ONLINE_ORDER_USER_NOT_ONLINE,
          message: ErrorCode.getError(ErrorCode.ONLINE_ORDER_USER_NOT_ONLINE),
        },
        HttpStatus.NOT_FOUND,
      );
    }

    await this.logger.log(
      {
        message: `detail schedule request: ${JSON.stringify(data)}`,
        fields: {
          info: `detail schedule request: ${JSON.stringify(data)}`,
          method: `GET`,
          url: `detail schedule request: ${JSON.stringify(data)}`,
          bodyReq: '{}',
          queryReq: '{}',
          paramsReq: '{}',
          headers: '{}',
          dataRes: JSON.stringify(data),
        },
      },
      false,
    );

    // Rule 1: Rq not found
    if (!existingScheduleRequest) {
      throw new SystemException(
        {
          code: ErrorCode.ONLINE_ORDER_NOTFOUND,
          message: ErrorCode.getError(ErrorCode.ONLINE_ORDER_NOTFOUND),
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // Rule: ONLINE_ORDER_PROCESSED
    if (existingScheduleRequest?.status !== STATUS_SCHEDULE_REQUEST.NEW) {
      throw new SystemException(
        {
          code: ErrorCode.ONLINE_ORDER_PROCESSED,
          message: ErrorCode.getError(ErrorCode.ONLINE_ORDER_PROCESSED),
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    if (isAuto) {
      // Rule 2: ORDER_ALREADY_ASSIGNED
      if (!!existingScheduleRequest?.insideCode) {
        throw new SystemException(
          {
            code: ErrorCode.ONLINE_ORDER_ASSIGNED,
            message: ErrorCode.getError(ErrorCode.ONLINE_ORDER_ASSIGNED),
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Rule 4: Nhân viên đang xử lý một đơn hàng khác (Không cần trong ngày)
      const { items: employeePendingRequetsRaw } = await this.elasticSearchService.searchScheduleRequest(
        process.env.ELA_INDEX_SCHEDULE_REQUEST,
        {
          insideId: [insideCode],
          status: [STATUS_SCHEDULE_REQUEST.NEW.toString()],
        },
      );

      await this.logger.log(
        {
          message: `list order chưa xử lý của ${insideCode}: ${JSON.stringify(employeePendingRequetsRaw)}`,
          fields: {
            info: `list order chưa xử lý của ${insideCode}: ${JSON.stringify(employeePendingRequetsRaw)}`,
            method: `GET`,
            url: `list order chưa xử lý của ${insideCode}: ${JSON.stringify(employeePendingRequetsRaw)}`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify(employeePendingRequetsRaw),
          },
        },
        false,
      );

      const employeePendingRequets = employeePendingRequetsRaw.filter((item: any) => {
        // const assignDateFormat = item?.assignedDate ? moment(item?.assignedDate).utcOffset(7).format('YYYY-MM-DD') : '';
        const shiftOrderItem = this.isWithinBusinessHours(item?.createdDate) ? Shift.Day : Shift.Night;
        return item?.type === SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST && shiftOrderItem === shiftOrder;
        // && assignDateFormat === today;
      });

      if (employeePendingRequets.length > 0) {
        throw new SystemException(
          {
            code: ErrorCode.ONLINE_ORDER_USER_ASSIGNED,
            message: ErrorCode.getError(ErrorCode.ONLINE_ORDER_USER_ASSIGNED),
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Rule 5: Nhân viên có online hay không
      const logCheckin = await this.employeeService.getEmployee(insideCode);
      await this.logger.log(
        {
          message: `Data login ${insideCode}: ${JSON.stringify(logCheckin)}`,
          fields: {
            info: `Data login ${insideCode}: ${JSON.stringify(logCheckin)}`,
            method: `GET`,
            url: `Data login ${insideCode}: ${JSON.stringify(logCheckin)}`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify(logCheckin),
          },
        },
        false,
      );
      if (logCheckin.online !== 1) {
        //Rule check user have checkin or not
        throw new SystemException(
          {
            code: ErrorCode.ONLINE_ORDER_USER_NOT_ONLINE,
            message: ErrorCode.getError(ErrorCode.ONLINE_ORDER_USER_NOT_ONLINE),
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    } else {
    }
    // Update the fields with the values from the request body
    Object.assign(existingScheduleRequest, {
      ...{
        // status: STATUS_SCHEDULE_REQUEST.PROCESSED,
        insideCode: insideCode,
        insideName: resData?.[0]?.employeeName || '',
        assignedDate: getDateTimeZone(),
        updatedBy: empCodeCurrent || '',
      },
    });
    const updatedScheduleRequest = await this.scheduleRequestRepository.save(
      plainToInstance(ScheduleRequest, existingScheduleRequest),
    );
    await this.elasticSearchService.updateDocumentByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
      updatedScheduleRequest,
      data?._index,
    );

    // Xoá user và rq khỏi queue in case not auto assign
    if (!isAuto) {
      // Xoá user và rq khỏi queue, add lại user bị đá ra nếu có
      const shiftRaw = insideBefore ? (await this.employeeService.getShiftRaw(insideBefore))?.shiftRaw || [] : [];
      await Promise.all([
        //Xoá rq khỏi queue
        this.vacCoreAssignJobService.removeQueueOnlineOrderAssignment({
          type: AssignTypesEnum.ecomOrder,
          sub: {
            listOrderCode: [uuid],
          },
        }),
        this.vacCoreAssignJobService.removeQueueOnlineOrderAssignment({
          type: AssignTypesEnum.ecomOrder,
          sub: {
            listOrderCode: [uuid],
          },
          isNightShift: true,
        }),
        // Xoá user khỏi queue
        this.vacCoreAssignJobService.removeQueueOnlineOrderAssignment({
          type: AssignTypesEnum.user,
          sub: {
            listInsideId: [insideCode],
          },
        }),
        this.vacCoreAssignJobService.removeQueueOnlineOrderAssignment({
          type: AssignTypesEnum.user,
          sub: {
            listInsideId: [insideCode],
          },
          isNightShift: true,
        }),
        // Add lại user vào queue nếu có
        shiftRaw?.includes(Shift.Day) &&
          this.vacCoreAssignJobService.addQueueOnlineOrderAssignment({
            type: AssignTypesEnum.user,
            sub: {
              listInsideId: [insideBefore],
            },
          }),
        shiftRaw?.includes(Shift.Night) &&
          this.vacCoreAssignJobService.addQueueOnlineOrderAssignment({
            type: AssignTypesEnum.user,
            sub: {
              listInsideId: [insideBefore],
            },
            isNightShift: true,
          }),
      ]);
    }

    try {
      await this.sendNotification(updatedScheduleRequest, insideCode);
    } catch (error) {
      await this.logger.log(
        {
          message: `send noti - error: ${JSON.stringify(error)}`,
          fields: {
            info: `send noti - error: ${JSON.stringify(error)}`,
            method: `GET`,
            url: `send noti - error: ${JSON.stringify(error)}`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify(error),
          },
        },
        false,
      );
    }

    return updatedScheduleRequest;
  }

  async updateForOnlineOrderRequest(
    existingScheduleRequest: ScheduleRequest,
    body: UpdateScheduleRequestDTO,
    user: IAuthUser,
    uuid: string,
    data: any,
  ) {
    Object.assign(existingScheduleRequest, {
      ...body,
      ...{ status: body.status || existingScheduleRequest['status'], updatedBy: user?.employee_code || '' },
    });
    const updatedScheduleRequest = await this.scheduleRequestRepository.save(
      plainToInstance(ScheduleRequest, existingScheduleRequest),
    );
    await this.elasticSearchService.updateDocumentByUUID(
      process.env.ELA_INDEX_SCHEDULE_REQUEST,
      uuid,
      updatedScheduleRequest,
      data?._index,
    );

    try {
      // Add user to queue
      const shiftRaw = existingScheduleRequest?.insideCode
        ? (await this.employeeService.getShiftRaw(existingScheduleRequest?.insideCode))?.shiftRaw || []
        : [];
      await Promise.all([
        shiftRaw?.includes(Shift.Day) &&
          this.vacCoreAssignJobService.addQueueOnlineOrderAssignment({
            type: AssignTypesEnum.user,
            sub: {
              listInsideId: [existingScheduleRequest.insideCode],
            },
          }),
        shiftRaw?.includes(Shift.Night) &&
          this.vacCoreAssignJobService.addQueueOnlineOrderAssignment({
            type: AssignTypesEnum.user,
            sub: {
              listInsideId: [existingScheduleRequest.insideCode],
            },
            isNightShift: true,
          }),
      ]);

      if (body.status && body.status !== STATUS_SCHEDULE_REQUEST.NEW) {
        // Remove task khỏi queue tại có case leader xử lý luôn
        await Promise.all([
          this.vacCoreAssignJobService.removeQueueOnlineOrderAssignment({
            type: AssignTypesEnum.ecomOrder,
            sub: {
              listOrderCode: [uuid],
            },
          }),
          this.vacCoreAssignJobService.removeQueueOnlineOrderAssignment({
            type: AssignTypesEnum.ecomOrder,
            sub: {
              listOrderCode: [uuid],
            },
            isNightShift: true,
          }),
        ]);
      }
    } catch (error) {
      await this.logger.log(
        {
          message: `add or remove queue - error: ${JSON.stringify(error)}`,
          fields: {
            info: `add or remove queue - error: ${JSON.stringify(error)}`,
            method: `GET`,
            url: `add or remove queue - error: ${JSON.stringify(error)}`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify(error),
          },
        },
        false,
      );
    }

    return updatedScheduleRequest;
  }

  async checkOnlineOrderRequestExist(orderCode: string): Promise<boolean> {
    const results = await this.elasticSearchService.searchScheduleRequest(process.env.ELA_INDEX_SCHEDULE_REQUEST, {
      orderCode,
      type: SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST,
    });

    if (results?.items?.length) {
      return true;
    }

    return false;
  }

  async getOnlineOrderRequest(orderCode: string) {
    const results = await this.elasticSearchService.searchScheduleRequest(process.env.ELA_INDEX_SCHEDULE_REQUEST, {
      orderCode,
      type: SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST,
    });

    if (results?.items?.length) {
      return results?.items?.[0];
    }

    return null;
  }
}
