import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class FtelSignInDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  @IsString()
  customerId?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  staffId: string;
}

export class FtelSignInResponseDto {
  @ApiProperty()
  @Expose()
  @IsString()
  token: string;
}
