import { Inject, Injectable } from '@nestjs/common';
import {
  GetPrinterDto,
  LCBillDto,
  LcPrintCenterService as LcPrintCenterLibService,
  PrinterType,
} from 'lc-nest-print-center';
import { PrinterInfo, GetPrinterResponse } from '../dto';
import { EmployeeStep, OMSService } from 'vac-nest-oms';
import _ from 'lodash';
import { PIMAppService } from 'vac-nest-pim-app';
import { JSONPath } from 'jsonpath-plus';
import { PriceListDto, PrintBillDataRes, ProductData } from '@libs/modules/print-center/dto';
import { getPaymentMethod } from '@libs/modules/print-center/functions';
import {
  getDepositedAmountByMethods,
  GetPaymentHistoryESLibResponse,
  PaymentGatewayService,
} from 'vac-nest-payment-gateway';
import { InsideService } from 'vac-nest-inside';
import { PaymentType } from 'vac-nest-examination';
import { OsrService } from 'vac-nest-osr';
import { GetCashBackByListAttCodeResponse } from 'vac-nest-osr/dist/dto/get-list-cash-back.dto';
import { PRINT_TYPE, PrintCenterService as PrintCenterCoreService } from 'vac-nest-print-center';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { OrderChannels } from '@shared';

@Injectable()
export class LcPrintCenterService {
  shopCode: string;
  orderChannel: string;
  constructor(
    private readonly lcPrintCenter: LcPrintCenterLibService,
    private readonly omsService: OMSService,
    private readonly pimCoreService: PIMAppService,
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly insideService: InsideService,
    private readonly osrService: OsrService,
    private readonly printCenterService: PrintCenterCoreService,

    @Inject(REQUEST)
    private readonly req: Request,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
    this.orderChannel = (this.req.headers?.['order-channel'] as string) || '';
  }

  async getPrinter(getPrinterDto: GetPrinterDto) {
    const listPrinter = await this.lcPrintCenter.getPrinter(getPrinterDto);

    const listPrinterMapping: PrinterInfo[] = listPrinter?.map((printer) => ({
      id: printer.id,
      macID: printer?.mac_address,
      macName: printer?.display_name,
      printerType: printer?.printer_type,
    }));

    const printerInfo: GetPrinterResponse = {
      listPrinterAll: listPrinterMapping || [],
      listPrinterA4: listPrinterMapping?.filter((printer) => printer?.printerType === PrinterType.PrinterA4) || [],
      listPrinterBill: listPrinterMapping?.filter((printer) => printer?.printerType === PrinterType.PrinterBill) || [],
    };

    return printerInfo;
  }

  async printPriceList(priceListDto: PriceListDto) {
    return this.printCenterService.printPriceList({
      printType: PRINT_TYPE.PRICE_LIST,
      printerInfo: priceListDto.printerInfo,
      ignorePrint: false,
      printData: { ...priceListDto.printData, shopCode: this.shopCode },
    });
  }
}
