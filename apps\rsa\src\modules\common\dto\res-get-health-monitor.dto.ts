import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class GetManyHealthMonitor {
  @ApiProperty()
  @Expose()
  @IsOptional()
  ticketCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  note?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  monitorTypeName?: string;

  @ApiProperty({ isArray: true, type: String })
  @Expose()
  @IsOptional()
  details?: string[];
}

export class GetManyHealthMonitorRes {
  @ApiProperty({ isArray: true, type: GetManyHealthMonitor })
  @Expose()
  @IsOptional()
  items?: GetManyHealthMonitor[];
}
