import { HttpStatus } from '@nestjs/common';

export interface ErrorValue {
  status: number;
  message: string;
}

/**
 * Error Code optimization that support for System Exception V2 which is declare everything in one place
 */
export class ErrorCodeV2 {
  static RSA_ECOM_ASSIGN_SURVEY_USER_NOT_CHECK_IN = 'RSA_ECOM_ASSIGN_SURVEY_USER_NOT_CHECK_IN:000400';
  static RSA_ECOM_ASSIGN_SURVEY_USER_NOT_ONLINE = 'RSA_ECOM_ASSIGN_SURVEY_USER_NOT_ONLINE:000400';
  static RSA_ECOM_BLOCK_OPEN_ORDER_SAME_SKU = 'RSA_ECOM_BLOCK_OPEN_ORDER_SAME_SKU:000400';
  static RSA_ECOM_APPOINTMENT_REMINDER_NOT_FOUND = 'RSA_ECOM_APPOINTMENT_REMINDER_NOT_FOUND:000400';
  static RSA_ECOM_APPOINTMENT_REMINDER_PROCESSED = 'RSA_ECOM_APPOINTMENT_REMINDER_PROCESSED:000400';
  static RSA_ECOM_SURVEYS_PROCESSED = 'SHOP_VE_TINH_SURVEY_ALREADY_PROCESSED:000403';
  static RSA_ECOM_SURVEY_ASSIGNED = 'SHOP_VE_TINH_SURVEY_ALREADY_ASSIGNED:000403';
  static RSA_ECOM_USER_ASSIGNED = 'SHOP_VE_TINH_USER_ALREADY_ASSIGN:000403';

  private static errorMap = ErrorCodeV2.createErrorMap();

  static getError(code: string): ErrorValue {
    if (this.errorMap.has(code)) {
      return this.errorMap.get(code);
    }
    return {
      message: 'Error code has not been defined',
      status: HttpStatus.INTERNAL_SERVER_ERROR,
    };
  }

  static defaultErrorCode() {
    return 'ERR:00000';
  }

  private static createErrorMap(): Map<string, ErrorValue> {
    const errorCode = new Map<string, ErrorValue>();
    errorCode.set(this.RSA_ECOM_ASSIGN_SURVEY_USER_NOT_ONLINE, {
      message: 'Nhân viên không online',
      status: HttpStatus.BAD_REQUEST,
    });
    errorCode.set(this.RSA_ECOM_ASSIGN_SURVEY_USER_NOT_CHECK_IN, {
      message: 'Nhân viên chưa checkin ngày hôm nay',
      status: HttpStatus.BAD_REQUEST,
    });
    errorCode.set(this.RSA_ECOM_BLOCK_OPEN_ORDER_SAME_SKU, {
      message: 'Khách hàng đang có đơn hàng chưa hoàn thành. Vui lòng kiểm tra lại đơn :orderCode',
      status: HttpStatus.BAD_REQUEST,
    });
    errorCode.set(this.RSA_ECOM_APPOINTMENT_REMINDER_NOT_FOUND, {
      message: 'Lịch nhắc hẹn không tìm thấy.',
      status: HttpStatus.BAD_REQUEST,
    });
    errorCode.set(this.RSA_ECOM_APPOINTMENT_REMINDER_PROCESSED, {
      message: 'Lịch nhắc hẹn đã được xử lý.',
      status: HttpStatus.BAD_REQUEST,
    });
    return errorCode;
  }
}
