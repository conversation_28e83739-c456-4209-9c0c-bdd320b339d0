import { HttpStatus, Inject, Injectable, LoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import {
  concurrentPromise,
  ErrorCode,
  IError,
  OrderChannels,
  RedisService,
  SystemException,
  TICKET_CREATE_KEY,
} from '@shared';
import { ElasticSearchService } from '@shared/modules/elastic-search/elastic-search.service';
import { plainToInstance } from 'class-transformer';
import { Request } from 'express';
import { JSONPath } from 'jsonpath-plus';
import _ from 'lodash';
import { EmployeeInfoContinueBuyingDto, PlaceOrderDto } from 'modules/modules/modules/orders/dto';
import { GetDetailsByTicketCodeDTO } from 'modules/modules/modules/orders/dto/get-details-by-ticket-code.dto';
import { PushsOrderPayloadDto } from 'modules/modules/modules/orders/dto/push-order.dto';
import { OrderUtilsService } from 'modules/modules/modules/orders/services/order-utils.services';
import { OrdersService as OrdersRsaService } from 'modules/modules/modules/orders/services/orders.service';
import moment from 'moment';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { Repository } from 'typeorm';
import { CreateTicketDto } from 'vac-nest-examination';
import { FamilyService } from 'vac-nest-family';
import { InsideService, GetEmployeeByCodeResponse } from 'vac-nest-inside';
import { LoggerService as LoggerServiceLib } from 'vac-nest-logger';
import {
  DetailAttachment,
  EcomDisplay,
  EmployeeStep,
  GetOneOrderLibResponse,
  OMSService,
  OrderStatus,
  PayloadUpdatedStatusOrderDto,
} from 'vac-nest-oms';
import { OrderRuleEngineService } from 'vac-nest-order-rule-engine';
import { OsrService } from 'vac-nest-osr';
import {
  getDepositDetailAmount,
  GetPaymentHistoryESLibResponse,
  PaymentGatewayService,
  PaymentOnlineStatus,
} from 'vac-nest-payment-gateway';
import { PaymentMethod } from 'vac-nest-payment-portal';
import { PIMAppService } from 'vac-nest-pim-app';
import { RegimenItem, RegimenService } from 'vac-nest-regimen';
import { ScheduleCoreService } from 'vac-nest-schedule';
import { EcomOrderService } from '../ecom-order/services/ecom-order.service';
import { WorkerService } from '../worker/worker.service';
import { ContinueBuyingRes } from './dto/continue-buying.dto';
import { GetOrderEsRes } from './dto/get-order-es.dto';
import { SaveOrderDto } from './dto/save-order.dto';
import { UpdateStatusOrderPayloadDto, UpdateStatusOrderPayloadV2Dto } from './dto/update-status-order.dto';
import { OrderDepositOnline } from './entities/order-deposit-online.entity';
import {
  checkNextActionForOrder,
  EnumNextActionOrder,
  isPossibleContinueBuyingByScheduleD2,
} from './utils/next-action.util';
import { timeLineOrder } from './utils/time-line.util';
import { ITEM_CODE_HANG_HIEM } from '../payment/enum';
import { convertPaymentMethodToName } from '../payment/utils';
import { OrderAttribute } from 'vac-nest-cart-app';
import { SCHEDULE_REQUEST_TYPE } from '../schedule-requests/constants/schedule-request.constants';

@Injectable()
export class OrdersService {
  private readonly indexEcomOrder = this.configService.get('ELA_INDEX_ECOM_ORDER');

  constructor(
    private readonly ordersRsaService: OrdersRsaService,
    private readonly configService: ConfigService,
    private readonly omsService: OMSService,
    private readonly elasticSearchService: ElasticSearchService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    private readonly workerService: WorkerService,
    private ecomOrderService: EcomOrderService,
    @InjectRepository(OrderDepositOnline)
    private readonly orderDepositOnlineRepository: Repository<OrderDepositOnline>,
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly orderUtilsService: OrderUtilsService,
    private readonly insideService: InsideService,
    private readonly orderRuleEngineService: OrderRuleEngineService,
    private readonly osrService: OsrService,
    private readonly paymentGWService: PaymentGatewayService,
    private readonly loggerServiceLib: LoggerServiceLib,
    private readonly scheduleService: ScheduleCoreService,
    private readonly familyService: FamilyService,
    private readonly redisService: RedisService,
    private readonly regimenService: RegimenService,
    private readonly pimAppService: PIMAppService,
  ) {}

  async placeOrder(placeOrderDto: PlaceOrderDto) {
    const returnData = await this.ordersRsaService.placeOrder(placeOrderDto, true);
    return returnData;
  }

  async updatePlaceOrder(orderCode: string, placeOrderDto: PlaceOrderDto) {
    await this.checkEmployeeCanAdjustOrder({
      orderCode,
    });
    const returnData = await this.ordersRsaService.updatePlaceOrder(orderCode, placeOrderDto, true);

    await this.ecomOrderService.update(orderCode, {
      source: placeOrderDto.source,
    });

    return returnData;
  }

  /**
   * @TODO
   *  - Lấy thông tin đơn hàng trên ES
   *  - Thông tin thanh toán
   */
  async getOrderOnEs(orderCode: string, ticketCode?: string): Promise<GetOrderEsRes> {
    const order = await this.ordersRsaService.getOrderOnEs(orderCode, ticketCode);
    const nextAction = checkNextActionForOrder(order, this.loggerServiceLib);
    const ecomOrder = await this.elasticSearchService.getEcomOrderByOrderCode(this.indexEcomOrder, orderCode);
    const depositOnlineInfo = await this.orderDepositOnlineRepository.findOneBy({
      orderCode,
    });
    const returnData = {
      ...order,
      ecomOrder,
      depositOnlineInfo,
      timeLine: timeLineOrder(order),
      nextAction,
    };

    return returnData;
  }

  /**
   * @TODO api get order detail
   * @param orderCode string required
   * @returns
   */
  async orderDetail(orderCode: string) {
    const order = await this.ordersRsaService.orderDetail(orderCode);
    const ecomOrder = await this.elasticSearchService.getEcomOrderByOrderCode(this.indexEcomOrder, orderCode);
    const depositOnlineInfo = await this.orderDepositOnlineRepository.findOneBy({
      orderCode,
    });
    const returnData = {
      ...order,
      ecomOrder,
      depositOnlineInfo,
    };

    return returnData;
  }

  /**
   * @TODO Thực hiện việc tiếp tục mua hàng
   * @DocsModified https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=155513333
   * @TicketModified https://reqs.fptshop.com.vn/browse/FV-9254
   */
  async continueBuying(orderCode: string, employeeInfo?: EmployeeInfoContinueBuyingDto): Promise<ContinueBuyingRes> {
    const orderOMS = await this.omsService.getOneOrder(orderCode);
    if (!orderOMS) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_ORDER_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND),
          validationErrors: null,
        },
        HttpStatus.NOT_FOUND,
      );
    }
    const isOrderAttribute8 = orderOMS?.orderAttribute === 8;
    //FV-17070
    let isByPassRuleForOnlineWebAppOrder = false;
    let resInside: GetEmployeeByCodeResponse[] = null;
    let scheduleRequest: any = null;
    if (OrderChannels.WEB_APP.includes(orderOMS?.orderChanel)) {
      const employeeCode = this.req['user']?.employee_code;
      resInside = await this.insideService.getLeaderEmployeeByCode({
        listEmployeeCode: [employeeCode],
      });
      const results = await this.elasticSearchService.searchScheduleRequest(process.env.ELA_INDEX_SCHEDULE_REQUEST, {
        orderCode,
        type: SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST,
      });
      scheduleRequest = results?.items?.[0] as any;
      isByPassRuleForOnlineWebAppOrder =
        resInside?.length > 0 || (scheduleRequest?.insideCode ? scheduleRequest?.insideCode === employeeCode : true);
    }
    !isOrderAttribute8 &&
      (await this.checkEmployeeCanAdjustOrder({
        orderCode,
        inputOrderDetail: orderOMS,
        dataCheckRuleForOrder: {
          scheduleRequest,
          resInside,
        },
      }));

    const isContinueD2 = isPossibleContinueBuyingByScheduleD2(orderOMS, this.loggerServiceLib)?.isPossible;
    if (
      !isContinueD2 &&
      OrderChannels.RSA_ECOM.includes(orderOMS.orderChanel) &&
      orderOMS.ecomDisplay === EcomDisplay.Save
    ) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_ORDER_CONTINUE_BUYING_NOT_ALLOWED,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CONTINUE_BUYING_NOT_ALLOWED),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CONTINUE_BUYING_NOT_ALLOWED),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    //#region check rule chặn xử lý đơn đặt cọc từ affiliate
    // get payment để check đơn affiliate thanh toán 0đ
    // đơn VT thì call qua payment để check thanh toán 0đ
    let isContinueOrderVT = false;
    if (OrderChannels.RSA_AFFILIATE.includes(orderOMS?.orderChanel)) {
      const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getPaymentRedis({
        paymentCodes: [orderOMS?.paymentRequestCode],
      });
      const totalDepositedAmount = (arrPaymentES?.length && getDepositDetailAmount(arrPaymentES?.at(0)?.detail)) || 0;
      // nếu đơn affiliate và thanh toán cọc 0đ => isContinueOrderVT = true
      // nếu đơn affiliate và thanh toán 100% => isContinueOrderVT = false
      isContinueOrderVT = arrPaymentES?.length && !arrPaymentES?.at(0)?.remainingAmount && totalDepositedAmount === 0;

      // nếu đơn affiliate và isContinueOrderVT = false => báo lỗi
      if (!isContinueOrderVT && arrPaymentES?.length) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_CONTINUE_BUYING_AFFILIATE,
            message: ErrorCode.getError(ErrorCode.RSA_ECOM_CONTINUE_BUYING_AFFILIATE),
            details: ErrorCode.getError(ErrorCode.RSA_ECOM_CONTINUE_BUYING_AFFILIATE),
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    // check trường hợp mới tạo là đơn pre-order, xong thay đổi loại đơn lung tung.
    const preOrderTrans = await this.osrService.getPreOrderTrans({
      orderCodes: [orderCode],
    });

    // nếu đơn !== pre-order và isContinueOrderVT = false => báo lỗi không xử lý đơn ecom
    // nếu đơn !== pre-order và isContinueOrderVT = true => pass rule xử lý tiếp đơn VT
    if (!isByPassRuleForOnlineWebAppOrder && !preOrderTrans.length) {
      if (orderOMS.orderAttribute !== 7 && !isContinueOrderVT && !isOrderAttribute8) {
        if (!OrderChannels.RSA_ECOM.includes(orderOMS.orderChanel)) {
          throw new SystemException(
            {
              code: ErrorCode.RSA_ECOM_PROCESS_OTHER_CHANNEL,
              message: ErrorCode.getError(ErrorCode.RSA_ECOM_PROCESS_OTHER_CHANNEL),
              details: ErrorCode.getError(ErrorCode.RSA_ECOM_PROCESS_OTHER_CHANNEL),
              validationErrors: null,
            },
            HttpStatus.NOT_FOUND,
          );
        }
      }
    }
    // #endregion check rule chặn xử lý đơn đặt cọc từ affiliate

    const dataContinueBuying = await this.ordersRsaService.continueBuying(orderCode, true, employeeInfo);
    const ecomOrder = await this.elasticSearchService.getEcomOrderByOrderCode(this.indexEcomOrder, orderCode);
    const voucher = await this.orderUtilsService.getVoucherByOrderCode(orderCode);
    const tmpReturnData = {
      ...dataContinueBuying?.order,
      voucher,
    };
    dataContinueBuying.order = tmpReturnData;

    const returnData = {
      ...dataContinueBuying,
      ecomOrder,
    };

    return returnData;
  }

  async pushOrder(body: PushsOrderPayloadDto) {
    const order = await this.ordersRsaService.getOrderOnEs(body.orderCode);

    // Check Rule Hàng khang hiếm phải thanh toán rồi mới cho đẩy đơn
    await this.checkRuleRareGood(order, true);

    const nextAction = checkNextActionForOrder(order, this.loggerServiceLib);
    if (
      nextAction === EnumNextActionOrder.SAVE_ORDER &&
      !isPossibleContinueBuyingByScheduleD2(order, this.loggerServiceLib)?.isPossible &&
      order?.ecomDisplay === EcomDisplay.Save
    ) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_ORDER_PUSH_ORDER_NOT_ALLOWED,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSH_ORDER_NOT_ALLOWED),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSH_ORDER_NOT_ALLOWED),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    await this.checkEmployeeCanAdjustOrder({
      orderCode: body.orderCode,
    });
    const res = await this.ordersRsaService.pushOrder(body);
    return res;
  }

  async pushPartialOrder(body: PushsOrderPayloadDto) {
    await this.checkEmployeeCanAdjustOrder({
      orderCode: body.orderCode,
    });
    const res = await this.ordersRsaService.pushPartialOrder(body);
    return res;
  }

  async finishOrder(body: UpdateStatusOrderPayloadDto, orderChanel?: string) {
    await this.logger.log(
      {
        message: 'finishOrder payload',
        fields: {
          url: `finishOrder payload`,
          dataRes: JSON.stringify(body),
        },
      },
      false,
    );
    const { orderCode, modifiedBy, modifiedByName } = body;
    const currentOrder = await this.omsService.getOneOrder(orderCode);
    const isPushOrderFirstTime = currentOrder.ecomDisplay === EcomDisplay.AtOnline;

    if (currentOrder.orderStatus === OrderStatus.Cancel) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_CANCELED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    if (
      OrderChannels.RSA_AFFILIATE.includes(currentOrder.orderChanel) &&
      currentOrder.orderStatus === OrderStatus.FinishDeposit
    ) {
      // do nothing, by pass.
    } else if (currentOrder.orderAttribute !== 7 && currentOrder.ecomDisplay === EcomDisplay.AtShop) {
      // vì pre order sang đơn từng phần và đơn từng phần sang đơn thường nên không check rule nữa
      // const exception: IError = {
      //   code: ErrorCode.RSA_ECOM_ORDER_PUSHED,
      //   message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
      //   details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
      //   validationErrors: null,
      // };
      // throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    // Nếu là đơn hàng đặt cọc online
    if (
      Number(currentOrder.orderAttribute) === 7 &&
      OrderChannels.RSA_ECOM.includes(orderChanel) &&
      isPushOrderFirstTime
    ) {
      if (currentOrder?.totalDeposit > body.totalDepositedAmount) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_PAYMENT_INVALID,
            message: ErrorCode.getError(ErrorCode.RSA_ECOM_PAYMENT_INVALID),
            details: ErrorCode.getError(ErrorCode.RSA_ECOM_PAYMENT_INVALID),
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      const detailAttachments: DetailAttachment[] = JSONPath({
        path: '$.details[*].detailAttachments[*]',
        json: currentOrder,
      });

      const lcvIds: string[] = _.compact(_.uniq(detailAttachments?.map((detail) => detail?.personIdSub)));

      await this.orderRuleEngineService.checkRulePreOrder({
        items: detailAttachments?.map((detail) => ({
          sku: detail?.itemCode,
          quantity: detailAttachments?.filter((entry) => entry?.itemCode === detail?.itemCode)?.length,
        })),
        lcvIds: [lcvIds?.at(0)],
      });

      const response = this.ordersRsaService.updateStatusOmsOrderDeposit(
        {
          orderStatus: OrderStatus.FinishDeposit,
          ecomDisplay: EcomDisplay.AtShop,
          modifiedBy: body.modifiedBy,
          modifiedByName: body.modifiedByName,
          orderCode: body.orderCode,
        },
        currentOrder,
      );

      // Vì đã nằm sau hàm update orderStatus: OrderStatus.FinishDeposit
      // nên chắc chắn là đã thanh toán thành công
      // --> Xóa payment code lưu trong db
      try {
        await this.workerService.removePaymentCodeStored(currentOrder);
      } catch (error) {}

      return {
        isSuccess: true,
        data: response,
      };
    }

    if (!body.paymentInfo?.isPayment) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_PAYMENT_INVALID,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_PAYMENT_INVALID),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_PAYMENT_INVALID),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      await this.omsService.updateStatusPayment(
        orderCode,
        PaymentOnlineStatus.Complete,
        currentOrder.paymentRequestCode,
      );
    } catch (error) {
      let errorContent = '{}';
      try {
        errorContent = JSON.stringify(error);
      } catch (e) {}
      await this.logger.log(
        {
          message: 'HttpException',
          fields: {
            info: `orderCode: ${orderCode} - cannot update status payment`,
            method: `PUT`,
            url: `orderCode: ${orderCode} - cannot update status payment`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: errorContent,
          },
        },
        'HttpException',
        false,
      );
    }

    const getOrder = await this.omsService.getOneOrder(orderCode);

    if (!(await this.ordersRsaService.isFullPaymentOrder(getOrder.totalBill, getOrder.orderPaymentCreate))) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_PAYMENT_NOT_FULL,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    const employeeStep5 = _.orderBy(
      getOrder?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const payload: PayloadUpdatedStatusOrderDto = {
      orderCode: orderCode,
      modifiedBy: employeeStep5?.employeeCode || modifiedBy,
      modifiedByName: employeeStep5?.employeeName || modifiedByName,
      orderStatus: OrderStatus.FinishDeposit,
      ecomDisplay: EcomDisplay.AtShop,
      orderType: getOrder.orderType,
      shopCode: getOrder.shopCode,
    };

    const data = await this.omsService.updateStatusOrderDeposit(payload);

    // Xóa payment code lưu trong db
    try {
      await this.workerService.removePaymentCodeStored(getOrder);
    } catch (error) {}

    // Tạo phiếu khám
    return this.ordersRsaService.handleLogicUpdateStatusOrder(body, getOrder, true, true, getOrder.shopCode);
  }

  /**
   * Clone ra từ finishOrder để sửa lại logic cho đơn từng phần
   */
  async finishPartialOrder(body: UpdateStatusOrderPayloadDto) {
    const { orderCode, modifiedBy, modifiedByName } = body;
    const currentOrder = await this.omsService.getOneOrder(orderCode);

    if (currentOrder.orderStatus === OrderStatus.Cancel) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_ORDER_CANCELED,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_CANCELED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
    // vì pre order sang đơn từng phần và đơn từng phần sang đơn thường nên không check rule nữa
    if (
      currentOrder.ecomDisplay === EcomDisplay.AtShop &&
      !OrderChannels.RSA_AFFILIATE.includes(currentOrder.orderChanel)
    ) {
      // const exception: IError = {
      //   code: ErrorCode.RSA_ECOM_ORDER_PUSHED,
      //   message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
      //   details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_PUSHED),
      //   validationErrors: null,
      // };
      // throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }

    try {
      await this.omsService.updateStatusPayment(
        orderCode,
        PaymentOnlineStatus.Complete,
        currentOrder.paymentRequestCode,
      );
    } catch (error) {
      let errorContent = '{}';
      try {
        errorContent = JSON.stringify(error);
      } catch (e) {}
      await this.logger.log(
        {
          message: 'HttpException',
          fields: {
            info: `orderCode: ${orderCode} - cannot update status payment`,
            method: `PUT`,
            url: `orderCode: ${orderCode} - cannot update status payment`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: errorContent,
          },
        },
        'HttpException',
        false,
      );
    }

    const getOrder = await this.omsService.getOneOrder(orderCode);

    // if (!(await this.ordersRsaService.isFullPaymentOrder(getOrder.totalBill, getOrder.orderPaymentCreate))) {
    //   throw new SystemException(
    //     {
    //       code: ErrorCode.RSA_ECOM_PAYMENT_NOT_FULL,
    //     },
    //     HttpStatus.BAD_REQUEST,
    //   );
    // } // Bỏ check full payment - đơn từng phần không cần full payment để hoàn thành

    const employeeStep5 = _.orderBy(
      getOrder?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
      ['modifiedDate', 'desc'],
    )?.at(0);

    const payload: PayloadUpdatedStatusOrderDto = {
      orderCode: orderCode,
      modifiedBy: employeeStep5?.employeeCode || modifiedBy,
      modifiedByName: employeeStep5?.employeeName || modifiedByName,
      orderStatus: OrderStatus.FinishDeposit,
      ecomDisplay: EcomDisplay.AtShop,
      orderType: getOrder.orderType,
      shopCode: getOrder.shopCode,
    };

    await this.omsService.updateStatusOrderDeposit(payload);

    // Xóa payment code lưu trong db
    try {
      await this.workerService.removePaymentCodeStored(getOrder);
    } catch (error) {}

    // Tạo phiếu khám
    return this.ordersRsaService.handleLogicUpdateStatusPartialOrder(body, getOrder, true, true, getOrder.shopCode);
  }

  async getDetailsByTicketCodeV2(ticketCode: string, getDetailsByTicketCodeDTO: GetDetailsByTicketCodeDTO) {
    const dataReturn = (await this.ordersRsaService.getDetailsByTicketCodeV2(
      ticketCode,
      getDetailsByTicketCodeDTO,
    )) as any;
    const groupItemVaccine = _.groupBy(dataReturn.appointmentSchedulesOrder, (a) => a.regimenId);
    dataReturn.orderDataDetails = [];
    for (const [key, value] of Object.entries(groupItemVaccine)) {
      let totalPrice = 0;
      for (const item of value as any) {
        totalPrice += Number(item.price);
      }
      dataReturn.orderDataDetails.push({
        ...value[0],
        totalPrice,
        itemNumber: (value as any)?.length,
      });
    }

    return dataReturn;
  }

  async finishOrderV2(payload: UpdateStatusOrderPayloadV2Dto, apiKey?: string) {
    let orderInfo = await this.omsService.getOneOrder(payload.orderCode);
    const paymentLists = await this.paymentGWService.getPaymentRedis({
      paymentCodes: [orderInfo.paymentRequestCode],
    });
    const paymentInfo = paymentLists?.at(0);

    if (!paymentInfo?.detail) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_PAYMENT_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_PAYMENT_NOT_FOUND),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_PAYMENT_NOT_FOUND),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    const totalDepositedAmount = getDepositDetailAmount(paymentInfo?.detail);

    // Chặn trường hợp đơn preOrder có trạng thái confirm và có link thanh toán trên Redis nhưng không có tiền
    if (orderInfo?.orderAttribute === OrderAttribute.PRE_ORDER && orderInfo?.orderStatus === OrderStatus.Confirmed) {
      // Danh sách các payment methods cần check
      const paymentMethods = [
        PaymentMethod.TRANSFER,
        PaymentMethod.ALEPAY,
        PaymentMethod.MOMO,
        PaymentMethod.ZALO_PAY,
        PaymentMethod.HOME_PAY_LATER,
        PaymentMethod.VNPAY,
      ];

      let hasPaymentLinkWithoutMoney = false;
      let paymentLinkInfo = null;

      // Sử dụng pipeline để tối ưu hiệu suất Redis
      const redisClient = this.redisService.getRedisClient();
      const pipeline = redisClient.pipeline();

      // Tạo danh sách keys cần check
      const keysToCheck = [];
      for (const method of paymentMethods) {
        const methodName = convertPaymentMethodToName(method);
        const createdPaymentLinkKey = `CreatedPaymentLink:${methodName}:${payload.orderCode}`;
        const paymentLinkKey = `PaymentLink:${methodName}:${payload.orderCode}`;

        keysToCheck.push({
          method,
          methodName,
          createdPaymentLinkKey,
          paymentLinkKey,
        });

        // Thêm các lệnh GET vào pipeline
        pipeline.get(createdPaymentLinkKey);
        pipeline.get(paymentLinkKey);
      }

      // Thực thi pipeline
      const results = await pipeline.exec();

      // Xử lý kết quả
      for (let i = 0; i < keysToCheck.length; i++) {
        const keyInfo = keysToCheck[i];
        const startIndex = i * 2; // Mỗi method có 2 keys

        const createdPaymentLink = results[startIndex]?.[1]; // [error, result]
        const paymentLink = results[startIndex + 1]?.[1];

        if (createdPaymentLink || paymentLink) {
          // Get TTL để biết thời gian còn lại
          let ttlSeconds = -1;
          let activeKey = '';

          if (createdPaymentLink) {
            ttlSeconds = await redisClient.ttl(keyInfo.createdPaymentLinkKey);
            activeKey = keyInfo.createdPaymentLinkKey;
          } else if (paymentLink) {
            ttlSeconds = await redisClient.ttl(keyInfo.paymentLinkKey);
            activeKey = keyInfo.paymentLinkKey;
          }

          paymentLinkInfo = {
            method: keyInfo.methodName,
            hasLink: true,
            ttlSeconds,
            expiredInMinutes: ttlSeconds > 0 ? Math.ceil(ttlSeconds / 60) : 0,
            activeKey,
          };

          // Nếu có link thanh toán nhưng không có tiền
          if (totalDepositedAmount <= 0) {
            hasPaymentLinkWithoutMoney = true;
            break;
          }
        }
      }

      // Nếu có link thanh toán trên Redis nhưng không có tiền thì throw error
      if (hasPaymentLinkWithoutMoney && paymentLinkInfo) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_PAYMENT_INVALID,
            message: `Đơn đã gửi link thanh toán. Vui lòng đồng bộ thông tin trước khi đẩy đơn, hoặc chờ ${paymentLinkInfo.expiredInMinutes} phút nếu khách hàng không thanh toán.`,
            details: `Đơn đã gửi link thanh toán. Vui lòng đồng bộ thông tin trước khi đẩy đơn, hoặc chờ ${paymentLinkInfo.expiredInMinutes} phút nếu khách hàng không thanh toán.`,
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    if (
      totalDepositedAmount &&
      orderInfo?.orderAttribute === OrderAttribute.PRE_ORDER &&
      orderInfo?.orderStatus === OrderStatus.Confirmed
    ) {
      // update totalDeposit
      await this.omsService.updateTotalDeposited(payload.orderCode, {
        orderCode: payload.orderCode,
        totalDeposit: totalDepositedAmount,
        modifiedBy: orderInfo?.modifiedBy || orderInfo?.createdBy,
      });
      orderInfo = await this.omsService.getOneOrder(payload.orderCode);
    }

    if (orderInfo.orderAttribute === 4) {
      if (totalDepositedAmount <= 0) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_PAYMENT_INVALID,
            message: ErrorCode.getError(ErrorCode.RSA_ECOM_PAYMENT_INVALID),
            details: ErrorCode.getError(ErrorCode.RSA_ECOM_PAYMENT_INVALID),
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
      return this.finishOrderV3(payload);
    }

    const { orderCode } = payload;
    const orderOnline = await this.orderDepositOnlineRepository.findOneBy({
      orderCode,
    });
    if (!orderOnline) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_ORDER_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND),
          details: '',
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // assign old headers
    this.req.headers['shop-code'] = orderOnline.headerData?.shopCode || '';
    this.req.headers['order-channel'] = orderOnline.headerData?.orderChannel || '';
    return this.finishOrder(
      { ...orderOnline.depositData, paymentInfo, totalDepositedAmount },
      OrderChannels.RSA_ECOM[0],
    );
  }

  /**
   * Giống hệt v2, nhưng clone ra giành riêng cho đơn từng phần.
   * Để khi sửa luồng liên quan đến đơn từng phần không ảnh hưởng đến luồng chung.
   */
  async finishOrderV3(payload: UpdateStatusOrderPayloadV2Dto) {
    // giành cho đơn pre-order
    const { orderCode, isPayment } = payload;
    const orderOnline = await this.orderDepositOnlineRepository.findOneBy({
      orderCode,
    });
    if (!orderOnline) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_ORDER_NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_NOT_FOUND),
          details: '',
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // assign old headers
    this.req.headers['shop-code'] = orderOnline.headerData?.shopCode || '';
    this.req.headers['order-channel'] = orderOnline.headerData?.orderChannel || '';

    return this.finishPartialOrder(orderOnline.depositData);
  }

  /**
   * @TODO check status order
   * @param orderCode string required
   * @returns
   */
  async checkStatusPushOrder(orderCode: string) {
    const order = await this.omsService.getOneOrder(orderCode);

    // https://reqs.fptshop.com.vn/browse/FV-11102
    if (OrderChannels.RSA_AFFILIATE.includes(order.orderChanel) && order.orderStatus === OrderStatus.FinishDeposit) {
      return false;
    }

    if (order.orderStatus === OrderStatus.FinishDeposit && order?.orderAttribute !== 7) {
      return false;
    }

    let phaseId;
    if (order.orderAttribute === 7) {
      // const arrSku: string[] = JSONPath({
      //   json: order,
      //   path: '$.details[*]..itemCode',
      // });
      // const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({ listSku: arrSku });
      // const phase1 = osrDepositAmountBySku?.find((e) => e?.phaseId === 1);
      // const isPhase1 = moment(moment().format('YYYY-MM-DD')).isBetween(
      //   moment(phase1?.fromDate).format('YYYY-MM-DD'),
      //   moment(phase1?.toDate).format('YYYY-MM-DD'),
      //   undefined,
      //   '[]',
      // );
      // if (isPhase1 && phase1) {
      //   phaseId = 1;
      // }

      // const phase2 = osrDepositAmountBySku?.find((e) => e.phaseId === 2);
      // const isPhase2 = moment(moment().format('YYYY-MM-DD')).isBetween(
      //   moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
      //   moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
      //   undefined,
      //   '[]',
      // );
      // if (isPhase2 && phase2) {
      //   phaseId = 2;
      // }

      // if (!phaseId) {
      //   throw new SystemException(
      //     {
      //       code: ErrorCode.PRE_ORDER_PHASE_ISSUES,
      //       message: ErrorCode.getError(ErrorCode.PRE_ORDER_PHASE_ISSUES),
      //       details: ErrorCode.getError(ErrorCode.PRE_ORDER_PHASE_ISSUES),
      //       validationErrors: null,
      //     },
      //     HttpStatus.BAD_REQUEST,
      //   );
      // }
      if (order.orderStatus === OrderStatus.Completed) {
        return true;
      }

      if (
        order.orderStatus === OrderStatus.FinishDeposit &&
        !order?.orderPaymentCreate?.filter((e) => e.paymentType === 1 && e.paymentStatus === 1)?.length
      ) {
        return true;
      }

      return false;
    }
    if (order?.orderStatus !== OrderStatus.Confirmed) {
      return true;
    }
    return false;
  }

  /**
   * @TODO check next action order
   * @param orderCode string required
   * @returns
   */
  async checkNextActionOrder(orderCode: string) {
    const order = await this.ordersRsaService.getOrderOnEs(orderCode);
    return {
      nextAction: checkNextActionForOrder(order, this.loggerServiceLib),
    };
  }

  /**
   * @TODO save data deposit online
   * @param orderCode string required
   * @returns
   */
  async saveOrderDepositOnline(dataBody: UpdateStatusOrderPayloadDto) {
    const order = await this.orderDepositOnlineRepository.findOneBy({
      orderCode: dataBody.orderCode,
    });

    let dataCreate = {
      orderCode: dataBody.orderCode,
      depositData: dataBody,
      headerData: {
        shopCode: this.req.headers?.['shop-code'] as string,
        orderChannel: this.req.headers?.['order-channel'] as string,
        authorization: this.req.headers?.['authorization'] as string,
      },
    };
    if (order) {
      dataCreate = {
        ...order,
        ...dataCreate,
      };
    }

    return await this.orderDepositOnlineRepository.save(plainToInstance(OrderDepositOnline, dataCreate));
  }

  /**
   * @TODO check only user that created and sale leader can adjust order
   * @param orderCode string required
   * @returns
   */
  async checkEmployeeCanAdjustOrder({
    orderCode,
    inputOrderDetail,
    dataCheckRuleForOrder,
  }: {
    orderCode: string;
    inputOrderDetail?: GetOneOrderLibResponse;
    dataCheckRuleForOrder?: {
      resInside?: GetEmployeeByCodeResponse[];
      scheduleRequest?: any;
    };
  }) {
    const employeeCode = this.req['user']?.employee_code;
    if (!employeeCode) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_NO_RIGHT_TO_ADJUST_ORDER,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_RIGHT_TO_ADJUST_ORDER),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_RIGHT_TO_ADJUST_ORDER),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    const resInside = dataCheckRuleForOrder?.resInside
      ? dataCheckRuleForOrder?.resInside
      : await this.insideService.getLeaderEmployeeByCode({
          listEmployeeCode: [employeeCode],
        });
    let orderDetail;
    if (inputOrderDetail) {
      orderDetail = inputOrderDetail;
    } else {
      orderDetail = await this.omsService.getOneOrder(orderCode);
    }

    // const check = resInside?.find((e) => e.employeeCode === employeeCode);
    // Nếu người tạo đơn hoặc leader thì cho đi tiếp xử lý đơn
    // Nếu đơn cọc thì cho đi tiếp
    if (
      resInside?.length > 0 ||
      employeeCode === orderDetail?.createdBy ||
      OrderChannels.RSA_AFFILIATE.includes(orderDetail?.orderChanel)
    ) {
      return true;
    }

    //FV-17070
    if (OrderChannels.WEB_APP.includes(orderDetail?.orderChanel)) {
      const results = dataCheckRuleForOrder?.scheduleRequest
        ? {
            items: [dataCheckRuleForOrder?.scheduleRequest],
          }
        : await this.elasticSearchService.searchScheduleRequest(process.env.ELA_INDEX_SCHEDULE_REQUEST, {
            orderCode,
            type: SCHEDULE_REQUEST_TYPE.ONLINE_ORDER_REQUEST,
          });
      const scheduleRequest = results?.items?.[0] as any;
      const isContinue =
        resInside?.length > 0 || (scheduleRequest?.insideCode ? scheduleRequest?.insideCode === employeeCode : true);
      if (isContinue) {
        return true;
      }
    }

    throw new SystemException(
      {
        code: ErrorCode.RSA_ECOM_NO_RIGHT_TO_ADJUST_ORDER,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_RIGHT_TO_ADJUST_ORDER),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_NO_RIGHT_TO_ADJUST_ORDER),
        validationErrors: null,
      },
      HttpStatus.BAD_REQUEST,
    );
  }

  async saveEcomOrderEarly(payload: SaveOrderDto) {
    const { orderCode, modifiedBy, modifiedByName } = payload;
    const order = await this.omsService.getOneOrder(orderCode);

    // Check Rule Hàng khang hiếm phải thanh toán rồi mới cho đẩy đơn
    await this.checkRuleRareGood(order, false);

    const nextAction = checkNextActionForOrder(order as any, this.loggerServiceLib);
    if (nextAction !== EnumNextActionOrder.SAVE_ORDER || order.ecomDisplay === EcomDisplay.Save) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_ORDER_SAVE_ORDER_NOT_ALLOWED,
          message: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_SAVE_ORDER_NOT_ALLOWED),
          details: ErrorCode.getError(ErrorCode.RSA_ECOM_ORDER_SAVE_ORDER_NOT_ALLOWED),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.omsService.updateEcomDisplay({
      orderCode,
      ecomDisplay: EcomDisplay.Save,
      modifiedBy,
      shopCode: order.shopCode,
    });

    const allDetailsAttachments: DetailAttachment[] = JSONPath({
      path: `$.details[*].detailAttachments[*]`,
      json: order,
    });

    const regimens: RegimenItem[] = [];
    const createTicketDto = await this.redisService.get<Array<CreateTicketDto>>(
      `${TICKET_CREATE_KEY}:${order?.orderCode}`,
    );
    const regimenIds = createTicketDto?.at(0)?.schedules?.map((scheduleItem) => scheduleItem.regimenId);
    if (regimenIds.length) {
      const regimenMap = await this.regimenService.getRegimenByIds({
        regimenIds,
      });
      regimens.push(...regimenMap);
    }

    const [products, familyByCusCode, familyByPersonIdSubs] = await concurrentPromise(
      this.pimAppService.getListProductBySku(allDetailsAttachments.map((dItem) => dItem.itemCode)),
      this.familyService.getPersonByCustId(order.custCode),
      this.familyService.getManyByLcvId({
        lcvId: _.compact(_.uniq(allDetailsAttachments.map((itemDetail) => itemDetail.personIdSub))),
      }),
    );

    // const products = await this.pimAppService.getListProductBySku(allDetailsAttachments.map((dItem) => dItem.itemCode));

    // // nguoi mua
    // const familyByCusCode = await this.familyService.getPersonByCustId(order.custCode);

    // // nguoi tiem
    // const familyByPersonIdSubs = await this.familyService.getManyByLcvId({
    //   lcvId: _.compact(_.uniq(allDetailsAttachments.map((itemDetail) => itemDetail.personIdSub))),
    // });

    return this.scheduleService.saveEcomScheduleReminderEarly({
      items: allDetailsAttachments.map((item) => {
        const familyByPersonFound = familyByPersonIdSubs.find((familyItem) => familyItem.lcvId === item.personIdSub);
        const scheduleFoundRedis = createTicketDto?.at(0)?.schedules?.find((ctdItem) => ctdItem.sku === item.itemCode);
        const regimenFound = regimens?.find((regimenItem) => regimenItem.id === scheduleFoundRedis?.regimenId);
        const productDetail = products.listProduct.find((proItem) => proItem.sku === item.itemCode);
        return {
          shopCode: order.shopCode,
          shopName: order.shopName,
          appointmentDate: item.appointmentDate as string,
          orderCode: order.orderCode,
          orderDetailAttachmentCode: item.orderDetailAttachmentCode,
          orderDetailAttachmentID: item.id,
          ecomProcessStatus: false,
          status: 0,
          isPaid: false,
          ticketCode: '',
          ecomProcessBy: order?.createdBy,
          ecomProcessName: order?.createdByName,
          note: '',
          ecomProcessNote: '',
          injections: item.orderInjection,
          sku: item.itemCode,
          skuName: item.itemName,
          lcvIdMain: familyByCusCode?.items?.at(0)?.lcvId,
          lcvNameMain: familyByCusCode?.items?.at(0)?.name,
          lcvPhoneNumberMain: familyByCusCode?.items?.at(0)?.phoneNumber,
          lcvName: familyByPersonFound?.name, //
          lcvPhoneNumber: familyByPersonFound?.phoneNumber,
          sourceId: 7,
          needAdvice: false,
          calendarType: 0,
          lcvId: item.personIdSub,
          origin: productDetail?.brandOrigin,
          packageId: null,
          packageName: null,
          vacOrderId: null,
          receptionistCode: modifiedBy,
          receptionistName: modifiedByName,
          personId: familyByPersonFound?.familyProfileDetails?.find((fItem) => fItem.lcvId === item.personIdSub)
            ?.personId,
          injectorId: familyByPersonFound?.familyProfileDetails?.find((fItem) => fItem.lcvId === item.personIdSub)
            ?.personId,
          taxonomies: regimenFound?.diseaseGroup?.disease,
          diseaseName: regimenFound?.diseaseGroup?.disease,
          regimenID: regimenFound?.id,
          unitCodeSale: item.unitCode,
          unitNameSale: item.unitName,
        };
      }),
    });
  }

  async checkRuleRareGood(order: GetOneOrderLibResponse, isPushOrder = false) {
    if (order?.orderStatus !== OrderStatus.Confirmed) return;

    const listSku: string[] = _.compact(
      _.uniq(
        JSONPath({
          path: '$.details[*].detailAttachments[*].itemCode',
          json: order,
        }),
      ),
    );

    if (!listSku?.length) return;
    const { listProduct } = await this.pimAppService.getListProductBySku(listSku);
    // Check trong listProduct có hàng khang hiếm không
    const productRareGood = listProduct?.find((i) => i?.attributeShop?.attributeOptionId === ITEM_CODE_HANG_HIEM);
    const isRareGood = !!productRareGood || false;
    const productName = productRareGood?.name || '';

    if (!isRareGood) return;

    const code = isPushOrder
      ? ErrorCode.RSA_PREVENTION_PUSH_ORDER_RARE_GOOD
      : ErrorCode.RSA_PREVENTION_SAVE_ORDER_RARE_GOOD;
    const messageError = isPushOrder
      ? ErrorCode.getError(ErrorCode.RSA_PREVENTION_PUSH_ORDER_RARE_GOOD)?.replace('{name}', productName)
      : ErrorCode.getError(ErrorCode.RSA_PREVENTION_SAVE_ORDER_RARE_GOOD)?.replace('{name}', productName);
    throw new SystemException(
      {
        code: code,
        message: messageError,
        details: messageError,
        validationErrors: null,
      },
      HttpStatus.FORBIDDEN,
    );
  }

  private convertPaymentMethodIdForWebApp(method: number) {
    if (method === 25) {
      return 5; // 25: Web app => 5: Online payment
    }

    return method;
  }
}
