import { RedisModule } from '@liaoliaots/nestjs-redis';
import { Module, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AuthGuard,
  BadRequestExceptionFilter,
  DatabaseConfigService,
  envValidator,
  HttpExceptionFilter,
  LoggingInterceptor,
  RedisConfigService,
  TimeoutInterceptor,
  TransformInterceptor,
  WinstonLoggerConfigService,
} from '@shared';
import { AllExceptionsFilter } from '@shared/common/filters/all-exception.filter';
import { S3ConfigService } from '@shared/config/s3.config';
import { HealthzModule } from '@shared/modules/healthz/health.module';
import { S3Module } from 'ict-nest-s3';
import { OrderPromissingWrapModule } from '@libs/modules/order-promissing-wrap/order-promissing-wrap.module';
import { NotesModule } from '@libs/modules/notes/notes.module';
import { InsidesModule } from 'modules/modules/modules/insides/insides.module';
import { OrdersModule } from 'modules/modules/modules/orders/orders.module';
import { WinstonModule } from 'nest-winston';
import { OrdersModule as AffiliateOrdersModule } from './modules/orders/orders.module';
import { DepositCancelAffiliateModule } from './modules/deposit-cancel/depost-cancel.module';
import { CustomersModule } from 'modules/modules/modules/customers/customers.module';
import { HistoryModule } from 'modules/modules/modules/history/history.module';
import { SchedulesModule } from 'modules/modules/modules/schedules/schedules.module';
import { AdministrativeModule } from 'modules/modules/modules/administrative/administrative.module';
import { TicketModule } from 'modules/modules/modules/ticket/ticket.module';
import { JourneyModule } from 'modules/modules/modules/journey/journey.module';
import { VouchersModule } from 'modules/modules/modules/vouchers/vouchers.module';
import { RegimensModule } from 'modules/modules/modules/regimens/regimens.module';
import { LcPrintCenterModule } from './modules/lc-print-center/lc-print-center.module';
import { ReasonsModule } from '@libs/modules/reasons/reasons.module';
import { CartPricingModule } from '@libs/modules/cart-pricing/cartPricing.module';
import { ConsultantsModule } from './modules/consultants/consultants.module';
import { PrintCenterModule } from '@libs/modules/print-center/printCenter.module';
import { GameUtopModule } from '@libs/modules/game-utop/game-utop.module';
import { EmailOtpModule } from '@libs/modules/email-otp/email-otp.module';
import { PaymentModule } from './modules/payment/payment.module';
import { CashbackModule } from 'modules/modules/modules/cashback/cashback.module';
import { ReportModule } from '@libs/modules/reports/reports.module';
import { ProductsModule } from '@libs/modules/products/products.module';
import { OsrModule } from '@libs/modules/osr/osr.module';
import { LoyaltyModule } from '@libs/modules/loyalty/loyalty.module';
import { SurveysModule } from './modules/surveys/surveys.module';
import { RecommendationsModule } from './modules/recommendations/recommendations.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validate: envValidator,
    }),
    WinstonModule.forRootAsync({
      useClass: WinstonLoggerConfigService,
    }),
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfigService,
    }),
    RedisModule.forRootAsync({
      useClass: RedisConfigService,
    }),
    S3Module.forRootAsync({
      useClass: S3ConfigService,
    }),
    DepositCancelAffiliateModule,
    AffiliateOrdersModule, //Phải để AffiliateOrdersModule trước ConsultantsModule
    ConsultantsModule,
    OrdersModule,
    PaymentModule,
    HealthzModule.forRootAsync(['http-checker', 'typeOrm-checker']),
    // InsideAffiliateModule,
    InsidesModule,
    TicketModule,
    LcPrintCenterModule,
    LoyaltyModule,
    JourneyModule,
    AdministrativeModule,
    RegimensModule,
    CustomersModule,
    HistoryModule,
    SchedulesModule,
    OrderPromissingWrapModule,
    NotesModule,
    VouchersModule,
    ReasonsModule,
    CartPricingModule,
    PrintCenterModule,
    GameUtopModule,
    EmailOtpModule,
    CashbackModule,
    ReportModule,
    ProductsModule,
    OsrModule,
    SurveysModule,
    RecommendationsModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useValue: new ValidationPipe({
        whitelist: false,
        skipMissingProperties: false,
        transform: true,
      }),
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TimeoutInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: BadRequestExceptionFilter,
    },
  ],
})
export class AppModule {}
