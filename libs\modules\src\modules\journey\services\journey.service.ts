import { Injectable } from '@nestjs/common';
import {
  GetJourneyByOrderCodeDto,
  JourneyService as JourneyCoreService,
  UpdateGiftDeliveryDto,
} from 'vac-nest-journey';

@Injectable()
export class JourneyLocalService {
  constructor(private readonly journeyCoreService: JourneyCoreService) {}

  async getJourneyIdByTicketCode(ticketCode: string) {
    const res = await this.journeyCoreService.getJourneyIdByTicketCode({ ticketCode: ticketCode });
    if (res.length < 0) {
      return {
        journeyId: '',
      };
    }
    return {
      journeyId: res.at(0)?.id,
    };
  }

  async getJourneyByOrderCode(orderCode: string) {
    const res = await this.journeyCoreService.getJourneyByOrderCode({ orderCode });
    return {
      journey: res || null,
    };
  }

  /**
   * @description cập nhật trạng thái quà tặng
   */
  async updateGiftDelivery(body: UpdateGiftDeliveryDto) {
    return await this.journeyCoreService.updateGiftDelivery(body);
  }
}
