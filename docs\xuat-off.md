# Xuất Off Module Documentation

## Overview

Xuất Off module là module quản lý xuất off từ hệ thống RSA V2. Module này cho phép quản lý quy trình xuất off cho các đơn hàng.

## Database Structure

### 1. Xuat Off (xuat_off)

<PERSON><PERSON>ng chính lưu thông tin phiếu xuất off:

```sql
- id: uuid (PK)                -- Primary key, auto-generated UUID
- xo_code: varchar(100)        -- M<PERSON> phiếu xuất off
- order_code_ecom: varchar(100) -- Mã đơn ecom
- order_ecom_create_date: timestamp
- order_ecom_push_date: timestamp
- order_ecom_complete_date: timestamp
- images: text[]              -- Danh sách hình ảnh
- files: text[]               -- Danh sách files
- notes: text                 -- Ghi chú
- reason_code: varchar(100)   -- Mã lý do
- reason_name: varchar(100)   -- Tên lý do
- status: int                 -- <PERSON>r<PERSON><PERSON> thái (1: <PERSON><PERSON>ử lý, 2: <PERSON><PERSON> lần 1, 3: <PERSON><PERSON> du<PERSON> lần 2, 4: <PERSON><PERSON> chố<PERSON>, 5: <PERSON><PERSON> hủy)
- type: int                   -- Loại (1: Manual, 2: Auto Satellite)
- is_delete: boolean         -- Đánh dấu xóa
- customer_phone: varchar(20) -- SĐT khách hàng
- customer_name: varchar(100) -- Tên khách hàng
- created_by: varchar(100)    -- Mã người tạo
- created_by_name: varchar(100) -- Tên người tạo
- modified_by: varchar(100)   -- Mã người sửa
- modified_by_name: varchar(100) -- Tên người sửa
- user_roles: text[]         -- Danh sách vai trò người dùng
- order_ecom_created_by: varchar(100) -- Mã người tạo đơn ecom
- order_ecom_created_by_name: varchar(100) -- Tên người tạo đơn ecom
- created_date: timestamp    -- Ngày tạo
- modified_date: timestamp   -- Ngày sửa
- deleted_date: timestamp    -- Ngày xóa
```

### 2. Xuat Off Detail (xuat_off_detail)

Chi tiết các đơn hàng trong phiếu xuất:

```sql
- id: uuid (PK)              -- Primary key, auto-generated UUID
- xo_id: uuid (FK)           -- Link to xuat_off
- xo_code: varchar(100)      -- Mã phiếu xuất off
- order_code_shop: varchar(100)  -- Mã đơn tại shop
- order_shop_create_date: timestamp
- order_shop_complete_date: timestamp
- customer_phone: varchar(20)
- customer_name: varchar(100)
- type: enum                 -- Loại (0: Pull Order, 1: Hand Entry)
- is_delete: boolean
- status: int               -- Trạng thái
- reason_cancel: text       -- Lý do hủy
- created_by: varchar(100)
- created_by_name: varchar(100)
- modified_by: varchar(100)
- modified_by_name: varchar(100)
- created_date: timestamp
- modified_date: timestamp
- deleted_date: timestamp
```

### 3. Xuat Off Step Info (xuat_off_step_info)

Lưu lịch sử các bước xử lý:

```sql
- id: uuid (PK)              -- Primary key, auto-generated UUID
- xo_id: uuid (FK)           -- Link to xuat_off
- xo_code: varchar(100)      -- Mã phiếu xuất off
- employee_code: varchar(100) -- Mã nhân viên xử lý
- employee_name: varchar(100) -- Tên nhân viên xử lý
- step: int                  -- Bước xử lý
- notes: text                -- Ghi chú
- created_date: timestamp
- modified_date: timestamp
- deleted_date: timestamp
```

### 4. Xuat Off Approve Flow (xuat_off_approve_flow)

Cấu hình quy trình phê duyệt:

```sql
- id: uuid (PK)              -- Primary key, auto-generated UUID
- creator_role: varchar      -- Vai trò người tạo
- approver_role: varchar     -- Vai trò người duyệt
- status_xo: int            -- Trạng thái xử lý
- is_active: boolean        -- Còn hiệu lực
- created_date: timestamp
- modified_date: timestamp
- deleted_date: timestamp
```

## Database Diagram

```mermaid
erDiagram
    XUAT_OFF {
        uuid id PK
        string xo_code
        string order_code_ecom
        timestamp order_ecom_create_date
        timestamp order_ecom_push_date
        timestamp order_ecom_complete_date
        string[] images
        string[] files
        string notes
        string reason_code
        string reason_name
        int status
        int type
        boolean is_delete
        string customer_phone
        string customer_name
        string created_by
        string created_by_name
        string modified_by
        string modified_by_name
        string[] user_roles
        string order_ecom_created_by
        string order_ecom_created_by_name
        timestamp created_date
        timestamp modified_date
        timestamp deleted_date
    }

    XUAT_OFF_DETAIL {
        uuid id PK
        uuid xo_id FK
        string xo_code
        string order_code_shop
        timestamp order_shop_create_date
        timestamp order_shop_complete_date
        string customer_phone
        string customer_name
        enum type
        boolean is_delete
        int status
        string reason_cancel
        string created_by
        string created_by_name
        string modified_by
        string modified_by_name
        timestamp created_date
        timestamp modified_date
        timestamp deleted_date
    }

    XUAT_OFF_STEP_INFO {
        uuid id PK
        uuid xo_id FK
        string xo_code
        string employee_code
        string employee_name
        int step
        string notes
        timestamp created_date
        timestamp modified_date
        timestamp deleted_date
    }

    XUAT_OFF_APPROVE_FLOW {
        uuid id PK
        string creator_role
        string approver_role
        int status_xo
        boolean is_active
        timestamp created_date
        timestamp modified_date
        timestamp deleted_date
    }

    XUAT_OFF ||--o{ XUAT_OFF_DETAIL : "has"
    XUAT_OFF ||--o{ XUAT_OFF_STEP_INFO : "has"

```

## Core Features

1. Quản lý Xuất Off

- Tạo phiếu xuất off
- Cập nhật phiếu xuất off
- Duyệt/từ chối phiếu xuất off
- Hủy phiếu xuất off
- Tìm kiếm phiếu xuất off

2. Quản lý chi tiết xuất off

- Thêm/sửa/xóa chi tiết xuất off
- Quản lý trạng thái của từng chi tiết
- Theo dõi lịch sử thay đổi

## API Endpoints

### 1. Xuất Off APIs (v1)

```http
# Tạo mới xuất off
POST /api/v1/xuat-off/create

# Tìm kiếm danh sách xuất off
GET /api/v1/xuat-off/search

# Lấy chi tiết một xuất off
GET /api/v1/xuat-off/detail/:id

# Cập nhật xuất off
PATCH /api/v1/xuat-off/update/:id

# Xóa xuất off
DELETE /api/v1/xuat-off/:id

# Xác minh đơn hàng có thể tạo xuất off
POST /api/v1/xuat-off/verify-order/:orderCode

# Lấy danh sách đơn xuất off theo số điện thoại
POST /api/v1/xuat-off/orders-info

# Kéo đơn hàng tay ở shop
GET /api/v1/xuat-off/order/:orderCode

# Hủy xuất off
PATCH /api/v1/xuat-off/cancel/:id

# Duyệt nhiều xuất off
POST /api/v1/xuat-off/approve-multiple

# Từ chối nhiều xuất off
POST /api/v1/xuat-off/reject-multiple
```

### 2. Xuất Off Detail APIs (v1)

```http
# Tạo chi tiết xuất off
POST /api/v1/xuat-off-detail

# Lấy danh sách chi tiết xuất off
GET /api/v1/xuat-off-detail

# Lấy chi tiết xuất off theo id
GET /api/v1/xuat-off-detail/:id

# Lấy chi tiết theo mã xuất off
GET /api/v1/xuat-off-detail/by-xo/:xoId

# Cập nhật chi tiết
PATCH /api/v1/xuat-off-detail/:id

# Xóa chi tiết
DELETE /api/v1/xuat-off-detail/:id
```

### 3. Xuất Off Step APIs (v1)

```http
# Tạo bước xử lý mới
POST /api/v1/xuat-off-step

# Lấy danh sách các bước
GET /api/v1/xuat-off-step

# Lấy thông tin bước theo id
GET /api/v1/xuat-off-step/:id

# Cập nhật bước
PUT /api/v1/xuat-off-step/:id

# Xóa bước
DELETE /api/v1/xuat-off-step/:id
```

## Workflow Status

```typescript
enum XuatOffStatus {
  Pending = 1, // Chờ xử lý
  ApprovedByLeaderOrManager = 2, // Đã duyệt lần 1
  ApprovedByNVVH = 3, // Đã duyệt lần 2
  Rejected = 4, // Từ chối
  Cancelled = 5, // Đã hủy
}
```

## Error Handling

Module sử dụng SystemException để xử lý các lỗi nghiệp vụ:

```typescript
// Các mã lỗi chính
- XO_UPDATE_ONLY_PENDING: Chỉ được cập nhật khi ở trạng thái chờ xử lý
- XO_CAN_CANCEL_ONLY_PENDING: Chỉ được hủy khi ở trạng thái chờ xử lý
- XO_APPROVE_ROLE_NOT_ALLOWED: Không có quyền duyệt/từ chối
- XO_INVALID_STATUS_TRANSITION: Chuyển đổi trạng thái không hợp lệ
- XO_EXIST: Đã tồn tại xuất off cho đơn hàng
- XO_DETAIL_EXIST: Chi tiết đã tồn tại trong xuất off khác
```

## API Details

### 1. Create Xuat Off

```http
POST /api/v1/xuat-off/create
```

Request headers:

```
Authorization: Bearer {token}
Content-Type: application/json
```

Request body:

```json
{
  "orderCodeEcom": "ECO123456789", // Mã đơn ecom
  "orderEcomCreateDate": "2023-12-20", // Ngày tạo đơn ecom
  "orderEcomPushDate": "2023-12-20", // Ngày đẩy đơn ecom
  "orderEcomCompleteDate": "2023-12-20", // Ngày hoàn thành đơn ecom
  "images": ["url1", "url2"], // Danh sách hình ảnh
  "files": ["url1", "url2"], // Danh sách files
  "notes": "Ghi chú", // Ghi chú
  "reasonCode": "REASON_01", // Mã lý do
  "reasonName": "Lý do xuất", // Tên lý do
  "customerPhone": "0123456789", // SĐT khách hàng
  "customerName": "Nguyễn Văn A", // Tên khách hàng
  "details": [
    // Chi tiết xuất off
    {
      "orderCodeShop": "SHOP123", // Mã đơn tại shop
      "orderShopCreateDate": "2023-12-20", // Ngày tạo đơn shop
      "orderShopCompleteDate": "2023-12-20", // Ngày hoàn thành đơn shop
      "customerPhone": "0123456789", // SĐT khách hàng
      "customerName": "Nguyễn Văn A", // Tên khách hàng
      "type": 0 // Loại (0: Pull Order, 1: Hand Entry)
    }
  ]
}
```

Response (200):

```json
{
  "id": "uuid-123",
  "xoCode": "XO123456789",
  "status": 1,
  "orderCodeEcom": "ECO123456789",
  "customerName": "Nguyễn Văn A",
  "customerPhone": "0123456789",
  "type": 1,
  "createdDate": "2023-12-20T10:00:00Z",
  "createdBy": "EMP123",
  "createdByName": "Người tạo",
  ...,  // Các trường khác của xuất off
  "details": [
    {
      "id": "uuid-456",
      "orderCodeShop": "SHOP123",
      "status": 1,
      "customerName": "Nguyễn Văn A",
      "customerPhone": "0123456789",
      ...  // Các trường khác của chi tiết
    },
    ...  // Có thể có nhiều chi tiết
  ],
  "steps": [
    {
      "id": "uuid-789",
      "step": 1,
      "employeeCode": "EMP123",
      "employeeName": "Người tạo",
      "notes": "Ghi chú",
      ...  // Các trường khác của step
    },
    ...  // Có thể có nhiều steps
  ]
}
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: POST /api/v1/xuat-off/create
    activate RSA-ECOM

    RSA-ECOM->>OMSService: getOneOrder(orderCode)
    activate OMSService
    OMSService-->>RSA-ECOM: order
    deactivate OMSService

    RSA-ECOM->>RSA-ECOM: verify order channel & display

    RSA-ECOM->>OMSService: getListOrderES()
    activate OMSService
    OMSService-->>RSA-ECOM: orders
    deactivate OMSService

    RSA-ECOM->>IAMService: getUserRPFByUserName()
    activate IAMService
    IAMService-->>RSA-ECOM: userRoles
    deactivate IAMService

    RSA-ECOM->>RSA-ECOM: Create XuatOffStepInfoEntity
    RSA-ECOM->>RSA-ECOM: Create XuatOffEntity

    RSA-ECOM->>XuatOffRepository: create(xuatOff)
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: result
    deactivate XuatOffRepository

    RSA-ECOM-->>FE: XuatOffResponseDto
    deactivate RSA-ECOM
```

Error responses:

```json
// 400 Bad Request
{
  "code": "XO_INVALID_INPUT",
  "message": "Invalid input data",
  "details": "Order code is required"
}

// 403 Forbidden
{
  "code": "RSA_ECOM_XUAT_OFF_VERIFY_ECOM",
  "message": "Invalid order type"
}

// 409 Conflict
{
  "code": "XO_EXIST",
  "message": "Order already exists in XO123456789"
}
```

### 2. Search Xuat Off

```http
GET /api/v1/xuat-off/search
```

Request headers:

```
Authorization: Bearer {token}
```

Query parameters:

```
xoCode: string               // Mã phiếu xuất off
type: number                 // Loại xuất off (1: Manual, 2: Auto Satellite)
status: number[]             // Trạng thái [1,2,3,4,5]
reasonCode: string[]         // Mã lý do
fromDate: string            // Từ ngày (YYYY-MM-DD)
toDate: string             // Đến ngày (YYYY-MM-DD)
createdBy: string[]        // Người tạo
keyword: string            // Tìm theo mã đơn/SĐT
page: number              // Trang (mặc định: 1)
limit: number             // Số lượng/trang (mặc định: 10)
```

Response (200):

```json
{
  "items": [
    {
      "id": "uuid-123",
      "xoCode": "XO123456789",
      "orderCodeEcom": "ECO123456789",
      "status": 1,
      "customerName": "Nguyễn Văn A",
      "customerPhone": "0123456789",
      "type": 1,
      "reasonCode": "REASON_01",
      "reasonName": "Lý do xuất",
      "createdDate": "2023-12-20T10:00:00Z",
      "createdBy": "EMP123",
      "createdByName": "Người tạo",
      ...,  // Các trường khác của xuất off
      "details": [
        {
          "id": "uuid-456",
          "orderCodeShop": "SHOP123",
          "status": 1,
          "shopCode": "SH001",
          "shopName": "Shop Test",
          "journeyId": "JN123",
          "orderStatus": "COMPLETED",
          "ticketCode": "TC123",
          ...  // Các trường khác của chi tiết
        },
        ...  // Có thể có nhiều chi tiết
      ],
      "steps": [
        {
          "step": 1,
          "employeeCode": "EMP123",
          "employeeName": "Người tạo",
          "notes": "Ghi chú",
          ...  // Các trường khác của step
        },
        ...  // Có thể có nhiều steps
      ]
    },
    ...  // Có thể có nhiều items
  ],
  "totalCount": 100,
  ...  // Các trường phân trang khác
}
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: GET /api/v1/xuat-off/search
    activate RSA-ECOM

    RSA-ECOM->>RSA-ECOM: Build base query
    RSA-ECOM->>RSA-ECOM: Add filter conditions

    RSA-ECOM->>TypeORM: queryBuilder.getManyAndCount()
    activate TypeORM
    TypeORM-->>RSA-ECOM: [listXuatOff, itemCount]
    deactivate TypeORM

    RSA-ECOM->>TypeORM: fullQueryBuilder.getMany()
    activate TypeORM
    TypeORM-->>RSA-ECOM: sortedListXuatOff
    deactivate TypeORM

    RSA-ECOM->>OMSService: getListOrderES()
    activate OMSService
    OMSService-->>RSA-ECOM: orders
    deactivate OMSService

    RSA-ECOM->>JourneyService: getStatusGiftByOrderCodes()
    activate JourneyService
    JourneyService-->>RSA-ECOM: journeys
    deactivate JourneyService

    RSA-ECOM->>RSA-ECOM: Map order & journey data
    RSA-ECOM->>RSA-ECOM: Transform to PaginatedResultDto

    RSA-ECOM-->>FE: PaginatedResultDto<GetXuatOffDto>
    deactivate RSA-ECOM
```

Error responses:

```json
// 400 Bad Request
{
  "code": "INVALID_DATE_RANGE",
  "message": "Invalid date range"
}
```

### 3. Get Xuat Off Detail

```http
GET /api/v1/xuat-off/detail/:id
```

Request headers:

```
Authorization: Bearer {token}
```

Response (200):

```json
{
  "id": "uuid-123",
  "xoCode": "XO123456789",
  "orderCodeEcom": "ECO123456789",
  "status": 1,
  "customerName": "Nguyễn Văn A",
  "customerPhone": "0123456789",
  ..., // Các trường khác của xuất off
  "details": [
    {
      "id": "uuid-456",
      "orderCodeShop": "SHOP123",
      "status": 1,
      ...  // Các trường khác của chi tiết
    }
  ],
  "steps": [
    {
      "step": 1,
      "employeeCode": "EMP123",
      ...  // Các trường khác của step
    }
  ]
}
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: GET /api/v1/xuat-off/detail/:id
    activate RSA-ECOM

    RSA-ECOM->>XuatOffRepository: findOne(id)
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: xuatOff
    deactivate XuatOffRepository

    RSA-ECOM->>FilesService: getS3PresignWithKeys()
    activate FilesService
    FilesService-->>RSA-ECOM: signedUrls
    deactivate FilesService

    RSA-ECOM->>RSA-ECOM: mapOrderDetail()

    RSA-ECOM-->>FE: GetXuatOffDto
    deactivate RSA-ECOM
```

### 4. Update Xuat Off

```http
PATCH /api/v1/xuat-off/update/:id
```

Request headers:

```
Authorization: Bearer {token}
Content-Type: application/json
```

Request body:

```json
{
  "notes": "Ghi chú mới",
  "reasonCode": "REASON_02",
  "reasonName": "Lý do xuất mới",
  "details": [
    {
      "id": "uuid-456", // Update detail có sẵn
      "notes": "Ghi chú mới"
    },
    {
      "orderCodeShop": "SHOP789", // Thêm detail mới
      "orderShopCreateDate": "2023-12-20",
      "customerPhone": "0123456789",
      "type": 0
    }
  ]
}
```

Response (200):

```json
{
  "id": "uuid-123",
  "xoCode": "XO123456789",
  "status": 1,
  ..., // Các trường khác
  "details": [
    {
      "id": "uuid-456",
      "notes": "Ghi chú mới",
      ...  // Các trường khác
    },
    {
      "id": "uuid-789",
      "orderCodeShop": "SHOP789",
      ...  // Các trường khác
    }
  ]
}
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: PATCH /api/v1/xuat-off/update/:id
    activate RSA-ECOM

    RSA-ECOM->>RSA-ECOM: _checkExistXuatOffDetails()
    RSA-ECOM->>XuatOffRepository: mustGetXuatOffById()
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: xuatOff
    deactivate XuatOffRepository

    RSA-ECOM->>RSA-ECOM: updateXuatOffDetail()
    RSA-ECOM->>XuatOffRepository: save()
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: updatedXuatOff
    deactivate XuatOffRepository

    RSA-ECOM-->>FE: GetXuatOffDto
    deactivate RSA-ECOM
```

### 5. Verify Order

```http
POST /api/v1/xuat-off/verify-order/:orderCode
```

Request headers:

```
Authorization: Bearer {token}
```

Response (200):

```json
{
  "orderCode": "ECO123456789",
  "orderStatus": "COMPLETED",
  "orderChanel": "FromRSAEcom",
  "ecomDisplay": "AtShop",
  ...  // Các thông tin khác của đơn hàng
}
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: POST /api/v1/xuat-off/verify-order/:orderCode
    activate RSA-ECOM

    RSA-ECOM->>OMSService: getOneOrder()
    activate OMSService
    OMSService-->>RSA-ECOM: order
    deactivate OMSService

    RSA-ECOM->>RSA-ECOM: verify order channel
    RSA-ECOM->>RSA-ECOM: verify ecom display
    RSA-ECOM->>RSA-ECOM: _checkExistXuatOff()

    RSA-ECOM-->>FE: GetOneOrderLibResponse
    deactivate RSA-ECOM
```

### 6. Get Orders Info

```http
POST /api/v1/xuat-off/orders-info
```

Request headers:

```
Authorization: Bearer {token}
Content-Type: application/json
```

Request body:

```json
{
  "phoneNumber": "0123456789",
  "fromDate": "2023-12-01",
  "toDate": "2023-12-31",
  "excludeXOCode": "XO123456789" // Optional: exclude orders from this XO
}
```

Response (200):

```json
[
  {
    "orderCode": "SHOP123",
    "orderStatus": "COMPLETED",
    "customerName": "Nguyễn Văn A",
    "customerPhone": "0123456789",
    ...  // Các thông tin khác của đơn
  }
]
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: POST /api/v1/xuat-off/orders-info
    activate RSA-ECOM

    RSA-ECOM->>JourneyService: getOrderInfoEcom()
    activate JourneyService
    JourneyService-->>RSA-ECOM: orderInfos
    deactivate JourneyService

    RSA-ECOM->>OMSService: getListOrderES()
    activate OMSService
    OMSService-->>RSA-ECOM: orders
    deactivate OMSService

    RSA-ECOM->>XuatOffDetailService: findByOrderCodeShops()
    activate XuatOffDetailService
    XuatOffDetailService-->>RSA-ECOM: existingDetails
    deactivate XuatOffDetailService

    RSA-ECOM->>RSA-ECOM: filter results

    RSA-ECOM-->>FE: GetOneOrderLibResponse[]
    deactivate RSA-ECOM
```

### 7. Get Shop Order

```http
GET /api/v1/xuat-off/order/:orderCode
```

Request headers:

```
Authorization: Bearer {token}
```

Response (200):

```json
{
  "orderCode": "SHOP123",
  "orderCreateDate": "2023-12-20T10:00:00Z",
  "customerInfo": {
    "phone": "0123456789",
    "name": "Nguyễn Văn A"
  },
  "shopInfo": {
    "code": "SH001",
    "name": "Shop Test"
  },
  ...  // Các thông tin khác của đơn shop
}
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: GET /api/v1/xuat-off/order/:orderCode
    activate RSA-ECOM

    RSA-ECOM->>OMSService: getOrderByCode()
    activate OMSService
    OMSService-->>RSA-ECOM: order
    deactivate OMSService

    RSA-ECOM->>ShopService: getShopInfo()
    activate ShopService
    ShopService-->>RSA-ECOM: shopInfo
    deactivate ShopService

    RSA-ECOM->>RSA-ECOM: mapToOrderResponse()

    RSA-ECOM-->>FE: GetShopOrderResponse
    deactivate RSA-ECOM
```

### 8. Cancel Xuat Off

```http
PATCH /api/v1/xuat-off/cancel/:id
```

Request headers:

```
Authorization: Bearer {token}
Content-Type: application/json
```

Request body:

```json
{
  "reason": "Lý do hủy phiếu xuất off"
}
```

Response (200):

```json
{
  "id": "uuid-123",
  "xoCode": "XO123456789",
  "status": 5,  // Cancelled
  "cancelReason": "Lý do hủy phiếu xuất off",
  "cancelledBy": "EMP123",
  "cancelledByName": "Người hủy",
  "cancelledDate": "2023-12-20T10:00:00Z",
  ...  // Các trường khác
}
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: PATCH /api/v1/xuat-off/cancel/:id
    activate RSA-ECOM

    RSA-ECOM->>XuatOffRepository: findOne(id)
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: xuatOff
    deactivate XuatOffRepository

    RSA-ECOM->>RSA-ECOM: validateCanCancel()
    RSA-ECOM->>RSA-ECOM: createCancelStep()

    RSA-ECOM->>XuatOffRepository: save()
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: cancelledXuatOff
    deactivate XuatOffRepository

    RSA-ECOM-->>FE: GetXuatOffDto
    deactivate RSA-ECOM
```

### 9. Approve Multiple Xuat Off

```http
POST /api/v1/xuat-off/approve-multiple
```

Request headers:

```
Authorization: Bearer {token}
Content-Type: application/json
```

Request body:

```json
{
  "ids": ["uuid-123", "uuid-456"],
  "notes": "Ghi chú duyệt"
}
```

Response (200):

```json
{
  "success": ["uuid-123", "uuid-456"],
  "failed": []
}
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: POST /api/v1/xuat-off/approve-multiple
    activate RSA-ECOM

    RSA-ECOM->>RSA-ECOM: validateUserCanApprove()
    RSA-ECOM->>XuatOffRepository: findByIds()
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: xuatOffs
    deactivate XuatOffRepository

    loop Each XuatOff
        RSA-ECOM->>RSA-ECOM: createApproveStep()
        RSA-ECOM->>RSA-ECOM: updateStatus()
    end

    RSA-ECOM->>XuatOffRepository: saveMany()
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: results
    deactivate XuatOffRepository

    RSA-ECOM-->>FE: ApproveMultipleResponse
    deactivate RSA-ECOM
```

### 10. Reject Multiple Xuat Off

```http
POST /api/v1/xuat-off/reject-multiple
```

Request headers:

```
Authorization: Bearer {token}
Content-Type: application/json
```

Request body:

```json
{
  "ids": ["uuid-123", "uuid-456"],
  "reason": "Lý do từ chối",
  "notes": "Ghi chú từ chối"
}
```

Response (200):

```json
{
  "success": ["uuid-123", "uuid-456"],
  "failed": []
}
```

#### Sequence Diagram

```mermaid
sequenceDiagram
    actor FE
    FE->>RSA-ECOM: POST /api/v1/xuat-off/reject-multiple
    activate RSA-ECOM

    RSA-ECOM->>RSA-ECOM: validateUserCanReject()
    RSA-ECOM->>XuatOffRepository: findByIds()
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: xuatOffs
    deactivate XuatOffRepository

    loop Each XuatOff
        RSA-ECOM->>RSA-ECOM: createRejectStep()
        RSA-ECOM->>RSA-ECOM: updateStatus()
    end

    RSA-ECOM->>XuatOffRepository: saveMany()
    activate XuatOffRepository
    XuatOffRepository-->>RSA-ECOM: results
    deactivate XuatOffRepository

    RSA-ECOM-->>FE: RejectMultipleResponse
    deactivate RSA-ECOM
```
