import { Body, Controller, Delete, Get, HttpStatus, Param, ParseUUIDPipe, Patch, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Public } from '@shared';
import { CurrentUser, UserDto } from '@shared/common/decorators/current-user.decorator';
import { UpdateXuatOffDetailDto } from '../dto/update-xuat-off.dto';
import { CancelXuatOffDetailDto, CreateXuatOffDetailDto } from '../dto/xuat-off-detail';
import { XuatOffDetailService } from '../services/xuat-off-detail.service';

@ApiTags('xuat-off-detail')
@ApiBearerAuth('defaultJWT')
@Controller({ path: 'xuat-off-detail', version: '1' })
export class XuatOffDetailController {
  constructor(private readonly xuatOffDetailService: XuatOffDetailService) {}

  @Patch('cancel-by-order-code')
  @Public()
  @ApiOperation({ summary: 'Cancel xuat-off detail' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Record successfully cancelled' })
  cancelXuatOffDetail(@Body() updateDto: CancelXuatOffDetailDto, @CurrentUser() user: UserDto) {
    return this.xuatOffDetailService.cancelXuatOffDetail(updateDto, user);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new xuat off detail record' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Record successfully created' })
  create(@Body() createDto: CreateXuatOffDetailDto, @CurrentUser() user: UserDto) {
    return this.xuatOffDetailService.create(createDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'Get all xuat off detail records' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Return all records' })
  findAll() {
    return this.xuatOffDetailService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a xuat off detail record by id' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Return the found record' })
  findOne(@Param('id') id: string) {
    return this.xuatOffDetailService.findOne(id);
  }

  @Get('by-xo/:xoId')
  @ApiOperation({ summary: 'Get all details by xuat off ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Return all matching records' })
  findByXoId(@Param('xoId') xoId: string) {
    return this.xuatOffDetailService.findByXoId(xoId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a xuat off detail record' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Record successfully updated' })
  update(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() updateDto: UpdateXuatOffDetailDto,
    @CurrentUser() user: UserDto,
  ) {
    return this.xuatOffDetailService.update(id, updateDto, user);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a xuat off detail record' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Record successfully deleted' })
  remove(@Param('id') id: string) {
    return this.xuatOffDetailService.remove(id);
  }
}
