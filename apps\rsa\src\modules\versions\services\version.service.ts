import { HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { DataSource, Not, Repository } from 'typeorm';
import { ErrorCode } from '@shared';
import { SystemException } from '@shared';
import { IError } from '@shared';
import { CreateVersionDto, GetListVersionDto, UpdateVersionDto } from '../dto';
import { Version } from '../entities/version.entity';
import _ from 'lodash';

@Injectable()
export class VersionsService {
  constructor(
    @InjectRepository(Version)
    private readonly versionRepository: Repository<Version>,
    private readonly dataSource: DataSource,
  ) {}

  async create(createVersionDto: CreateVersionDto) {
    await this.checkConflictVersion(createVersionDto);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const createVersion = plainToClass(Version, createVersionDto);
      const dataSave = await queryRunner.manager.save(createVersion);
      await queryRunner.commitTransaction();
      return dataSave;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(getListVersionDto: GetListVersionDto) {
    try {
      const limit = Number(getListVersionDto.perPage);
      const offset = (Number(getListVersionDto.page) - 1) * limit;
      delete getListVersionDto.page;
      delete getListVersionDto.perPage;
      const [versions, total] = await this.versionRepository.findAndCount({
        where: _.pickBy(getListVersionDto, _.identity),
        take: limit,
        skip: offset,
      });
      return {
        total,
        versions,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(id: string) {
    try {
      const data = await this.versionRepository.findOneBy({ id });
      if (!data) {
        const exception: IError = {
          code: ErrorCode.NOT_FOUND,
          message: ErrorCode.getError(ErrorCode.NOT_FOUND),
          details: ErrorCode.getError(ErrorCode.NOT_FOUND),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.CONFLICT);
      }
      return data;
    } catch (error) {
      throw error;
    }
  }

  async update(id: string, updateVersionDto: UpdateVersionDto) {
    await this.findOne(id);
    await this.checkConflictVersion(updateVersionDto, id);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const updateVersion = plainToClass(Version, updateVersionDto);
      await queryRunner.manager.update(Version, id, updateVersion);
      await queryRunner.commitTransaction();
      return { isSuccess: true };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: string) {
    try {
      await this.versionRepository.softDelete(id);
      return { isSuccess: true };
    } catch (error) {
      throw error;
    }
  }

  async latestVersion(type: number) {
    try {
      const version = await this.versionRepository.find({
        where: {
          type: type,
        },
        order: {
          createdAt: 'DESC',
        },
        take: 1,
      });
      return version.at(0) || null;
    } catch (error) {
      throw error;
    }
  }

  async checkConflictVersion(createVersionDto: CreateVersionDto, id?: string) {
    try {
      const { status, type, version, versionCode } = createVersionDto;
      const where = {
        status,
        type,
        version,
        versionCode,
      };
      if (id) {
        where['id'] = Not(id);
      }
      const checkExist = await this.versionRepository.findOneBy(where);
      if (checkExist) {
        const exception: IError = {
          code: ErrorCode.CONFLICT,
          message: ErrorCode.getError(ErrorCode.CONFLICT),
          details: ErrorCode.getError(ErrorCode.CONFLICT),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.CONFLICT);
      }
    } catch (error) {
      throw error;
    }
  }
}
