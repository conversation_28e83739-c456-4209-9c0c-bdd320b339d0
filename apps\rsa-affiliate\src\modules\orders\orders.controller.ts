import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { BlockAdjustOrderInterceptor, ClassErrorResponse, CustomHeaders, generalSchema } from '@shared';
import {
  ContinueBuyingRes,
  GetOrderEsRes,
  PlaceOrderDto,
  UpdateStatusOrderPartialPaymentDto,
  UpdateStatusOrderResponse,
} from 'modules/modules/modules/orders/dto';
import { GetDetailsByTicketCodeDTO } from 'modules/modules/modules/orders/dto/get-details-by-ticket-code.dto';
import { PushsOrderPayloadDto } from 'modules/modules/modules/orders/dto/push-order.dto';
import { CreateOrderRes } from 'vac-nest-oms';
import { OrdersService } from './orders.service';

@Controller({ path: 'orders', version: '1' })
@ApiTags('Order')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(GetOrderEsRes, ContinueBuyingRes)
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@CustomHeaders()
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Get('continue-buying/:orderCode')
  @ApiOperation({
    summary: 'Tiếp tục đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả hủy đơn hàng',
    schema: generalSchema(ContinueBuyingRes, 'object'),
  })
  @UseInterceptors(BlockAdjustOrderInterceptor)
  continueBuying(@Param('orderCode') orderCode: string) {
    return this.ordersService.continueBuying(orderCode);
  }

  @Post('push-order')
  @ApiOperation({
    summary: 'Cập nhật ecomDisplay = 1',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Cập nhật ecomDisplay = 1',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  pushOrder(@Body() body: PushsOrderPayloadDto) {
    return this.ordersService.pushOrder(body);
  }

  /**
   * @TODO xem chi tiết theo ticket code
   */
  @Get('tickets/:ticketCode')
  @Version('2')
  @ApiOperation({
    summary: 'Xem chi tiết theo ticket code',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Xem chi tiết theo ticket code',
  })
  async getDetailsByTicketCodeV2(
    @Param('ticketCode') ticketCode: string,
    @Query() getDetailsByTicketCodeDTO: GetDetailsByTicketCodeDTO,
  ) {
    return this.ordersService.getDetailsByTicketCodeV2(ticketCode, getDetailsByTicketCodeDTO);
  }

  @Post('oms/update-status-order-deposit')
  @ApiOperation({
    summary: 'Cập nhật trạng thái hoàn tất cọc đơn hàng trực tiếp sang OMS',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  updateStatusOmsOrderDeposit(@Body() body: UpdateStatusOrderPartialPaymentDto) {
    return this.ordersService.updateStatusOmsOrderDeposit(body);
  }
}
