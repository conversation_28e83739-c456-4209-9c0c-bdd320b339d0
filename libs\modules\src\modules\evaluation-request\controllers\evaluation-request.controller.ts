import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiExtraModels, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import {
  ChangeStatusEvaluationDto,
  CreateEvaluationRequestDto,
  CreateEvaluationRequestRes,
  GetEvaluationRequestByLcvIdRes,
  GetEvaluationRequestByTicketCodeRes,
  GetEvaluationRequestDto,
  GetEvaluationRequestRes,
  SearchEvaluationRequestDto,
  SearchEvaluationRequestRes,
  UpdateEvaluationRequestDto,
  UpdateEvaluationRequestRes,
} from 'vac-nest-examination';
import { ClassErrorResponse, ClassResponse, CustomHeaders, generalSchema } from '../../../../../shared/src';
import { EvaluationRequestService } from '../services/evaluation-request.service';

@Controller({ path: 'evaluation-request', version: '1' })
@ApiTags('Evaluation Request')
@CustomHeaders()
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(
  ClassResponse,
  CreateEvaluationRequestRes,
  GetEvaluationRequestRes,
  UpdateEvaluationRequestRes,
  GetEvaluationRequestByLcvIdRes,
  SearchEvaluationRequestRes,
  GetEvaluationRequestByTicketCodeRes,
)
export class EvaluationRequestController {
  constructor(private readonly evaluationRequestService: EvaluationRequestService) {}

  @Get()
  @ApiOkResponse({
    schema: generalSchema(GetEvaluationRequestRes, 'object'),
  })
  getEvaluationRequest(@Query() getEvaluationRequestDto: GetEvaluationRequestDto) {
    return this.evaluationRequestService.getEvaluationRequest(getEvaluationRequestDto);
  }

  @Post()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(CreateEvaluationRequestRes, 'object'),
  })
  createEvaluationRequest(@Body() createEvaluationRequestDto: CreateEvaluationRequestDto) {
    return this.evaluationRequestService.createEvaluationRequest(createEvaluationRequestDto);
  }

  @Put(':evaluationRequestCode')
  @ApiOkResponse({
    schema: generalSchema(UpdateEvaluationRequestRes, 'object'),
  })
  updateEvaluationRequest(
    @Param('evaluationRequestCode') evaluationRequestCode: string,
    @Body() updateEvaluationRequestDto: UpdateEvaluationRequestDto,
  ) {
    return this.evaluationRequestService.updateEvaluationRequest(evaluationRequestCode, updateEvaluationRequestDto);
  }

  @Get('lcvId/:lcvId')
  @ApiOkResponse({
    schema: generalSchema(GetEvaluationRequestByLcvIdRes, 'object'),
  })
  async getEvaluationRequestByLcvId(@Param('lcvId') lcvId: string) {
    return { items: await this.evaluationRequestService.getEvaluationRequestByLcvId(lcvId) };
  }

  @Get('ticket-code/:ticketCode')
  @ApiOkResponse({
    schema: generalSchema(GetEvaluationRequestByTicketCodeRes, 'object'),
  })
  async getEvaluationRequestByTicketCode(@Param('ticketCode') lcvId: string) {
    return { items: await this.evaluationRequestService.getEvaluationRequestByTicketCode(lcvId) };
  }

  @Post('search')
  @ApiOkResponse({
    schema: generalSchema(SearchEvaluationRequestRes, 'object'),
  })
  async searchEvaluationRequest(@Body() searchEvaluationRequestDto: SearchEvaluationRequestDto) {
    return this.evaluationRequestService.searchEvaluationRequest(searchEvaluationRequestDto);
  }

  @Put('change-status/:evaluationRequestCode')
  @ApiOkResponse({
    schema: generalSchema(UpdateEvaluationRequestRes, 'object'),
  })
  changeEvaluationRequestStatus(
    @Param('evaluationRequestCode') evaluationRequestCode: string,
    @Body() changeStatusEvaluationDto: ChangeStatusEvaluationDto,
  ) {
    return this.evaluationRequestService.changeEvaluationRequestStatus(
      evaluationRequestCode,
      changeStatusEvaluationDto,
    );
  }
}
