import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { GetOrdersInfoDto } from 'vac-nest-journey';

export class GetOrderInfoServiceDto extends PickType(GetOrdersInfoDto, ['fromDate', 'toDate']) {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  phoneNumber: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  excludeXOCode?: string;
}

export class GetOneOrderInfoDto {
  @ApiProperty({ required: true })
  @Expose()
  fromDate?: string;

  @ApiProperty({ required: true })
  @Expose()
  toDate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  excludeXOCode?: string;
}
