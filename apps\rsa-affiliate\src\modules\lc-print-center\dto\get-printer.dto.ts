import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class GetPrinterDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  shopCode: string;
}

export class PrinterInfo {
  id: number;
  macID: string;
  macName: string;
  printerType: string;
}

export class GetPrinterResponse {
  listPrinterAll: PrinterInfo[];
  listPrinterA4: PrinterInfo[];
  listPrinterBill: PrinterInfo[];
}
