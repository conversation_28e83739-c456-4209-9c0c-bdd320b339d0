import * as Excel from 'exceljs';
import { isDate } from 'moment';

export const REPORT_TEMPLATE_TYPE = {
  SHOP: 1,
  SKU: 2,
  EXPOSURE: 3,
};

export const REPORT_COL_OFFSET = {
  SKU: 1,
  EXPOSURE: 2,
};

export const OUTLINE_PROPS = {
  SHOP_NAME: 'shopName',
  PLACE_TIME: 'placeTime',
  REPORT_TIME: 'reportDate',
};

export const EXCEL_OUTLINE_CONTENTS = [
  { col: 'B3', endCol: 'M3', value: 'CÔNG TY CỔ PHẨN DƯỢC PHẨM FPT LONG CHÂU', isBold: true, size: 14 },
  { col: 'B4', endCol: 'K4', value: OUTLINE_PROPS.SHOP_NAME, isBold: true, size: 12 },
  {
    col: 'O3',
    endCol: 'AQ3',
    value: 'BÁO CÁO TIÊM VACXIN PHÒNG DẠI VÀ HUYẾT THANH KHÁNG DẠI ',
    isBold: true,
    size: 14,
  },
  { col: 'O4', endCol: 'AQ4', value: OUTLINE_PROPS.REPORT_TIME, isBold: true, size: 12 },
  { col: 'B', endCol: 'G', value: 'Người làm báo cáo', isBold: true, size: 12, isDynamic: true, row: 13 },
  { col: 'B', endCol: 'G', value: '.......................', isBold: false, size: 14, isDynamic: true, row: 19 },
  {
    col: 'AA',
    endCol: 'AQ',
    value: OUTLINE_PROPS.PLACE_TIME,
    isBold: false,
    size: 12,
    isDynamic: true,
    row: 12,
  },
  { col: 'AA', endCol: 'AQ', value: 'Lãnh đạo đơn vị', isBold: true, size: 12, isDynamic: true, row: 13 },
  { col: 'AA', endCol: 'AQ', value: '( ký tên, đóng dấu)', isBold: false, size: 12, isDynamic: true, row: 14 },
  { col: 'AA', endCol: 'AQ', value: '.......................', isBold: false, size: 14, isDynamic: true, row: 19 },
];

export const EXCEL_TABLE_SUB_HEADERS = [
  { col: 'D', value: 'Nam', property: 'gender_male_total' },
  { col: 'E', value: 'Nữ', property: 'gender_female_total' },
  { col: 'F', value: '<15 tuổi', property: 'agerange_under15_total' },
  { col: 'G', value: '15 - 24 tuổi', property: 'agerange_15to24_total' },
  { col: 'H', value: '24 - 49 tuổi', property: 'agerange_25to49_total' },
  { col: 'I', value: '≥ 50 tuổi', property: 'agerange_over50_total' },
  { col: 'J', value: '<10 ngày', property: 'bitefrom_under10d_total' },
  { col: 'K', value: '≥ 10 ngày', property: 'bitefrom_over10d_total' },
  { col: 'L', value: 'Số người được tư vấn', mergeVertical: true, property: 'person_advised' },
  { col: 'M', value: 'Chó', property: 'animaltype_dog_total' },
  { col: 'N', value: 'Mèo', property: 'animaltype_cat_total' },
  { col: 'O', value: 'Dơi', property: 'animaltype_bat_total' },
  { col: 'P', value: 'Khác', property: 'animaltype_other_total' },
  { col: 'Q', value: 'Đầu mặt cổ', property: 'woundposition_headfaceneck_total' },
  { col: 'R', value: 'Thân', property: 'woundposition_body_total' },
  { col: 'S', value: 'Tay', property: 'woundposition_hand_total' },
  { col: 'T', value: 'Chân', property: 'woundposition_foot_total' },
  { col: 'U', value: 'Độ I', property: 'woundlevel_lv1_total' },
  { col: 'V', value: 'Độ II', property: 'woundlevel_lv2_total' },
  { col: 'W', value: 'Độ III', property: 'woundlevel_lv3_total' },
  { col: 'X', value: 'Bình thường', property: 'animalstatus_normal_total' },
  { col: 'Y', value: 'Ốm', property: 'animalstatus_sick_total' },
  { col: 'Z', value: 'Chạy rong, mất tích', property: 'animalstatus_roam_total' },
  { col: 'AA', value: 'Lên cơn dại', property: 'animalstatus_rabies_total' },
  {
    col: 'AB',
    value: 'Số người xử lý vết thương trước khi tới cơ sở y tế',
    mergeVertical: true,
    property: 'person_first_aid',
  },
  { col: 'AC', value: 'Tiêm bắp', property: 'injectionroute_intramuscular_total' },
  { col: 'AD', value: 'Tiêm trong da', property: 'injectionroute_intradermal_total' },
  { col: 'AE', value: 'Số người dùng HTKD', mergeVertical: true, property: 'useserum_total' },
  { col: 'AF', value: 'Đau', property: 'sideeffect_dau_total' },
  { col: 'AG', value: 'Quần cổ', property: 'sideeffect_quangco_total' },
  { col: 'AH', value: 'Tụ máu', property: 'sideeffect_tumau_total' },
  { col: 'AI', value: 'Phù nề/nốt cứng', property: 'sideeffect_phune_notcung_total' },
  { col: 'AJ', value: 'Sốt', property: 'systemicsideeffect_sot_total' },
  { col: 'AK', value: 'Khó chịu', property: 'systemicsideeffect_khochiu_total' },
  { col: 'AL', value: 'Ngứa mẩn đỏ', property: 'systemicsideeffect_nguamando_total' },
  { col: 'AM', value: 'Đau cơ khớp', property: 'systemicsideeffect_daucokhop_total' },
  { col: 'AN', value: 'Rối loạn tiêu hoá', property: 'systemicsideeffect_roiloantieuhoa_total' },
  { col: 'AO', value: 'Khác', property: 'systemicsideeffect_khac_total' },
  { col: 'AP', value: 'Có tiêm', property: 'death_vaccinated_total' },
  { col: 'AQ', value: 'Không tiêm', property: 'death_novaccinated_total' },
  {
    col: 'AR',
    value: 'Số người tiêm dự phòng',
    mergeVertical: true,
    orient: 'horizontal',
    property: 'preventivevaccination_total',
  },
];

export const EXCEL_LEFT_TABLE_TEMP1_HEADERS = [
  { col: 'B', header: 'STT' },
  { col: 'C', header: 'Điểm tiêm' },
];

export const EXCEL_LEFT_TABLE_TEMP2_HEADERS = [
  { col: 'B', header: 'STT' },
  { col: 'C', header: 'Điểm tiêm' },
  { col: 'D', header: 'Tên VACCINE' },
];

export const EXCEL_LEFT_TABLE_TEMP3_HEADERS = [
  { col: 'B', header: 'STT' },
  { col: 'C', header: 'Điểm tiêm' },
  { col: 'D', header: 'Tên VACCINE' },
  { col: 'E', header: '' },
];

export const EXCEL_RIGHT_TABLE_HEADERS = [
  { col: 'D', header: 'Giới', mergeRowNum: 2 },
  { col: 'F', header: 'Tuổi', mergeRowNum: 4 },
  { col: 'J', header: 'Thời gian từ lúc', mergeRowNum: 2 },
  { col: 'M', header: 'Loại động vật', mergeRowNum: 4 },
  { col: 'Q', header: 'Số người có vị trí vết thương', mergeRowNum: 4 },
  { col: 'U', header: 'Số người có mức độ vết thương', mergeRowNum: 3 },
  { col: 'X', header: 'Tình trạng động vật', mergeRowNum: 4 },
  { col: 'AC', header: 'Số người', mergeRowNum: 2 },
  { col: 'AF', header: 'Số người có phản ứng phụ tại chỗ', mergeRowNum: 4 },
  { col: 'AJ', header: 'Số người có phản ứng phụ toàn thân', mergeRowNum: 6 },
  { col: 'AP', header: 'BN tử vong', mergeRowNum: 2 },
];

export const MOCK_VACC_SHOP_REPORT = [];

export const REPORT_S3_TYPE = {
  SHOP: 'vac_shop_reports',
  SKU: 'vac_sku_reports',
  EXPOSURE: 'vac_exposure_reports',
  APPENDIX_ONE: 'vac_appendix_one',
  SYMPTOM_AFTER_INJECTION: 'symptom_after_injection',
  REPORT_INJECTION_STATISTICS: 'vac_report_injection_statistics',
  REPORT_USE: 'vac_report_use',
  VAT_6IN1: '6_in_1_vat_reports',
  SOI_RUBELLA_INJECTION_RESULT: 'soi_rubella_injection_result_reports',
};

export const ACCUMULATION = 'Cộng dồn';

export const TOTAL = 'Tổng';

export const PRE_SIGNED_LINK_EXPIRATION = 3600;

export const BLACK_BORDER: Partial<Excel.Border> = { style: 'thin', color: { argb: 'FF000000' } };
export const CENTER_ALIGNMENT: Partial<Excel.Alignment> = { horizontal: 'center', vertical: 'middle', wrapText: true };
export const LEFT_ALIGNMENT: Partial<Excel.Alignment> = { horizontal: 'left', vertical: 'middle', wrapText: true };
export const TEXT_FONT = 'Times New Roman';

// RabiesReportAppendixOne

export const TEMP_RABIES_REPORT_APPENDIX_ONE = {
  headerLeft: [
    { col: 'B', header: 'TT', mergeVertical: true, width: 3 },
    { col: 'C', header: 'Họ và tên', mergeVertical: true, width: 6 },
    { col: 'D', header: 'Địa chỉ', mergeVertical: true, width: 26 },
    { col: 'E', header: 'Số điện thoại', mergeVertical: true, width: 26 },
  ],
  headerRight: [
    { col: 'F', header: 'Tuổi (Năm sinh)', mergeVertical: true, mergeRowNum: 2 },
    { col: 'I', header: 'Loại động vật', mergeRowNum: 4 },
    { col: 'N', header: 'Vị trí, số lượng vết thương', mergeRowNum: 4 },
    { col: 'R', header: 'Tình trạng động vật sau cắn', mergeRowNum: 4 },
    { col: 'V', header: 'Phác đồ tiêm bắp (ghi rõ ngày, tháng, năm vào từng ô)', mergeRowNum: 5 },
    { col: 'AA', header: 'Phác đồ tiêm trong da (ghi rõ ngày, tháng, năm vào từng ô)', mergeRowNum: 4 },
    { col: 'AE', header: 'Sử dụng HTKD', mergeRowNum: 4 },
    { col: 'AK', header: 'Phản ứng tại chỗ tiêm', mergeRowNum: 4 },
    { col: 'AO', header: 'Phán ứng toàn thân sau tiêm', mergeRowNum: 7 },
  ],
  subHeader: [
    { col: 'F', value: 'Nam', property: 'ageMale', width: 4 },
    { col: 'G', value: 'Nữ', property: 'ageFemale', width: 4 },
    {
      col: 'H',
      value: 'Ngày bị động vật cắn/tiếp xúc',
      mergeVertical: true,
      property: 'biteTime',
      width: 7,
      isDate: true,
    },
    { col: 'I', value: 'Chó', property: 'animalTypeDog', width: 3 },
    { col: 'J', value: 'Mèo', property: 'animalTypeCat', width: 3 },
    { col: 'K', value: 'Tiếp Xúc', property: 'animalTypeBat', width: 3 },
    { col: 'L', value: 'Khác', property: 'animalTypeOther', width: 3 },
    { col: 'M', value: 'Mức độ vết thương (Độ I, II, III)', mergeVertical: true, property: 'woundLevel', width: 5 },
    { col: 'N', value: 'Đầu, mặt, cổ', property: 'woundPositionHeadFaceNeck', width: 3 },
    { col: 'O', value: 'Thân', property: 'woundPositionBody', width: 3 },
    { col: 'P', value: 'Tay', property: 'woundPositionHand', width: 3 },
    { col: 'Q', value: 'Chân', property: 'woundPositionFoot', width: 3 },
    { col: 'R', value: 'Bình thường', property: 'animalStatusNormal', width: 3 },
    { col: 'S', value: 'Ốm', property: 'animalStatusSick', width: 3 },
    { col: 'T', value: 'Chạy rông, mất tích', property: 'animalStatusRoam', width: 3 },
    { col: 'U', value: 'Lên cơn dại', property: 'animalStatusRabies', width: 3 },
    { col: 'V', value: 'Ngày 0', property: 'regimenIntramuscularDay0', width: 3, isDate: true },
    { col: 'W', value: 'Ngày 3', property: 'regimenIntramuscularDay3', width: 3, isDate: true },
    { col: 'X', value: 'Ngày 7', property: 'regimenIntramuscularDay7', width: 3, isDate: true },
    { col: 'Y', value: 'Ngày 14', property: 'regimenIntramuscularDay14', width: 3, isDate: true },
    { col: 'Z', value: 'Ngày 28', property: 'regimenIntramuscularDay28', width: 3, isDate: true },
    { col: 'AA', value: 'Ngày 0', property: 'regimenIntradermalDay0', width: 3, isDate: true },
    { col: 'AB', value: 'Ngày 3', property: 'regimenIntradermalDay3', width: 3, isDate: true },
    { col: 'AC', value: 'Ngày 7', property: 'regimenIntradermalDay7', width: 3, isDate: true },
    { col: 'AD', value: 'Ngày 28', property: 'regimenIntradermalDay28', width: 3, isDate: true },
    { col: 'AE', value: 'Ngày tiêm', property: 'serumVaccinatedDate', width: 3, isDate: true },
    { col: 'AF', value: 'Lô HTKD', property: 'serumLotNumber', width: 3 },
    { col: 'AG', value: 'Tại chỗ (ml)', property: 'serumOnSite', width: 3 },
    { col: 'AH', value: 'Đường tiêm (ml)', property: 'serumInjectionRoute', width: 3 },
    { col: 'AI', value: 'Tên vắc xin', mergeVertical: true, property: 'vaccineName', width: 3 },
    { col: 'AJ', value: 'Lô vắc xin', mergeVertical: true, property: 'lotNumber', width: 3 },
    { col: 'AK', value: 'Đau', property: 'sideEffectDau', width: 3 },
    { col: 'AL', value: 'Quầng đỏ', property: 'sideEffectQuangCo', width: 3 },
    { col: 'AM', value: 'Tụ máu', property: 'sideEffectTuMau', width: 3 },
    { col: 'AN', value: 'Phù nề, nốt cứng', property: 'sideEffectPhuNeNotCung', width: 3 },
    { col: 'AO', value: 'Sốt', property: 'systemicSideEffectSot', width: 3 },
    { col: 'AP', value: 'Khó chịu', property: 'systemicSideEffectKhoChiu', width: 3 },
    { col: 'AQ', value: 'Ngứa', property: 'systemicSideEffectNgua', width: 3 },
    { col: 'AR', value: 'Mẩn đỏ', property: 'systemicSideEffectManDo', width: 3 },
    { col: 'AS', value: 'Đau cơ, đau khớp', property: 'systemicSideEffectDauCoKhop', width: 3 },
    { col: 'AT', value: 'Rối loạn tiêu hoá', property: 'systemicSideEffectRoiLoanTieuHoa', width: 3 },
    { col: 'AU', value: 'khác', property: 'systemicSideEffectKhac', width: 3 },
    {
      col: 'AV',
      value: 'Được xử lý vết thương trước khi tới cơ sở y tế',
      mergeVertical: true,
      property: 'treatedTheWoundBefore',
      width: 3,
      fontSize: 10,
    },
    { col: 'AW', value: 'Ghi chú', mergeVertical: true, property: 'note', width: 6 },
  ],
  outLineContent: [
    { col: 'B2', endCol: 'F2', value: 'CÔNG TY CỔ PHẨN DƯỢC PHẨM FPT LONG CHÂU', isBold: true, size: 14 },
    { col: 'B3', endCol: 'F3', value: OUTLINE_PROPS.SHOP_NAME, isBold: true, size: 12 },
    {
      col: 'B4',
      endCol: 'AW4',
      value: 'BẢNG THEO DÕI NGƯỜI TIÊM VẮC XIN PHÒNG DẠI VÀ HUYẾT THANH KHÁNG DẠI',
      isBold: true,
      size: 14,
    },
    { col: 'B5', endCol: 'AW5', value: OUTLINE_PROPS.REPORT_TIME, isBold: true, size: 12 },
    { col: 'B', endCol: 'G', value: 'Người làm báo cáo', isBold: true, size: 12, isDynamic: true, row: 10 },
    { col: 'B', endCol: 'G', value: '.......................', isBold: false, size: 14, isDynamic: true, row: 16 },
    {
      col: 'AE',
      endCol: 'AV',
      value: OUTLINE_PROPS.PLACE_TIME,
      isBold: false,
      size: 12,
      isDynamic: true,
      row: 9,
    },
    { col: 'AE', endCol: 'AV', value: 'Lãnh đạo đơn vị', isBold: true, size: 12, isDynamic: true, row: 10 },
    { col: 'AE', endCol: 'AV', value: '( ký tên, đóng dấu)', isBold: false, size: 12, isDynamic: true, row: 11 },
    { col: 'AE', endCol: 'AV', value: '.......................', isBold: false, size: 14, isDynamic: true, row: 16 },
  ],
  fontSize: {
    normal: 9,
    title: 11,
  },
  lineData: {
    titleFisrt: 6,
    titleSecond: 7,
    dataRowFisrt: 8,
    dataRowSecond: 9,
  },
};

export const TEMP_RABIES_REPORT_USE = {
  header: [
    { col: 'A', value: 'STT', width: 5 },
    { col: 'B', value: 'LOẠI VẮC XIN', property: 'vaccineTypeName', width: 19, isLeftAlignment: true },
    {
      col: 'C',
      value: 'TÊN THƯƠNG MẠI',
      property: 'commercialName',
      width: 24,
      isLeftAlignment: true,
    },
    { col: 'D', value: 'TÊN NHÀ SẢN XUẤT', property: 'producer', width: 27, isLeftAlignment: true },
    { col: 'E', value: 'SỐ LÔ', property: 'lotNumber', width: 12, isLeftAlignment: true },
    { col: 'F', value: 'SỐ ĐĂNG KÝ', property: 'registNum', width: 16, isLeftAlignment: true },
    { col: 'G', value: 'HẠN DÙNG', property: 'expireDate', width: 11, isDate: true },
    {
      col: 'H',
      value: 'TÊN NHÀ CUNG CẤP',
      mergeVertical: true,
      property: 'supplier',
      width: 30,
      isLeftAlignment: true,
    },
    { col: 'I', value: 'SỐ TỒN THÁNG TRƯỚC', property: 'stockQuantity', width: 10 },
    { col: 'J', value: 'SỐ NHẬP', property: 'inputQuantity', width: 10 },
    { col: 'K', value: 'SỐ SỬ DỤNG', property: 'recordVaccine', width: 10 },
    { col: 'L', value: 'SỐ HỦY', property: 'cancelQuantity', width: 10 },
    { col: 'M', value: 'SỐ TIÊM', property: 'injectedQuantity', width: 10 },
    { col: 'N', value: 'SỐ ĐIỀU CHUYỂN', property: 'lcnb', width: 10 },
    { col: 'O', value: 'SỐ HIỆN CÒN', property: 'restQuantity', width: 10 },
  ],
  outLineContent: [
    { col: 'A1', endCol: 'D1', value: 'CÔNG TY CỔ PHẨN DƯỢC PHẨM FPT LONG CHÂU', isBold: true, size: 11 },
    { col: 'A2', endCol: 'D2', value: OUTLINE_PROPS.SHOP_NAME, isBold: true, size: 11 },
    {
      col: 'E4',
      endCol: 'H4',
      value: 'BÁO CÁO TÌNH HÌNH SỬ DỤNG VẮC XIN TIÊM CHỦNG DỊCH VỤ',
      isBold: true,
      size: 11,
    },
    { col: 'E5', endCol: 'H5', value: OUTLINE_PROPS.REPORT_TIME, isBold: false, isItalic: true, size: 11 },
    { col: 'B', endCol: 'D', value: 'NGƯỜI LÀM BÁO CÁO', isBold: true, size: 11, isDynamic: true, row: 9 },
    {
      col: 'B',
      endCol: 'D',
      value: '(Ký, ghi rõ chức danh, họ tên)',
      isBold: false,
      isItalic: true,
      size: 11,
      isDynamic: true,
      row: 10,
    },
    {
      col: 'I',
      endCol: 'M',
      value: OUTLINE_PROPS.PLACE_TIME,
      isBold: false,
      isItalic: true,
      size: 11,
      isDynamic: true,
      row: 9,
    },
    { col: 'I', endCol: 'M', value: 'TRƯỞNG TRUNG TÂM TIÊM CHỦNG', isBold: true, size: 11, isDynamic: true, row: 10 },
    {
      col: 'I',
      endCol: 'M',
      value: '(Ký tên, đóng dấu)',
      isBold: false,
      isItalic: true,
      size: 11,
      isDynamic: true,
      row: 11,
    },
  ],
  fontSize: {
    normal: 9,
    title: 11,
  },
  lineData: {
    titleFisrt: 7,
    dataRowFisrt: 8,
    dataRowSecond: 9,
    limitCellNum: 15,
    limitCellNumTemp2: 16,
  },
};

export const TEMP_SYMPTOM_AFTER_INJECTION = {
  header: [
    { col: 'A', value: 'STT', width: 5 },
    { col: 'B', value: 'Họ và tên', property: 'name', width: 16, isLeftAlignment: true },
    {
      col: 'C',
      value: 'Mã TCQG',
      property: 'nationalVaccineCode',
      width: 16,
    },
    {
      col: 'D',
      value: 'Ngày sinh',
      property: 'dateOfBirth',
      width: 10,
      isDate: true,
      isTime: false,
    },
    { col: 'E', value: 'Người giám hộ', property: 'personHostName', width: 16, isLeftAlignment: true },
    { col: 'F', value: 'Địa chỉ', property: 'address', width: 30, isLeftAlignment: true },
    { col: 'G', value: 'Số điện thoại', property: 'phone', width: 12 },
    { col: 'H', value: 'Vaccine', property: 'vaccineName', width: 12, isLeftAlignment: true, isDate: true },
    {
      col: 'I',
      value: 'Giờ tiêm',
      mergeVertical: true,
      property: 'trackingTime',
      width: 10,
      isTime: true,
    },
    // { col: 'I', value: 'Giờ kiểm tra', property: 'completedTrackingTime', width: 10, isTime: true },
    { col: 'J', value: 'Tình trạng', property: 'textSymptom', width: 10, isLeftAlignment: true },
    { col: 'K', value: 'Ký tên', property: 'empty', width: 10 },
    { col: 'L', value: 'Ghi chú', property: 'note', width: 10, isLeftAlignment: true },
  ],
  fontSize: {
    normal: 9,
    title: 11,
  },
  lineData: {
    titleFisrt: 3,
    dataRowFisrt: 4,
    dataRowSecond: 5,
    limitCellNum: 12,
    limitCellNumTemp2: 13,
  },
};

export const TEMP_INVENTORY = {
  header: [
    { col: 'A', value: 'STT', property: 'stt', width: 5 },
    { col: 'B', value: 'Ngày', property: 'transDate', width: 11, isDate: true },
    {
      col: 'C',
      value: 'Tên vắc-xin, vật tư',
      property: 'skuName',
      width: 30,
      isLeftAlignment: true,
    },
    { col: 'D', value: 'Số lô', property: 'lotNumber', width: 16, isLeftAlignment: true },
    { col: 'E', value: 'Hạn dùng', property: 'expiryDate', width: 11, isDate: true },
    { col: 'F', colSecond: 'G', value: 'Số chứng từ', isOneRow: true },
    { col: 'F', value: 'Nhập', property: 'transCode', width: 26, isSecondRow: true, isLeftAlignment: true },
    { col: 'G', value: 'Xuất', property: 'transCode', width: 26, isSecondRow: true, isLeftAlignment: true },
    { col: 'H', value: 'Loại giao dịch', property: 'transName', width: 20, isLeftAlignment: true },
    { col: 'I', colSecond: 'L', value: 'Số lượng', width: 12, isOneRow: true },
    { col: 'I', value: 'Tồn đầu', property: 'begin', width: 9, isSecondRow: true },
    { col: 'J', value: 'Nhập', property: 'in', width: 8, isSecondRow: true },
    { col: 'K', value: 'Xuất', property: 'out', width: 8, isSecondRow: true },
    { col: 'L', value: 'Tồn cuối', property: 'end', width: 9, isSecondRow: true },
  ],
  outLineContent: [
    // { col: 'A1', endCol: 'F1', value: 'CÔNG TY CỔ PHẨN DƯỢC PHẨM FPT LONG CHÂU', isBold: true, size: 11 },
    // { col: 'A2', endCol: 'F2', value: OUTLINE_PROPS.SHOP_NAME, isBold: true, size: 11 },
    {
      col: 'A1',
      endCol: 'L1',
      value: 'THẺ KHO',
      isBold: true,
      size: 15,
    },
    { col: 'A2', endCol: 'L2', value: OUTLINE_PROPS.REPORT_TIME, isBold: false, isItalic: true, size: 11 },
  ],
  fontSize: {
    normal: 11,
    title: 11,
  },
  lineData: {
    titleFisrt: 8,
    titleSecond: 9,
    dataRowFisrt: 10,
    dataRowSecond: 11,
    limitCellNum: 12,
    limitCellNumTemp2: 13,
  },
};

export const TransType = {
  BanHang: 1,
  LCNB: 5,
};
