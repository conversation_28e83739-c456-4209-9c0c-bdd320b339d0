import { NotificationTemplate } from '../dto/notification-template.dto';
import { NotificationType } from '../enum/notification-type.enum';

/**
 * <key> is variable in template, it will be raplace by value in params
 */

export const AssignSurveyTemplate = (extraProperties: any): NotificationTemplate => {
  return {
    templateId: process.env.NOTIFICATION_TEMPLATE_ID,
    notificationTitle: 'Yêu cầu KH tiềm năng mới. SDT: <phoneNumberSurvey>.',
    notificationContent: 'Yêu cầu KH tiềm năng mới. SDT: <phoneNumberSurvey>.',
    messageLink: `potential-customer?redirect=<idSurvey>`,
    imageLink: '',
    contentFailOver: {},
    extraProperties: {
      type: NotificationType.SURVEYS_ASSIGN,
      ...extraProperties,
    },
  };
};

export const AssignSurveyOcrTemplate = (extraProperties: any): NotificationTemplate => {
  return {
    templateId: process.env.NOTIFICATION_TEMPLATE_ID,
    notificationTitle: 'Yêu cầu OCR đơn thuốc mới. SĐT: <phoneNumberSurvey>',
    notificationContent: 'Yêu cầu OCR đơn thuốc mới. SĐT: <phoneNumberSurvey>',
    messageLink: `potential-customer?type=4&surveyId=<idSurvey>&surveyPhoneNumber=<phoneNumberSurvey>`,
    imageLink: '',
    contentFailOver: {},
    extraProperties: {
      type: NotificationType.SURVEYS_ASSIGN,
      ...extraProperties,
    },
  };
};

export const AssignSurveyOsrTemplate = (extraProperties: any): NotificationTemplate => {
  return {
    templateId: process.env.NOTIFICATION_TEMPLATE_ID,
    notificationTitle: 'Vui lòng liên hệ lại khách hàng. SĐT: <phoneNumberSurvey>',
    notificationContent: 'Vui lòng liên hệ lại khách hàng. SĐT: <phoneNumberSurvey>',
    messageLink: `potential-customer?type=4&surveyId=<idSurvey>&surveyPhoneNumber=<phoneNumberSurvey>`,
    imageLink: '',
    contentFailOver: {},
    extraProperties: {
      type: NotificationType.SURVEYS_ASSIGN,
      ...extraProperties,
    },
  };
};
