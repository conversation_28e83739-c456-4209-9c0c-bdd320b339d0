import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { GetRabiesDeclarationRes, PostRabiesDeclarationDto, PostRabiesDeclarationRes } from 'vac-nest-examination';
import { ItemQuestionRabiesDto } from 'vac-nest-monitor';

export class CreateRabiesDto {
  @ApiProperty({ isArray: true, type: PostRabiesDeclarationDto })
  @Expose()
  @Type(() => PostRabiesDeclarationDto)
  rabies: PostRabiesDeclarationDto[];
}

export class CreateRabiesRes {
  @ApiProperty({ isArray: true, type: PostRabiesDeclarationRes })
  @Expose()
  @Type(() => PostRabiesDeclarationRes)
  items: PostRabiesDeclarationRes[];
}

export class GetHistoryRabiesRes {
  @ApiProperty({ isArray: true, type: GetRabiesDeclarationRes })
  @Expose()
  @Type(() => GetRabiesDeclarationRes)
  items: GetRabiesDeclarationRes[];

  @ApiProperty({ isArray: true, type: ItemQuestionRabiesDto })
  @Expose()
  questions: ItemQuestionRabiesDto[];
}
