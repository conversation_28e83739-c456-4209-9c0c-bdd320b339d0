import { Injectable } from '@nestjs/common';
import { NotificationService } from 'modules/modules/modules/notification';
import {
  formatNotificationPayload,
  generateRoleShopAddresses,
} from 'modules/modules/modules/notification/notification.utils';
import {
  PostVaccinationFinishMonitor,
  PostVaccinationFinishMonitorWithParam,
} from 'modules/modules/modules/notification/templates/post-vaccination-finish-monitor';
import { SendNotificationFinishVaccineDto } from './dto/send-notification-finish-vaccine.dto';
import { NOTI_REGISTER_ROLE } from 'modules/modules/modules/notification/notification.enum';

@Injectable()
export class PostVaccinationService {
  constructor(private readonly notificationService: NotificationService) {}

  finishVaccination(payload: SendNotificationFinishVaccineDto) {
    return this.notificationService.sendNotifications(
      [
        formatNotificationPayload({
          To: generateRoleShopAddresses([NOTI_REGISTER_ROLE.Nursing], payload.shopCode),
          Cc: [],
          Bcc: [],
          template: PostVaccinationFinishMonitorWithParam({
            customerName: payload.customerName,
            ticketId: payload.ticketId,
            shopCode: payload.shopCode,
            ticketCode: payload.ticketId,
          }),
          replaceParams: {
            title: {
              customerName: payload.customerName,
            },
            content: {
              ticketId: payload.ticketId,
            },
          },
        }),
      ],
      true,
    );
  }
}
