import { Module } from '@nestjs/common';
import { RsaBeV2Module } from 'vac-nest-rsa-be-v2';
import { DepositCancelForEcomController } from './controllers/deposit-cancel-ecom.controller';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { OMSModule } from 'vac-nest-oms';

@Module({
  imports: [RsaBeV2Module, ExaminationCoreModule, OMSModule],
  controllers: [DepositCancelForEcomController],
})
export class DepositCancelAffiliateModule {}
