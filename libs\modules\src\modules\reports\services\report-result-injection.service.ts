import { Injectable, Logger } from '@nestjs/common';
import { InjectS3, S3Client } from 'ict-nest-s3';
import { S3_OPERATIONS } from 'ict-nest-s3/dist/s3.constant';
import _ from 'lodash';
import moment from 'moment';
import { ExaminationCoreService, ItemTicket } from 'vac-nest-examination';
import { FamilyService } from 'vac-nest-family';
import {
  CreateResultInjectingDto,
  CreateResultInjectingRes,
  OsrService,
  ResultInjection,
  SearchResultInjectingByQueryDto,
  SearchResultInjectingByQueryRes,
  UpdateResultInjectingDto,
  UpdateResultInjectingRes,
} from 'vac-nest-osr';
import { PrintCenterRes, PrintCenterService } from 'vac-nest-print-center';
import { ReportResultInjectionAbstractFactory } from '../adapters/report-result-injection.abstract';
import { PRE_SIGNED_LINK_EXPIRATION, REPORT_S3_TYPE } from '../constants';
import { PrintReportResultInjectionDto } from '../dto/print-report-result-injection.dto';
import {
  DEFERRED_SUBJECTS_THIS_BATCH,
  POST_VACCINATION_REACTION_REPORT,
  TRIPLE_DOSE_INJECTION,
  VACCINATION_REPORT_SECTION,
  VACCINE_SUPPLIES,
} from '../enum/report-result-injection.enum';
import { generateStringByDateTime, getResultInjectionExcelReport } from '../functions';

@Injectable()
export class ReportResultInjectionService extends ReportResultInjectionAbstractFactory {
  constructor(
    protected readonly osrService: OsrService,
    protected readonly printCenterService: PrintCenterService,
    protected readonly examinationCoreService: ExaminationCoreService,
    protected readonly familyService: FamilyService,
    @InjectS3(process.env.S3_CONNECTION)
    private readonly s3Client: S3Client,
  ) {
    super();
  }

  async printPDF(printReportResultInjectionDto: PrintReportResultInjectionDto): Promise<PrintCenterRes> {
    const YEAR_START = 2009;
    const YEAR_END = 2023;

    const { printerInfo } = printReportResultInjectionDto;
    const { items } = await this.osrService.searchResultInjectingByQuery(printReportResultInjectionDto);

    const printDataDto: { key: string; value: string; value2: string }[] = [];
    Array.from({ length: YEAR_END - YEAR_START + 1 }, (__, i) => {
      const itemFind = items.find((item) => item.code === (YEAR_START + i).toString());
      printDataDto.push({
        key: (YEAR_START + i).toString(),
        value: itemFind?.value || '',
        value2: itemFind?.value2 || '',
      });
    });
    const itemsFind = items.find((item) => item.code === TRIPLE_DOSE_INJECTION.CODE);
    printDataDto.push({
      key: TRIPLE_DOSE_INJECTION.CODE,
      value: itemsFind?.value || '',
      value2: itemsFind?.value2 || '',
    });

    const codesToSearch = [
      ...Object.values(DEFERRED_SUBJECTS_THIS_BATCH),
      ...Object.values(VACCINE_SUPPLIES),
      ...Object.values(POST_VACCINATION_REACTION_REPORT),
    ];

    codesToSearch.forEach((value) => {
      const itemsFindCode = items.find((item) => item.code === value);
      printDataDto.push({
        key: value,
        value: itemsFindCode?.value || '',
        value2: itemsFindCode?.value2 || '',
      });
    });

    return this.printCenterService.printInvoicePreview({
      ...printReportResultInjectionDto,
      printerInfo: printerInfo,
      printType: 'report_vaccine_mr',
      ignorePrint: printerInfo?.ignorePrint,
      printData: {
        ...printReportResultInjectionDto,
        shopName: items.find((item) => item.shopCode === printReportResultInjectionDto.shopCode?.at(0))?.shopName,
        tableData: printDataDto,
      },
    });
  }

  async printExcel(printReportResultInjectionDto: PrintReportResultInjectionDto): Promise<PrintCenterRes> {
    const YEAR_START = 2009;
    const YEAR_END = 2023;

    const { items } = await this.osrService.searchResultInjectingByQuery(printReportResultInjectionDto);

    const printDataDto: { key: string; value: string; value2: string }[] = [];
    Array.from({ length: YEAR_END - YEAR_START + 1 }, (__, i) => {
      const itemFind = items.find((item) => item.code === (YEAR_START + i).toString());
      printDataDto.push({
        key: (YEAR_START + i).toString(),
        value: itemFind?.value || '',
        value2: itemFind?.value2 || '',
      });
    });
    const itemsFind = items.find((item) => item.code === TRIPLE_DOSE_INJECTION.CODE);
    printDataDto.push({
      key: TRIPLE_DOSE_INJECTION.CODE,
      value: itemsFind?.value || '',
      value2: itemsFind?.value2 || '',
    });

    const codesToSearch = [
      ...Object.values(DEFERRED_SUBJECTS_THIS_BATCH),
      ...Object.values(VACCINE_SUPPLIES),
      ...Object.values(POST_VACCINATION_REACTION_REPORT),
    ];

    codesToSearch.forEach((value) => {
      const itemsFindCode = items.find((item) => item.code === value);
      printDataDto.push({
        key: value,
        value: itemsFindCode?.value || '',
        value2: itemsFindCode?.value2 || '',
      });
    });

    const formattedDate = moment(printReportResultInjectionDto?.fromInjectionDate).format('DD/MM/YYYY');
    const sheetName = moment(printReportResultInjectionDto?.fromInjectionDate).format('DD.MM.YYYY');
    printDataDto.push({ key: 'date', value: formattedDate, value2: '' });

    // Tính cột tổng mũi tiêm delay
    const totalDelayInjections = _.sumBy(
      ['ONE_MONTH_LIVE_VACCINE_COUNT', 'OTHER_DEFERRED_SUBJECTS', 'CONTRAINDICATION_COUNT'],
      (key) => +(_.find(printDataDto, { key })?.value || 0),
    );

    printDataDto.push({ key: 'TOTAL_DELAY_INJECTIONS', value: `${totalDelayInjections}`, value2: '' });

    const shopCode = items.find((item) => item.shopCode === printReportResultInjectionDto.shopCode?.at(0))?.shopCode;
    const buffer = await getResultInjectionExcelReport({
      sheetName,
      excelData: printDataDto,
    });

    const dateString = generateStringByDateTime();
    const fileName = `${REPORT_S3_TYPE.SOI_RUBELLA_INJECTION_RESULT}/BaoCaoTiemSoiRubella_${sheetName}_${shopCode}_${dateString}.xlsx`;
    await this.s3Client.uploadFileToS3(buffer, fileName);

    const arrLinks = await this.s3Client.presignObjects(
      [fileName],
      S3_OPERATIONS.GET_OBJECT,
      PRE_SIGNED_LINK_EXPIRATION,
    );

    return { url: arrLinks[0], imageUrl: '' };
  }

  createBulk(createResultInjectingDto: CreateResultInjectingDto): Promise<CreateResultInjectingRes> {
    return this.osrService.createResultInjecting(createResultInjectingDto);
  }

  updateBulk(updateResultInjectingDto: UpdateResultInjectingDto): Promise<UpdateResultInjectingRes> {
    return this.osrService.updateResultInjecting(updateResultInjectingDto);
  }

  async searchByQuery(
    searchResultInjectingByQueryDto: SearchResultInjectingByQueryDto,
  ): Promise<SearchResultInjectingByQueryRes> {
    const { items, totalCount } = await this.osrService.searchResultInjectingByQuery(searchResultInjectingByQueryDto);
    const yearAutoMap: ResultInjection[] = [];
    if (!items?.length) {
      let allDataFetched = false;
      let skipCount = 0;
      const maxResultCount = 100;
      const arrItems: ItemTicket[] = [];
      while (!allDataFetched) {
        try {
          const { items: batchData } = await this.examinationCoreService.searchTicketBySku({
            skus: ['00047222'],
            fromDate: searchResultInjectingByQueryDto.fromInjectionDate,
            toDate: searchResultInjectingByQueryDto.toInjectionDate,
            shopCode: searchResultInjectingByQueryDto.shopCode?.at(0),
            statuses: [8],
            skipCount,
            maxResultCount,
          });

          // Assuming batchData is an array of the data fetched
          if (batchData.length > 0) {
            // Process and add the batch data to arrReturn
            // For example, if you need to transform or directly add the batchData
            arrItems.push(...batchData); // Adjust this line based on the actual structure of batchData
            if (batchData.length < maxResultCount) {
              // If the number of items in the batch is less than maxResultCount, it means this is the last batch
              allDataFetched = true;
            } else {
              // Prepare for the next iteration
              skipCount += maxResultCount;
            }
          } else {
            // No more data to fetch
            allDataFetched = true;
          }
        } catch (error) {
          allDataFetched = true;
          Logger.log({
            fields: {
              info: '[searchByQuery] Error fetching data',
              error: error,
            },
          });
        }
      }

      const arrLCVId = _.uniq(arrItems.map((item) => item.lcvId));
      if (!arrLCVId?.length) {
        return { items: [], totalCount: 0, yearAutoMap: [] } as any;
      }
      const person = await this.familyService.getListPrimaryPerson(arrLCVId);
      const arrYear = person.map((item) => new Date(item.dateOfBirth).getFullYear());
      Logger.log(`arrYear: ${JSON.stringify(arrYear)}`);
      // count by year use new Map
      const countByYear = arrYear.reduce(
        (acc, curr) => acc.set(curr, 1 + (acc.get(curr) || 0)),
        new Map<number, number>(),
      );
      const YEAR_START = 2009;
      const YEAR_END = 2023;
      Array.from({ length: YEAR_END - YEAR_START + 1 }, (__, i) => {
        const valueYear = countByYear.get(YEAR_START + i);
        yearAutoMap.push({
          code: (YEAR_START + i).toString(),
          value: valueYear?.toString() || '',
          value2: '',
          area: VACCINATION_REPORT_SECTION.SECTION_1,
          injectionDate: moment(searchResultInjectingByQueryDto.toInjectionDate).format('YYYY-MM-DD'),
          shopCode: searchResultInjectingByQueryDto.shopCode?.at(0),
          shopName: '',
        });
      });
    }

    return { items, totalCount, yearAutoMap } as any;
  }
}
