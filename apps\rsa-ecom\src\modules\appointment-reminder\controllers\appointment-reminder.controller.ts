import { Body, Controller, Get, Post, Put } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AppointmentReminderService } from '../services';
import { GetARDetailDto } from 'vac-nest-schedule';
import { UpdateARDtoV2 } from '../dto/update-ar.dto';
import { SearchARV2Dto } from '../dto/search-ar-v2.dto';
import { ConfigService } from '@nestjs/config';
import { SendNotificationDto } from '../dto/send-noitification.dto';
import { Public } from '@shared';

@Controller({ path: 'appointment-reminder', version: '1' })
@ApiTags('Appointment Reminder')
@ApiBearerAuth('defaultJWT')
export class AppointmentReminderController {
  constructor(private readonly arService: AppointmentReminderService, private readonly configService: ConfigService) {}

  @Get('master-data/all-status')
  getAllStatus() {
    return this.arService.getAllStatus();
  }

  @Post('search')
  searchARList(@Body() payload: SearchARV2Dto) {
    return this.arService.searchARList(payload);
  }

  @Post('detail')
  getARDetails(@Body() payload: GetARDetailDto) {
    return this.arService.getARDetails(payload);
  }

  @Put('update-appointments')
  updateAppointments(@Body() payload: UpdateARDtoV2) {
    return this.arService.updateAppointments(payload);
  }

  @Public()
  @Post('send-notification')
  sendNotification(@Body() { phoneNumbers, customerName, appointmentDate }: SendNotificationDto) {
    return this.arService.sendSMSNotification(phoneNumbers, customerName, appointmentDate);
  }
}
