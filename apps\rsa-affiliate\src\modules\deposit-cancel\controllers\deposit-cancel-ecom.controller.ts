import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, UseInterceptors } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, CustomHeaders, Public, generalSchema } from '@shared';
import { TicketDetailRes } from 'vac-nest-examination';
import {
  CancelDepositTransfer,
  CancelDepositWithOtpDto,
  CancelDepositWithSMDto,
  ConfirmReasonCancelDeposit,
  CreateDepositCancelDto,
  CreateDepositCancelRes,
  GetListDepositCancelRes,
  GetOneDepositCancelRes,
  ReturnCashOTPDto,
  ReturnCashSMDto,
  UpdateDepositCancelRes,
} from '../../../../../../libs/modules/src/modules/deposit-cancel/dto';
import { SendSMSTransferDto } from '../../../../../../libs/modules/src/modules/deposit-cancel/dto/send-sms-transfer';
import { AppHeaders } from '@shared/common/decorators/app-header.decorator';
import { RsaBeV2Service } from 'vac-nest-rsa-be-v2';
import { CancelOrderLibResponse, GetOneOrderLibResponse } from 'vac-nest-oms';
import { SendSMSSMDto } from 'modules/modules/modules/deposit-cancel/dto/send-sms-sm.dto';
import { BlockDepositCancelAffiliateInterceptor } from '../intercepter/depost-cancel-affiliate.interceptor';

@Controller({ path: 'deposit-cancel', version: '1' })
@ApiTags('DepositCancel')
@ApiExtraModels(
  ClassResponse,
  CreateDepositCancelRes,
  GetListDepositCancelRes,
  GetOneDepositCancelRes,
  UpdateDepositCancelRes,
)
@CustomHeaders()
@UseInterceptors(BlockDepositCancelAffiliateInterceptor)
@ApiBearerAuth('defaultJWT')
export class DepositCancelForEcomController {
  constructor(private readonly rsaService: RsaBeV2Service) {}

  @Post('confirm-cancel')
  @ApiOperation({
    summary: 'Xác nhận lý do hủy cọc',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin hủy cọc',
    schema: generalSchema(CreateDepositCancelRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  confirmReasonCancelDeposit(@Body() confirmReasonCancelDeposit: ConfirmReasonCancelDeposit) {
    return this.rsaService.confirmCancel<ConfirmReasonCancelDeposit, unknown>(confirmReasonCancelDeposit);
  }

  @Get(':orderCode')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết hủy cọc orderCode',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin hủy cọc',
    schema: generalSchema(CreateDepositCancelRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  getDetail(@Param('orderCode') orderCode: string) {
    return this.rsaService.getDepositCancelDetail<CreateDepositCancelDto>(orderCode);
  }

  @Get('for-ntlc/:orderCode')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết hủy cọc orderCode',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin hủy cọc',
    schema: generalSchema(CreateDepositCancelRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  getDetailForNTLC(@Param('orderCode') orderCode: string) {
    return this.rsaService.getDepositCancelDetailForNTLC(orderCode);
  }

  /**
   * @TODO huỷ cọc chi tiền với tiền mặt bằng OTP
   * @param body
   * @returns
   */
  @Public()
  @Post('confirm-payment-deposit-cancel-by-otp')
  @ApiOperation({
    summary: 'chi tiền hủy cọc bằng OTP',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin huỷ cọc đối với chi tiền bằng OTP',
    schema: generalSchema(CreateDepositCancelRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  cancelDepositWithOtp(@Body() cancelDepositOtp: CancelDepositWithOtpDto) {
    return this.rsaService.cancelDepositWithOtp<
      CancelDepositWithOtpDto,
      CancelOrderLibResponse | GetOneOrderLibResponse
    >(cancelDepositOtp);
  }

  @Public()
  @Post('confirm-payment-deposit-cancel-by-transfer')
  @ApiOperation({
    summary: 'chi tiền hủy cọc bằng Chuyen khoan',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin huỷ cọc đối với chi tiền bằng Chuyen63 khoan',
    schema: generalSchema(CreateDepositCancelRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  cancelDepositByTransfer(@Body() cancelDepositTransfer: CancelDepositTransfer) {
    return this.rsaService.cancelDepositByTransfer<CancelDepositTransfer, CancelOrderLibResponse>(
      cancelDepositTransfer,
    );
  }

  /**
   * @TODO xác nhận chi tiền hủy cọc bằng SM
   */
  @Public()
  @Post('confirm-payment-deposit-cancel-by-sm')
  @ApiOperation({
    summary: 'xác nhận chi tiền hủy cọc bởi SM',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin xác nhận chi tiền hủy cọc bởi SM',
    schema: generalSchema(CreateDepositCancelRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  cancelDepositWithSM(@Body() cancelDepositBySM: CancelDepositWithSMDto, @AppHeaders('authorization') author) {
    return this.rsaService.cancelDepositOtpBySM(cancelDepositBySM, {
      headers: {
        Authorization: author,
      },
    });
  }

  /**
   * @TODO Chi tiền thừa verify OTP
   */
  @Public()
  @Post('return-cash-otp')
  @ApiOperation({
    summary: 'Chi tiền thừa verify OTP',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin phiếu khám',
    schema: generalSchema(TicketDetailRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  returnCashOTP(@Body() body: ReturnCashOTPDto) {
    return this.rsaService.returnCashOtp<ReturnCashOTPDto, CancelOrderLibResponse | GetOneOrderLibResponse>(body);
  }

  /**
   * @TODO Chi tiền thừa SM approve
   */
  @Public()
  @Post('return-cash-sm')
  @ApiOperation({
    summary: 'Chi tiền thừa SM approve',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin phiếu khám',
    schema: generalSchema(TicketDetailRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  returnCashDeposit(@Body() body: ReturnCashSMDto) {
    return this.rsaService.returnCashSM<ReturnCashSMDto, CancelOrderLibResponse | GetOneOrderLibResponse>(body);
  }

  @Public()
  @Post('send-sms-transfer')
  @ApiOperation({
    summary: 'Gửi link chuyển khoản',
  })
  @HttpCode(HttpStatus.OK)
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  sendSMSTransfer(@Body() body: SendSMSTransferDto) {
    return this.rsaService.sendDepositCancelSMSTransfer<SendSMSTransferDto, unknown>(body);
  }

  @Public()
  @Post('send-sms-sm')
  @ApiOperation({
    summary: 'Gửi link chuyển khoản',
  })
  @HttpCode(HttpStatus.OK)
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  sendSMSSM(@Body() body: SendSMSSMDto) {
    return this.rsaService.sendDepositCancelSMSSM<SendSMSSMDto, unknown>(body);
  }
}
