import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddValueType3Clone1704788521781 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '3', '1', '<PERSON>h<PERSON>ch không tiêm được hôm nay')`,
    );
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '3', '1', '<PERSON>h<PERSON>ch có nhu cầu tiêm thêm mũi')`,
    );
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '3', '1', '<PERSON>h<PERSON>ch muốn đổi vắc xin')`,
    );
    await queryRunner.query(`INSERT INTO reasons ("type", "status", "name") VALUES( '3', '1', '<PERSON> thao tác')`);
    await queryRunner.query(`INSERT INTO reasons ("type", "status", "name") VALUES( '3', '1', 'Khác')`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('');
  }
}
