import { OrderChannels } from '@shared/constants';

export enum TENANT {
  VACCINE = 'D1B85F69-5565-4301-8602-0FAA88DB6F39',
}

export enum APPLICATION {
  RSA = '3e6246b4-bf7b-49c2-a387-0e40c7522fec',
  RSA_AFFILIATE = '1a10cdd7-2d87-4e1a-9d8a-a4416d1e0d04',
  mRSALC = '0b2e85eb-3f3a-4933-9ac9-7f59cea72bb9',
  RSA_ECOM = '456e0dc4-475a-423c-9689-4413ab1f9c0b',
}

export const PERMISSION = {
  HUY_COC_CHI_TIEN: [
    'VAC_Permission_Order_CancelDeposit_ASMApproveOTP',
    'RSA_Affiliate_Permission_Order_CancelDeposit_ASMApproveOTP',
    'VAC_mRSA_Permission_Order_CancelDeposit_ASMApproveOTP',
    'VAC_Permission_ASMApproveOTP',
  ],
  DELETE_SCHEDULE_HISTORY_TCQG: ['VAC_Permission_HistorySyncTCQG_Delete_ASMApproveOTP', 'VAC_Permission_ASMApproveOTP'],
  FAMILY_PACKAGE: 'VAC_Permission_FamilyPackage_ApproveASMOTP',
  ASM_PERMISSION: 'VAC_Permission_ASMApproveOTP',
};

export const GetApplicationByChanel = (chanel: string) => {
  if (!chanel) return APPLICATION.RSA;
  if (OrderChannels.RSA.includes(chanel)) {
    return APPLICATION.RSA;
  }

  if (OrderChannels?.RSA_AFFILIATE?.includes(chanel)) {
    return APPLICATION.RSA_AFFILIATE;
  }
};
