import { Injectable } from '@nestjs/common';
import { GetEmployeeWorkingInfoRes, GetInforEmployeeShiftDateDto, InsideService } from 'vac-nest-inside';
import { GetEmployeeWorkingInfoDto, GetListEmployeeShiftDateDto } from '../dto';
import _ from 'lodash';

@Injectable()
export class UsersService {
  constructor(private readonly insideService: InsideService) {}

  async getInforEmployeeShiftDate(getListEmployeeShiftDateDto: GetListEmployeeShiftDateDto) {
    const getInforEmployeeShiftDateDto: GetInforEmployeeShiftDateDto = {
      WarehouseCode: getListEmployeeShiftDateDto?.shopCode || '',
      Ngay: getListEmployeeShiftDateDto?.shiftDate || null,
    };

    const getInforEmployee = await this.insideService.getInforEmployeeShiftDate(getInforEmployeeShiftDateDto);

    const listEmployee = getListEmployeeShiftDateDto?.jobTitleCode?.length
      ? getInforEmployee?.filter((employee) =>
          getListEmployeeShiftDateDto?.jobTitleCode?.includes(employee?.MaChucDanh),
        )
      : getInforEmployee;

    const listEmployeeMapInfo = _.uniqBy(
      listEmployee?.map((entry) => ({
        shopCode: entry?.MaShop,
        employeeCode: entry?.MaNhanVien,
        employeeName: entry?.TenNhanVien,
        jobTitleCode: entry?.MaChucDanh,
        jobTitleName: entry?.TenChucDanh,
        phone: entry?.SDT,
        shiftStartTime: entry?.ThoiGianBatDau,
        shiftEndTime: entry?.ThoiGianKetThuc,
        email: entry?.Email,
      })) || [],
      (item) => `${item.employeeCode}-${item.employeeName}`,
    ); //FV-13959

    return listEmployeeMapInfo;
  }
  async getListEmployeeWorkingInfo(getEmployeeWorkingInfoDto: GetEmployeeWorkingInfoDto) {
    const jobCodeForSale = (process.env.INSIDE_JOB_CODE_SALE || '')?.split(',');
    const payload = {
      WarehouseCode: getEmployeeWorkingInfoDto.shopCode,
    };
    const getInforEmployee = await this.insideService.getEmployeeWorkingInfo(payload);
    const arrTitle = getInforEmployee?.filter((e) => jobCodeForSale.includes(e?.jobTitleCode));
    // uni
    return _.uniqBy(arrTitle || [], (item: GetEmployeeWorkingInfoRes) => `${item?.employeeCode}-${item?.employeeName}`);
  }
}
