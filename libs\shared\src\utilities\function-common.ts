/* eslint-disable @typescript-eslint/no-inferrable-types */
import moment from 'moment';
import _ from 'lodash';
import { Logger } from '@nestjs/common';

type Await<T> = T extends Promise<infer U> ? U : T;
type AwaitProps<T> = { [P in keyof T]: Await<T[P]> };

/**
 *
 * @param {Promise} ...args
 * @returns arrPromise
 */
export async function concurrentPromise<T extends Promise<any>[]>(...args: T): Promise<AwaitProps<T>> {
  const arrPromise = await Promise.allSettled(args);
  return arrPromise.map((entry) => {
    if (entry.status === 'fulfilled') {
      return entry.value;
    }
    return null;
  }) as AwaitProps<T>;
}

/**
 *
 * @param {Promise} ...args
 * @returns arrData
 */
export async function concurrentPromiseThrowError<T extends Promise<any>[]>(...args: T): Promise<AwaitProps<T>> {
  const arrPromise = await Promise.all(args);
  return arrPromise as AwaitProps<T>;
}

/**
 *
 * @param {Date} date format date
 * @param {String} timeZone default +00:00
 * @param {String} format default ""
 * @returns String
 */
export function parseDateTimeZone(date: Date, timeZone: '+00:00' | '+07:00', format: string = ''): string {
  return moment(date).utcOffset(timeZone).format(format);
}

/**
 *
 * @param date string
 * @returns Boolean
 */
export const isDayInThePast = (date: Date) => {
  const inputDate = moment(date).utcOffset('+07:00');
  const yesterday = moment().utcOffset('+07:00').startOf('day'); //.subtract(1, 'milliseconds');
  return inputDate.isBefore(yesterday);
};

/**
 *
 * @param {Date} date format date
 * @param {String} timeZone default +00:00
 * @param {String} format default ""
 * @returns String
 */
export function parseDateTimeZoneV2(date: Date, timeZone: '+00:00' | '+07:00') {
  return moment(date).utcOffset(timeZone);
}

/**
 *
 * @param dateTimeString string
 * @returns Date with timezone
 */
export const dateWithTimezone = (dateTimeString: string) => {
  //2024-05-09T10:42:32
  const timezoneRegex = /(\+[\d:]+$)|\z$/;
  const hasTimezoneOffset = timezoneRegex.test(dateTimeString);
  let dateTimeStr = dateTimeString;
  if (!hasTimezoneOffset) {
    // Split without affecting timezone offset
    const parts = dateTimeString.split('T', 2); // Limit split to 2 elements
    const dateStr = parts[0];
    const timeStr = parts[1];
    dateTimeStr = `${dateStr}T${timeStr}+07:00`;
  }
  return dateTimeStr;
};

/**
 *
 * @param {Date} date format date
 * @param {String} timeZone default +00:00
 * @param {String} format default ""
 * @returns String
 */
export function getStartDate(date: Date, timeZone: '+00:00' | '+07:00', format: string = ''): string {
  return moment(date).format(format);
}

export function getStartDateV2(date: Date, timeZone: '+00:00' | '+07:00', format: string = ''): string {
  return moment(date).utcOffset(timeZone).format(format);
}

export function isSameDate(dateA: Date, dateB: Date, timzeZone: string = '+07:00'): boolean {
  return moment(moment(dateA).utcOffset(timzeZone).format('YYYY-MM-DD')).isSame(
    moment(dateB).utcOffset(timzeZone).format('YYYY-MM-DD'),
    'D',
  );
}

export function formatDate(date: Date): string {
  return moment(date).utcOffset(7).format('DD-MM-YYYY');
}

export function getYear(date: Date): number {
  const formattedDate = moment(moment(date).utcOffset(7).format('YYYY-MM-DD'));
  return Number(formattedDate?.year());
}

/**
 * @description parse phone number
 */
export function parsePhoneNumber(phoneNumber: string, suffix?: '84'): string {
  if (phoneNumber.startsWith('0')) {
    phoneNumber = `${suffix}${phoneNumber.substring(1)}`;
  }
  return phoneNumber;
}

/**
 *  @description parse json
 */
export function parseJson(stringInput) {
  try {
    return JSON.parse(stringInput);
  } catch (ex) {
    return {};
  }
}

/**
 * @TODO valid phone number
 * @param phoneNumber
 * @returns boolean
 */
export function validateVietnamesePhoneNumber(phoneNumber) {
  const regex = /^(0[2-9]|84[2-9])(\d{8}|\d{9})$/;
  return regex.test(phoneNumber);
}

export function getRemainingSecondsToMidnight() {
  // Lấy thời điểm hiện tại
  const now = moment();
  // Tạo thời điểm 24h cùng ngày
  const midnight = moment().endOf('day');
  // Tính thời gian chênh lệch giữa 2 thời điểm (tính bằng miligiây)
  const diffMilliseconds = midnight.diff(now);
  // Chuyển đổi sang giây
  const diffSeconds = diffMilliseconds / 1000;
  // Trả về kết quả
  return Math.floor(diffSeconds);
}

export function removeVietnameseTones(str) {
  if (_.isEmpty(str) || str == null) {
    return '';
  }
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A');
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E');
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O');
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U');
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y');
  str = str.replace(/Đ/g, 'D');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
  // Remove extra spaces
  // Bỏ các khoảng trắng liền nhau
  str = str.replace(/ + /g, ' ');
  str = str.trim();
  // Remove punctuations
  // Bỏ dấu câu, kí tự đặc biệt
  str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g, ' ');
  return str;
}

export function getShortedLcvId(lcvId: string, length: number = 9) {
  if (!lcvId) return '';

  const matchAllNumericExceptCharacter = new RegExp(/[0-9]/g);

  // remove hết số trong chuỗi
  const prefixChar = lcvId.replace(matchAllNumericExceptCharacter, '');

  // trả về các ký tự đầu và lấy các ký tự ở cuối sao cho tổng độ dài = 9 hoặc = length truyền vào
  return `${prefixChar}${lcvId.slice(-(length - prefixChar?.length))}`;
}

/**
 * @TODO Compare 2 objects
 * @param object
 * @returns boolean
 */
export function haveDifferentValues(objectBefore, objectAfter) {
  // Get all the keys from both objects
  const keys = new Set([...Object.keys(objectBefore), ...Object.keys(objectAfter)]);

  // Iterate through each key
  for (const key of keys) {
    const val1 = objectBefore[key];
    const val2 = objectAfter[key];

    // Consider undefined and null as equivalent
    if (val1 !== val2 && !(val1 == null && val2 == null)) {
      return true;
    }
  }

  return false;
}

export function getDayMonthYear(date: Date): { day: number; month: number; year: number } {
  const formattedDate = moment(moment(date).utcOffset(7).format('YYYY-MM-DD'));
  return {
    day: Number(formattedDate?.date()),
    month: Number(formattedDate?.month() + 1),
    year: Number(formattedDate?.year()),
  };
}
/**
 * @description so sánh ngày sau nhỏ hơn hoặc bằng ngày trước
 * @param dateA
 * @param dateB
 * @param timzeZone
 * @returns
 */
export function isDateBSmallerDateA(dateA: Date, dateB: Date, timzeZone: string = '+07:00'): boolean {
  Logger.log(`DateA: ${moment(dateA).utcOffset(timzeZone).format('YYYY-MM-DD')}`);
  Logger.log(`DateB: ${moment(dateB).utcOffset(timzeZone).format('YYYY-MM-DD')}`);
  return moment(moment(dateA).utcOffset(timzeZone).format('YYYY-MM-DD')).isSameOrBefore(
    moment(dateB).utcOffset(timzeZone).format('YYYY-MM-DD'),
    'D',
  );
}

export const checkStringIncludes = (includeText: string, text: string | string[]) => {
  let textForCompare = text;
  if (typeof textForCompare === 'string') {
    textForCompare = [textForCompare];
  }

  return textForCompare?.some((subText) => {
    return removeVietnameseTones(subText)
      ?.trim()
      ?.toLocaleLowerCase()
      ?.includes(removeVietnameseTones(includeText)?.trim()?.toLowerCase());
  });
};

export const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('vi-VN', { minimumFractionDigits: 0, maximumFractionDigits: 2 }).format(value);
};

export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
