import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, CustomHeaders, Public, generalSchema } from '@shared';
import {
  CreateCustomerAddressDto,
  CreateCustomerAddressParasDto,
  DeleteCustomerAddressDto,
  UpdateCustomerAddressDto,
  UpdateCustomerAddressParasDto,
} from 'vac-nest-customer-core';
import {
  CreatePersonDto,
  CreatePersonRes,
  DeleteFamilyProfileDto,
  GetDetailFamilyDto,
  GetFamilyByLcvIdDto,
  GetPersonByIdRes,
  PayloadGetManyLcvIdDto,
  Person,
  SearchPersonDto,
  SearchPersonRes,
  UpdatePersonDto,
  UpdatePersonRes,
  VerifyByPhoneRes,
  VerifyPhoneNumberDto,
  VerifyPhoneNumberRes,
} from 'vac-nest-family';
import {
  CreateCustomerTcqgBody,
  CreateCustomerTcqgResponse,
  CreateManyPersonDto,
  CustomSearchCustomerResponse,
  DeletePhoneNumberForPersonDto,
  GetCustomerByLcvIdRes,
  GetCustomerByPhoneDTO,
  GetImgIdentificationRes,
  GetManyLcvIdRes,
  GetTcqgDto,
  GetTitleRes,
  InactivePersonPayload,
  MapTcqgToPersonBody,
  MapTcqgToPersonResponse,
  PayloadCreateOtpDto,
  PayloadVerifyOTPDto,
  ResCreateOtp,
  SearchCustomerResponse,
  SearchPersonByNationalVaccineCodeDto,
  SearchPersonByOrderCodeDto,
  UpdateVaccineCodeRes,
  VerifyUpdateChangePhoneDto,
} from '../dto';
import { VerifyUpdateHostForPersonDto } from '../dto/update-host.dto';
import { CustomersService } from '../services/customers.service';
import { GetUserPlatformActiveDto } from '../dto/getUserPlatformActive.dto';

@Controller({ path: 'customers', version: '1' })
@ApiTags('Customer')
@ApiExtraModels(
  ClassResponse,
  SearchPersonRes,
  CreatePersonRes,
  GetPersonByIdRes,
  ResCreateOtp,
  VerifyByPhoneRes,
  GetTitleRes,
  UpdatePersonRes,
  GetManyLcvIdRes,
  VerifyPhoneNumberRes,
  GetImgIdentificationRes,
  UpdateVaccineCodeRes,
  CreateCustomerTcqgResponse,
  SearchCustomerResponse,
  CustomSearchCustomerResponse,
  GetCustomerByLcvIdRes,
)
@CustomHeaders()
@ApiBearerAuth('defaultJWT')
export class CustomersController {
  constructor(private readonly customersService: CustomersService) {}

  @Get()
  @ApiOperation({
    summary: 'Tìm kiếm khách hàng bằng keyword',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Tìm kiếm khách hàng từ lcv hoặc tcqg',
    schema: generalSchema(SearchCustomerResponse, 'object'),
  })
  searchCustomer(@Query() searchPersonDto: SearchPersonDto, @Req() req: any) {
    return this.customersService.searchCustomer(searchPersonDto, req?.user?.employee_code);
  }

  @Post('map-tcqg-to-person')
  @ApiOperation({
    summary: 'Gắn mã tcqg vào trong person',
  })
  @HttpCode(HttpStatus.OK)
  @Public()
  @ApiOkResponse({
    description: '',
    schema: generalSchema(MapTcqgToPersonResponse, 'object'),
  })
  mapTcqgToPerson(@Body() mapTcqgToPersonDto: MapTcqgToPersonBody) {
    return this.customersService.mapTcqgToPerson(mapTcqgToPersonDto);
  }

  @Post('tcqg')
  @ApiOperation({
    summary: 'Tạo mới khách hàng trên TCQG',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin khách hàng',
    schema: generalSchema(CreateCustomerTcqgResponse, 'object'),
  })
  createCustomerTCQG(
    @Body() createCustomerDto: CreateCustomerTcqgBody,
    @Req() req: any,
  ): Promise<CreateCustomerTcqgResponse> {
    return this.customersService.createTcqg(createCustomerDto, req?.user?.employee_code);
  }

  /**
   * @TODO Lấy thông tin khách hàng theo số điện thoại
   */
  @Get('/get-by-phone')
  @ApiOperation({
    summary: 'Lấy thông tin khách hàng theo số điện thoại',
  })
  @Public()
  @ApiOkResponse({
    description: "Lấy thông tin khách hàng theo số điện thoại'",
    schema: generalSchema(CreatePersonRes, 'object'),
  })
  getCustomerByPhone(@Query() getCustomerByPhoneDTO: GetCustomerByPhoneDTO) {
    return this.customersService.getCustomerByPhone(getCustomerByPhoneDTO);
  }

  @Post()
  @ApiOperation({
    summary: 'Tạo mới khách hàng',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin khách hàng',
    schema: generalSchema(CreatePersonRes, 'object'),
  })
  createCustomer(@Body() createPersonDto: CreatePersonDto, @Req() req: any) {
    return this.customersService.createCustomer(createPersonDto, req?.user?.employee_code);
  }

  /**
   * @TODO cập nhật thông tin khách hàng
   */
  @Put()
  @ApiOperation({
    summary: 'Cập nhật thông tin khách hàng',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Cập nhật thông tin khách hàng',
    schema: generalSchema(UpdatePersonRes, 'object'),
  })
  updateCustomer(@Body() updateCustomerDto: UpdatePersonDto, @Req() req: any) {
    return this.customersService.updateCustomer(updateCustomerDto, req?.user?.employee_code);
  }

  @Post('create-many')
  @ApiOperation({
    summary: 'Tạo mới nhiều khách hàng',
  })
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin nhiều khách hàng',
    schema: generalSchema(CreatePersonRes, 'array'),
  })
  createManyCustomer(@Body() createManyCustomerDto: CreateManyPersonDto, @Req() req: any) {
    return this.customersService.createManyCustomer(createManyCustomerDto, req?.user?.employee_code);
  }

  @Post('create-many-for-stc')
  @ApiOperation({
    summary: 'Tạo mới nhiều khách hàng cho STC',
  })
  @HttpCode(HttpStatus.OK)
  @Public()
  @ApiOkResponse({
    description: 'Thông tin nhiều khách hàng',
    schema: generalSchema(CreatePersonRes, 'array'),
  })
  createManyCustomerForStc(@Body() createManyCustomerDto: CreateManyPersonDto, @Headers() headers) {
    return this.customersService.createManyCustomer(createManyCustomerDto, headers?.employee_code);
  }

  /**
   * @TODO get thông tin khách hàng từ person và tcqg
   */
  @Post('search-person-tcqg')
  @ApiOperation({
    summary: 'Tìm kiếm thông tin khách hàng từ person và tcqg',
  })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Trả về Thông tin khách hàng',
    schema: generalSchema(GetPersonByIdRes, 'object'),
  })
  searchPersonOrTcqg(@Body() filter: GetTcqgDto, @Req() req: any) {
    return this.customersService.searchPersonOrTcqg(filter, req?.user?.employee_code);
  }

  @Get('transfer-injection/:keyWord')
  @ApiOperation({
    summary: 'Lấy thông tin khách được phép chuyển mũi tiêm',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin khách hàng',
    schema: generalSchema(GetPersonByIdRes, 'object'),
  })
  getCustomerByForTransferInjection(@Param('keyWord') keyWord: string) {
    return this.customersService.getCustomerByForTransferInjection(keyWord);
  }

  @Get(':personId')
  @ApiOperation({
    summary: 'Lấy thông tin khách hàng By ID',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin khách hàng',
    schema: generalSchema(GetPersonByIdRes, 'object'),
  })
  getCustomerByPersonId(@Param('personId') personId: string) {
    return this.customersService.getCustomerById(personId);
  }

  /**
   * @TODO lấy thông tin khách hàng từ tiêm chủng quốc gia bởi customerID
   */
  @Get('detail/:customerID')
  @ApiOperation({
    summary: 'Lấy thông tin khách hàng từ tiêm chủng quốc gia bởi customerID',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin khách hàng từ tiêm chủng quốc gia bởi customerID',
    // schema: generalSchema(GetPersonByIdRes, 'object'),
  })
  getCustomerById(@Param('customerID', ParseIntPipe) customerID: number, @Req() req: any) {
    return this.customersService.getCustomerByCustomerId(customerID, req?.user?.employee_code);
  }

  /**
   * @TODO tạo otp
   */
  @Post('create-otp')
  @ApiOperation({
    summary: 'Tạo otp',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin tạo otp thành công',
    schema: generalSchema(ResCreateOtp, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  customerCreatedOtp(@Body() createOtp: PayloadCreateOtpDto) {
    return this.customersService.createOtp(createOtp);
  }

  /**
   * @TODO tạo otp
   */
  @Post('verify-otp')
  @ApiOperation({
    summary: 'verify otp',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin tạo otp thành công',
    schema: generalSchema(ResCreateOtp, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  customerVerifyOtp(@Body() createOtp: PayloadVerifyOTPDto) {
    return this.customersService.verifyOtp(createOtp);
  }

  /**
   * @TODO get thông tin khách hàng bằng LcvId
   */
  @Get('lcvid/:lcvId')
  @ApiOperation({
    summary: 'Lấy thông tin khách hàng By LcvId',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin khách hàng',
    schema: generalSchema(GetPersonByIdRes, 'object'),
  })
  getCustomerByLcvId(@Param('lcvId') lcvId: string, @Query() filter: GetDetailFamilyDto, @Req() req: any) {
    return this.customersService.getCustomerByLcvId(lcvId, filter, req?.user?.employee_code);
  }

  /**
   * @TODO verify sdt customer
   */
  @Public()
  @Get('verify-by-phone/:phone')
  @ApiOperation({
    summary: 'Xác nhận số điện thoại',
  })
  @ApiOkResponse({
    description: 'Thông tin số điện thoại',
    schema: generalSchema(VerifyByPhoneRes, 'object'),
  })
  verifyPhoneNumber(@Param('phone') phone: string) {
    return this.customersService.verifyPhoneNumber(phone);
  }

  /**
   * @TODO lấy danh sách chức danh
   */
  @Public()
  @Get('title/get-list')
  @ApiOperation({
    summary: 'Lấy danh sách chức danh',
  })
  @ApiOkResponse({
    description: 'Danh sách chức danh',
    schema: generalSchema(GetTitleRes, 'object'),
  })
  getListTitleFamily() {
    return this.customersService.getListTitle();
  }

  /**
   * @TODO lấy thông tin hộ gia đình
   */
  @Public()
  @Get('person/get-many-by-lcvid')
  @ApiOperation({
    summary: 'List danh sách thông tin khách hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'List danh sách thông tin khách hàng',
    schema: generalSchema(GetManyLcvIdRes, 'object'),
  })
  async getManyByLcvId(@Query() filter: PayloadGetManyLcvIdDto) {
    return this.customersService.getManyByLcvId(filter);
  }

  @Post(':customerId/address')
  @ApiOperation({
    summary: 'Tạo mới địa chỉ khách hàng',
  })
  createCustomerAddress(
    @Param() paras: CreateCustomerAddressParasDto,
    @Body() createCustomerAddressDto: CreateCustomerAddressDto,
  ) {
    return this.customersService.createCustomerAddress(paras, createCustomerAddressDto);
  }

  @Get(':customerId/address')
  @ApiOperation({
    summary: 'Lấy thông tin địa chỉ khách hàng',
  })
  getCustomerCustomerAddress(@Param() paras: CreateCustomerAddressParasDto) {
    return this.customersService.getCustomerAddress(paras);
  }

  /**
   * @TODO cập nhật địa chỉ khách hàng
   */
  @Put(':customerId/address/:addressId')
  @ApiOperation({
    summary: 'Cập nhật địa chỉ khách hàng',
  })
  @Public()
  @ApiOkResponse({
    description: 'Cập nhật địa chỉ khách hàng',
    schema: generalSchema(UpdateCustomerAddressParasDto, 'object'),
  })
  updateCustomerAddress(@Param() paras: UpdateCustomerAddressParasDto, @Body() payload: UpdateCustomerAddressDto) {
    return this.customersService.updateCustomerAddress(paras, payload);
  }

  /**
   * @TODO Xóa địa chỉ khách hàng
   */
  @Delete(':customerId/address/:addressId')
  @ApiOperation({
    summary: 'Xóa địa chỉ khách hàng',
  })
  @Public()
  @ApiOkResponse({
    description: 'Xóa địa chỉ khách hàng',
    schema: generalSchema(DeleteCustomerAddressDto, 'object'),
  })
  deleteCustomerAddress(@Param() paras: DeleteCustomerAddressDto) {
    return this.customersService.deleteCustomerAddress(paras);
  }

  /**
   * @TODO Danh sách person theo OrderCode
   */
  @Get('search/search-person-by-orderCode')
  @ApiOperation({
    summary: 'Lấy danh sách person theo OrderCode',
  })
  @Public()
  @ApiOkResponse({
    description: 'Danh sách person',
    schema: generalSchema(SearchPersonRes, 'object'),
  })
  searchPersonByOrderCode(@Query() filter: SearchPersonByOrderCodeDto) {
    return this.customersService.searchPersonByOrderCode(filter);
  }

  /**
   * @TODO Kiểm tra thông tin customer
   */
  @Get('validate/:lcvId')
  @ApiOperation({
    summary: 'Kiểm tra thông tin khách hàng',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin khách hàng',
    schema: generalSchema(true, 'boolean'),
  })
  validateCustomerInfo(@Param('lcvId') lcvId: string) {
    return this.customersService.validateCustomerByLcvId(lcvId);
  }

  /**
   * @TODO lấy thông tin family bởi lcvid
   */
  @Get('family/get-by-lcvId')
  @ApiOperation({
    summary: 'Lấy thông tin family bởi lcvId',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin family',
    schema: generalSchema(Person, 'object'),
  })
  familyByLcvId(@Query() query: GetFamilyByLcvIdDto) {
    return this.customersService.getFamilyByLcvId(query);
  }

  /**
   * @TODO verify phoneNumber khi thay đổi sdt
   */
  @Post('person/verify-phoneNumber')
  @ApiOperation({
    summary: 'Xác minh số điện thoại tồn tại hay chưa',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin ',
    schema: generalSchema(VerifyPhoneNumberRes, 'object'),
  })
  verifyPhoneNumberForPerson(@Body() body: VerifyPhoneNumberDto) {
    return this.customersService.verifyPhoneNumberPerson(body);
  }

  /**
   * @TODO verify phoneNumber khi thay đổi sdt
   */
  @Put('person/update-phoneNumber')
  @ApiOperation({
    summary: 'Xác nhận cập nhật số điện thoại',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin ',
    schema: generalSchema(VerifyPhoneNumberRes, 'object'),
  })
  updatePhoneNumberForPerson(@Body() body: VerifyUpdateChangePhoneDto, @Req() req: any) {
    return this.customersService.updatePhoneNumberForPerson(body, req.headers['order-channel']);
  }

  /**
   * @TODO lấy hình ảnh tùy thân
   */
  @Get('person/identification')
  @ApiOperation({
    summary: 'Lấy danh sách hình ảnh tùy thân',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin hình ảnh tùy thân',
    schema: generalSchema(GetImgIdentificationRes, 'object'),
  })
  getIdentification(@Query('personId') personId: string) {
    return this.customersService.getIdentificationPerson(personId);
  }

  /**
   * @TODO xóa số điện thoại
   */
  @Delete('person/delete-phoneNumber')
  @ApiOperation({
    summary: 'Xóa số điện thoại',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin person ',
    schema: generalSchema(VerifyPhoneNumberRes, 'object'),
  })
  deletePhoneNumberForPerson(@Body() body: DeletePhoneNumberForPersonDto, @Req() req: any) {
    return this.customersService.deletePhoneForPerson(body, req.headers['order-channel']);
  }

  /*
   * @TODO lấy thông tin profile với mã tiêm chủng quốc gia
   */
  @Get('person/search-by-nationalVaccineCode')
  @ApiOperation({
    summary: 'Lấy thông tin profile với mã tiêm chủng quốc gia',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin person',
    schema: generalSchema(Person, 'object'),
  })
  getProfileWithNationalVaccineCode(@Query() query: SearchPersonByNationalVaccineCodeDto, @Req() req: any) {
    return this.customersService.getPersonByNationalVaccineCode(query, req?.user?.employee_code);
  }

  /**
   * @TODO verify phoneNumber khi thay đổi chủ hộ
   */
  @Put('person/change-host-person')
  @ApiOperation({
    summary: 'Đổi chủ hộ',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin person sau khi update',
    schema: generalSchema(Person, 'object'),
  })
  updateIsHostForPerson(@Body() body: VerifyUpdateHostForPersonDto, @Req() req: any) {
    return this.customersService.changeHostPerson(body, req.headers['order-channel'], req?.user?.employee_code);
  }

  /**
   * @TODO Inactive person
   */
  @Post('person/inactive-person')
  @ApiOperation({
    summary: 'Inactive person',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin person',
    schema: generalSchema(true, 'boolean'),
  })
  inactivePerson(@Body() body: InactivePersonPayload) {
    return this.customersService.inactivePerson(body);
  }

  /**
   * @TODO Inactive person
   */
  @Get('/get-customer-by-lcvId/:lcvId')
  @ApiOperation({
    summary: 'Get customer không trả về thông tin family',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin person',
    schema: generalSchema(Person, 'object'),
  })
  getCustomerBylcvId(@Param('lcvId') lcvId: string) {
    return this.customersService.getCustomerNotResFamily(lcvId);
  }

  /*
   * @TODO Xóa family
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa thành viên',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin thành viên',
    schema: generalSchema(Person, 'object'),
  })
  deleteFamilyProfile(@Body() body: DeleteFamilyProfileDto) {
    return this.customersService.deleteFamilyProfile(body);
  }

  @Get('user-platform-active/get-redis-by-lcvId')
  @ApiOperation({
    summary: 'Get user platform active by lcvId',
  })
  @Public()
  getUserPlatformActiveByLcvId(@Query() param: GetUserPlatformActiveDto) {
    return this.customersService.getUserPlatformActiveByLcvId(param.lcvId);
  }
}
