import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddReasonFields1721048088573 implements MigrationInterface {
  name = 'AddReasonFields1721048088573';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE reasons ADD COLUMN "is_send_message" BOOLEAN DEFAULT FALSE`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE reasons DROP COLUMN isSendMessage`);
  }
}
