import { Module } from '@nestjs/common';
import { LcPrintCenterController } from './controllers/lc-print-center.controller';
import { LcPrintCenterModule as LcPrintCenterLibModule } from 'lc-nest-print-center';
import { LcPrintCenterService } from './services/lc-print-center.service';
import { OsrModule } from 'vac-nest-osr';
import { OMSModule } from 'vac-nest-oms';
import { PaymentGatewayModule } from 'vac-nest-payment-gateway';
import { InsideModule } from 'vac-nest-inside';
import { PIMAppModule } from 'vac-nest-pim-app';
import { PrintCenterModule } from 'vac-nest-print-center';

@Module({
  imports: [
    LcPrintCenterLibModule,
    OMSModule,
    PIMAppModule,
    PaymentGatewayModule,
    InsideModule,
    OsrModule,
    PrintCenterModule,
  ],
  controllers: [LcPrintCenterController],
  providers: [LcPrintCenterService],
  exports: [LcPrintCenterService],
})
export class LcPrintCenterModule {}
