import { ApiProperty, OmitType, PickType } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsIn, IsOptional } from 'class-validator';
import { Gender, SearchCustomerDto } from 'vac-nest-tcqg-integration';

export class SearchFamilyAndTCQGDto extends PickType(SearchCustomerDto, [
  'fullName',
  'dateOfBirthFrom',
  'dateOfBirthTo',
  'gender',
  'shopCode',
  'provinceCode',
  'wardCode',
  'ethnicCode',
  'districtCode',
  'pageNumber',
  'pageSize',
] as const) {
  @ApiProperty({
    required: false,
    default: '58001',
  })
  @IsOptional()
  @Expose()
  shopCode: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  keyword?: string;

  @ApiProperty({
    description: '0:Name, 1. Nữ, 2. Khác',
    required: true,
  })
  @Expose()
  @IsIn([0, 1, 2])
  gender?: Gender;

  @ApiProperty({
    required: true,
  })
  @Expose()
  fullName?: string;

  @ApiProperty({
    required: true,
  })
  @Expose()
  dateOfBirthFrom: Date;

  @ApiProperty({
    required: true,
  })
  @Expose()
  dateOfBirthTo: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Expose()
  pageNumber: number;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Expose()
  pageSize: number;
}

export class ReqSearchDto extends SearchFamilyAndTCQGDto {
  @ApiProperty()
  @Expose()
  phone?: string;

  @ApiProperty()
  @Expose()
  nationalVaccineCode?: string;
}

export class SearchFamilyAndTCQGNormalDto extends PickType(SearchCustomerDto, [
  'shopCode',
  'pageNumber',
  'pageSize',
] as const) {
  @ApiProperty({
    required: true,
    default: '58001',
  })
  @Expose()
  shopCode: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  keyword?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Expose()
  pageNumber: number;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Expose()
  pageSize: number;
}

export class ReqSearchKeywordDto extends SearchFamilyAndTCQGNormalDto {
  @ApiProperty()
  @Expose()
  phone?: string;

  @ApiProperty()
  @Expose()
  nationalVaccineCode?: string;
}

// request for family
export class ReqSearchFamilyDto extends PickType(SearchFamilyAndTCQGDto, ['keyword'] as const) {
  @ApiProperty()
  @Expose()
  Name?: string;
}

// request for tcqg
export class ReqSearchTCQG extends OmitType(SearchFamilyAndTCQGDto, [
  'keyword',
  'dateOfBirthFrom',
  'dateOfBirthTo',
] as const) {}

// request for tcqg
export class ReqSearchAdvanceTCQG extends OmitType(SearchFamilyAndTCQGDto, ['keyword'] as const) {}
