import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { concurrentPromiseThrowError, ErrorCode, parseDateTimeZone, SystemException } from '@shared';
import dayjs from 'dayjs';
import { Request } from 'express';
import _ from 'lodash';
import moment from 'moment';
import { ExaminationCoreService } from 'vac-nest-examination';
import { FamilyService } from 'vac-nest-family';
import { InsideService } from 'vac-nest-inside';
import {
  CancelVaccineShotItemsDto,
  CreateVaccineShotCancellationDto,
  GetShotBySkuShopBarcodeDto,
  GetVaccineShotByListSku,
  GetVaccineShotCancellationDto,
  GetVaccineShotItemsByQueryDto,
  ItemBarcodeRes,
  MultiDoseType,
  OsrService,
  SearchVaccineShotCancellationDto,
} from 'vac-nest-osr';
import { PRINT_TYPE, PrintCenterService as PrintCenterCoreService, PrintCenterRes } from 'vac-nest-print-center';
import { RegimenService } from 'vac-nest-regimen';
import { GetVaccineShotItemsByQueryResult, vaccineShotTransResult } from '../dto/multi-dose-shot.dto';
import {
  CancelJarDto,
  genBarcodeDto,
  getBarcodeByLoDateDto,
  getDetailBarcodeDto,
  PrintQrCodeDto,
  PrintVaccineCancellationRecordDto,
  QRCodeCreatedDto,
} from '../dto/multi-dose.dto';
import { MultiDoseUtilService } from './multi-dose-util.service';
import { plainToInstance } from 'class-transformer';
import { BarcodeToPrintDto } from '../dto/multi-dose-print-pre-process.dto';
import { IMSService } from 'vac-nest-ims';
import { UnitCodeSale } from '../constants';

@Injectable()
export class MultiDoseService {
  shopCode: string;
  constructor(
    private readonly osrService: OsrService,
    protected readonly regimenService: RegimenService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly printCenterService: PrintCenterCoreService,
    private readonly multiDoseUtilService: MultiDoseUtilService,
    private readonly familyService: FamilyService,
    private readonly insideService: InsideService,
    private readonly imsService: IMSService,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
  }

  /**
   * @description lấy barcode theo sku và shop code
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=158252295
   */
  async getBarcodeBySku(body: getBarcodeByLoDateDto) {
    const { arrSku, ticketCode, lotNumber, expired } = body;
    const outPut: { items: ItemBarcodeRes[]; qrCodeCreated: QRCodeCreatedDto } = {
      items: [],
      qrCodeCreated: null,
    };
    /**
     * 1. call OSR lấy danh sách lọ mở theo sku
     * 2. filter theo productItemCode từ OSR
     * 3. Nếu không có danh sách lọ mở thì trả ra item để show popup tạo QRCode mới
     * 4. call qua ticket để lấy danh sách phiếu
     */
    // data lọ mở
    // call ticket
    const [listJarOpen, ticket] = await concurrentPromiseThrowError(
      this.osrService.getBarcodeByListSku({
        shopCode: this.shopCode,
        listSku: _.uniq(arrSku?.map((item) => item?.sku) || []),
      }),
      this.examinationCoreService.getTicket({ ticketCode }),
    );
    const itemBySku = ticket?.indications?.find((f) => f?.sku === arrSku?.at(0)?.sku);

    const data = this.multiDoseUtilService.filterVaccineWithProductItem({
      arrDataOSR: listJarOpen,
      unitCodeSale: itemBySku?.unitCodeSale,
      productItemCode: arrSku?.at(0)?.productItemCode,
    });

    if (!data?.length) {
      return {
        ...outPut,
        qrCodeCreated: {
          sku: itemBySku?.sku,
          productItemCode: arrSku?.at(0)?.productItemCode,
          ticketCode,
          lotNumber, // lô sản xuất
          expired, // hạn sử dụng
          vaccineName: itemBySku?.vaccineName,
          manufactor: itemBySku?.manufactor, // nước sản xuất
          openTime: new Date(), // thời gian mở
          expiryTime: new Date(), // thời gian đóng
          unitCodeSale: itemBySku?.unitCodeSale,
          unitNameSale: itemBySku?.unitNameSale,
        },
      };
    }

    return {
      ...outPut,
      items: data,
    };
  }

  /**
   * @description lấy barcode theo list barcode
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=158252295
   */
  async getDetailBarcode(params: getDetailBarcodeDto) {
    /**
     * 1. call OSR lấy thông tin chi tiết của barcode
     */
    const data = await this.osrService.getBarcodeByListBarcode({
      sku: params?.sku,
      listBarCode: [params?.barCode],
      shopCode: this.shopCode,
    });

    if (params?.regimenId) {
      const regimen = await this.regimenService.getRegimenByIds({ regimenIds: [params?.regimenId] });
      if (regimen) {
        data.at(0).disease = regimen?.at(0)?.diseaseGroup?.name;
      }
    }

    return {
      barCodeInfor: data?.at(0) || null,
    };
  }

  /**
   * @description lấy barcode theo list barcode
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=158252295
   */
  async genBarcode(body: genBarcodeDto) {
    const { items, printerInfo } = body;
    let printReponse = {};

    //#region check tồn khi tạo QRCode
    // get quota theo sku
    const [getQuotaBySku, listInventory] = await concurrentPromiseThrowError(
      this.osrService.getVaccineShotByListSku({
        listSku: [items?.at(0)?.sku],
      }),
      this.imsService.getInventoryProductItemByProductItemsWhsWithUnit({
        whsCode: this.shopCode + `010`,
        productItems: items?.map((item) => item?.productItemCode),
      }),
    );

    let isStockQuantity = false;
    let numberAvailable = 0;
    if (listInventory?.length) {
      // tìm đơn vị liều
      const itemLieu = _.maxBy(listInventory, 'unitLevel');
      // nếu FE truyền lên đơn vị lọ thì find inventory theo đơn vị lọ
      // quantityExchange đơn vị quy đổi lọ sang liều
      //   const findItemByUnit = listInventory.find((i) => i?.unitCode === items?.at(0)?.unitCodeSale);
      if (!getQuotaBySku?.length || getQuotaBySku?.at(0)?.quantity > itemLieu?.quantityAvailable) {
        isStockQuantity = true;
        numberAvailable = itemLieu ? (itemLieu?.quantityAvailable < 0 ? 0 : itemLieu?.quantityAvailable) : 0;
      }
    }

    if (isStockQuantity) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_INVENTORY_BY_LODATE_MULTIDOSE,
          message: ErrorCode.getError(ErrorCode.RSA_INVENTORY_BY_LODATE_MULTIDOSE)
            ?.replace('{lotDate}', items?.at(0)?.lotNumber)
            ?.replace('{expiryDate}', moment(items?.at(0)?.lotDate).utcOffset(7)?.format('DD/MM/YYYY'))
            ?.replace('{quantityExchange}', '' + numberAvailable),
          details: ErrorCode.getError(ErrorCode.RSA_INVENTORY_BY_LODATE_MULTIDOSE)
            ?.replace('{lotDate}', items?.at(0)?.lotNumber)
            ?.replace('{expiryDate}', moment(items?.at(0)?.lotDate).utcOffset(7)?.format('DD/MM/YYYY'))
            ?.replace('{quantityExchange}', '' + numberAvailable),
        },
        HttpStatus.FORBIDDEN,
      );
    }
    //#endregion check tồn khi tạo QRCode

    const ticket = await this.examinationCoreService.getTicket({ ticketCode: items?.at(0)?.ticketCode });
    // const [result, ticket] = await concurrentPromiseThrowError(
    //   this.osrService.createdBarcode(items),
    //   this.examinationCoreService.getTicket({ ticketCode: items?.at(0)?.ticketCode }),
    // );
    const itemSku = ticket?.indications?.find((i) => i?.sku === items?.at(0)?.sku);
    if (!itemSku) {
      return {
        data: [],
        printReponse: { url: '', imageUrl: '', printStatus: 400 },
      };
    }

    const result = await this.osrService.createdBarcode([
      {
        ...items?.at(0),
        clinicId: ticket?.injectionClinicId,
        clinicName: ticket?.injectionClinicName,
      },
    ]);

    const data = this.multiDoseUtilService.filterVaccineWithProductItem({
      arrDataOSR: result,
      unitCodeSale: itemSku?.unitCodeSale,
      productItemCode: items?.at(0)?.productItemCode,
    });

    if (result?.length > 0) {
      try {
        const createdBarcode = result?.at(0) || {};

        //filter theo shop code
        const listEmployeeInfor = await this.insideService.getListShopByEmployee(body?.items?.at(0)?.createdBy);
        const employeeInfor =
          listEmployeeInfor?.shops?.find((shop) => shop?.shopCode === body?.items?.at(0)?.shopCode) || null;

        /**
         * @description luồng gen barcode sẽ không có mũi đã tiêm nên chỉ cần truyền mảng rỗng
         */
        const actuallyInjectedList = [];

        const templateData = plainToInstance(
          BarcodeToPrintDto,
          { ...ticket, ...createdBarcode, ...employeeInfor, actuallyInjectedList },
          {
            excludeExtraneousValues: true,
            exposeUnsetFields: false,
          },
        );

        const printUrlRes = await this.printCenterService.printPriorityVaccine({
          ignorePrint: printerInfo?.ignorePrint,
          printType: PRINT_TYPE.PRIORITY_VACCINE,
          printerInfo: printerInfo,
          printData: templateData,
        });

        printReponse = { ...printUrlRes, printStatus: 200 };
      } catch (error) {
        printReponse = { url: '', imageUrl: '', printStatus: error?.status || 400 };
      }
    }

    return { data, ...printReponse };
  }

  /**
   * @description Danh sách thuốc đã tiêm
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=158252295
   */
  async getVaccineShotItemsByQuery(body: GetVaccineShotItemsByQueryDto) {
    body.shopCode = [this.shopCode];

    const resVaccineShot = await this.osrService.getVaccineShotItemsByQuery(body);
    const listItemsVaccineShot: GetVaccineShotItemsByQueryResult[] = [...resVaccineShot?.items];
    if (listItemsVaccineShot?.length > 0) {
      const resRegimen = await this.regimenService.getRegimenByListSku({
        skus: _.uniq(resVaccineShot?.items?.map((e) => e.sku)),
      });

      listItemsVaccineShot?.forEach((item) => {
        const regimenInfo = resRegimen?.data?.find((regimen) => regimen.vaccine?.sku === item.sku) || null;
        const vaccineName = `${regimenInfo?.diseaseGroup?.name} - ${regimenInfo?.vaccine?.name} - ${regimenInfo?.vaccine?.manufactor}`;
        item.regimenInfo = regimenInfo;
        item.quantityNeedToCancel = item?.quantity - item?.quantityUsed;
        item.vaccineName = vaccineName;
        item.lotNumberAndLotDate = `${item?.lotNumber} - ${parseDateTimeZone(
          new Date(item.lotDate),
          '+07:00',
          'DD/MM/YYYY',
        )} - ${item?.sku}`;
        item.isExpired = item?.expiredTime
          ? moment.tz(item?.expiredTime, 'Asia/Ho_Chi_Minh').isBefore(moment())
          : false;
      });
    }

    // Group by SKU
    const groupedBySku = _.groupBy(listItemsVaccineShot, 'sku');

    // Group each SKU by productItemCode
    const groupedByProductItemCode = _.mapValues(groupedBySku, (skuData) => {
      return _.groupBy(skuData, 'productItemCode');
    });

    return {
      items: listItemsVaccineShot,
      totalCount: resVaccineShot?.totalCount,
      groupedData: groupedByProductItemCode,
    };
  }

  /**
   * @description Lấy detail
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=158252295
   */
  async getBySkuShopBarcode(payload: GetShotBySkuShopBarcodeDto) {
    const resGetShots = await this.osrService.getShotBySkuShopBarcode(payload);

    const resVaccineShotTrans = resGetShots?.vaccineShotTrans || [];
    const resvaccineShotItems = resGetShots?.vaccineShotItems || {};
    let vaccineShotTrans: vaccineShotTransResult[] = [...resVaccineShotTrans];
    const vaccineShotItems: any = { ...resvaccineShotItems };
    if (vaccineShotItems?.sku) {
      const resRegimen = await this.regimenService.getRegimenByListSku({
        skus: [vaccineShotItems?.sku],
      });

      const regimenInfo = resRegimen?.data?.find((regimen) => regimen.vaccine?.sku === vaccineShotItems.sku) || null;
      const vaccineName = `${regimenInfo?.diseaseGroup?.name} - ${regimenInfo?.vaccine?.name} - ${regimenInfo?.vaccine?.manufactor}`;
      vaccineShotItems.regimenInfo = regimenInfo;
      vaccineShotItems.quantityNeedToCancel = vaccineShotItems?.quantity - vaccineShotItems?.quantityUsed;
      vaccineShotItems.vaccineName = vaccineName;
      vaccineShotItems.lotNumberAndLotDate = `${vaccineShotItems?.lotNumber} - ${parseDateTimeZone(
        new Date(vaccineShotItems.lotDate),
        '+07:00',
        'DD/MM/YYYY',
      )} - ${vaccineShotItems?.sku}`;
    }
    if (vaccineShotTrans?.length > 0) {
      const resGetTicket = await this.examinationCoreService.getManyTicket({
        ticketCodes: vaccineShotTrans?.map((e) => e.ticketCode),
      });
      let resGetPerson = [];
      if (resGetTicket?.length) {
        resGetPerson = await this.familyService.getManyByLcvId({ lcvId: resGetTicket?.map((e) => e.lcvId) });
      }

      vaccineShotTrans?.forEach((item) => {
        const ticketInfo = resGetTicket?.find((e) => e.ticketCode === item.ticketCode);
        item.ticketInfo = ticketInfo;
        item.personInfo = resGetPerson?.find((e) => e.lcvId === ticketInfo?.lcvId);
      });
    }

    vaccineShotTrans = vaccineShotTrans.sort(
      (a, b) => new Date(a?.ticketInfo?.trackingTime).getTime() - new Date(b?.ticketInfo?.trackingTime).getTime(),
    );
    return { vaccineShotItems, vaccineShotTrans };
  }

  async updatedListVaccineShotItems(body: CancelJarDto) {
    /**
     * 1. hủy lọ đã mở
     * 2. lấy lại danh sách lọ mở theo sku, shopCode
     * 3. filter theo productItemCode
     */
    await this.osrService.updatedListVaccineShotItems([
      {
        id: body?.id,
        status: body?.status || MultiDoseType.CANCEL,
        modifiedBy: body?.modifiedBy,
        sku: body?.sku,
        vaccineShotId: body?.vaccineShotId,
        reasonCancel: body?.reasonCancel,
        reasonCancelCode: body?.reasonCancelCode,
      },
    ]);

    // get lai thông tin lọ
    const [result, ticket] = await concurrentPromiseThrowError(
      this.osrService.getBarcodeByListSku({
        shopCode: this.shopCode,
        listSku: [body?.sku],
      }),
      this.examinationCoreService.getTicket({ ticketCode: body?.ticketCode }),
    );

    const itemBySku = ticket?.indications?.find((f) => f?.sku === body?.sku);

    const data = this.multiDoseUtilService.filterVaccineWithProductItem({
      arrDataOSR: result,
      unitCodeSale: itemBySku?.unitCodeSale,
      productItemCode: body?.productItemCode,
    });

    return data;
  }

  /**
   * @description In phiếu hủy vaccine
   */
  async printVaccineCancellationRecord(payload: PrintVaccineCancellationRecordDto) {
    const { printerInfo, printData } = payload;

    try {
      const exception = {
        code: ErrorCode.RSA_PRINT_MULTIDOSE_INVALID_PAYLOAD,
        message: ErrorCode.getError(ErrorCode.RSA_PRINT_MULTIDOSE_INVALID_PAYLOAD),
        details: ErrorCode.getError(ErrorCode.RSA_PRINT_MULTIDOSE_INVALID_PAYLOAD),
        validationErrors: null,
      };

      if (!printData?.code) {
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }

      const detailVaccineShotRes = await this.osrService.getVaccineShotCancellation({ codes: [printData?.code] });

      if (!detailVaccineShotRes) {
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }

      const detailVaccineShot = detailVaccineShotRes?.[0] || {};

      const skus = detailVaccineShot?.details?.map((e) => e.sku);
      let resRegimen;
      if (skus?.length > 0) {
        resRegimen = await this.regimenService.getRegimenByListSku({
          skus: _.uniq(skus),
        });
      }

      const cancelVaccineList = detailVaccineShot?.details?.map((e) => {
        const regimenInfo = resRegimen?.data?.find((regimen) => regimen.vaccine?.sku === e.sku) || null;
        const vaccineName = `${regimenInfo?.vaccine?.name}`; // - ${regimenInfo?.vaccine?.manufactor}`;
        return {
          vaccineName,
          ...e,
        };
      });

      const vaccineCancellationRes = await this.printCenterService.printVaccineCancellationRecord({
        ignorePrint: printerInfo?.ignorePrint,
        printType: PRINT_TYPE.VACCINE_CANCELLATION_RECORD,
        printerInfo: printerInfo,
        printData: { ...detailVaccineShot, cancelVaccineList: cancelVaccineList || [] },
      });

      return vaccineCancellationRes;
    } catch (error) {
      throw error;
    }
  }

  /**
   * @description In vắc xin ưu tiên
   */
  async printQrCode(payload: PrintQrCodeDto): Promise<PrintCenterRes> {
    const { printerInfo, printData } = payload;

    try {
      const { vaccineShotItems, vaccineShotTrans } = await this.getBySkuShopBarcode(printData);

      if (!vaccineShotItems) {
        const exception = {
          code: ErrorCode.RSA_PRINT_MULTIDOSE_INVALID_PAYLOAD,
          message: ErrorCode.getError(ErrorCode.RSA_PRINT_MULTIDOSE_INVALID_PAYLOAD),
          details: ErrorCode.getError(ErrorCode.RSA_PRINT_MULTIDOSE_INVALID_PAYLOAD),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }

      //filter theo shop code
      const listEmployeeInfor = await this.insideService.getListShopByEmployee(vaccineShotItems?.createdBy);
      const employeeInfor =
        listEmployeeInfor?.shops?.find((shop) => shop?.shopCode === vaccineShotItems.shopCode) || null;

      const actuallyInjectedList = [];

      vaccineShotTrans?.forEach((item) => {
        actuallyInjectedList.push({
          nurseInjection: item?.ticketInfo?.injectingNursingName,
          injectionTime: item?.ticketInfo?.trackingTime,
        });
      });

      const templateData = plainToInstance(
        BarcodeToPrintDto,
        { ...vaccineShotItems, ...employeeInfor, actuallyInjectedList },
        {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        },
      );

      const vaccineCancellationRes = await this.printCenterService.printPriorityVaccine({
        ignorePrint: printerInfo?.ignorePrint,
        printType: PRINT_TYPE.PRIORITY_VACCINE,
        printerInfo: printerInfo,
        printData: templateData,
      });

      return vaccineCancellationRes;
    } catch (error) {
      throw error;
    }
  }

  /**
   * @description Tạo biên bản hủy
   */
  async createVaccineShotCancellation(payload: CreateVaccineShotCancellationDto) {
    const resOsr = await this.osrService.createVaccineShotCancellation(payload);

    const printData = await this.multiDoseUtilService.printVaccineShotCancellation(resOsr);

    return { ...resOsr, printData };
  }

  /**
   * @description Tìm biên bản hủy
   */
  async searchVaccineShotCancellation(payload: SearchVaccineShotCancellationDto) {
    payload.fromDate = payload.fromDate
      ? moment.tz(payload.fromDate, 'Asia/Ho_Chi_Minh').startOf('days').format()
      : undefined;
    payload.toDate = payload.toDate ? moment.tz(payload.toDate, 'Asia/Ho_Chi_Minh').endOf('days').format() : undefined;
    return this.osrService.searchVaccineShotCancellation(payload);
  }

  /**
   * @description Get biên bản hủy
   */
  async getVaccineShotCancellation(payload: GetVaccineShotCancellationDto) {
    const res = await this.osrService.getVaccineShotCancellation(payload);
    let printData = {};
    const vaccineShotCancellation = res?.[0];
    if (vaccineShotCancellation) {
      printData = await this.multiDoseUtilService.printVaccineShotCancellation(vaccineShotCancellation);
    }
    return { ...vaccineShotCancellation, printData };
  }

  /**
   * @description Get vaccine shot by list sku
   *
   */
  async getVaccineShotByListSku(payload: GetVaccineShotByListSku) {
    return this.osrService.getVaccineShotByListSku(payload);
  }

  /**
   * @description Hủy list vaccine shot items
   *
   */
  async cancelVaccineShotItems(payload: CancelVaccineShotItemsDto[]) {
    return this.osrService.cancelVaccineShotItems(payload);
  }
}
