import { Injectable } from '@nestjs/common';
import { PickupAtShopByLocationReq } from '../dto';
import { OrderPromissingService } from 'vac-order-promising';

@Injectable()
export class OrderPromissingWrapService {
  constructor(private readonly orderPromissingService: OrderPromissingService) {}

  async pickupAtShopByLocation(dataBody: PickupAtShopByLocationReq) {
    dataBody?.product?.forEach((product) => {
      if (product) {
        product.whsCodeEndWith = product?.whsCodeEndWith ? product?.whsCodeEndWith?.slice(-3) : null;
      }
    });
    const shopDatas = await this.orderPromissingService.pickupAtShopByLocation(dataBody as any);
    return shopDatas;
  }
}
