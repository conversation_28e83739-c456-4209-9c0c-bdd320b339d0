import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';

export class CheckBillDto {
  @ApiProperty()
  @IsNumber()
  total: number;

  @ApiProperty()
  @IsNumber()
  cash: number;

  @ApiProperty()
  @IsNumber()
  transfer: number;

  @ApiProperty()
  @IsNumber()
  card: number;

  @ApiProperty()
  @IsNumber()
  voucher: number;

  @ApiProperty()
  @IsNumber()
  cod: number;

  @ApiProperty()
  @IsNumber()
  ewallet: number;

  @ApiProperty()
  @IsNumber()
  point: number;

  @ApiProperty()
  @IsNumber()
  topUp: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  payAtShop?: number;
}

export class CheckBillRes {
  total: number;
  remain: number;
  voucher: number;
  depositPrice: number;
  depositRemain: number;
  point: number;
}
