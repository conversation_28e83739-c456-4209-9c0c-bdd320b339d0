import { Body, Controller, Delete, Get, HttpStatus, Param, Post, Put, Query, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOkResponse, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CustomHeaders, generalSchema, Public } from '@shared';
import { GetPersonByIdRes, SearchPersonDto } from 'vac-nest-family';
import {
  AddPersonGroupExtendDto,
  ChangeOwnerDto,
  CreateFamilyPackageConvertDto,
  CreateFamilyPackageResponse,
  GetManyPersonDto,
  InactiveFamilyPackageDto,
  InsertOrUpdateEvidenceExtendDto,
  RemovePersonInGroupDto,
  SearchCustomerResponse,
  GetAttributeByLcvIdsDto,
  CalculateTempPricingByRankResponseDto,
} from '../dto';
import { CustomersFamilyPackageService } from '../services/customers-family-package.service';
import {
  GetFamilyPackageByGroupCodesDto,
  GetFamilyPackageByLcvIdsDto,
  GetGroupTypeDataByCodeDto,
  GetRankGroupTypeDataResDto,
  GroupRanking,
} from 'vac-nest-family-package';
import { CheckExistPersonInGroupDto } from 'vac-nest-family-rule';
import { SearchGroupDto } from '../dto/search-group.dto';
import { CheckRuleDeleteMemberDto } from 'vac-nest-family-rule/dist/dto/check-rule-delete-member.dto';
import { CheckRuleChangeOwnerDto } from 'vac-nest-family-rule/dist/dto/check-rule-change-owner.dto';
import { CalculateTempPriceingByRankDto } from '../dto/calculate-temp-pricing-by-rank.dto';

@Controller({ path: 'customers-family-package', version: '1' })
@ApiTags('customers family package')
@ApiExtraModels(
  SearchCustomerResponse,
  GetRankGroupTypeDataResDto,
  GroupRanking,
  CreateFamilyPackageResponse,
  CalculateTempPricingByRankResponseDto,
) // Add the new DTO to ApiExtraModels
@CustomHeaders()
@ApiBearerAuth('defaultJWT')
export class CustomersFamilyPackageController {
  constructor(private readonly customersService: CustomersFamilyPackageService) {}

  @Get()
  @ApiOperation({
    summary: 'Tìm kiếm khách hàng bằng keyword',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Tìm kiếm khách hàng từ lcv hoặc tcqg',
    schema: generalSchema(SearchCustomerResponse, 'object'),
  })
  searchCustomer(@Query() searchPersonDto: SearchPersonDto, @Req() req: any) {
    return this.customersService.searchCustomer(searchPersonDto, req?.user?.employee_code);
  }

  @Post()
  @ApiOperation({
    summary: 'Tạo object group family',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin chi tiết group',
    schema: generalSchema(CreateFamilyPackageResponse, 'object'),
  })
  createGroupFamilyPackage(@Body() body: CreateFamilyPackageConvertDto): Promise<CreateFamilyPackageResponse> {
    return this.customersService.createGroupFamilyPackage(body);
  }

  @Post('get-group-by-lcvids')
  @ApiOperation({
    summary: 'Tìm group',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin chi tiết group',
    schema: generalSchema(CreateFamilyPackageResponse, 'object'),
  })
  getGroupByLcvIds(@Body() payload: GetFamilyPackageByLcvIdsDto) {
    return this.customersService.getGroupByLcvIds(payload);
  }

  @Get('get-group-by-group-code')
  @ApiOperation({
    summary: 'Tìm group',
  })
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin chi tiết group',
    schema: generalSchema(CreateFamilyPackageResponse, 'object'),
  })
  getGroupByGroupCode(@Query() payload: GetFamilyPackageByGroupCodesDto) {
    return this.customersService.getGroupByGroupCode(payload);
  }

  @Get('group-type/evidence/get-all-data')
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'group type data',
    // schema: generalSchema(SearchCustomerResponse, 'object'),
  })
  getAllGroupTypeData() {
    return this.customersService.getAllGroupTypeData();
  }

  @Get('group-type/evidence/get-by-group-type-code')
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'group type data',
    // schema: generalSchema(SearchCustomerResponse, 'object'),
  })
  getGroupTypeDataByCode(@Query() payload: GetGroupTypeDataByCodeDto) {
    return this.customersService.getGroupTypeDataByCode(payload);
  }

  @Get('group-type/get-all-data')
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'group type ranking data',
    schema: generalSchema(GetRankGroupTypeDataResDto, 'array'),
  })
  getRankGroupTypeData() {
    return this.customersService.getRankGroupTypeData();
  }

  @Get('group-type/get-by-group-type-code')
  @ApiBearerAuth()
  @ApiResponse({
    status: HttpStatus.OK,
    type: GetRankGroupTypeDataResDto,
  })
  getRankGroupTypeDataByCode(@Query() payload: GetGroupTypeDataByCodeDto) {
    return this.customersService.getRankGroupTypeDataByCode(payload);
  }

  @Post('family-rule/verify/check-exist-person-in-group')
  @ApiBearerAuth()
  @ApiResponse({
    status: HttpStatus.OK,
    // type: CheckExistPersonInGroupDto,
  })
  checkExistPersonInGroup(@Body() payload: CheckExistPersonInGroupDto) {
    return this.customersService.checkExistPersonInGroup(payload);
  }

  @Post('family-rule/verify/delete-member')
  @ApiBearerAuth()
  @ApiResponse({
    status: HttpStatus.OK,
    // type: CheckExistPersonInGroupDto,
  })
  checkRuleDeleteMember(@Body() payload: CheckRuleDeleteMemberDto) {
    return this.customersService.checkRuleDeleteMember(payload);
  }

  @Post('family-rule/verify/change-owner')
  @ApiBearerAuth()
  @ApiResponse({
    status: HttpStatus.OK,
    // type: CheckExistPersonInGroupDto,
  })
  checkRuleChangeOwner(@Body() payload: CheckRuleChangeOwnerDto) {
    return this.customersService.checkRuleChangeOwner(payload);
  }

  @Put('group/change-group-owner')
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin chi tiết group',
    schema: generalSchema(CreateFamilyPackageResponse, 'object'),
  })
  changeGroupOwner(@Body() payload: ChangeOwnerDto): Promise<CreateFamilyPackageResponse> {
    return this.customersService.changeGroupOwner(payload);
  }

  @Get('group/search')
  @Public()
  @ApiResponse({
    status: HttpStatus.OK,
  })
  searchGroup(@Query() payload: SearchGroupDto) {
    return this.customersService.searchGroup(payload);
  }

  @Post('group-person-junction/add-person')
  @ApiBearerAuth()
  @ApiResponse({
    status: HttpStatus.OK,
    // type: CheckExistPersonInGroupDto,
  })
  @ApiOkResponse({
    description: 'Thông tin chi tiết group',
    schema: generalSchema(CreateFamilyPackageResponse, 'object'),
  })
  addPersonGroup(@Body() payload: AddPersonGroupExtendDto): Promise<CreateFamilyPackageResponse> {
    return this.customersService.addPersonGroup(payload);
  }

  @Delete('group-person-junction/remove-person')
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Thông tin chi tiết group',
    schema: generalSchema(CreateFamilyPackageResponse, 'object'),
  })
  removePersonGroup(@Query() payload: RemovePersonInGroupDto): Promise<CreateFamilyPackageResponse> {
    return this.customersService.removePersonGroup(payload);
  }

  @Post('evidence/insert-or-update')
  @ApiBearerAuth()
  @ApiResponse({
    status: HttpStatus.OK,
    // type: CheckExistPersonInGroupDto,
  })
  insertOrUpdateEvidence(@Body() payload: InsertOrUpdateEvidenceExtendDto) {
    return this.customersService.insertOrUpdateEvidence(payload);
  }

  @Post('many-person')
  @ApiBearerAuth()
  @ApiOkResponse({
    description: 'Chi tiết thông tin person theo lcvId',
    schema: generalSchema(GetPersonByIdRes, 'array'),
  })
  getManyPerson(@Body() payload: GetManyPersonDto): Promise<GetPersonByIdRes[]> {
    return this.customersService.getManyPerson(payload);
  }

  @Get('get-attribute-by-lcvIds')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get attributes by lcvIds',
  })
  @ApiResponse({
    status: HttpStatus.OK,
  })
  getAttributeByLcvIds(@Query() payload: GetAttributeByLcvIdsDto) {
    return this.customersService.getAttributeByLcvIds(payload);
  }

  @Get('family-promotion-fee/:orderCode')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get attributes by lcvIds',
  })
  @ApiResponse({
    status: HttpStatus.OK,
  })
  getFamilyPromotionFeeByOrderCode(@Param('orderCode') orderCode: string) {
    return this.customersService.getFamilyPromotionFeeByOrderCode(orderCode);
  }

  @Post('inactive-family-package')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Inactive family package group',
  })
  @ApiOkResponse({
    description: 'Family package inactivated successfully',
    schema: generalSchema(Boolean, 'boolean'),
  })
  inactiveFamilyPackage(@Body() payload: InactiveFamilyPackageDto): Promise<boolean> {
    return this.customersService.inactiveFamilyPackage(payload);
  }

  @Post('calculate-temp-pricing-by-rank')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'calculate temp pricing by rank',
  })
  @ApiOkResponse({
    description: 'Thông tin giá tạm tính theo hạng',
    schema: generalSchema(CalculateTempPricingByRankResponseDto, 'array'),
  })
  calCulateTempPricingByRank(@Body() body: CalculateTempPriceingByRankDto) {
    return this.customersService.calCulateTempPricingByRank(body);
  }

  /**
   * @TODO check exist otp
   */
  @Get('check-exist-otp/:groupCode')
  @ApiOperation({
    summary: 'Check expired otp',
  })
  @Public()
  @ApiOkResponse({
    description: 'Check exist otp',
    schema: generalSchema(true, 'boolean'),
  })
  customerCheckExpiredOtp(@Param('groupCode') groupCode: string) {
    return this.customersService.customerCheckExpiredOtp(groupCode);
  }
}
