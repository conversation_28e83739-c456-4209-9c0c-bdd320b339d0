import { ApiProperty, PickType } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { IsArray, IsNumber, IsOptional } from 'class-validator';

export class ContentFailOverDto {
  @Expose()
  @Transform(({ value }) => value || '')
  customerName: string;

  @Expose()
  @Transform(({ value }) => value || '')
  advisorName: string;

  @Expose()
  @Transform(({ value }) => value || '')
  firstDiseaseName: string;

  @Expose()
  @Transform(({ value }) => value || '')
  firstDiseaseLink: string;

  @Expose()
  @Transform(({ value }) => value || '')
  secondDiseaseName: string;

  @Expose()
  @Transform(({ value }) => value || '')
  secondDiseaseLink: string;
}

export class ExtraPropertiesDto {
  @Expose()
  @Transform(({ value }) => value || '')
  customerName: string;

  @Expose()
  @Transform(({ value }) => value || '')
  customerId: string;

  @Expose()
  @Transform(({ value }) => value || '')
  advisorName: string;

  @Expose()
  @Transform(({ value }) => value || '')
  firstDiseaseName: string;

  @Expose()
  @Transform(({ value }) => value || '')
  secondDiseaseName: string;

  @Expose()
  @Transform(({ value }) => value || '')
  firstToken: string;

  @Expose()
  @Transform(({ value }) => value || '')
  secondToken: string;
}

export class ContentFailOverCase1Dto extends PickType(ContentFailOverDto, [
  'customerName',
  'firstDiseaseName',
  'firstDiseaseLink',
]) {}
export class ExtraPropertiesCase1Dto extends PickType(ExtraPropertiesDto, [
  'customerName',
  'customerId',
  'firstDiseaseName',
  'firstToken',
]) {}
export class ContentFailOverCase2Dto extends PickType(ContentFailOverDto, [
  'customerName',
  'firstDiseaseName',
  'firstDiseaseLink',
  'secondDiseaseName',
  'secondDiseaseLink',
]) {}
export class ExtraPropertiesCase2Dto extends PickType(ExtraPropertiesDto, [
  'customerName',
  'customerId',
  'firstDiseaseName',
  'firstToken',
  'secondDiseaseName',
  'secondToken',
]) {}

export class ContentFailOverCase3Dto extends PickType(ContentFailOverDto, [
  'advisorName',
  'firstDiseaseName',
  'firstDiseaseLink',
]) {}
export class ExtraPropertiesCase3Dto extends PickType(ExtraPropertiesDto, [
  'customerName',
  'customerId',
  'firstDiseaseName',
  'firstToken',
  'advisorName',
]) {}

export class ContentFailOverCase4Dto extends PickType(ContentFailOverDto, [
  'advisorName',
  'firstDiseaseName',
  'firstDiseaseLink',
  'secondDiseaseName',
  'secondDiseaseLink',
]) {}

export class ExtraPropertiesCase4Dto extends PickType(ExtraPropertiesDto, [
  'customerName',
  'customerId',
  'advisorName',
  'firstDiseaseName',
  'firstToken',
  'secondDiseaseName',
  'secondToken',
]) {}

export class NotifyOsrDto {
  @ApiProperty({ example: ['string'] })
  @IsOptional()
  @IsArray()
  requestIds: string[];

  @ApiProperty({ example: 1 })
  @IsOptional()
  @IsNumber()
  type: string[];
}
