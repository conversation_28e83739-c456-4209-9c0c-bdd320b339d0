import { ApiProperty, OmitType, PickType } from '@nestjs/swagger';
import { CallLogEntity } from '../entities/call-log.entity';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

export class CallLogDto extends OmitType(CallLogEntity, ['id', 'createdAt']) {
  isFirstLog?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  nameEvent?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  isHidden?: boolean;
}

export class FilterCallLog extends PickType(CallLogEntity, [
  'insideCode',
  'fromPhone',
  'toPhone',
  'callID',
  'callType',
  'dialogState',
  'endTime',
  'startTime',
  'recordId',
  'sessionId',
]) {
  @ApiProperty() @IsNumber() skipCount: number;
  @ApiProperty() @IsNumber() maxResultCount: number;
  @ApiProperty() phone: string;
}

export class UpdateNoteCallLogDto extends PickType(CallLogEntity, ['note']) {}
