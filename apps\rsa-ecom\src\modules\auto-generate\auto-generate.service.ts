import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SurveyIndex } from './entities/survey-index.entity';
import { SurveyInsideIndex } from './entities/survey-inside-index.entity';

@Injectable()
export class AutoGenerateService {
  constructor(
    @InjectRepository(SurveyIndex)
    private readonly surveyIndexRepo: Repository<SurveyIndex>,
    @InjectRepository(SurveyInsideIndex)
    private readonly surveyInsideIndexRepo: Repository<SurveyInsideIndex>,
  ) {}

  /**
   * Generate a an index unique always increment
   * Use in sort features
   */
  async createSurveyIndex() {
    try {
      const surveyIndex = await this.surveyIndexRepo.save({});
      return surveyIndex.id;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Generate a an index unique always increment
   * Use in sort features
   */
  async createSurveyInsideIndex() {
    try {
      const surveyInsideIndex = await this.surveyInsideIndexRepo.save({});
      return surveyInsideIndex.id;
    } catch (error) {
      throw error;
    }
  }
}
