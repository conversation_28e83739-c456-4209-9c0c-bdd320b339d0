import { HttpStatus, Injectable } from '@nestjs/common';
import { concurrentPromiseThrowError, ErrorCode, IError, SystemException } from '@shared';
import { plainToInstance } from 'class-transformer';
import { JSONPath } from 'jsonpath-plus';
import { DSMSService } from 'vac-nest-dsms';
import {
  CheckEmployeeShowPopupQueryParamsDto,
  GetEmployeeByShopCodeLCDto,
  GetUserShopInfoDto,
  InsideService,
} from 'vac-nest-inside';
import { getShopVaccineByShopLCDto, itemsShopLC } from './dto/insides.dto';
import * as _ from 'lodash';
import moment from 'moment';

@Injectable()
export class InsidesService {
  constructor(private insideService: InsideService, private dsmsService: DSMSService) {}

  async findAll() {
    const { status: resStatus, data: resData } = await this.insideService.employeeListSales({
      ListEmployeeCode: undefined,
    });
    if (resStatus != HttpStatus.OK) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_INSIDE_ERROR,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_INSIDE_ERROR),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_INSIDE_ERROR),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    //FV-13959
    return _.uniqBy(resData, (item) => `${item.employeeCode}-${item.employeeName}`);
  }

  async getEmployeeInfoLC(params: GetUserShopInfoDto) {
    const [{ shops }, listAllShopVT] = await concurrentPromiseThrowError(
      this.insideService.getEmployeeLC(params),
      this.dsmsService.getAllShopLC(),
    );

    if (!shops?.length || !listAllShopVT?.length) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_AFFILIATE_AUTH_INSIDE,
          message: ErrorCode.getError(ErrorCode.RSA_AFFILIATE_AUTH_INSIDE),
          details: ErrorCode.getError(ErrorCode.RSA_AFFILIATE_AUTH_INSIDE),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    const itemShopLC: string[] = JSONPath({
      path: `$[*].[shopLCAround][code]`,
      json: listAllShopVT,
    });

    const resFilterShopVT = shops?.filter((i) => itemShopLC?.includes(i?.shopCode));

    if (!resFilterShopVT?.length) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_AFFILIATE_AUTH_INSIDE,
          message: ErrorCode.getError(ErrorCode.RSA_AFFILIATE_AUTH_INSIDE),
          details: ErrorCode.getError(ErrorCode.RSA_AFFILIATE_AUTH_INSIDE),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    return {
      shops: resFilterShopVT,
    };
  }

  async getEmployeeByShopCodeLC(params: GetEmployeeByShopCodeLCDto) {
    let { employees } = await this.insideService.getEmployeeByShopCodeLC(params);
    employees = (employees?.length && _.uniqBy(employees, 'MaNhanVien')) || [];
    return {
      employees,
    };
  }

  async getShopVaccineByShopCodeLC(params: getShopVaccineByShopLCDto) {
    let resShopVX = await this.dsmsService.getAllShopLC({
      params: {
        shopLCAround: params?.shopLCAround,
      },
    });

    resShopVX = (resShopVX?.length && _.orderBy(resShopVX, ['shopCode'], ['asc'])) || [];

    resShopVX =
      resShopVX?.length &&
      resShopVX.filter((i) =>
        moment(moment().utcOffset(7).format('YYYY-MM-DD')).isBetween(
          moment(i?.fromDate).utcOffset(7).format('YYYY-MM-DD'),
          moment(i?.toDate).utcOffset(7).format('YYYY-MM-DD'),
          undefined,
          '[]',
        ),
      );

    const shops =
      (resShopVX?.length &&
        plainToInstance(itemsShopLC, resShopVX, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        })) ||
      [];
    return {
      shops,
    };
  }

  async checkEmployeeShowPopup(payload: CheckEmployeeShowPopupQueryParamsDto) {
    return this.insideService.checkEmployeeShowPopup(payload);
  }
}
