import { Injectable } from '@nestjs/common';
import { AppointmentReminder, GetARDetailDto, ItemScheduleByPerson, ScheduleCoreService } from 'vac-nest-schedule';
import { FamilyService, GetPersonByIdRes } from 'vac-nest-family';
import * as _ from 'lodash';
import { calculateAge, calculateSkipCount } from '@shared';
import { getStatusNumber } from '../constants';
import { UpdateARDtoV2 } from '../dto/update-ar.dto';
import { SearchARV2Dto } from '../dto/search-ar-v2.dto';
import { NotificationService } from 'vac-nest-notification';
import { ConfigService } from '@nestjs/config';
import moment from 'moment';
import { SystemExceptionV2 } from '@shared/common/exceptions/system.exception-v2';
import { ErrorCodeV2 } from '@shared/common/errors/error-code-v2';
import { LoggerService } from 'vac-nest-logger';
import { PaidStatus } from '../enum/paid-status.enum';
import { SchedulesUtilsService } from 'modules/modules/modules/schedules/services/schedules-utils.service';
import { VacHistoryService } from 'vac-nest-history';
import { GetAllScheduleResponse } from '../dto/get-all-schedule.dto';
import { AppointmentReminderUtil } from '../utils/appontment-reminder.util';

@Injectable()
export class AppointmentReminderService {
  constructor(
    private readonly scheduleCoreService: ScheduleCoreService,
    private readonly familyService: FamilyService,
    private readonly notificationService: NotificationService,
    private readonly configService: ConfigService,
    private readonly loggerService: LoggerService,
    private readonly schedulesUtilsService: SchedulesUtilsService,
    private readonly vacHistoryService: VacHistoryService,
    private readonly appointmentReminderUtilsService: AppointmentReminderUtil,
  ) {}

  private findHostByLcvId(familyData: GetPersonByIdRes[], lcvId: string) {
    const myFamily = familyData.find((item) => item.lcvId === lcvId);
    if (!myFamily) {
      return null;
    }
    const host = myFamily.familyProfileDetails.find((item) => item.isHost);
    return host || null;
  }

  private findMe(familyData: GetPersonByIdRes[], lcvId: string) {
    const myFamily = familyData.find((item) => item.lcvId === lcvId);
    if (!myFamily) {
      return null;
    }
    const me = myFamily.familyProfileDetails.find((item) => item.lcvId === lcvId);
    return me || null;
  }

  private formatDate(appointmentDate: string) {
    return moment(appointmentDate).utcOffset('+07:00').startOf('days').format() || undefined;
  }

  /**
   * Task: https://reqs.fptshop.com.vn/browse/FV-7466
   * Nếu botNote có thông tin => lịch trễ hẹn này được gọi từ bot => không phải nhân viên ECOM xử lý.
   * Copy botNote sang ecomNote => Mục đích là để FE có thể tiện trong việc lấy data từ 1 field.
   * BotNote có template cụ thể.
   */
  private formatEcomNote(appointment: AppointmentReminder) {
    if (!appointment.botNote) return null;

    const [callStatus, nextAction] = appointment.botNote?.toString()?.split(' - ');
    const noteLines = [
      `Số lần gọi lại: ${appointment.botTryTimes}`,
      `Kết quả cuộc gọi: ${callStatus || ''}`,
      `Hành động tiếp theo: ${nextAction || ''}`,
    ];

    return {
      ecomNote: noteLines.join('\n'),
    };
  }

  private groupByLcvid(appointments: any[]) {
    const appointmentsFormatDate = appointments.map((appointment) => ({
      ...appointment,
      formattedAppointmentDate: appointment.appointmentDate?.split('T')[0] || null,
      ...this.formatEcomNote(appointment),
    }));

    const arGroupByLcvId = _.groupBy(
      appointmentsFormatDate,
      (appointment) => `${appointment.lcvId}&&${appointment.formattedAppointmentDate}`,
    );

    return _.map(arGroupByLcvId, (appointmentItem, lcvIdCompileWithDate) => {
      const [lcvId, formattedAppointmentDate] = lcvIdCompileWithDate.split('&&');
      return {
        lcvId,
        formattedAppointmentDate,
        keyGroup: lcvIdCompileWithDate,
        name: appointmentItem[0].custName || null,
        ..._.pick(appointmentItem[0], [
          'custName',
          'ecomStatus',
          'appointmentDate',
          'ecomProcessDate',
          'ecomProcessStatus',
          'shopCode',
          'shopName',
          'ecomSaleCode',
          'ecomNote',
          'ecomSaleName',
          'botStatus',
          'botNote',
          'botTryTimes',
        ]),
        scheduleReminder: appointmentItem,
      };
    });
  }

  private async getAllFamilyMemberLcvIds(keyword: string) {
    // keyword is phone number or lcvId
    const lcvIds = [];
    const familyData = await this.familyService.searchPerson({
      keyword,
    });
    if (familyData.items.length === 1) {
      const person = familyData.items[0];
      lcvIds.push(person.lcvId, ...person.familyProfileDetails.map((item) => item.lcvId));
      return _.uniq(lcvIds);
    }

    return [];
  }

  private async formatSearchPayload(payloadInput: SearchARV2Dto) {
    // remove all empty value
    const payload: SearchARV2Dto = _.pickBy(payloadInput, (value: any) => value !== undefined && value !== '');
    const { ecomProcessStatus } = payload as unknown as { ecomProcessStatus: number[] };
    if (ecomProcessStatus?.length === 2) {
      delete payload.ecomProcessStatus;
    } else {
      payload.ecomProcessStatus = getStatusNumber[ecomProcessStatus?.at(0)];
    }
    if (payload.keyword) {
      // find all family members by phone number or lcvId
      const lcvIds = await this.getAllFamilyMemberLcvIds(payload.keyword);
      payload.lcvIds = lcvIds;
      if (!lcvIds.length) {
        // fix case, search lcvid empty, but still return list of appointments
        payload.returnEmpty = true;
      }
    }

    if (!payload.page) {
      payload.page = 1;
    } else {
      payload.page = Number(payload.page);
    }

    if (!payload.pageSize) {
      payload.pageSize = 10;
    } else {
      payload.pageSize = Number(payload.pageSize);
    }

    payload.maxResultCount = payload.pageSize;
    payload.skipCount = calculateSkipCount(payload.page, payload.pageSize);

    if (payload.appointmentDateFrom) {
      payload.appointmentDateFrom = this.formatDate(payload.appointmentDateFrom);
    }

    if (payload.appointmentDateTo) {
      payload.appointmentDateTo = this.formatDate(payload.appointmentDateTo);
    }

    if (payload.paidStatus && payload.paidStatus.length) {
      switch (payload.paidStatus.length) {
        case 1:
          // https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=150559017
          if (payload.paidStatus[0] === PaidStatus.PAID) {
            payload.isPaid = true;
          }
          if (payload.paidStatus[0] === PaidStatus.UNPAID) {
            payload.isPaid = false;
          }
          break;
        default:
          delete payload.isPaid;
          // all
          break;
      }
    }

    return _.pickBy(_.omit(payload, ['keyword', 'page', 'pageSize', 'paidStatus']), (value) => value !== undefined);
  }

  /**
   * Map status ispaid, đối với đơn từng phần thì cần xử lý thêm
   * nếu waittingPaid = 5 thì là waiting paid (vẫn chưa thanh toán, chờ thanh toán)
   * nếu ispaid = true và waittingPaid != 0 thì là paid
   * còn lại là chưa thanh toán.
   */
  private formatAppointmentPaid(appointments: AppointmentReminder[]) {
    return appointments.map((appointment) => {
      let paidStatus: PaidStatus = PaidStatus.WAITING_PAID; //appointment.waittingPaid === 5

      if (appointment.waittingPaid === 6 && !appointment.isPaid) {
        return {
          ...appointment,
          paidStatus: PaidStatus.WAITING_PAID_FROM_WEB_APP,
        };
      }

      if (appointment.waittingPaid !== 5) {
        switch (appointment.isPaid) {
          case true:
            paidStatus = PaidStatus.PAID;
            break;
          default:
            paidStatus = PaidStatus.UNPAID;
            break;
        }
      }

      return {
        ...appointment,
        paidStatus,
      };
    });
  }

  async sendSMSNotification(phoneNumbers: any, customerName: string, currentAppointmentDate?: string | null) {
    try {
      await this.notificationService.sendNotification({
        FromSys: this.configService.get('APP_NAME'),
        Sender: this.configService.get('APP_NAME'),
        Messages: [
          {
            TemplateId: this.configService.get('NOTIFICATION_TEMPLATEID_SMS_APPOINTMENT_REMINDER'),
            To: phoneNumbers,
            Bcc: [],
            Cc: [],
            Param: {
              Title: {},
              Content: {
                customerName: customerName || '',
                vaccineDate: currentAppointmentDate
                  ? moment(currentAppointmentDate).utcOffset('+07:00').format('DD/MM/YYYY')
                  : '',
              },
              ContentFailOver: {},
              ExtraProperties: {},
            },
            ImageLink: '',
            MessageLink: '',
          },
        ],
      });
    } catch (e) {}
  }

  async getAllStatus() {
    return this.scheduleCoreService.getARStatus();
  }

  async searchARList(payloadInput: SearchARV2Dto) {
    const emptyResponse = {
      items: [],
      totalCount: 0,
    };
    const payload = await this.formatSearchPayload(payloadInput); // modify payload

    if (payload.returnEmpty) {
      return emptyResponse;
    }

    if (payloadInput.keyword) {
      const isHaveSchedules = await this.appointmentReminderUtilsService.isHaveAppointmentReminder({
        keyword: payloadInput.keyword,
        type: 'normal',
        appointmentDateFrom: payload.appointmentDateFrom,
        appointmentDateTo: payload.appointmentDateTo,
      });

      if (!isHaveSchedules) {
        return emptyResponse;
      }
    }

    const { items: appointments, totalCount } = await this.scheduleCoreService.searchARList(payload);
    const appointmentsMapPaidStatus = this.formatAppointmentPaid(appointments);
    const appointmentsInfo = this.groupByLcvid(appointmentsMapPaidStatus);
    const lcvIds = _.uniq(appointmentsInfo.map((item) => item.lcvId));
    const familyInfo = await this.familyService.getFamilyForReportSymptom(lcvIds);

    const appointmentWithHost = appointmentsInfo.map((appointmentItem) => {
      const host = this.findHostByLcvId(familyInfo, appointmentItem.lcvId);
      return {
        hostPhoneNumber: host?.phoneNumber || null,
        hostLcvId: host?.lcvId || null,
        hostName: host?.name || null,
        ...appointmentItem,
      };
    });

    const allScheduleRecalculatedInjectionOrder = await this.getAllSchedules(lcvIds);
    const recalculatedInjectionOrderItems = appointmentWithHost.map((appointmentItem) =>
      this.mapRecalculatedInjectionOrder(appointmentItem, allScheduleRecalculatedInjectionOrder),
    );

    return {
      items: _.orderBy(
        recalculatedInjectionOrderItems,
        ['ecomProcessStatus', 'formattedAppointmentDate', 'hostLcvId'],
        ['asc', 'desc', 'asc'],
      ),
      totalCount,
    };
  }

  /**
   * Tính lại thứ tự mũi, lịch tiêm get từ API về có thứ tự mũi không chính xác.
   *
   * Thứ tự mũi chính xác phải dựa trên:
   *   + Lịch sử tiêm (đã tiêm)
   *   + Lịch hẹn.
   *
   * Tính lại thứ tự mũi tiêm bằng cách gộp lịch sử tiêm và lịch hẹn với cùng loại vaccine và đếm thứ tự.
   *
   * Ví dụ:
   *  + Lịch sử tiêm: Vaccine A đã tiêm mũi 1.
   *  + Lịch hẹn: Vaccine A tiêm mũi 1, và mũi 2.
   *
   * => gộp lại:
   *    + Vaccine A tiêm mũi 1 (lịch sử)
   *    + Vaccine A tiêm mũi 1 (Lịch hẹn)
   *    + Vaccine A tiêm mũi 2 (Lịch hẹn)
   *
   * => Đếm lại từ đầu và ra kết quả:
   *    + Vaccine A tiêm mũi 1 (lịch sử)
   *    + Vaccine A tiêm mũi 2 (Lịch hẹn)
   *    + Vaccine A tiêm mũi 3 (Lịch hẹn)
   */
  private mapRecalculatedInjectionOrder(appointmentItem: any, allScheduleRecalculatedInjectionOrder: any[]) {
    const allSchedulesOfOnePerson = allScheduleRecalculatedInjectionOrder.find(
      (item) => item.lcvId === appointmentItem.lcvId,
    );
    return {
      ...appointmentItem,
      scheduleReminder: appointmentItem.scheduleReminder.map((scheduleReminderItem) => {
        const scheduleByPersonMapSku = (allSchedulesOfOnePerson?.schedules || []).find(
          (scheduleItem) => scheduleItem.id === scheduleReminderItem.calendarId,
        );
        return {
          ...scheduleReminderItem,
          ...(scheduleByPersonMapSku
            ? {
                injections: (scheduleByPersonMapSku as any).injections,
                injection: (scheduleByPersonMapSku as any).injection,
              }
            : null),
        };
      }),
    };
  }

  async getARDetails(payload: GetARDetailDto) {
    if (payload.appointmentDate) {
      payload.appointmentDate = await this.formatDate(payload.appointmentDate);
    }
    const appointments = await this.scheduleCoreService.getARDetail(payload);
    const appointmentsMapPaidStatus = this.formatAppointmentPaid(appointments);
    const appointment = this.groupByLcvid(appointmentsMapPaidStatus)[0] || null;
    if (!appointment) {
      return null;
    }
    const familyInfo = await this.familyService.getFamilyByLcvId({ lcvId: appointment.lcvId });
    const host = this.findHostByLcvId([familyInfo], appointment.lcvId);
    const me = this.findMe([familyInfo], appointment.lcvId);
    const { months: ageInMonths, years: ageInYears } = calculateAge(familyInfo.dateOfBirth as any);

    const allSchedules = await this.getAllSchedules(payload.lcvIds);

    /**
     * handle case get customer detail has familyProfileDetails is empty => me is null, host is null.
     * so re-get again to gain information.
     */
    let customerInjectionPhoneNumber = me?.phoneNumber;
    if (!customerInjectionPhoneNumber) {
      const customerDetailInfo = await this.familyService.getFamilyByLcvId({ lcvId: appointment.lcvId });
      customerInjectionPhoneNumber = customerDetailInfo?.phoneNumber || null;
    }

    return this.mapRecalculatedInjectionOrder(
      {
        ...appointment,
        phoneNumber: customerInjectionPhoneNumber,
        ageInMonths,
        ageInYears,
        dateOfBirth: familyInfo.dateOfBirth,
        hostPhoneNumber: host?.phoneNumber || null,
        hostGender: host?.gender,
        gender: familyInfo.gender,
        titleName: me?.titleName || null,
        hostName: host?.name || null,
      },
      allSchedules,
    );
  }

  async updateAppointments(payload: UpdateARDtoV2) {
    if (payload.currentAppointmentDate) {
      payload.currentAppointmentDate = await this.formatDate(payload.currentAppointmentDate);
    }

    const currentAppointments = await this.scheduleCoreService.getAppointmentReminderByIds(payload.ids);
    if (!currentAppointments.length) {
      throw new SystemExceptionV2({
        code: ErrorCodeV2.RSA_ECOM_APPOINTMENT_REMINDER_NOT_FOUND,
      });
    }
    if (currentAppointments.some((item) => item.ecomProcessStatus || (item.isPaid && item.waittingPaid !== 5))) {
      throw new SystemExceptionV2({
        code: ErrorCodeV2.RSA_ECOM_APPOINTMENT_REMINDER_PROCESSED,
      });
    }

    const now = moment().utcOffset('+07:00').format();
    const updateResponse = await this.handleUpdateAppointment(
      {
        ..._.omit(payload, ['lcvId', 'currentAppointmentDate', 'onlyUpdateSale']),
        ...(payload.onlyUpdateSale ? {} : { ecomProcessDate: now }),
      },
      'Update customer appointments',
    );
    if (payload.currentAppointmentDate && payload.lcvId && payload.ecomSaleCode) {
      // all ids belong to the same lcvId, nguoi tiem.
      // update sale ecom process to all family members
      const allFamilycvIds = await this.getAllFamilyMemberLcvIds(payload.lcvId);
      if (allFamilycvIds.length) {
        const appointmentReminderInTheDay = await this.scheduleCoreService.searchARList({
          appointmentDateFrom: this.formatDate(payload.currentAppointmentDate),
          appointmentDateTo: this.formatDate(payload.currentAppointmentDate),
          lcvIds: allFamilycvIds,
        });
        if (appointmentReminderInTheDay.items.length) {
          const allFamilyMembersAppointmentIds = appointmentReminderInTheDay.items.map((item) => item.id);
          const allFamilyMembersExcludedInputIds = allFamilyMembersAppointmentIds.filter(
            (item) => !payload.ids.includes(item),
          );
          if (allFamilyMembersExcludedInputIds.length) {
            await this.handleUpdateAppointment(
              {
                ids: allFamilyMembersExcludedInputIds,
                ecomSaleCode: payload.ecomSaleCode,
                ecomSaleName: payload.ecomSaleName,
              },
              'Update others family members appointments',
            );
          }
        }
      }
    }

    if (payload.ecomStatus === 1) {
      // khong goi duoc
      const destinationPhoneNumber = [];
      const familyInfo = await this.familyService.getFamilyByLcvId({ lcvId: payload.lcvId });
      const host = this.findHostByLcvId([familyInfo], payload.lcvId);

      if (familyInfo.phoneNumber) {
        destinationPhoneNumber.push(familyInfo.phoneNumber);
      }

      if (host?.phoneNumber) {
        destinationPhoneNumber.push(host.phoneNumber);
      }

      if (destinationPhoneNumber.length) {
        await this.sendSMSNotification(_.uniq(destinationPhoneNumber), familyInfo.name, payload.currentAppointmentDate);
      }
    }

    return updateResponse;
  }

  async handleUpdateAppointment(payload: UpdateARDtoV2, message?: string) {
    // core không truyền nó update null, nên kao phải làm cái này :v
    const ids = [...payload.ids];
    delete payload.ids;
    const formattedPayload = ids.map((id) => ({ id, ...payload }));

    const currentAppointments = await this.scheduleCoreService.getAppointmentReminderByIds(ids);
    if (currentAppointments.length !== ids.length) {
      throw new SystemExceptionV2({
        code: ErrorCodeV2.RSA_ECOM_APPOINTMENT_REMINDER_NOT_FOUND,
      });
    }

    const payloadUpdate = _.cloneDeep(currentAppointments).map((item) => {
      const fieldsNeedToUpdate = formattedPayload.find((payloadItem) => payloadItem.id === item.id);
      return {
        ...item,
        ...fieldsNeedToUpdate,
      };
    });

    if (message) {
      await this.loggerService.logV2({
        message,
        details: payloadUpdate,
      });
    }

    return this.scheduleCoreService.updateAppointmentsV2(payloadUpdate);
  }

  async getAllSchedules(lcvIdsParam: string[]): Promise<GetAllScheduleResponse[]> {
    const lcvIds = _.uniq(lcvIdsParam);
    const historyInjections = await this.vacHistoryService.getManyByLcvIds(lcvIds);
    const { items: schedules } = await this.scheduleCoreService.getScheduleByPersonCodes({
      personCodes: lcvIds,
      status: [0],
      maxResultCount: 200,
      skipCount: 0,
    });
    const scheduleGroupByLCVID = _.groupBy(schedules, 'lcvId');
    const schedulesWithLcvId = Object.entries(scheduleGroupByLCVID).map(([lcvId, schedulesByLcvid]) => {
      const history = historyInjections.find((item) => item.lcvId === lcvId)?.history || [];
      return {
        lcvId,
        schedules: this.schedulesUtilsService
          .calculateAndOverwriteSchedule([
            ...history.map((h) => ({
              ...h,
              appointmentDate: h.vaccinatedDate,
              skuName: h.vaccineName,
              isHistory: true,
            })),
            ...(schedulesByLcvid as ItemScheduleByPerson[]),
          ])
          .filter((x) => (schedulesByLcvid as ItemScheduleByPerson[]).map((i) => i.id).includes(x.id)),
      };
    });
    return schedulesWithLcvId;
  }
}
