import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from '@shared';
import { ClassResponse } from '@shared';
import { CustomHeaders, generalSchema } from '@shared';
import { PrintCenterRes, VaccineBarcodeRes } from 'vac-nest-print-center';
import {
  AttachBillReturnHomeDto,
  BillAffiliateDto,
  GetPrinterDto,
  GetPrinterResponse,
  GetServiceContractDto,
  InvoiceDto,
  MedicalCardDto,
  PriceListDto,
  PrintHistoryDto,
  PrintInvoiceFamilyPackageDto,
  PrintInvoiceFamilyPackageForZaloDto,
  PrintOrderReturnDto,
  PrintReportRebiesDto,
  PrintReportUsedDto,
  PrintServiceContractRes,
  PrintVaccinationConfirmDto,
  ScheduleInjectionDto,
  ServiceContractFamilyPackageDto,
  VaccineBarcodePayloadDto,
  ZaloInvoiceDto,
} from '../dto';
import { PrintCenterService } from '../services/printCenter.service';
import { PrintUtilsService } from '../services/print-utils.services';

@Controller({ path: 'print', version: '1' })
@ApiTags('Print')
@ApiExtraModels(ClassResponse, GetPrinterResponse, VaccineBarcodeRes, PrintServiceContractRes)
@CustomHeaders()
export class PrintCenterController {
  constructor(
    private readonly printCenterService: PrintCenterService,
    private readonly printUtilsService: PrintUtilsService,
  ) {}

  @Get('printers')
  @ApiOperation({
    summary: 'Lấy thông tin máy in theo shop',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin máy in',
    schema: generalSchema(GetPrinterResponse, 'object'),
  })
  getPrinter(@Query() getPrinterDto: GetPrinterDto) {
    return this.printCenterService.getPrinter(getPrinterDto);
  }

  @Post('barcode')
  @ApiOperation({
    summary: 'In barcode',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Barcode',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  vaccineBarcode(@Body() vaccineBarcodePayloadDto: VaccineBarcodePayloadDto) {
    return this.printCenterService.vaccineBarcode(vaccineBarcodePayloadDto);
  }

  @Post('invoice')
  @ApiOperation({
    summary: 'In Phiếu thu',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Phiếu Thu',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printInvoice(@Body() invoiceDto: InvoiceDto) {
    return this.printCenterService.printInvoice(invoiceDto);
  }

  @Post('bill-family-package')
  @ApiOperation({
    summary: 'In Bill cho đơn nhóm gia đình',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Phiếu Thu',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printInvoiceFamilyPackage(@Body() invoiceDto: PrintInvoiceFamilyPackageDto) {
    return this.printUtilsService.printInvoiceFamilyPackage(invoiceDto);
  }

  @Post('bill-family-package/zalo')
  @ApiOperation({
    summary: 'In Bill cho đơn nhóm gia đình cho zalo',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Phiếu Thu',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printInvoiceFamilyPackageForZalo(@Body() invoiceDto: PrintInvoiceFamilyPackageForZaloDto) {
    return this.printCenterService.printInvoiceFamilyPackageForZalo(invoiceDto);
  }

  @Post('list-attach-bill')
  @ApiOperation({
    summary: 'In Bảng kê',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Bảng kê',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printListAttachBill(@Body() invoiceDto: InvoiceDto) {
    return this.printCenterService.printListAttachBill(invoiceDto);
  }

  @Post('medical-card')
  @ApiOperation({
    summary: 'In Phiếu chỉ định',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Bảng kê',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printMedicalCard(@Body() medicalCardDto: MedicalCardDto) {
    return this.printCenterService.printMedicalCard(medicalCardDto);
  }

  @Post('history')
  @ApiOperation({
    summary: 'In Lịch sử tiêm',
  })
  @Public()
  @ApiOkResponse({
    description: 'Lịch sử tiêm',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printHistory(@Body() printHistoryDto: PrintHistoryDto) {
    return this.printCenterService.printHistory(printHistoryDto);
  }

  @Post('report-used')
  @ApiOperation({
    summary: 'In báo cáo sử dụng vaccine',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Phiếu Thu',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printReportUsed(@Body() body: PrintReportUsedDto) {
    return this.printCenterService.printReportUsed(body);
  }

  @Post('report-rabies')
  @ApiOperation({
    summary: 'In báo cáo tổng hợp theo vaccine',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Phiếu Thu',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printReportRabies(@Body() body: PrintReportRebiesDto) {
    return this.printCenterService.printReportRabies(body);
  }

  @Post('report-rabies-by-cust')
  @ApiOperation({
    summary: 'In báo cáo tổng hợp theo khách hàng',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Phiếu Thu',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printReportRabiesByCust(@Body() body: PrintReportRebiesDto) {
    return this.printCenterService.printReportRabiesByCust(body);
  }

  @Post('attach-bill-return')
  @ApiOperation({
    summary: 'In Bảng kê trả hàng',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin bảng kê trả hàng',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printAttachBillReturn(@Body() body: AttachBillReturnHomeDto) {
    return this.printCenterService.printAttachBillReturn(body);
  }

  @Post('price-list')
  @ApiOperation({
    summary: 'In bảng giá tham khảo dịch vụ vaccine',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin file pdf, image',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printPriceList(@Body() body: PriceListDto) {
    return this.printCenterService.printPriceList(body);
  }

  @Post('service-contract')
  @ApiOperation({
    summary: 'In hợp đồng dịch vụ',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin file pdf, image',
    schema: generalSchema(PrintServiceContractRes, 'object'),
  })
  printServiceContract(@Body() body: GetServiceContractDto) {
    return this.printCenterService.printServiceContract(body);
  }

  @Post('vaccination-confirm')
  @ApiOperation({
    summary: 'In giấy xác nhận',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin file pdf, image',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printVaccinationConfirm(@Body() body: PrintVaccinationConfirmDto) {
    return this.printCenterService.printVaccinationConfirm(body);
  }

  @Post('schedules-injection')
  @ApiOperation({
    summary: 'In giấy phiếu hẹn tiêm',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin file pdf, image',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printSchedulesInjection(@Body() body: ScheduleInjectionDto) {
    return this.printCenterService.printSchedulesInjection(body);
  }

  @Post('bill')
  @ApiOkResponse({
    description: 'Trả về URL S3 của Image và PDF',
    schema: generalSchema(PrintCenterRes, 'object'),
  })
  @ApiOperation({
    summary: 'In bill Long Châu',
  })
  @Public()
  async printLcBill(@Body() payload: BillAffiliateDto) {
    return this.printCenterService.lcBill(payload);
  }

  @Post('order-return')
  @ApiOperation({
    summary: 'In phiếu trả hàng',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin phiếu trả hàng',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printOrderReturn(@Body() body: PrintOrderReturnDto) {
    return this.printCenterService.printOrderReturn(body);
  }

  @Post('invoice-many')
  @ApiOperation({
    summary: 'In Phiếu thu',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin Phiếu Thu',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printInvoiceMany(@Body() invoiceDto: InvoiceDto[]) {
    return this.printCenterService.printInvoiceMany(invoiceDto);
  }

  @Post('service-contract-family-package')
  @ApiOperation({
    summary: 'In hợp đồng dịch vụ cho đơn nhóm gia đình',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin file pdf, image',
    schema: generalSchema(PrintServiceContractRes, 'object'),
  })
  printServiceContractFamilyPackage(@Body() body: ServiceContractFamilyPackageDto) {
    return this.printCenterService.printServiceContractFamilyPackage(body);
  }

  @Post('invoice/zalo')
  @ApiOperation({
    summary: 'Gửi phiếu thu qua Zalo',
  })
  @Public()
  @ApiOkResponse({
    description: 'Kết quả gửi phiếu thu qua Zalo',
    schema: generalSchema(PrintCenterRes, 'object'),
  })
  async sendInvoiceToZalo(@Body() invoiceDto: ZaloInvoiceDto) {
    return this.printCenterService.sendZaloInvoice(invoiceDto);
  }
}
