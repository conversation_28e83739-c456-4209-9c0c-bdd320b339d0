import { FilterPaymentMethodDto } from '../dto/filter-payment-method.dto';
import { PaymentMethod } from 'vac-nest-payment-gateway';

export const ALLOWED_PAYMENT_METHOD: FilterPaymentMethodDto[] = [
  {
    allowedPaymentMethodId: PaymentMethod.VNPAY,
    allowedVendors: 'ALL',
  },
  {
    allowedPaymentMethodId: PaymentMethod.ALEPAY,
    allowedVendors: 'ALL',
  },
  {
    allowedPaymentMethodId: PaymentMethod.PAY_AT_SHOP,
    allowedVendors: 'ALL',
  },
  {
    allowedPaymentMethodId: PaymentMethod.HOME_PAY_LATER,
    allowedVendors: 'ALL',
  },
  {
    allowedPaymentMethodId: PaymentMethod.ZALO_PAY,
    allowedVendors: 'ALL',
  },
];

export enum PaymentQRCodeType {
  POINT_OFFLINE = 1, // Cho các điểm offline
  PRODUCT_PRICE_DEFAULT = 2, // <PERSON> sản phẩm có mệnh giá cố định
  ORDER_OFFLINE = 4, //Cho đơn hàng trên online
}
export enum PaymentTerminalId {
  LONGCHAU = 'LONGCHAU1',
}
export enum PaymentInitMethod {
  TINH = 11, // TĨNH => Đối với QR code loại 1, 3 có phương thức khởi tạo là tĩnh.
  DONG = 12, // ĐỘNG => Đối với kiểu QR code loại 4 chỉ có phương thức khởi tạo là động.
}
export enum PaymentPartnerType {
  VNPayQR = 1,
  VietQR = 2,
  UnionPayQR = 3,
}
export enum PaymentFromSystem {
  RSA = 'mRSA',
}
