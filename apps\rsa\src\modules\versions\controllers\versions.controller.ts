import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiBadRequestResponse, ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from '@shared';
import { ClassErrorResponse, ClassResponse } from '@shared';
import { CustomHeaders, generalSchema } from '@shared';
import {
  GetListVersionDto,
  GetListVersionResponse,
  GetOneVersionResponse,
  UpdateVersionDto,
  UpdateVersionResponse,
} from '../dto';
import { CreateVersionDto, CreateVersionResponse } from '../dto/create-version.dto';
import { VersionsService } from '../services/version.service';

@Controller({ path: 'versions', version: '1' })
@ApiTags('Version')
@ApiExtraModels(
  ClassResponse,
  CreateVersionResponse,
  GetListVersionResponse,
  GetOneVersionResponse,
  UpdateVersionResponse,
)
@CustomHeaders()
export class VersionsController {
  constructor(private readonly versionManagementService: VersionsService) {}

  @Post()
  @ApiOperation({
    summary: 'Tạo version Version',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin version được tạo',
    schema: generalSchema(CreateVersionResponse, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  create(@Body() createVersionDto: CreateVersionDto) {
    return this.versionManagementService.create(createVersionDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách Version',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách version',
    schema: generalSchema(GetListVersionResponse, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  findAll(@Query() getListVersionDto: GetListVersionDto) {
    return this.versionManagementService.findAll(getListVersionDto);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin Version theo id',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Version',
    schema: generalSchema(GetOneVersionResponse, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  findOne(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.versionManagementService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'cập nhật thông tin Version theo id',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin cập nhật thành cong7',
    schema: generalSchema(UpdateVersionResponse, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  update(@Param('id', new ParseUUIDPipe()) id: string, @Body() updateVersionDto: UpdateVersionDto) {
    return this.versionManagementService.update(id, updateVersionDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Xoá version theo id',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin xoá thành công',
    schema: generalSchema(UpdateVersionResponse, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  remove(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.versionManagementService.remove(id);
  }

  @Get('latest/:type')
  @ApiOperation({
    summary: 'Lấy version mới nhất theo type',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Version',
    schema: generalSchema(GetOneVersionResponse, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  latestVersion(@Param('type', new ParseIntPipe()) type: number) {
    return this.versionManagementService.latestVersion(type);
  }
}
