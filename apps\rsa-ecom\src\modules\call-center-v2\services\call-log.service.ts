import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import moment from 'moment';
import { DataSource, In, Repository } from 'typeorm';
import { CallLogDto, FilterCallLog, UpdateNoteCallLogDto } from '../dto';
import { CallLogEntity } from '../entities/call-log.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { ElasticSearchService as ElasticsearchService } from '@shared/modules/elastic-search/elastic-search.service';
import _ from 'lodash';

@Injectable()
export class CallLogService {
  readonly indexEs = this.configService.get('ELA_INDEX_CALL_LOG');

  private readonly logger = new Logger(CallLogService.name);

  constructor(
    @InjectRepository(CallLogEntity)
    private readonly callLogRepository: Repository<CallLogEntity>,
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
    private readonly esService: ElasticsearchService,
  ) {}

  getIndexEs() {
    return this.indexEs;
  }

  async createCallLog(callLogDatas: CallLogDto): Promise<CallLogEntity> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const data = plainToInstance(CallLogEntity, callLogDatas);
      const dataSave = await queryRunner.manager.save(data);
      if (!callLogDatas.isHidden) {
        await this.insertIntoES(dataSave);
      }
      await queryRunner.commitTransaction();
      return dataSave;
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async insertIntoES(callLogDatas: CallLogEntity) {
    try {
      await this.esService.indexDocument(this.getIndexEs(), callLogDatas, callLogDatas.sessionId, true);
    } catch (error) {
      throw error;
    }
  }

  async filter(callLogDatas: FilterCallLog) {
    const key = Object.keys(
      _.pick(callLogDatas, ['insideCode', 'callID', 'callType', 'dialogState', 'recordId', 'sessionId']),
    );

    const queries = key?.map((e) => {
      return JSON.parse(`{"term":{"${e}.keyword": {"value":"${callLogDatas[e]}"}}}`);
    });
    if (callLogDatas?.startTime && callLogDatas?.endTime) {
      const [gte, lte] = [
        moment(callLogDatas?.startTime).format('YYYY-MM-DD'),
        moment(callLogDatas?.endTime).format('YYYY-MM-DD'),
      ];

      queries.push(
        ...[
          {
            range: {
              startTime: { gte: gte, lte: lte, format: 'date_optional_time' },
            },
          },
          {
            range: {
              endTime: { gte: gte, lte: lte, format: 'date_optional_time' },
            },
          },
        ],
      );
    }

    const shouldQueryPhone = [];
    const toPhone = callLogDatas.phone || callLogDatas.toPhone;
    const fromPhone = callLogDatas.phone || callLogDatas.fromPhone;
    toPhone &&
      shouldQueryPhone.push({
        wildcard: { toPhone: `*${toPhone}*` },
      });
    fromPhone &&
      shouldQueryPhone.push({
        wildcard: { fromPhone: `*${fromPhone}*` },
      });
    queries.push({
      bool: {
        should: shouldQueryPhone,
      },
    });

    try {
      const data = await this.esService.getClient()?.search({
        index: this.indexEs,
        query: {
          bool: {
            must: queries,
          },
        },
        size: callLogDatas?.maxResultCount || 20,
        from: callLogDatas.skipCount || 0,
        sort: [
          {
            createdAt: 'desc',
          },
        ],
      });

      return this.esService.getDatasAsFilter(data);
    } catch (error) {
      throw error;
    }
  }

  async updateDurationCallLogDB(callLogDatas: CallLogDto, sessionId: string) {
    return this.updateCallLogDBAndES(callLogDatas, sessionId);
  }

  async updateCallLogDBAndES(callLogDatas: CallLogDto, sessionId: string) {
    let dataSave;

    // update db
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      dataSave = await queryRunner.manager
        .getRepository(CallLogEntity)
        .createQueryBuilder('call_log_v2')
        .update<CallLogEntity>(CallLogEntity, {
          duration: callLogDatas.duration,
          endTime: callLogDatas.endTime,
          startTime: callLogDatas.startTime,
          recordId: callLogDatas.recordId,
          dialogState: callLogDatas.dialogState,
          callEvent: callLogDatas.callEvent,
          callType: callLogDatas.callType,
          note: callLogDatas.note,
          listRefSessionId: callLogDatas.listRefSessionId,
        })
        .where('call_log_v2.session_id = :id', { id: sessionId })
        .execute();
      // update es
      if (!callLogDatas.isHidden) {
        try {
          await this.esService.update(this.getIndexEs(), callLogDatas, sessionId, true);
        } catch (error) {
          this.logger.error(`Error updating call log in Elasticsearch: `, error);
        }
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }

    return dataSave;
  }

  async updateNoteCallLogDB(dataCall: UpdateNoteCallLogDto, sessionId: string) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const dataSave = await queryRunner.manager
        .getRepository(CallLogEntity)
        .createQueryBuilder('call_log_v2')
        .update<CallLogEntity>(CallLogEntity, {
          note: dataCall.note,
        })
        .where('call_log_v2.session_id = :id', { id: sessionId })
        .execute();

      const dataCallLog = await queryRunner.manager.findOne<CallLogEntity>(CallLogEntity, {
        where: {
          sessionId: sessionId,
        },
      });
      if (!dataCallLog) {
        throw new Error('Call log not found');
      }

      // Update the note in Elasticsearch as well
      await this.esService.update(this.getIndexEs(), dataCallLog, sessionId);
      await queryRunner.commitTransaction();
      return dataSave;
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getLogByOriginSessionId(sessionId: string) {
    const querys = [{ wildcard: { sessionId: `${sessionId}*` } }];
    try {
      const data = await this.esService.getClient().search({
        index: this.indexEs,
        query: {
          bool: {
            must: querys,
          },
        },
        sort: [
          {
            createdAt: 'desc',
          },
        ],
      });

      return this.esService.getDatasAsFilter(data);
    } catch (error) {
      throw error;
    }
  }

  async updateCallIdToLogBySessionId(callId: string, sessionId: string): Promise<any> {
    let dataSave;

    // update db
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      dataSave = await queryRunner.manager
        .getRepository(CallLogEntity)
        .createQueryBuilder('call_log_v2')
        .update<CallLogEntity>(CallLogEntity, {
          callID: callId,
        })
        .where('call_log_v2.session_id LIKE :id', { id: `${sessionId}%` })
        .execute();
      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }

    // update es
    try {
      const callsInfo = await this.getLogByOriginSessionId(sessionId);
      const listUpdateCallEs = callsInfo.items.map((callItem) =>
        this.esService.update(
          this.getIndexEs(),
          {
            ...(callItem as CallLogEntity),
            callID: callId,
          },
          sessionId,
          true,
        ),
      );

      await Promise.all(listUpdateCallEs);
    } catch (error) {
      throw error;
    }

    return dataSave;
  }

  async getLogsBySessionIds(sessionIds: string[]): Promise<{ items: CallLogEntity[]; totalCount: number }> {
    if (!sessionIds.length) {
      return { items: [], totalCount: 0 };
    }

    try {
      const callLogEntities = await this.callLogRepository
        .createQueryBuilder('call_log_v2')
        .where('call_log_v2.session_id LIKE ANY (:sessionIds)', {
          sessionIds: sessionIds.map((id) => `${id}%`),
        })
        .getMany();

      // Get additional logs with matching sessionIds from listRefSessionId
      const additionalSessionIds = callLogEntities
        .flatMap((entity) => entity.listRefSessionId || [])
        .filter((id) => id && !sessionIds.includes(id));

      if (additionalSessionIds.length > 0) {
        const additionalLogs = await this.callLogRepository
          .createQueryBuilder('call_log_v2')
          .where('call_log_v2.session_id LIKE ANY (:sessionIds)', {
            sessionIds: additionalSessionIds?.map((id) => `${id}%`),
          })
          .getMany();

        callLogEntities.push(...additionalLogs);
      }

      return {
        items: _.uniqBy(callLogEntities, 'id') || [],
        totalCount: _.uniqBy(callLogEntities, 'id')?.length || 0,
      };
    } catch (error) {
      throw error;
    }
  }
}
