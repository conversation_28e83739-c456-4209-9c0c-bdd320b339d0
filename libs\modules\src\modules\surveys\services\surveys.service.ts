import { HttpStatus, Inject, Injectable, LoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ElasticSearchService,
  ErrorCode,
  OrderChannels,
  SystemException,
  concurrentPromise,
  dateWithTimezone,
  parseDateTimeZone,
  translateVietnameseAccents,
} from '@shared';
import { plainToInstance } from 'class-transformer';
import { Request } from 'express';
import { JSONPath } from 'jsonpath-plus';
import _ from 'lodash';
import { NotificationService as LocalNotificationService } from 'modules/modules/modules/notification';
import moment from 'moment';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { Repository } from 'typeorm';
import { calculateTimeDifference } from 'vac-commons';
import { FamilyService } from 'vac-nest-family';
import { InsideService } from 'vac-nest-inside';
import { NotificationService, ShortLinkService } from 'vac-nest-notification';
import { GetListConsultantResultForTCQGRes, OsrService, SearchSurveyTCQGByQueryDto } from 'vac-nest-osr';
import { RegimenService } from 'vac-nest-regimen';
import {
  AssignSurveyDto,
  AssignUnpaidSurveysDto,
  POTENTIAL_CUSTOMER_TICKET_STATUS,
  RSAIntegrationService,
  SurveyConsultantResult,
  UpdateSurveyDto,
  formatItemSurvey,
} from 'vac-nest-rsa-integration';
import { GetUnpaidCustomerDto } from 'vac-nest-rsa-integration/dist/dto/unpaid-survey-customer.dto';
import { UpdateUnpaidSurveyDto } from 'vac-nest-rsa-integration/dist/dto/update-unpaid-survey.dto';
import { VacStorefrontService } from 'vac-nest-storefront';
import { NotificationType } from '../../notification/enum/notification-type.enum';
import { formatNotificationPayload } from '../../notification/notification.utils';
import { SurveyContactAgainTemplate, UnpaidSurveyAssignTemplate } from '../../notification/templates';
import { AssignSurveyTemplate } from '../../notification/templates/assign-survey.template';
import {
  NOTI_REGISTER_CHANEL_NAME,
  POTENTIAL_CUSTOMER_REQUEST_STATUS,
  SHOP_CODE_ECOM_VACCINE,
  generateAddressId,
} from '../constants';
import { ContactAgainDto } from '../dto/contact-again.dto';
import { GetUnpaidDetailDto } from '../dto/get-unpaid-detail.dto';
import { AssignManySurveysDto, UpdatedSurveyCustomerTcqgDto } from '../dto/survey-tcqg.dto';
import { AssignSurveysConvertDto, PayloadSearchSurveyTCQGByQueryDto } from '../dto/survey.dto';
import { NotiSurveys } from '../entitys/noti-surveys.entity';
import { AssignTypeEnum, TenantCode } from '../enum/tenant-code.enum';
import { SMSSurveysService } from './sms.service';
import { ErrorCodeV2 } from '@shared/common/errors/error-code-v2';
import { SendNotiDto } from '../dto/send-noti-survey.dto';

@Injectable()
export class SurveysService {
  constructor(
    private readonly notificationService: NotificationService,
    private readonly rsaIntegrationService: RSAIntegrationService,
    private readonly configService: ConfigService,
    private readonly esService: ElasticSearchService,
    private readonly localNotificationService: LocalNotificationService,
    private readonly insideService: InsideService,
    private readonly osrService: OsrService,
    private readonly shortLinkService: ShortLinkService,
    private readonly storeFontService: VacStorefrontService,
    private readonly familyCoreService: FamilyService,
    private readonly regimenService: RegimenService,
    private readonly sMSSurveysService: SMSSurveysService,

    @InjectRepository(NotiSurveys)
    private readonly notiSurveysRepository: Repository<NotiSurveys>,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  keyEs = this.configService.get('ELA_INDEX_SURVEY');

  async jobRemind() {
    let listItem = [];
    let check;
    try {
      // get all
      const pageSize = 600;
      const pageSizeOsr = 200;
      const page = Math.ceil(pageSize / pageSizeOsr);
      const promises = [];
      for (let i = 0; i < page; i++) {
        promises.push(i);
      }
      const resListSurvey = await Promise.all(
        promises?.map((e) => {
          return this.rsaIntegrationService.getListSurveyByEs(
            {
              consultantResult: POTENTIAL_CUSTOMER_REQUEST_STATUS.CANT_CONTACT,
              skipCount: e * pageSizeOsr,
              maxResultCount: pageSizeOsr,
              sortOrder: 1,
              modifiedDate: parseDateTimeZone(new Date(), '+07:00', 'YYYY-MM-DD'),
              shopCodeVaccine: SHOP_CODE_ECOM_VACCINE,
            },
            {
              headers: {
                'order-channel': '7',
              },
            },
          );
        }),
      );
      let itemsSurvey = resListSurvey?.reduce((final, curr) => {
        return curr?.items?.length ? [...final, ...curr?.items] : final;
      }, []);
      itemsSurvey = _.uniqBy(itemsSurvey, (e) => e.id);
      if (!itemsSurvey || itemsSurvey?.length <= 0) return null;
      const listSentNoti: NotiSurveys[] = await this.esService.searchSurvey(this.keyEs, {
        uuid: itemsSurvey?.filter((item) => item.id)?.map((e) => e.id),
        pageSize: pageSize,
      });

      check = {
        modifiedDate: itemsSurvey?.[0]?.modifiedDate,
        now: moment().utcOffset('+07:00').format(),
        diff: this.getDiffMinutes(itemsSurvey?.[0]?.modifiedDate),
      };

      // filter
      const timeRemind = Number(this.configService.get('TIME_REMIND_SURVEY') || 15);
      const idSentNoti = listSentNoti?.map((item) => item.surveyId);

      try {
        await this.logger.log(
          {
            message: `Data es remind survey`,
            fields: {
              info: `Data es remind survey`,
              method: `GET`,
              url: `Data es remind survey`,
              bodyReq: '{}',
              queryReq: '{}',
              paramsReq: '{}',
              headers: '{}',
              dataRes: JSON.stringify(idSentNoti || []),
            },
          },
          false,
        );
      } catch (error) {}

      listItem = itemsSurvey.filter(
        (e) =>
          e.consultantResult === POTENTIAL_CUSTOMER_REQUEST_STATUS.CANT_CONTACT &&
          !idSentNoti?.includes(e.id) &&
          this.getDiffMinutes(e.modifiedDate) >= timeRemind,
      );

      const messages = listItem?.map((e) => {
        const content = `Vui lòng liên hệ lại KH tiềm năng`;
        return {
          TemplateId: process.env.NOTIFICATION_TEMPLATE_ID,
          To: [generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, e.modifiedBy)], //e.modifiedBy
          Param: {
            Title: {
              title: content,
            },
            Content: {
              content: content,
            },
            ContentFailOver: {},
            ExtraProperties: {
              type: NotificationType.SURVEYS,
              code: e.id,
              customerName: e.customerName,
              phoneNumber: e.phoneNumber,
            },
          },
          MessageLink: `potential-customer?redirect=${e.id}`,
        };
      });

      // send noti
      if (messages?.length > 0) {
        try {
          const res = await this.notificationService.sendNotification({
            FromSys: process.env.APP_NAME, //Hệ thống gửi
            Sender: process.env.APP_NAME, //Tên người gửi
            Messages: messages,
          });
          await this.logger.log(
            {
              message: `Noti job remind survey`,
              fields: {
                info: `Noti job remind survey`,
                method: `POST`,
                url: `api/notification survey`,
                bodyReq: JSON.stringify({
                  FromSys: process.env.APP_NAME, //Hệ thống gửi
                  Sender: process.env.APP_NAME, //Tên người gửi
                  Messages: messages,
                }),
                queryReq: '{}',
                paramsReq: '{}',
                headers: '{}',
                dataRes: JSON.stringify(res),
              },
            },
            false,
          );
        } catch (error) {
          await this.logger.log(
            {
              message: `Noti job remind survey error`,
              fields: {
                info: `Noti job remind survey`,
                method: `POST`,
                url: `api/notification survey`,
                bodyReq: JSON.stringify({
                  FromSys: process.env.APP_NAME, //Hệ thống gửi
                  Sender: process.env.APP_NAME, //Tên người gửi
                  Messages: messages,
                }),
                queryReq: '{}',
                paramsReq: '{}',
                headers: '{}',
                dataRes: JSON.stringify(error || {}),
              },
            },
            false,
          );
        }

        const listSave = listItem?.map((e) => {
          return {
            surveyId: e.id,
            modifiedDate: e.modifiedDate,
            modifiedBy: e.modifiedBy,
            customerName: e.customerName,
            phoneNumber: e.phoneNumber,
          };
        });

        const resDb = await this.notiSurveysRepository.insert(listSave);

        const resEs = await this.esService.indexDocuments(
          this.keyEs,
          listSave.map((e) => {
            return { ...e, uuid: e.surveyId };
          }),
        );

        try {
          await this.logger.log(
            {
              message: `Save data remind survey`,
              fields: {
                info: `Save data remind survey`,
                method: `POST`,
                url: `Save data remind survey`,
                bodyReq: '{}',
                queryReq: '{}',
                paramsReq: '{}',
                headers: '{}',
                dataRes: JSON.stringify({
                  resEs,
                  resDb: resDb?.raw,
                }),
              },
            },
            false,
          );
        } catch (error) {}
      }
      // luu redis
      return {
        items: listItem,
        totalCount: listItem?.length,
        check,
      };
    } catch (error) {
      return { error, items: listItem, check };
    }
  }

  async assign(updateSurveyDto: AssignSurveyDto) {
    const res = await this.rsaIntegrationService.assignSurvey(updateSurveyDto);
    const resGetSurvey = await this.rsaIntegrationService.getSurveyById(updateSurveyDto.id);

    // add survey tail because extraProperties may make noti work not right
    const info = {
      idSurvey: updateSurveyDto.id,
      phoneNumberSurvey: resGetSurvey.phoneNumber,
      assignTimeSurvey: moment().utcOffset('+07:00').format('HH:mm DD/MM/YYYY'),
      createdTimeSurvey: moment(resGetSurvey.createdDate).utcOffset('+07:00').format('HH:mm DD/MM/YYYY'),
    };
    await this.localNotificationService.sendNotifications([
      formatNotificationPayload({
        To: [generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, updateSurveyDto.assigneeId)],
        Cc: [],
        Bcc: [],
        template: AssignSurveyTemplate(info),
        replaceParams: {
          title: info,
          content: info,
          messageLink: info,
        },
      }),
    ]);
    return res;
  }

  async assignUnPaidSurvey(updateSurveyDto: AssignUnpaidSurveysDto) {
    return this.rsaIntegrationService.assignUnpaidSurvey(updateSurveyDto);
  }

  private getDiffMinutes(dateTimeString: string) {
    const then = moment(dateWithTimezone(dateTimeString));
    const now = moment();
    const diffInMinutes = now.diff(then, 'minutes');
    return diffInMinutes;
  }

  async updateSurvey(updateSurveyDto: UpdateSurveyDto) {
    const isEcom = OrderChannels.RSA_ECOM.includes((this.req.headers['order-channel'] as string) || '');
    const survey = await this.rsaIntegrationService.updateSurvey(updateSurveyDto, {
      headers: {
        'tenant-code': TenantCode.VACCINE,
      },
    });

    // Nếy ((không nghe máy hoặc tham khảo hoặc tắt máy) và có listVaccineConsultant)
    // => gửi tin nhắn, không thì trả ra thông tin survey

    const isSendSMS =
      [
        SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE,
        SurveyConsultantResult.REFERENCE_1,
        SurveyConsultantResult.CANT_CONTACT,
      ].includes(updateSurveyDto?.consultantResult) && updateSurveyDto?.listVaccineConsultant?.length;

    if (!isSendSMS) {
      return { survey };
    }

    let errorMessage = '';
    try {
      const employeeInfo = await this.insideService.getEmployeeByCode({
        listEmployeeCode: [updateSurveyDto?.modifiedBy],
      });

      /**
       * Lấy thông tin code, name và link từ OSR theo sku
       */
      const vaccineCategorizations = await this.osrService.getListVaccineCategorizationByCodes(
        updateSurveyDto?.listVaccineConsultant?.map((e) => e.code + ''),
      );
      const arrShortLinkPromise = [];
      vaccineCategorizations?.forEach((vaccineCategorization) => {
        if (vaccineCategorization?.link) {
          const shortLinkPromise = this.shortLinkService.createShortLink(
            {
              originalUrl: vaccineCategorization?.link,
              expires: moment().utcOffset('+07:00').add(30, 'days').format(),
            },
            {
              headers: {
                'Content-Type': 'application/json',
                Authorization: this.configService.get('SMS_AUTHEN_BASIC'),
              },
            },
          );
          arrShortLinkPromise.push(shortLinkPromise);
        }
      });

      let arrShortLinkRes = [];
      if (arrShortLinkPromise?.length) {
        arrShortLinkRes = await concurrentPromise(...arrShortLinkPromise);
      }

      vaccineCategorizations?.forEach((vaccineCategorization, index) => {
        vaccineCategorization.link = arrShortLinkRes?.[index]?.shortLink;
        vaccineCategorization['token'] = arrShortLinkRes?.[index]?.token;
      });

      // Cắt thông tin ShopNamedisplay chỉ lấy thông tin shop, không lấy thông tin giờ mở cửa (trước "(Mở cửa")
      const shopInfo = await this.storeFontService.getShop(survey?.shopCodeVaccine);
      const shopNameDisplay = shopInfo?.shopNameDisplay?.split('(Mở cửa')?.at(0);

      /**
       * TemplateId config theo channel và consultantResult
       */
      let templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_1A_2');
      const isRsa = OrderChannels.RSA.includes((this.req.headers['order-channel'] as string) || '');
      const isTemplate1 = [
        SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE,
        SurveyConsultantResult.CANT_CONTACT,
      ].includes(survey?.consultantResult);
      const isTemplate2 = [SurveyConsultantResult.REFERENCE_1].includes(+survey?.consultantResult);

      /**
       * @link https://reqs.fptshop.com.vn/browse/FV-11556
       * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=163696996
       */
      // send sms vaccine quan tâm
      // Nếu source === 2 && phoneNumberSendSMS !== '' && customerSendSMS !== ''
      const isSendSMSVaccineCare =
        survey?.sources === 2 && updateSurveyDto?.phoneNumberSendSMS && updateSurveyDto?.customerSendSMS ? true : false;

      if (isEcom) {
        if (updateSurveyDto?.listVaccineConsultant?.length === 2) {
          if (isTemplate1) {
            templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_1B_2');
          }
          if (isTemplate2) {
            templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_2B_2');
          }
        } else if (updateSurveyDto?.listVaccineConsultant?.length === 1) {
          if (isTemplate1) {
            templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_1B_1');
          }
          if (isTemplate2) {
            templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_2B_1');
          }
        }
      }
      if (isRsa) {
        if (updateSurveyDto?.listVaccineConsultant?.length === 2) {
          if (isTemplate1) {
            if (isSendSMSVaccineCare) {
              templateId = this.configService.get('TEMPLATE_SEND_SMS_FOR_VACCINE_CARE_1_2'); // template 1 có 2 vaccine quan tâm
            } else {
              templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_1A_2');
            }
          }
          if (isTemplate2) {
            if (isSendSMSVaccineCare) {
              templateId = this.configService.get('TEMPLATE_SEND_SMS_FOR_VACCINE_CARE_2_2'); // template 2 có 2 vaccine quan tâm
            } else {
              templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_2A_2');
            }
          }
        } else if (updateSurveyDto?.listVaccineConsultant?.length === 1) {
          if (isTemplate1) {
            if (isSendSMSVaccineCare) {
              templateId = this.configService.get('TEMPLATE_SEND_SMS_FOR_VACCINE_CARE_1_1'); // template 1 có 1 vaccine quan tâm
            } else {
              templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_1A_1');
            }
          }
          if (isTemplate2) {
            if (isSendSMSVaccineCare) {
              templateId = this.configService.get('TEMPLATE_SEND_SMS_FOR_VACCINE_CARE_2_1'); // template 2 có 1 vaccine quan tâm
            } else {
              templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_SURVEY_2A_1');
            }
          }
        }
      }
      // Hệ thông và sender hard theo yêu cầu của BA
      const fromSystem = isRsa ? 'be-rsa-v2' : 'be-rsa-ecom';

      // Gửi tin nhắn theo từng template đã handle ở trên
      const phoneNumberFinalSendSMS = isSendSMSVaccineCare ? updateSurveyDto?.phoneNumberSendSMS : survey?.phoneNumber;
      const customerName = isSendSMSVaccineCare ? updateSurveyDto?.customerSendSMS : survey?.customerName;
      if (updateSurveyDto?.listVaccineConsultant?.length === 1) {
        await this.notificationService.sendNotification({
          FromSys: fromSystem,
          Sender: fromSystem,
          Messages: [
            {
              TemplateId: templateId,
              To: [phoneNumberFinalSendSMS],
              Param: {
                Title: {},
                Content: {},
                ContentFailOver: {
                  advisorName: translateVietnameseAccents(employeeInfo?.at(0)?.employeeName) || '',
                  firstDiseaseName: translateVietnameseAccents(vaccineCategorizations?.at(0)?.name) || '',
                  firstDiseaseLink: vaccineCategorizations?.at(0)?.link || '',
                  advisorPhoneNumber: employeeInfo?.at(0)?.phoneNumber || '',
                },
                ExtraProperties: {
                  customerName: customerName || '',
                  customerId: phoneNumberFinalSendSMS ? `*******${phoneNumberFinalSendSMS?.slice(-3)}` : '',
                  advisorName: employeeInfo?.at(0)?.employeeName || '',
                  shopAddress: shopNameDisplay || '',
                  firstDiseaseName: vaccineCategorizations?.at(0)?.name || '',
                  firstToken: vaccineCategorizations?.at(0)?.['token'] || '',
                },
              },
            },
          ],
        });
      } else if (updateSurveyDto?.listVaccineConsultant?.length === 2) {
        await this.notificationService.sendNotification({
          FromSys: fromSystem,
          Sender: fromSystem,
          Messages: [
            {
              TemplateId: templateId,
              To: [phoneNumberFinalSendSMS],
              Param: {
                Title: {},
                Content: {},
                ContentFailOver: {
                  advisorName: translateVietnameseAccents(employeeInfo?.at(0)?.employeeName) || '',
                  firstDiseaseName: translateVietnameseAccents(vaccineCategorizations?.at(0)?.name) || '',
                  secondDiseaseName: translateVietnameseAccents(vaccineCategorizations?.at(1)?.name) || '',
                  firstDiseaseLink: vaccineCategorizations?.at(0)?.link || '',
                  secondDiseaseLink: vaccineCategorizations?.at(1)?.link || '',
                  advisorPhoneNumber: employeeInfo?.at(0)?.phoneNumber || '',
                },
                ExtraProperties: {
                  customerName: customerName || '',
                  customerId: phoneNumberFinalSendSMS ? `*******${phoneNumberFinalSendSMS?.slice(-3)}` : '',
                  firstDiseaseName: vaccineCategorizations?.at(0)?.name || '',
                  secondDiseaseName: vaccineCategorizations?.at(1)?.name || '',
                  advisorName: employeeInfo?.at(0)?.employeeName || '',
                  shopAddress: shopNameDisplay || '',
                  firstToken: vaccineCategorizations?.at(0)?.['token'] || '',
                  secondToken: vaccineCategorizations?.at(1)?.['token'] || '',
                },
              },
            },
          ],
        });
      }
    } catch (error) {
      errorMessage = error.message;
    }

    return { survey, errorMessage };
  }

  async contactAgainNotification(body: ContactAgainDto) {
    const messages = body?.listItem.map((e) => {
      const info = {
        surveyId: e?.surveyId,
        surveyPhoneNumber: e?.surveyPhoneNumber,
        lcvId: e?.lcvId,
      };
      return formatNotificationPayload({
        To: [generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, e?.assigneeId)],
        Cc: [],
        Bcc: [],
        template: SurveyContactAgainTemplate(info),
        replaceParams: {
          title: info,
          content: info,
          messageLink: info,
        },
      });
    });
    if (messages?.length <= 0) {
      return false;
    }
    return await this.localNotificationService.sendNotifications(messages);
  }

  async assignUnpaidSurveyNotification(body: ContactAgainDto) {
    const messages = body?.listItem.map((e) => {
      const info = {
        surveyId: e?.surveyId,
        surveyPhoneNumber: e?.surveyPhoneNumber,
        lcvId: e?.lcvId,
      };
      return formatNotificationPayload({
        To: [generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, e?.assigneeId)],
        Cc: [],
        Bcc: [],
        template: UnpaidSurveyAssignTemplate(info),
        replaceParams: {
          title: info,
          content: info,
          messageLink: info,
        },
      });
    });
    if (messages?.length <= 0) {
      return false;
    }
    return await this.localNotificationService.sendNotifications(messages);
  }

  async getUnpaidCustomer(getUnpaidCustomerDto: GetUnpaidCustomerDto) {
    const res = (await this.rsaIntegrationService.getUnpaidCustomer(getUnpaidCustomerDto))?.data;
    // Handle ageGroup
    const arrLcvIds = _.uniq(
      JSONPath({
        path: '$.items[*].detail[*].injectedPersonLCV',
        json: res,
      }),
    )?.filter(Boolean) as string[];
    const [listPerson, ageRanges] = await Promise.all([
      arrLcvIds?.length ? this.familyCoreService.getListPrimaryPerson(arrLcvIds) : [],
      this.regimenService.getAgeRanges(),
    ]);
    // End handle
    const itemsConvert = res?.items?.map((item) => ({
      ...item,
      detail: item?.detail?.map((itemDetail) => {
        // calculate age group
        const findPerson = listPerson?.find((itemPerson) => itemPerson?.lcvId === itemDetail?.injectedPersonLCV);
        const ageGroup = findPerson
          ? calculateTimeDifference(
              findPerson?.dateOfBirth,
              findPerson?.from,
              findPerson?.to,
              ageRanges,
              findPerson.ageUnitCode,
            )?.textDisplay
          : '';
        // End calculate
        return {
          ...itemDetail,
          ageGroup: findPerson?.dateOfBirth ? ageGroup : itemDetail?.ageGroup,
          listVaccine: itemDetail?.listVaccine?.map((itemListVaccine) => itemListVaccine?.deseaseGroupName)?.join(', '), //Hanh BA confirm
        };
      }),
    }));
    return {
      ...res,
      items: itemsConvert,
    };
  }

  private processSurveyConsultant(listVaccineConsultant: any) {
    const transactions = listVaccineConsultant || [];
    if (transactions?.length === 0) return [];

    // Tìm createdAt lớn nhất bằng moment
    const maxCreatedDate = transactions.reduce((max, t) => {
      const current = moment(t.createdDate);
      return current.isAfter(max) ? current : max;
    }, moment(transactions[0].createdDate));

    // Filter những transaction có cùng thời gian chính xác
    return transactions.filter(
      (t) => moment(t.createdDate).format('YYYY-MM-DD HH:mm:ss') === maxCreatedDate.format('YYYY-MM-DD HH:mm:ss'),
    );
  }

  async getUnpaidCustomerDetail(getUnpaidDetailDto: GetUnpaidDetailDto) {
    const res = (await this.rsaIntegrationService.getUnpaidCustomerDetail(getUnpaidDetailDto.phoneNumber))?.data;
    const listDetail = res?.detail;
    const findItem = listDetail?.find((item) => item?.id === getUnpaidDetailDto.id);
    // Handle ageGroup
    const [listPerson, ageRanges] = await Promise.all([
      findItem?.injectedPersonLCV ? this.familyCoreService.getListPrimaryPerson([findItem?.injectedPersonLCV]) : [],
      this.regimenService.getAgeRanges(),
    ]);
    // End handle
    // calculate age group
    const findPerson = listPerson?.[0];
    const ageGroup = findPerson
      ? calculateTimeDifference(
          findPerson?.dateOfBirth,
          findPerson?.from,
          findPerson?.to,
          ageRanges,
          findPerson.ageUnitCode,
        )?.textDisplay
      : '';
    // End calculate
    return {
      ...res,
      ...findItem,
      detail: undefined,
      // Để tạm cho FE map trước
      doctorInside: findItem?.doctorInside || '',
      doctorName: findItem?.doctorName || '',
      listVaccineConsultant: this.processSurveyConsultant(findItem?.listVaccineConsultant) || [],
      pharmacistNote: findItem?.pharmacistNote || '',
      shopCodeVaccine: findItem?.shopCodeVaccine || '',
      shopNameVaccine: findItem?.shopNameVaccine || '',
      sources: findItem?.sources || null,
      surveyTransactions: findItem?.surveyTransactions || [],
      // age group
      ageGroup: findPerson?.dateOfBirth ? ageGroup : findItem?.ageGroup,
      listVaccine: _.orderBy(findItem?.listVaccine || [], ['appointmentDate'], ['asc']),
    };
  }

  async updateUnpaidCustomerById(updateUnpaidSurveyDto: UpdateUnpaidSurveyDto) {
    const tenantCode = TenantCode.VACCINE_ECOM;
    const resUpdate = (
      await this.rsaIntegrationService.updateUnpaidCustomerById(updateUnpaidSurveyDto, {
        headers: {
          'tenant-code': tenantCode,
        },
      })
    ).data;
    // Handle ageGroup
    const [listPerson, ageRanges] = await Promise.all([
      resUpdate?.injectedPersonLCV ? this.familyCoreService.getListPrimaryPerson([resUpdate?.injectedPersonLCV]) : [],
      this.regimenService.getAgeRanges(),
    ]);
    // End handle
    // calculate age group
    const findPerson = listPerson?.[0];
    const ageGroup = findPerson
      ? calculateTimeDifference(
          findPerson?.dateOfBirth,
          findPerson?.from,
          findPerson?.to,
          ageRanges,
          findPerson.ageUnitCode,
        )?.textDisplay
      : '';
    // End calculate

    // Handle noti (NghiaTX1)

    // Nếy ((không nghe máy hoặc tham khảo hoặc tắt máy) và có listVaccineConsultant)
    // => gửi tin nhắn, không thì trả ra thông tin survey

    const isSendSMS =
      [
        SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE,
        SurveyConsultantResult.REFERENCE_1,
        SurveyConsultantResult.CANT_CONTACT,
      ].includes(updateUnpaidSurveyDto?.consultantResult) && updateUnpaidSurveyDto?.listVaccineConsultant?.length;

    const resSurvey = {
      ...resUpdate,
      listVaccineConsultant: this.processSurveyConsultant(resUpdate?.listVaccineConsultant) || [],
      // age group
      ageGroup: findPerson?.dateOfBirth ? ageGroup : findPerson?.ageGroup,
    };
    if (!isSendSMS) {
      return {
        survey: resSurvey,
        errorMessage: '',
      };
    }

    let errorMessage = '';
    try {
      /**
       * Lấy thông tin employee
       * Lấy thông tin code, name và link từ OSR theo sku
       */
      const [employeeInfo, vaccineCategorizations] = await concurrentPromise(
        this.insideService.getEmployeeByCode({
          listEmployeeCode: [updateUnpaidSurveyDto?.modifiedBy],
        }),
        this.osrService.getListVaccineCategorizationByCodes(
          updateUnpaidSurveyDto?.listVaccineConsultant?.map((e) => e.code + ''),
        ),
      );

      const arrShortLinkPromise = [];
      vaccineCategorizations?.forEach((vaccineCategorization) => {
        if (vaccineCategorization?.link) {
          const shortLinkPromise = this.shortLinkService.createShortLink(
            {
              originalUrl: vaccineCategorization?.link,
              expires: moment().utcOffset('+07:00').add(30, 'days').format(),
            },
            {
              headers: {
                'Content-Type': 'application/json',
                Authorization: this.configService.get('SMS_AUTHEN_BASIC'),
              },
            },
          );
          arrShortLinkPromise.push(shortLinkPromise);
        }
      });

      let arrShortLinkRes = [];
      if (arrShortLinkPromise?.length) {
        arrShortLinkRes = await concurrentPromise(...arrShortLinkPromise);
      }

      vaccineCategorizations?.forEach((vaccineCategorization, index) => {
        vaccineCategorization.link = arrShortLinkRes?.[index]?.shortLink;
        vaccineCategorization['token'] = arrShortLinkRes?.[index]?.token;
      });

      /**
       * TemplateId config theo consultantResult
       */
      let templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_UNPAID_1B_2');
      const isTemplate1 = [
        SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE,
        SurveyConsultantResult.CANT_CONTACT,
      ].includes(resUpdate?.consultantResult);
      const isTemplate2 = [SurveyConsultantResult.REFERENCE_1].includes(+resUpdate?.consultantResult);

      switch (true) {
        case isTemplate1 && updateUnpaidSurveyDto?.listVaccineConsultant?.length === 2:
          templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_UNPAID_1B_2');
          break;
        case isTemplate1 && updateUnpaidSurveyDto?.listVaccineConsultant?.length === 1:
          templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_UNPAID_1B_1');
          break;
        case isTemplate2 && updateUnpaidSurveyDto?.listVaccineConsultant?.length === 2:
          templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_UNPAID_2B_2');
          break;
        case isTemplate2 && updateUnpaidSurveyDto?.listVaccineConsultant?.length === 1:
          templateId = this.configService.get('TEMPLATE_SEND_SMS_UPDATE_UNPAID_2B_1');
          break;
        default:
          this.configService.get('TEMPLATE_SEND_SMS_UPDATE_UNPAID_1B_2');
          break;
      }
      // Hệ thông và sender hard theo yêu cầu của BA
      const fromSystem = 'be-rsa-ecom';
      let contentFailOver = null;
      let extraProperties = null;

      const phoneNumber = updateUnpaidSurveyDto?.phoneNumberSendSMS
        ? updateUnpaidSurveyDto?.phoneNumberSendSMS
        : updateUnpaidSurveyDto?.phoneNumber;

      if (updateUnpaidSurveyDto?.listVaccineConsultant?.length === 1) {
        contentFailOver = {
          customerName: updateUnpaidSurveyDto?.customerSendSMS || resUpdate?.customerName || '', //FV-15899
          advisorName: translateVietnameseAccents(employeeInfo?.at(0)?.employeeName) || '',
          firstDiseaseName: translateVietnameseAccents(vaccineCategorizations?.at(0)?.name) || '',
          firstDiseaseLink: vaccineCategorizations?.at(0)?.link || '',
        };
        extraProperties = {
          customerName: updateUnpaidSurveyDto?.customerSendSMS || resUpdate?.customerName || '', //FV-15899
          injectedPersonName: resUpdate?.injectedPersonName || '',
          customerId: phoneNumber ? `*******${phoneNumber.slice(-3)}` : '',
          advisorName: employeeInfo?.at(0)?.employeeName || '',
          firstDiseaseName: vaccineCategorizations?.at(0)?.name || '',
          firstToken: vaccineCategorizations?.at(0)?.['token'] || '',
        };
      } else if (updateUnpaidSurveyDto?.listVaccineConsultant?.length === 2) {
        contentFailOver = {
          customerName: updateUnpaidSurveyDto?.customerSendSMS || resUpdate?.customerName || '', //FV-15899
          advisorName: translateVietnameseAccents(employeeInfo?.at(0)?.employeeName) || '',
          firstDiseaseName: translateVietnameseAccents(vaccineCategorizations?.at(0)?.name) || '',
          secondDiseaseName: translateVietnameseAccents(vaccineCategorizations?.at(1)?.name) || '',
          firstDiseaseLink: vaccineCategorizations?.at(0)?.link || '',
          secondDiseaseLink: vaccineCategorizations?.at(1)?.link || '',
        };
        extraProperties = {
          customerName: updateUnpaidSurveyDto?.customerSendSMS || resUpdate?.customerName || '', //FV-15899
          injectedPersonName: resUpdate?.injectedPersonName || '',
          customerId: phoneNumber ? `*******${phoneNumber.slice(-3)}` : '',
          firstDiseaseName: vaccineCategorizations?.at(0)?.name || '',
          secondDiseaseName: vaccineCategorizations?.at(1)?.name || '',
          advisorName: employeeInfo?.at(0)?.employeeName || '',
          firstToken: vaccineCategorizations?.at(0)?.['token'] || '',
          secondToken: vaccineCategorizations?.at(1)?.['token'] || '',
        };
      }

      if (
        updateUnpaidSurveyDto?.listVaccineConsultant?.length === 1 ||
        updateUnpaidSurveyDto?.listVaccineConsultant?.length === 2
      ) {
        await this.notificationService.sendNotification({
          FromSys: fromSystem,
          Sender: fromSystem,
          Messages: [
            {
              TemplateId: templateId,
              To: [phoneNumber],
              Param: {
                Title: {},
                Content: {},
                ContentFailOver: contentFailOver,
                ExtraProperties: extraProperties,
              },
            },
          ],
        });
      }
    } catch (error) {
      errorMessage = error.message;
    }

    // End Handle noti

    return {
      survey: resSurvey,
      errorMessage,
    };
  }
  /**
   * @link https://reqs.fptshop.com.vn/browse/FV-10849
   * @docs https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=163696898
   * @param payload
   * @returns
   */
  async searchSurveyTCQGByQuery(payload: PayloadSearchSurveyTCQGByQueryDto) {
    const payloadOms: SearchSurveyTCQGByQueryDto = {
      maxResultCount: payload?.maxResultCount || 10,
      skipCount: payload?.skipCount || 0,
      status: payload?.status || [],
      consultantResult: payload?.consultantResult || [],
      shopCodeVaccine: payload?.shopCodeVaccine || [],
      customerName: payload?.customerName ? [payload?.customerName] : [],
      customerPhone: payload?.customerPhone ? [payload?.customerPhone] : [],
      nationalVaccineCode: payload?.nationalVaccineCode ? [payload?.nationalVaccineCode] : [],
      fromContactDate: payload?.fromDate ? payload?.fromDate : '',
      toContactDate: payload?.toDate ? payload?.toDate : '',
      assigneeId: payload?.assigneeId || [],
    };
    const res = await this.osrService.searchSurveyTCQGByQuery(payloadOms);

    if (res?.items?.length) {
      res.items = res.items?.map((i) => {
        const customAge = calculateTimeDifference(i?.injectedPersonDOB, 0, 0, [], 0);
        const item = {
          ...i,
          ageGroup: customAge?.textDisplay,
        };
        return formatItemSurvey(item);
      });
    }

    return res;
  }

  /**
   * @ticket https://reqs.fptshop.com.vn/browse/FV-11775
   * @param surveyId
   * @returns
   */
  async getListKQTVForTCQG(surveyId?: string) {
    const { items } = await this.osrService.getKQTVForTCQG({ surveyId });
    const res = plainToInstance(GetListConsultantResultForTCQGRes, items);
    return res?.filter((item) => item?.code !== 11); //FV-17850
  }

  /**
   * @task https://reqs.fptshop.com.vn/browse/FV-11778
   * @param surveyId
   * @returns
   */
  async getDetailSurveyTcqgById(id?: string) {
    const { survey } = await this.osrService.getSurveyTcqgById({ id });
    const { textDisplay } = calculateTimeDifference(survey?.injectedPersonDOB, 0, 0, [], 0);
    return {
      survey: {
        ...formatItemSurvey(survey),
        ageGroup: textDisplay,
      },
    };
  }

  /**
   * @task https://reqs.fptshop.com.vn/browse/FV-11777
   * @param surveyId
   * @returns
   */
  async updatedSurveyTcqgById(body?: UpdatedSurveyCustomerTcqgDto) {
    const payload = {
      modifiedBy: body?.modifiedBy || '',
      modifiedByName: body?.modifiedByName || '',
      doctorInside: body?.doctorInside || '',
      doctorName: body?.doctorName || '',
      assigneeId: body?.assigneeId || '',
      assigneeName: body?.assigneeName || '',
      consultantResult: body?.consultantResult || 0,
      doctorNote: body?.doctorNote || '',
      id: body?.id,
      listVaccineConsultant: body?.listVaccineConsultant || [],
      crCurrentStep: body?.step || 0,
      shopCodeVaccine: body?.shopCodeVaccine || '',
      shopNameVaccine: body?.shopNameVaccine || '',
    };

    // Nếu consultantResult = 2,3,4,5,10 thì status chuyển về Done = 3
    if (
      [
        SurveyConsultantResult.APPOINTMENT_SUCCESS,
        SurveyConsultantResult.REFERENCE_1,
        SurveyConsultantResult.NO_NEED,
        SurveyConsultantResult.CANT_CONTACT,
        SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE,
      ].includes(body.consultantResult)
    ) {
      payload['status'] = POTENTIAL_CUSTOMER_TICKET_STATUS.DONE;
    }

    const { survey } = await this.osrService.updatedSurveyTcqgById(
      { id: body?.id, body: payload },
      {
        headers: {
          'tenant-code': TenantCode.VACCINE,
        },
      },
    );

    const { textDisplay } = calculateTimeDifference(survey?.injectedPersonDOB, 0, 0, [], 0);

    const isSendSMS =
      [
        SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE,
        SurveyConsultantResult.REFERENCE_1,
        SurveyConsultantResult.CANT_CONTACT,
      ].includes(body?.consultantResult) && body?.listVaccineConsultant?.length;

    if (!isSendSMS) {
      return {
        survey: {
          ...formatItemSurvey(survey),
          ageGroup: textDisplay,
        },
      };
    }

    /**
     * Lấy thông tin code, name và link từ OSR theo sku
     */
    const vaccineCategorizations = await this.osrService.getListVaccineCategorizationByCodes(
      body?.listVaccineConsultant?.map((e) => e.code + ''),
    );

    if (survey?.sources === 2 && body?.phoneNumberSendSMS) {
      const { errorMessage } = await this.sMSSurveysService.sendSMSSurvey({
        shopCode: body?.shopCodeVaccine,
        vaccineCategorizations: vaccineCategorizations,
        modifiedBy: body?.modifiedBy,
        consultantResult: survey?.consultantResult,
        listVaccineConsultant: body?.listVaccineConsultant,
        phoneCustomerSendSMS: body?.phoneNumberSendSMS,
        customerName: body?.customerSendSMS,
      });
      return {
        survey: {
          ...formatItemSurvey(survey),
          ageGroup: textDisplay,
        },
        errorMessage,
      };
    }

    return {
      survey: {
        ...formatItemSurvey(survey),
        ageGroup: textDisplay,
      },
    };
  }

  /**
   * @ticket https://reqs.fptshop.com.vn/browse/FV-11940
   * @param body
   * @returns
   */
  async updateAssignSurvey(body?: AssignManySurveysDto[]) {
    const rsSurveyByIds = await this.osrService.getSurveyTcqgByListId(body?.map((i) => i?.id));

    // tìm phiếu có inProcess
    // chỉ cần 1 phiếu thì báo lỗi
    const isExitsInProcess = rsSurveyByIds?.find((i) => !formatItemSurvey(i)?.isInProcess);
    if (isExitsInProcess) {
      throw new SystemException(
        {
          code: ErrorCode.SURVEY_IS_IN_PROCESSING,
          message: ErrorCode.getError(ErrorCode.SURVEY_IS_IN_PROCESSING),
          details: ErrorCode.getError(ErrorCode.SURVEY_IS_IN_PROCESSING),
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check rule phân công
    // Nếu assignType = 0,1 và có assigneeId thì báo lỗi
    rsSurveyByIds.forEach((e) => {
      const itemSurvey = body.find((item) => item.id === e.id);
      if ([AssignTypeEnum.ASSIGN, AssignTypeEnum.SELF_RECIEVE].includes(itemSurvey.assignType)) {
        if (e.assigneeId) {
          throw new SystemException(
            {
              code: ErrorCode.SURVEY_ALREADY_ASSIGN,
              message: ErrorCode.getError(ErrorCode.SURVEY_ALREADY_ASSIGN),
              details: ErrorCode.getError(ErrorCode.SURVEY_ALREADY_ASSIGN),
              validationErrors: null,
            },
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    });

    // assignType = 2
    // updated OSR
    const assigneeDate = parseDateTimeZone(new Date(), '+07:00');
    body.forEach((e) => {
      const survey = rsSurveyByIds?.find((item) => item.id === e.id);
      if (survey?.assigneeDate) {
        e.reAssigneeDate = assigneeDate;
      } else {
        e.assigneeDate = assigneeDate;
      }
    });

    return await this.osrService.updateAssignSurvey({ items: body });
  }

  async assignManySurveys(body: AssignSurveysConvertDto[]) {
    const res = await this.rsaIntegrationService.assignManySurveys(
      body?.map((itemBody) => ({
        ...itemBody,
        phoneNumber: undefined,
      })),
    );
    // Assign relative survey
    await this.assignRelativeSurvey(body);
    return res;
  }

  private async assignRelativeSurvey(body: AssignSurveysConvertDto[]) {
    // 1. Phân công lại là phải phân công cho tất cả các phiếu có cùng số điện thoại

    try {
      const listRequestIdInBody = body?.map((itemBody) => itemBody?.id);
      const today = moment().utcOffset(7).format('YYYY-MM-DD');
      const dayBefore4Days = moment().utcOffset(7).subtract(4, 'days').format('YYYY-MM-DD');
      const assigneeBody = {
        assigneeId: body[0]?.assigneeId,
        assigneeName: body[0]?.assigneeName,
        modifiedBy: body[0]?.modifiedBy,
      };
      // Luồng phân công cho các đơn 0đ có cùng số điện thoại mà chưa được xử lý
      const listPhoneNumber: string[] = _.uniq(body.map((item) => item.phoneNumber));
      // Step 1: Lấy danh sách các đơn hàng 0đ có cùng số điện thoại với đơn hàng đã phân công
      const resAllGetSurveySamePhoneNumber = listPhoneNumber?.length
        ? await Promise.all(
            listPhoneNumber?.map((phoneNumber) =>
              this.rsaIntegrationService.getListSurveyByEs(
                {
                  skipCount: 0,
                  maxResultCount: 100,
                  phoneNumber: phoneNumber,
                  fromDate: dayBefore4Days,
                  toDate: today,
                  consultantResult: [
                    SurveyConsultantResult.PENDING,
                    SurveyConsultantResult.REFERENCE_1,
                    SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE,
                  ],
                  status: [POTENTIAL_CUSTOMER_TICKET_STATUS.NEW, POTENTIAL_CUSTOMER_TICKET_STATUS.AGAIN],
                  shopCodeVaccine: this.req.headers['shop-code'] as string,
                },
                {
                  headers: {
                    'order-channel': this.req.headers['order-channel'] as string,
                  },
                },
              ),
            ),
          )
        : [];
      const listSurveySamePhoneNumber = resAllGetSurveySamePhoneNumber
        ?.map((itemRes) => itemRes?.items)
        ?.flat(1)
        ?.filter(
          (item) =>
            !listRequestIdInBody?.includes(item?.id) &&
            ((item?.status === POTENTIAL_CUSTOMER_TICKET_STATUS.NEW &&
              item?.consultantResult === SurveyConsultantResult.PENDING) ||
              (item?.status === POTENTIAL_CUSTOMER_TICKET_STATUS.AGAIN &&
                [SurveyConsultantResult.REFERENCE_1, SurveyConsultantResult.DO_NOT_ANSWER_THE_PHONE]?.includes(
                  item?.consultantResult,
                ))),
        );
      // Step 2: Phân công
      // Asssign many
      listSurveySamePhoneNumber?.length &&
        (await this.rsaIntegrationService.assignManySurveys(
          listSurveySamePhoneNumber?.map((request) => ({
            ...assigneeBody,
            id: request?.id,
            assignType: request?.assigneeId ? AssignTypeEnum?.RE_ASSIGN : AssignTypeEnum.ASSIGN,
            isReAssign: !!request?.assigneeId,
          })),
        ));
    } catch (err) {
      await this.logger.log(
        {
          message: `Assign manual survey for request same phone number error`,
          fields: {
            info: `Assign manual survey for request same phone number error`,
            method: `POST`,
            url: `Assign manual survey for request same phone number error`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify({ err }),
          },
        },
        false,
      );
    }
  }

  async sendNotficationAssignSurvey(body: SendNotiDto) {
    const { idSurvey, phoneNumber, createdDate, assigneeId } = body;
    const info = {
      idSurvey,
      phoneNumberSurvey: phoneNumber,
      assignTimeSurvey: moment().utcOffset('+07:00').format('HH:mm DD/MM/YYYY'),
      createdTimeSurvey: moment(createdDate).utcOffset('+07:00').format('HH:mm DD/MM/YYYY'),
    };
    await this.localNotificationService.sendNotifications([
      formatNotificationPayload({
        To: [generateAddressId(NOTI_REGISTER_CHANEL_NAME.RSA_ECOM_WEB, assigneeId)],
        Cc: [],
        Bcc: [],
        template: AssignSurveyTemplate(info),
        replaceParams: {
          title: info,
          content: info,
          messageLink: info,
        },
      }),
    ]);
  }
}
