import { BaseEntity } from '@shared';
import { Column, Entity } from 'typeorm';

@Entity({
  name: 'shop_hub',
  orderBy: { created_at: 'DESC' },
})
export class ShopHub extends BaseEntity {
  @Column()
  shopCode: string;

  @Column({
    comment: 'shop hub',
  })
  shopHub: string;

  @Column({
    comment: 'shop name',
  })
  shopName: string;

  @Column({
    comment: 'shopHubName name',
  })
  shopHubName: string;

  @Column({
    comment: 'active name',
  })
  isActive: boolean;
}
