import { Injectable } from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import {
  GetScheduleRequestDTO,
  GetScheduleRequestPreOrderDTO,
} from '../../../../../apps/rsa-ecom/src/modules/schedule-requests/dtos/request';
import { ScreenLevel2 } from '../../../../../apps/rsa-ecom/src/modules/screen-level-2s/entities/screen-level-2.entity';
import { GetScreenLevel2DTO } from '../../../../../apps/rsa-ecom/src/modules/screen-level-2s/dto';
import { EcomOrder } from 'apps/rsa-ecom/src/modules/ecom-order/entities/ecom-order.entity';
import { GetManyOrdersInterface } from 'apps/rsa-ecom/src/modules/schedule-requests/interfaces';
import { ScheduleRequestType } from 'apps/rsa-ecom/src/modules/schedule-requests/enums/schedule-request-type.enum';
import { SearchByLcvIdAndPhoneDto } from 'apps/rsa-ecom/src/modules/schedule-requests/dtos/request/search-by-lcvid-or-phone.dto';
import moment from 'moment';
import { removeVietnameseTones } from '@shared/utilities';

@Injectable()
export class ElasticSearchService {
  private readonly client: Client;

  constructor() {
    const auth: { password: string; username: string } =
      process.env.ELA_USER && process.env.ELA_PASSWORD
        ? {
            username: process.env.ELA_USER,
            password: process.env.ELA_PASSWORD,
          }
        : undefined;

    this.client =
      process.env.ELA_CONNECTION &&
      new Client({
        node: process.env.ELA_CONNECTION.split(','),
        auth,
      });
  }

  getClient = () => this.client;

  async indexDocument(index: string, body: any, id?: string, refresh: boolean = true): Promise<any> {
    return this.client.index({
      index,
      body,
      refresh,
      id,
    });
  }

  async indexDocuments(index: string, documents: any[], refresh: boolean = true): Promise<any> {
    const body = documents.flatMap((doc) => [{ index: { _index: index, _id: doc.uuid } }, doc]);
    return this.client.bulk({
      body,
      refresh,
    });
  }

  async updateDocumentByUUID(indexEs: string, uuid: string, body: any, indexAlias?: string): Promise<any> {
    let index = indexAlias;
    if (!index) {
      const record = await this.client.search({
        index: indexEs,
        query: {
          match: {
            _id: uuid,
          },
        },
      });
      index = record.hits.hits[0]._index;
    }

    return this.client.update({
      index,
      id: uuid,
      body: {
        script: {
          source: 'ctx._source = params.updatedBody',
          lang: 'painless',
          params: {
            updatedBody: body,
          },
        },
      },
    });
  }

  async searchSurvey(index: string, query: any): Promise<any> {
    const queryBuilder: any = {
      from: query.page && query.pageSize && (query.page - 1) * query.pageSize,
      size: query.pageSize && query.pageSize,
      query: {
        bool: {
          should: [
            query.uuid &&
              query.uuid.length && {
                terms: { 'uuid.keyword': query.uuid },
              },
          ].filter(Boolean),
        },
      },
    };
    const records = await this.client.search({
      index: index,
      body: queryBuilder,
    });
    return records.hits.hits.map((z) => z._source);
  }

  async searchScheduleRequest(
    index: string,
    query: GetScheduleRequestDTO,
    type: ScheduleRequestType = ScheduleRequestType.All,
  ) {
    let extraQuery = null;
    if (type === ScheduleRequestType.Default) {
      extraQuery = {
        bool: {
          should: [
            {
              bool: {
                must_not: {
                  exists: {
                    field: 'isPreOrder',
                  },
                },
              },
            },
            {
              bool: {
                must_not: {
                  match: {
                    isPreOrder: true,
                  },
                },
              },
            },
          ],
        },
      };
    }

    if (type === ScheduleRequestType.PreOrder) {
      extraQuery = {
        match: {
          isPreOrder: true,
        },
      };
    }

    const queryBuilder: any = {
      from: query.page && query.pageSize && (query.page - 1) * query.pageSize,
      size: query.pageSize && query.pageSize,
      query: {
        bool: {
          must: [
            query.insideId && query.insideId.length && { terms: { insideCode: query.insideId } },
            query.status && query.status.length && { terms: { status: query.status } },
            query.type && {
              match: {
                type: query.type,
              },
            },
            query.idLink && {
              match: {
                idLink: query.idLink,
              },
            },
            query.orderCode && {
              match: {
                orderCode: query.orderCode,
              },
            },
            query.customerType && query.customerType.length && { terms: { customerType: query.customerType } },
            query.partner && query.partner.length && { terms: { ['partner.keyword']: query.partner } },
            query.orderChannel &&
              query.orderChannel.length && {
                terms: { orderChannel: query.orderChannel },
              },
            query.landingType &&
              query.landingType.length && {
                terms: { landingType: query.landingType },
              },
            query.fromDate &&
              query.toDate && {
                range: {
                  createdDate: {
                    gte: query.fromDate,
                    lte: query.toDate,
                  },
                },
              },
            query.keyword &&
              /^\d+$/.test(query.keyword) && {
                multi_match: {
                  query: query.keyword.trim(),
                  fields: ['id^1.0', 'phoneNumber^1.0'],
                  type: 'cross_fields',
                  operator: 'and',
                  tie_breaker: 0.3,
                },
              },
            query.keyword &&
              !/^\d+$/.test(query.keyword) && {
                multi_match: {
                  query: query.keyword.trim(),
                  fields: ['customerName^1.0'],
                  type: 'cross_fields',
                  operator: 'and',
                },
              },
            ,
            extraQuery && extraQuery,
            {
              bool: {
                should: [
                  { match: { isOutOfQuota: false } },
                  { bool: { must_not: { exists: { field: 'isOutOfQuota' } } } }, // Handle null or non-existent fields
                ],
              },
            },
          ].filter(Boolean),
        },
      },
      sort: [
        {
          id: {
            order: 'desc',
          },
        },
      ],
    };

    const records = await this.client.search({
      index: index,
      body: queryBuilder,
    });
    const { count } = await this.client.count({
      index: index,
      body: {
        query: queryBuilder.query,
      },
    });
    return {
      totalCount: count,
      items: records.hits.hits.map((z) => z._source),
    };
  }

  async searchScheduleRequestPreOrder(index: string, query: GetScheduleRequestPreOrderDTO) {
    const extraQuery = null;

    const queryBuilder: any = {
      from: query.page && query.pageSize && (query.page - 1) * query.pageSize,
      size: query.pageSize && query.pageSize,
      query: {
        bool: {
          must: [
            {
              match: {
                isPreOrder: true,
              },
            },
            query.insideId && query.insideId.length && { terms: { insideCode: query.insideId } },
            query.status && query.status.length && { terms: { status: query.status } },
            query.type && {
              terms: {
                type: query.type,
              },
            },
            query.idLink && {
              match: {
                idLink: query.idLink,
              },
            },
            query.orderCode && {
              match: {
                orderCode: query.orderCode,
              },
            },
            query.customerType && {
              match: {
                customerType: query.customerType,
              },
            },
            query.orderChannel &&
              query.orderChannel.length && {
                terms: { orderChannel: query.orderChannel },
              },
            query.landingType &&
              query.landingType.length && {
                terms: { landingType: query.landingType },
              },
            query.fromDate &&
              query.toDate && {
                range: {
                  createdDate: {
                    gte: query.fromDate,
                    lte: query.toDate,
                  },
                },
              },
            query.keyword &&
              /^\d+$/.test(query.keyword) && {
                multi_match: {
                  query: query.keyword.trim(),
                  fields: ['id^1.0', 'phoneNumber^1.0'],
                  type: 'cross_fields',
                  operator: 'and',
                  tie_breaker: 0.3,
                },
              },
            query.keyword &&
              !/^\d+$/.test(query.keyword) && {
                multi_match: {
                  query: query.keyword.trim(),
                  fields: ['customerName^1.0'],
                  type: 'cross_fields',
                  operator: 'and',
                },
              },
            ,
            extraQuery && extraQuery,
          ].filter(Boolean),
        },
      },
      sort: [
        {
          id: {
            order: 'desc',
          },
        },
      ],
    };

    const records = await this.client.search({
      index: index,
      body: queryBuilder,
    });
    const { count } = await this.client.count({
      index: index,
      body: {
        query: queryBuilder.query,
      },
    });
    return {
      totalCount: count,
      items: records.hits.hits.map((z) => z._source),
    };
  }

  async getManyOrders(index: string, orderCodes: string[]) {
    const queryBuilder: any = {
      from: 0,
      size: orderCodes.length,
      query: {
        bool: {
          must: [
            {
              terms: {
                orderCode: orderCodes,
              },
            },
          ],
        },
      },
      sort: [
        {
          createdDate: {
            order: 'desc',
          },
        },
      ],
      track_total_hits: true,
    };

    const records = await this.client.search<GetManyOrdersInterface>({
      index: index,
      body: queryBuilder,
    });
    return {
      total: records.hits.total['value'],
      items: records.hits.hits.map((z) => z._source),
    };
  }

  async searchRequestLV2(index: string, query: GetScreenLevel2DTO) {
    const queryBuilder: any = {
      from: query.page && query.pageSize && (query.page - 1) * query.pageSize,
      size: query.pageSize && query.pageSize,
      query: {
        bool: {
          must: [
            query.status && {
              match: {
                status: query.status,
              },
            },
            query.employeeAssigned && { employeeAssigned: query.employeeAssigned },
            query.employeeAssign && { employeeAssign: query.employeeAssign },
            query.groupAssigned && { groupAssigned: query.groupAssigned },
            query.groupAssignedName && {
              match: {
                groupAssigned: query.groupAssignedName,
              },
            },
            query.groupAssign && { groupAssign: query.groupAssign },
            query.groupAssignName && {
              match: {
                groupAssign: query.groupAssignName,
              },
            },
            query.source && { source: query.source },
            query.fromDate &&
              query.toDate && {
                range: {
                  createdAt: {
                    gte: query.fromDate,
                    lte: query.toDate,
                  },
                },
              },
            query.phone &&
              /^\d+$/.test(query.phone) && {
                multi_match: {
                  query: query.phone.trim(),
                  fields: ['phone^1.0'],
                  type: 'cross_fields',
                  operator: 'and',
                  tie_breaker: 0.3,
                },
              },
            query.name &&
              !/^\d+$/.test(query.name) && {
                multi_match: {
                  query: query.name.trim(),
                  fields: ['name^1.0'],
                  type: 'cross_fields',
                  operator: 'and',
                },
              },
          ].filter(Boolean),
        },
      },
      sort: [
        {
          createdAt: {
            order: 'desc',
          },
        },
      ],
    };

    const records = await this.client.search({
      index: index,
      body: queryBuilder,
    });
    const { count } = await this.client.count({
      index: index,
      body: {
        query: queryBuilder.query,
      },
    });
    return {
      totalCount: count,
      items: records.hits.hits.map((z) => z._source),
    };
  }

  getDatasAsFilter<T>(responseFromSearchES: any): { totalCount: number; items: T[] } {
    return {
      totalCount: responseFromSearchES?.hits?.total?.value,
      items: responseFromSearchES?.hits.hits?.map((e) => e?._source as T),
    };
  }

  async getScheduleRequestByUUID(index: string, uuid: string) {
    const record = await this.client.search({
      index: index,
      query: {
        match: {
          _id: uuid,
        },
      },
    });
    return record.hits.hits[0]._source || null;
  }

  async getScheduleRequestByUUIDAlias(index: string, uuid: string) {
    const record = await this.client.search({
      index: index,
      query: {
        match: {
          _id: uuid,
        },
      },
    });
    return record.hits.hits[0] || null;
  }

  async getRequestLevel2ByUUID(index: string, uuid: string) {
    const record = await this.client.search<ScreenLevel2>({
      index: index,
      query: {
        match: {
          _id: uuid,
        },
      },
    });
    const returnData: any = record?.hits.hits[0]._source || {};

    return { ...returnData, index: record.hits.hits[0]._index };
  }

  async getEcomOrderByOrderCode(index: string, orderCode: string) {
    try {
      const record = await this.client.search<EcomOrder>({
        index: index, // This can be the alias or specific index
        query: {
          match: {
            _id: orderCode,
          },
        },
      });

      const returnData: any = record.hits.hits.length ? record.hits.hits[0]._source : {};

      return returnData;
    } catch (error) {
      console.log(error?.message);
    }

    return {};
  }

  async update(indexEs, doc, id, refresh = true, indexAlias?: string) {
    let index = indexAlias;
    if (!index) {
      const record = await this.client.search<EcomOrder>({
        index: indexEs, // This can be the alias or specific index
        query: {
          match: {
            _id: id,
          },
        },
      });
      index = record?.hits?.hits[0]?._index;
    }

    if (index) {
      await this.client.update({
        index,
        doc,
        id,
        refresh,
      });
    }
  }

  async updateOrCreate(indexEs, doc, id, refresh = true, indexAlias?: string) {
    let index = indexAlias;
    if (!index) {
      const record = await this.client.search<EcomOrder>({
        index: indexEs, // This can be the alias or specific index
        query: {
          match: {
            _id: id,
          },
        },
      });
      index = record?.hits?.hits?.at(0)?._index;
    }

    if (!index) {
      return await this.indexDocument(indexEs, doc, id, refresh);
    }

    await this.client.update({
      index,
      doc,
      id,
      refresh,
    });
  }

  async searchScheduleRequestByLcvIdOrPhone(index: string, query: SearchByLcvIdAndPhoneDto) {
    try {
      const { keyword } = query;

      // check if key isPreOrder is exist
      const checkIsPreOrder =
        query.hasOwnProperty('isPreOrder') && (query.isPreOrder === true || query.isPreOrder === false);

      let extendQuery = [];
      let isSearchNormal = true;
      if (checkIsPreOrder) {
        extendQuery = [
          // search request preOrder
          query.isPreOrder && {
            match: {
              isPreOrder: query.isPreOrder,
            },
          },
          // search request not preOrder
          !query.isPreOrder && {
            bool: {
              must_not: {
                match: {
                  isPreOrder: true,
                },
              },
            },
          },
        ].filter(Boolean);
      }

      if (query.isPreOrder) {
        const searchExtra = [
          // query.name && { match: { 'subjectInjections.nameOfSubjectsInjection.keyword': [query.name] } },
          query.name &&
            !/^\d+$/.test(query.name) && {
              multi_match: {
                query: removeVietnameseTones(query.name.trim()),
                fields: ['subjectInjections.nameOfSubjectsInjection^1.0', 'subjectInjections.unsignedName'],
                type: 'phrase',
                operator: 'AND',
                // fuzziness: 'AUTO',
                // analyzer: 'my_analyzer',
              },
            },
          // query.phone && { terms: { 'subjectInjections.phoneOfSubjectsInjection.keyword': [query.phone] } },
        ].filter(Boolean);

        let searchDate = [];
        if (query.dateOfBirth) {
          const fromDate = moment
            .tz(query.dateOfBirth, 'Asia/Ho_Chi_Minh')
            .utcOffset('+07:00')
            .startOf('days')
            .toISOString();
          const toDate = moment
            .tz(query.dateOfBirth, 'Asia/Ho_Chi_Minh')
            .utcOffset('+07:00')
            .endOf('days')
            .toISOString();
          searchDate = [
            {
              range: {
                'subjectInjections.dateOfBirthOfSubjectsInjection': [
                  {
                    gte: new Date(fromDate),
                    lte: new Date(toDate),
                  },
                ],
              },
            },
          ];
        }

        const searchByFrequentlyAddress = [
          query.frequentlyDistrictCode && {
            terms: { 'subjectInjections.districtCode.keyword': [query.frequentlyDistrictCode] },
          },
          query.frequentlyProvinceCode && {
            terms: { 'subjectInjections.provinceCode.keyword': [query.frequentlyProvinceCode] },
          },
          query.frequentlyWardCode && {
            terms: { 'subjectInjections.wardCode.keyword': [query.frequentlyWardCode] },
          },
        ].filter(Boolean);

        const searchByTemporaryAddress = [
          query.temporaryDistrictCode && {
            terms: { 'subjectInjections.districtCode.keyword': [query.temporaryDistrictCode] },
          },
          query.temporaryProvinceCode && {
            terms: { 'subjectInjections.provinceCode.keyword': [query.temporaryProvinceCode] },
          },
          query.temporaryDistrictCode && {
            terms: { 'subjectInjections.wardCode.keyword': [query.temporaryDistrictCode] },
          },
        ].filter(Boolean);

        extendQuery = [...extendQuery, ...searchExtra, ...searchDate];

        if (searchByFrequentlyAddress?.length > 0 && searchByTemporaryAddress?.length > 0) {
          extendQuery.push({
            bool: {
              should: [
                {
                  bool: {
                    must: searchByFrequentlyAddress,
                  },
                },
                {
                  bool: {
                    must: searchByTemporaryAddress,
                  },
                },
              ],
            },
          });
        } else if (searchByFrequentlyAddress?.length > 0) {
          extendQuery.push({
            bool: {
              must: searchByFrequentlyAddress,
            },
          });
        } else if (searchByTemporaryAddress?.length > 0) {
          extendQuery.push({
            bool: {
              must: searchByTemporaryAddress,
            },
          });
        }

        if (
          searchExtra?.length ||
          searchDate?.length ||
          searchByFrequentlyAddress?.length ||
          searchByTemporaryAddress?.length
        ) {
          isSearchNormal = false;
        }
      }

      if (!keyword && isSearchNormal) {
        return {
          totalCount: 0,
          items: [],
        };
      }

      const queryBuilder: any = {
        from: query.page && query.pageSize && (query.page - 1) * query.pageSize,
        size: query.pageSize && query.pageSize,
        query: {
          bool: {
            must: [
              isSearchNormal && {
                bool: {
                  should: [
                    {
                      terms: {
                        'subjectInjections.phoneOfSubjectsInjection.keyword': [keyword],
                      },
                    },
                    {
                      term: {
                        'phoneNumber.keyword': {
                          value: keyword,
                        },
                      },
                    },
                    {
                      terms: {
                        'subjectInjections.lcvId.keyword': [keyword],
                      },
                    },
                  ],
                },
              },
              ...extendQuery,
            ].filter(Boolean),
          },
        },
        sort: [
          {
            createdDate: {
              order: 'desc',
            },
          },
        ],
      };

      const records = await this.client.search({
        index: index,
        body: queryBuilder,
      });
      const { count } = await this.client.count({
        index: index,
        body: {
          query: queryBuilder.query,
        },
      });
      return {
        totalCount: count,
        items: records.hits.hits.map((z) => z._source),
      };
    } catch (error) {
      return {
        totalCount: 0,
        items: [],
        error,
      };
    }
  }
}
