import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCampaignType1747709934790 implements MigrationInterface {
  name = 'AddCampaignType1747709934790';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "schedule_requests" ADD "campaign_type" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "schedule_requests" DROP COLUMN "campaign_type"`);
  }
}
