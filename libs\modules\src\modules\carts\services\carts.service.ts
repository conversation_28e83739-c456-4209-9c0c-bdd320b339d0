import { HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { ErrorCode, IError, ITEM_VACCINE_SO1, SystemException, isSameDate, parseDateTimeZone } from '@shared';
import { Request } from 'express';
import * as _ from 'lodash';
import {
  AddDiscountAdjustmentDto,
  AddExtraDataDto,
  AddListCartItemDto,
  AdjustOrderLibDto,
  CartAppService,
  GetCartLibDto,
  GetCartLibResponse,
  GiftReplaceCartAppDto,
  GiftReplacePromotionCartTotalDto,
  ItemType,
  ReqPromotionCartTotalDto,
  SelectedPromotionCartTotalDto,
  SelectedPromotionDto,
  SkuReplaceDto,
  TypeVaccineNow,
  UpdateDobDto,
  UpdateDobRes,
  UpdateListCartItemRedisDto,
} from 'vac-nest-cart-app';
import { ExaminationCoreService, IndicationStatus } from 'vac-nest-examination';
import { FamilyService } from 'vac-nest-family';
import { IMSService, getWhsTail } from 'vac-nest-ims';
import { JourneyService, TRANSACTION_TYPE } from 'vac-nest-journey';
import { CheckRuleCreateUpdateCartDto, OrderRuleEngineService } from 'vac-nest-order-rule-engine';
import { PIMAppService } from 'vac-nest-pim-app';
import { GiftDto, GiftReplaceDto, PromotionService } from 'vac-nest-promotion';
import { PayloadSchedule, ScheduleEngineAppService } from 'vac-nest-schedule-engine-app';
import {
  AddCartItemDto,
  CheckRuleAddCartDto,
  ConfirmPushingCartDto,
  ConfirmPushingCartRes,
  DateSchedule,
  DeleteCartItemDto,
  GenericSessionDto,
  GetCartDto,
  GetCartResponse,
  GetConfirmedCartParam,
  GetSessionByOrderCodeTicketCodeDto,
  GetTotalBillDto,
  GetTotalBillRes,
  MergeCartToAnonymous,
  MergeCartToCustomer,
  SchedulePreview,
  UpdateCartItemDto,
  UpdateDobParam,
} from '../dto';
import { AddVoucherRSADto } from '../dto/add-voucher-rsa.dto';
import { CartRulesService } from './carts-rule.service';
import { GetMultipleGiftReplaceDto } from 'vac-nest-promotion/dist/dto/get-multiple-gift-replace.dto';
import { DetailAttachment, OMSService, OrderStatus } from 'vac-nest-oms';
import { JSONPath } from 'jsonpath-plus';
import { getDepositDetailAmount, PaymentGatewayService } from 'vac-nest-payment-gateway';
import { OsrService } from 'vac-nest-osr';
import moment from 'moment';

@Injectable()
export class CartsService {
  shopCode: string;
  constructor(
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly cartAppService: CartAppService,
    private readonly familyCoreService: FamilyService,
    private readonly journeyCoreService: JourneyService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly scheduleEngineAppService: ScheduleEngineAppService,
    private readonly pimAppService: PIMAppService,
    private readonly cartRulesService: CartRulesService,
    private readonly promotionService: PromotionService,
    private readonly imsService: IMSService,
    private readonly oMSService: OMSService,
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly osrService: OsrService,
    private readonly orderRuleEngineService: OrderRuleEngineService,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
  }

  async genericSession(genericSessionDto: GenericSessionDto) {
    return this.cartAppService.genericSession(genericSessionDto);
  }

  async getCart(getCartDto: GetCartDto): Promise<GetCartResponse> {
    return this.genericResponseWithRule(getCartDto, await this.cartAppService.getCart(getCartDto));
  }

  async addCartItem(addCartItemDto: AddCartItemDto) {
    // check rule tiêm số mũi tối đa
    // rule mũi max sẽ chuyển sang rule hội chẩn Y Khoa
    // const lstRuleMaxInjection = await this.orderRuleEngineService.checkRuleMaxInjection({
    //   items: [
    //     {
    //       sku: addCartItemDto?.cartItem?.itemCart,
    //       quantity: addCartItemDto?.cartItem?.orderInjections?.length,
    //       regimenId: addCartItemDto?.cartItem?.regimenId,
    //     },
    //   ],
    //   lcvId: addCartItemDto?.cartItem?.lcvId,
    // });

    // if (lstRuleMaxInjection?.items?.at(0)?.isRestrict) {
    //   if (lstRuleMaxInjection?.items?.at(0)?.quantityRemain === 0) {
    //     throw new SystemException(
    //       {
    //         code: ErrorCode.RULE_MAX_INJECTION_DISEASE,
    //         message: lstRuleMaxInjection?.items?.at(0)?.message,
    //         details: lstRuleMaxInjection?.items?.at(0)?.message,
    //       },
    //       HttpStatus.FORBIDDEN,
    //     );
    //   } else {
    //     addCartItemDto.cartItem.quantity = lstRuleMaxInjection?.items?.at(0)?.quantityRemain;
    //     addCartItemDto.cartItem.orderInjections = addCartItemDto.cartItem.orderInjections?.slice(
    //       0,
    //       lstRuleMaxInjection?.items?.at(0)?.quantityRemain,
    //     );
    //   }
    // }

    await this.cartRulesService._checkVersionTicket({
      ticketCode: addCartItemDto?.ticketCode,
      ticketVersion: addCartItemDto?.ticketVersion,
      sessionId: addCartItemDto?.sessionId, // dto required
    });

    if (!addCartItemDto?.cartItem?.objectGroupId && _.min(addCartItemDto?.cartItem?.orderInjections) === 1) {
      const checkRuleDto: CheckRuleCreateUpdateCartDto = {
        DOB: null,
        items: [{ regimenId: addCartItemDto?.cartItem?.regimenId }],
      };

      await this.cartRulesService._checkRuleCreateUpdateCart(checkRuleDto, [addCartItemDto?.cartItem?.lcvId]);
      await this.cartRulesService._checkRuleCreateUpdateCartPreOrder(checkRuleDto, [addCartItemDto?.cartItem?.lcvId]);
    }

    const addCartItemData = await this.cartAppService.addCartItem(addCartItemDto);
    return this.genericResponseWithRule(addCartItemDto, addCartItemData);
  }

  async addDiscountAdjustment(id: string, addDiscountAdjustmentDto: AddDiscountAdjustmentDto) {
    return this.cartAppService.addDiscountAdjustment(id, addDiscountAdjustmentDto);
  }

  async removeDiscountAdjustment(id: string, getCart: GetCartLibDto) {
    return this.cartAppService.removeDiscountAdjustment(id, getCart);
  }

  async addListCartItem(addListCartItemDto: AddListCartItemDto) {
    // check rule tiêm số mũi tối đa
    // rule mũi max sẽ chuyển sang rule hội chẩn Y Khoa
    // const lstRuleMaxInjection = await this.orderRuleEngineService.checkRuleMaxInjection({
    //   items: addListCartItemDto?.listCartItemsInput?.map((cartItem) => ({
    //     sku: cartItem?.itemCart,
    //     quantity: cartItem?.orderInjections?.length,
    //     regimenId: cartItem?.regimenId,
    //   })),
    //   lcvId: addListCartItemDto?.listCartItemsInput?.at(0)?.lcvId,
    // });

    // const lstRegimenMax = lstRuleMaxInjection?.items?.filter((item) => item?.isRestrict && item?.quantityRemain === 0);
    // const errorMessage = lstRegimenMax?.map((entry) => entry?.message)?.join('\n');

    // const arrRegimenMax = lstRegimenMax?.map((entry) => entry?.regimenId);

    // const lstRegimenModified = lstRuleMaxInjection?.items?.filter(
    //   (item) => item?.isRestrict && item?.quantityRemain > 0,
    // );

    // addListCartItemDto.listCartItemsInput = addListCartItemDto.listCartItemsInput?.filter(
    //   (itemCart) => !arrRegimenMax?.includes(itemCart?.regimenId),
    // );
    // addListCartItemDto.listCartItemsInput?.forEach((itemCart) => {
    //   const productFind = lstRegimenModified?.find((entry) => entry?.regimenId === itemCart?.regimenId);
    //   if (productFind) {
    //     itemCart.quantity = productFind?.quantityRemain;
    //     itemCart.orderInjections = itemCart.orderInjections?.slice(0, productFind?.quantityRemain);
    //   }
    // });

    // if (!addListCartItemDto.listCartItemsInput?.length) {
    //   throw new SystemException(
    //     {
    //       code: ErrorCode.RULE_MAX_INJECTION_DISEASE,
    //       message: lstRuleMaxInjection?.items?.at(0)?.message,
    //       details: lstRuleMaxInjection?.items?.at(0)?.message,
    //     },
    //     HttpStatus.FORBIDDEN,
    //   );
    // }

    await this.cartRulesService._checkVersionTicket({
      ticketCode: addListCartItemDto?.ticketCode,
      ticketVersion: addListCartItemDto?.ticketVersion,
      sessionId: addListCartItemDto?.sessionId, // dto required
    });

    const arrRegimen: string[] = _.compact(
      _.uniq(
        addListCartItemDto?.listCartItemsInput?.map((e) => {
          if (!e.objectGroupId && _.min(e?.orderInjections) === 1) {
            return e?.regimenId;
          }
        }),
      ),
    );
    if (arrRegimen?.length) {
      const checkRuleDto: CheckRuleCreateUpdateCartDto = {
        DOB: null,
        items: arrRegimen?.map((e) => ({ regimenId: e })),
      };

      await this.cartRulesService._checkRuleCreateUpdateCart(
        checkRuleDto,
        addListCartItemDto?.listCartItemsInput?.map((e) => e?.lcvId) || [],
      );
      try {
        await this.cartRulesService._checkRuleCreateUpdateCartPreOrder(
          checkRuleDto,
          addListCartItemDto?.listCartItemsInput?.map((e) => e?.lcvId) || [],
        );
      } catch (error) {
        // update header
        await this.cartAppService.updateDob(addListCartItemDto.sessionId, {
          orderAttribute: 0,
        });
        throw error;
      }
    }

    return this.genericResponseWithRule(addListCartItemDto, {
      ...(await this.cartAppService.addListCartItem(addListCartItemDto)),
    });
  }

  async updateCartItem(id: string, updateCartItemDto: UpdateCartItemDto) {
    // check rule tiêm số mũi tối đa
    // rule mũi max sẽ chuyển sang rule hội chẩn Y Khoa
    // const lstRuleMaxInjection = await this.orderRuleEngineService.checkRuleMaxInjection({
    //   items: [
    //     {
    //       sku: updateCartItemDto?.cartItem?.itemCart,
    //       quantity: updateCartItemDto?.cartItem?.orderInjections?.length,
    //       regimenId: updateCartItemDto?.cartItem?.regimenId,
    //     },
    //   ],
    //   lcvId: updateCartItemDto?.cartItem?.lcvId,
    // });

    // if (lstRuleMaxInjection?.items?.at(0)?.isRestrict) {
    //   if (lstRuleMaxInjection?.items?.at(0)?.quantityRemain === 0) {
    //     throw new SystemException(
    //       {
    //         code: ErrorCode.RULE_MAX_INJECTION_DISEASE,
    //         message: lstRuleMaxInjection?.items?.at(0)?.message,
    //         details: lstRuleMaxInjection?.items?.at(0)?.message,
    //       },
    //       HttpStatus.FORBIDDEN,
    //     );
    //   } else {
    //     updateCartItemDto.cartItem.quantity = lstRuleMaxInjection?.items?.at(0)?.quantityRemain;
    //     updateCartItemDto.cartItem.orderInjections = updateCartItemDto.cartItem.orderInjections?.slice(
    //       0,
    //       lstRuleMaxInjection?.items?.at(0)?.quantityRemain,
    //     );
    //   }
    // }

    await this.cartRulesService._checkVersionTicket({
      ticketCode: updateCartItemDto?.ticketCode,
      ticketVersion: updateCartItemDto?.ticketVersion,
      sessionId: updateCartItemDto?.sessionId, // dto required
    });

    const updateCartItemData = await this.cartAppService.updateCartItem(id, updateCartItemDto);
    return this.genericResponseWithRule(updateCartItemDto, {
      ...updateCartItemData,
    });
  }

  async updateListCartItemRedis(updateListCartItemRedisDto: UpdateListCartItemRedisDto) {
    const updateCartItemData = await this.cartAppService.updateListCartItemRedis(updateListCartItemRedisDto);
    return await this.genericResponseWithRule(updateListCartItemRedisDto, updateCartItemData);
  }

  async deleteCartItem(deleteCartItemDto: DeleteCartItemDto) {
    await this.cartRulesService._checkVersionTicket({
      ticketCode: deleteCartItemDto?.ticketCode,
      ticketVersion: deleteCartItemDto?.ticketVersion,
      sessionId: deleteCartItemDto?.sessionId, // dto required
    });

    const deleteCartItemData = await this.cartAppService.deleteCartItem(deleteCartItemDto);
    return await this.genericResponseWithRule(deleteCartItemDto, deleteCartItemData);
  }

  async mergeCartToCustomer(mergeCartToCustomer: MergeCartToCustomer) {
    const mergeCartToCustomerData = await this.cartAppService.mergeCartToCustomer(mergeCartToCustomer);
    return await this.genericResponseWithRule(mergeCartToCustomer, mergeCartToCustomerData);
  }

  async mergeCartToAnonymous(mergeCartToAnonymous: MergeCartToAnonymous) {
    const mergeCartToAnonymousData = await this.cartAppService.mergeCartToAnonymous(mergeCartToAnonymous);
    return this.genericResponseWithRule(mergeCartToAnonymous, mergeCartToAnonymousData);
  }

  async addVoucher(addVoucherDto: AddVoucherRSADto) {
    return this.cartAppService.addVoucher(addVoucherDto);
  }

  async selectedPromotion(selectedPromotionDto: SelectedPromotionDto) {
    return await this.genericResponseWithRule(
      selectedPromotionDto,
      await this.cartAppService.selectedPromotion(selectedPromotionDto),
    );
  }

  async addExtraData(addExtraDataDto: AddExtraDataDto) {
    return await this.genericResponseWithRule(addExtraDataDto, await this.cartAppService.addExtraData(addExtraDataDto));
  }

  async skuReplace(skuReplaceDto: SkuReplaceDto) {
    await this.cartRulesService._checkVersionTicket({
      ticketCode: skuReplaceDto?.ticketCode,
      ticketVersion: skuReplaceDto?.ticketVersion,
      sessionId: skuReplaceDto?.sessionId, // dto required
    });

    if (
      _.min(
        skuReplaceDto?.orderInjectionsByRegimen?.length
          ? skuReplaceDto?.orderInjectionsByRegimen
          : skuReplaceDto?.orderInjections,
      ) === 1
    ) {
      const checkRuleDto: CheckRuleCreateUpdateCartDto = {
        DOB: null,
        items: [{ regimenId: skuReplaceDto?.regimenId || '' }],
      };

      await this.cartRulesService._checkRuleCreateUpdateCart(checkRuleDto, [skuReplaceDto?.lcvId]);
      await this.cartRulesService._checkRuleCreateUpdateCartPreOrder(checkRuleDto, [skuReplaceDto?.lcvId]);
    }

    return this.cartAppService.skuReplace(skuReplaceDto);
  }

  async getGiftReplace(giftReplaceDto: GiftReplaceDto) {
    const res = await this.promotionService.giftReplace(giftReplaceDto);
    const arrSku = res?.giftReplaces.map((e) => e?.itemCode);
    if (!arrSku.length) return res;

    const { listProduct: products } = await this.pimAppService.getListProductBySkuNoRule(arrSku);
    const { inventories } = await this.imsService.getListStockMedicAtShop({
      shopCodes: [this.shopCode || giftReplaceDto.shopCode],
      skuCodes: arrSku,
    });

    for (const giftReplace of res?.giftReplaces) {
      const product = products.find((e) => e.sku === giftReplace.itemCode);
      giftReplace['product'] = product;
      giftReplace['inventory'] =
        inventories?.find(
          (e) =>
            e.sku === giftReplace.itemCode &&
            e.unitCode === giftReplace.unitCode &&
            getWhsTail(e.whsCode) === getWhsTail(giftReplace.whsType),
        ) || null;
    }
    return res;
  }

  async getMultipleGiftReplace(giftReplaceDto: GetMultipleGiftReplaceDto) {
    const { gifts, ...others } = await this.promotionService.getMultipleGiftReplace(giftReplaceDto);
    const skuList = gifts.map((gift) => gift.giftReplaces.map((giftReplace) => giftReplace.itemCode)).flat();
    const { listProduct: products } = await this.pimAppService.getListProductBySkuNoRule(skuList);
    const { inventories } = await this.imsService.getListStockMedicAtShop({
      shopCodes: [this.shopCode || giftReplaceDto.shopCode],
      skuCodes: skuList,
    });

    const processedGifts = gifts.map((gift) => ({
      ...gift,
      giftReplaces: gift.giftReplaces.map((giftReplace) => ({
        ...giftReplace,
        product: products.find((product) => product.sku === giftReplace.itemCode),
        inventory:
          inventories?.find(
            (inventory) =>
              inventory.sku === giftReplace.itemCode &&
              inventory.unitCode === giftReplace.unitCode &&
              getWhsTail(inventory.whsCode) === getWhsTail(giftReplace.whsType),
          ) || null,
      })),
    }));

    return {
      ...others,
      gifts: processedGifts,
    };
  }

  async giftReplace(giftReplaceDto: GiftReplaceCartAppDto) {
    return this.cartAppService.giftReplace(giftReplaceDto);
  }

  async adjustCart(adjustOrderDto: AdjustOrderLibDto) {
    const adjustCart = await this.cartAppService.adjustOrder(adjustOrderDto);
    return this.genericResponseWithRule({ ...adjustOrderDto, sessionId: adjustCart.headerData.sessionId }, adjustCart);
  }

  async confirmPushingCart(confirmPushingCartDto: ConfirmPushingCartDto): Promise<ConfirmPushingCartRes> {
    const getCart = await this.cartAppService.getCart(confirmPushingCartDto);
    const { arrSchedule } = confirmPushingCartDto;

    const returnData: ConfirmPushingCartRes = {
      pushingCart: [],
      pushingCartNew: [],
      calculatorPriceInfo: getCart?.calculatorPriceInfo,
    };

    // Get Data pim
    const arrSku = arrSchedule.map((e) => e.sku);
    if (!arrSku.length) return returnData;
    const { listProduct: products } = await this.pimAppService.getListProductBySkuNoRule(arrSku);

    for (const schedule of arrSchedule) {
      const { dateSchedules, regimenId } = schedule;
      const productFind = products.find((e) => e.sku === schedule.sku);
      const cartFind = getCart?.listCartSelected.find((e) => e.itemCart === schedule.sku);
      let index = 0;
      schedule.dateSchedules = _.orderBy(returnData.pushingCart, ['date'], ['asc']);
      schedule.dateSchedules = _.orderBy(returnData.pushingCartNew, ['date'], ['asc']);
      for (const dateSchedule of dateSchedules) {
        const obj = {
          itemCode: schedule.sku,
          itemName: productFind?.name || '',
          quantity: 1,
          price: cartFind?.detailCalculatorPriceInfo?.detailAttachments?.at(index)?.price || 0,
          serviceFee: cartFind?.detailCalculatorPriceInfo?.detailAttachments?.at(index)?.serviceFee || 0,
          serviceFeePercent: cartFind?.detailCalculatorPriceInfo?.detailAttachments?.at(index)?.serviceFeePercent || 0,
          priceAfterFee: cartFind?.detailCalculatorPriceInfo?.detailAttachments?.at(index)?.priceAfterFee || 0,
          totalAmount: cartFind?.detailCalculatorPriceInfo?.detailAttachments?.at(index)?.totalAmount || 0,
          unitCode: productFind?.measures?.find((e) => e.isDefault === true)?.measureUnitId || 0,
          unitName: productFind?.measures?.find((e) => e.isDefault === true)?.measureUnitName || '',
          personIdSub: cartFind?.detailCalculatorPriceInfo?.detailAttachments?.at(index)?.personIdSub || 0,
          detailCalculatorPriceInfo: cartFind?.detailCalculatorPriceInfo,
          vaccinatedNow:
            parseDateTimeZone(new Date(dateSchedule.date), '+07:00', 'YYYY-MM-DD') ===
            parseDateTimeZone(new Date(), '+07:00', 'YYYY-MM-DD')
              ? TypeVaccineNow.NOW
              : TypeVaccineNow.LATER,
          date: dateSchedule.date,
          regimenId: regimenId,
          taxonomies: productFind?.taxonomies,
          origin: schedule?.origin,
          manufactor: schedule?.manufactor,
          orderCodeCounter: dateSchedule?.orderCodeCounter,
          dosage: dateSchedule?.dosage,
          injectionRoute: dateSchedule?.injectionRoute,
          orderInjectionId: dateSchedule?.orderInjectionId,
          orderDetailAttachmentId: dateSchedule?.orderDetailAttachmentId,
          orderDetailAttachmentCode: dateSchedule?.orderDetailAttachmentCode,
          orderInjections: dateSchedule?.orderInjections,
          index: ++index,
        };

        const pushingCartNewFindIndex = returnData.pushingCartNew.findIndex(
          (e) => e.itemCode === obj.itemCode && e.vaccinatedNow === TypeVaccineNow.LATER,
        );

        if (pushingCartNewFindIndex !== -1) {
          returnData.pushingCartNew[pushingCartNewFindIndex] = {
            ...returnData.pushingCartNew[pushingCartNewFindIndex],
            orderDetailAttachmentCode:
              obj.orderDetailAttachmentCode ||
              returnData.pushingCartNew[pushingCartNewFindIndex].orderDetailAttachmentCode,
            detailCalculatorPriceInfo:
              obj.detailCalculatorPriceInfo ||
              returnData.pushingCartNew[pushingCartNewFindIndex].detailCalculatorPriceInfo ||
              null,
            quantity: returnData.pushingCartNew[pushingCartNewFindIndex].quantity + 1,
          };
        } else {
          returnData.pushingCartNew.push(obj as any);
        }

        returnData.pushingCart.push(obj as any);
      }
    }

    return returnData;
  }

  /**
   * @TODO chi tiét đơn hàng màn preview
   */

  async orderPreviewService(journeyID: string) {
    // gọi api chi tiết journey bơi journeyID
    // lấy sessionID cart
    const journey = await this.journeyCoreService.getJourneyDetail(journeyID);

    if (!journey) {
      return {};
    }

    // lấy journey step có TRANSACTION_TYPE === 1
    // 1 là session ID cart
    const journeyStepSession = journey?.journeySteps?.find(
      (step) => step.transactionType === TRANSACTION_TYPE.CART_SESSION,
    );

    // // lấy ticketCode
    const journeyStepCreatedTicket = journey?.journeySteps?.find(
      (step) => step.transactionType === TRANSACTION_TYPE.TICKET_CODE,
    );

    if (!journeyStepSession || !journeyStepCreatedTicket) {
      return {};
    }

    const ticketCode = journeyStepCreatedTicket['transactionNums'];
    // const ticketCode = '58001421191699330438563';
    // goi api get ticket lấy personID
    // ticketCode bên journey trả ra
    const ticket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });

    // call family lấy thông tin khách
    // name - giới tinh - ngày sinh
    const { name, gender, dateOfBirth, phoneNumber } = await this.familyCoreService.getPersonById(ticket?.personId);

    const sessionID = journeyStepSession['transactionNums'];
    // const sessionID = 'session:58001:1699330338333';
    // call api get cart app lấy thông tin tạm tính
    // payload sessionID

    const payloadGetCart: GetCartLibDto = {
      sessionId: sessionID,
      phoneNumber: phoneNumber,
      shopCode: journey?.shopCode,
    };
    const getCart = await this.cartAppService.getCart(payloadGetCart);

    let arrSchedule: SchedulePreview[] = [];

    ticket?.schedules.forEach((itemSchedule) => {
      const schedule = arrSchedule?.find((item) => item?.sku === itemSchedule?.sku);
      if (itemSchedule.status === IndicationStatus.WAITING_FOR_MONITORING) return;
      const dateSchedule: DateSchedule = {
        date: itemSchedule?.appointmentDate,
        dosage: itemSchedule?.dosage,
        injectionRoute: itemSchedule?.injectionRoute,
        orderInjections: itemSchedule?.orderInjections,
        orderDetailAttachmentCode: itemSchedule?.orderDetailAttachmentCode,
      };
      if (!schedule) {
        const objSchedule: SchedulePreview = {
          sku: itemSchedule?.sku,
          dateSchedules: [dateSchedule],
          manufactor: itemSchedule?.manufactor,
          orderInjections: itemSchedule?.orderInjections,
          lvcId: ticket?.lcvId,
        };
        arrSchedule = [...arrSchedule, objSchedule];
      } else {
        schedule['dateSchedules'].push(dateSchedule);
      }
    });

    let { pushingCart, pushingCartNew } = await this.confirmPushingCart({
      arrSchedule,
      ...payloadGetCart,
    });

    // gọi lịch tiêm
    const payloadSchedule: PayloadSchedule = {
      personId: ticket?.personId,
    };

    // pushingCart.forEach((item) => {
    //   if (!item?.orderDetailAttachmentCode) {
    //     item['detailCalculatorPriceInfo'] = null;
    //   }
    // });
    pushingCartNew.forEach((item) => {
      if (!item['detailCalculatorPriceInfo']) return;
      item['detailCalculatorPriceInfo'].totalBill = item['detailCalculatorPriceInfo'].totalBill * item.quantity;
    });
    pushingCart = pushingCart.filter((e) => e?.detailCalculatorPriceInfo);
    pushingCartNew = pushingCartNew.filter((e) => e?.detailCalculatorPriceInfo);

    const schedules = await this.scheduleEngineAppService.getInjectionSchedule(payloadSchedule);

    return {
      name,
      gender,
      dateOfBirth,
      calculatorPriceInfo: getCart?.calculatorPriceInfo,
      pushingCart,
      pushingCartNew,
      schedules: schedules,
    };
  }

  async getDetailsByTicketCode(ticketCode: string, payloadGetCart: GetCartLibDto) {
    const ticket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });
    if (!ticket) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_TICKET_NOT_FOUND,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_TICKET_NOT_FOUND)?.replace('{ticketCode}', ticketCode),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_TICKET_NOT_FOUND)?.replace('{ticketCode}', ticketCode),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }
    const cart = await this.cartAppService.getCart(payloadGetCart);
    if (!cart) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_CART_NOT_FOUND,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_CART_NOT_FOUND)?.replace('{sessionId}', ticket.sessionId),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_CART_NOT_FOUND)?.replace('{sessionId}', ticket.sessionId),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }
    const immediatelyVaccinesMap = new Map(
      ticket.indications.map((vaccine) => [`${vaccine.sku}-${vaccine.unitCode}-${vaccine.orderInjections}`, vaccine]),
    );
    const subsequentVaccinesMap = new Map(
      [...cart.listCartSelected].map((vaccine) => [`${vaccine.itemCart}-${vaccine.unitCode}`, vaccine]),
    );
    const subsequentVaccinesIdMap = new Map([...cart.listCartSelected].map((vaccine) => [`${vaccine.id}`, vaccine]));
    const subsequentVaccines = cart.listCartSelected.map((subsequentVaccine) => {
      let correspondingVaccine = null;
      for (const orderInjection of subsequentVaccine.orderInjections) {
        correspondingVaccine = immediatelyVaccinesMap.get(
          `${subsequentVaccine.itemCart}-${subsequentVaccine.unitCode}-${orderInjection}`,
        );
        if (correspondingVaccine) {
          break;
        }
      }
      if (correspondingVaccine) {
        subsequentVaccine.quantity -= 1;
      }
      return subsequentVaccine;
    });
    return {
      subsequentVaccines: subsequentVaccines
        .filter((item) => item.quantity !== 0 && item.itemType === ItemType.Product)
        .map(function (subsequentVaccine) {
          const itemCart = subsequentVaccinesIdMap.get(`${subsequentVaccine.id}`);
          return {
            ...subsequentVaccine,
            ...{
              ...itemCart,
              ...{
                totalPriceVaccine: (itemCart?.detailCalculatorPriceInfo?.priceAfterDiscount || 0) * itemCart.quantity,
              },
            },
          };
        }),
      immediateVaccines: ticket.indications.map(function (indication) {
        const itemCart = subsequentVaccinesMap.get(`${indication.sku}-${indication.unitCode}`);
        return {
          ...indication,
          ...{
            ...itemCart,
            ...{
              quantity: 1,
              totalPriceVaccine: itemCart?.orderInjections?.includes(indication.orderInjections)
                ? itemCart?.detailCalculatorPriceInfo?.priceAfterDiscount || 0
                : 0,
            },
          },
          orderInjections: indication.orderInjections,
        };
      }),
      calculatorPriceInfo: cart.calculatorPriceInfo,
    };
  }

  async getOrderDetailsBySessionId(payloadGetCart: GetCartLibDto) {
    const cart = await this.cartAppService.getCart(payloadGetCart);
    if (!cart) {
      const exception: IError = {
        code: ErrorCode.RSA_ECOM_CART_NOT_FOUND,
        message: ErrorCode.getError(ErrorCode.RSA_ECOM_CART_NOT_FOUND)?.replace(
          '{sessionId}',
          payloadGetCart.sessionId,
        ),
        details: ErrorCode.getError(ErrorCode.RSA_ECOM_CART_NOT_FOUND)?.replace(
          '{sessionId}',
          payloadGetCart.sessionId,
        ),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.NOT_FOUND);
    }
    const subsequentVaccinesIdMap = new Map([...cart.listCartSelected].map((vaccine) => [`${vaccine.id}`, vaccine]));
    const subsequentVaccines = cart.listCartSelected;
    return {
      subsequentVaccines: subsequentVaccines
        .filter((item) => item.quantity !== 0 && item.itemType === ItemType.Product)
        .map(function (subsequentVaccine) {
          const itemCart = subsequentVaccinesIdMap.get(`${subsequentVaccine.id}`);
          return {
            ...subsequentVaccine,
            ...{
              ...itemCart,
              ...{
                totalPriceVaccine: (itemCart?.detailCalculatorPriceInfo?.priceAfterDiscount || 0) * itemCart.quantity,
              },
            },
          };
        }),
      immediateVaccines: [],
      calculatorPriceInfo: cart.calculatorPriceInfo,
    };
  }

  protected async genericResponseWithRule(input: GetCartDto, result: GetCartLibResponse) {
    return {
      ...result,
      rule: null,
    };
  }

  async getConfirmedCart(param: GetConfirmedCartParam) {
    const { orderCode } = param;
    return this.cartAppService.getCartConfirmByOrderCode(orderCode);
  }

  async getSessionByOrderCodeTicketCode(query: GetSessionByOrderCodeTicketCodeDto) {
    const { orderCode, ticketCode } = query;

    if (orderCode) {
      const orderInfor = await this.cartAppService.getCartConfirmByOrderCode(orderCode);
      return { sessionId: orderInfor?.sessionId };
    }

    if (ticketCode) {
      const ticketInfor = await this.examinationCoreService.getManyTicket({ ticketCodes: [ticketCode] });
      return { sessionId: ticketInfor?.at(0)?.sessionId };
    }

    return { sessionId: '' };
  }

  async checkRuleAddCart(checkRuleAddCartDto: CheckRuleAddCartDto) {
    // const { items, lcvId } = checkRuleAddCartDto;

    // const arrRegimen: string[] = _.compact(
    //   _.uniq(
    //     items?.map((e) => {
    //       if (e?.orderInjections === 1) {
    //         return e?.regimenId;
    //       }
    //     }),
    //   ),
    // );
    // if (arrRegimen?.length) {
    //   const checkRuleDto: CheckRuleCreateUpdateCartDto = {
    //     DOB: null,
    //     items: arrRegimen?.map((e) => ({ regimenId: e })),
    //   };

    //   await this.cartRulesService._checkRuleCreateUpdateCart(checkRuleDto, [lcvId]);
    // }
    return true;
  }

  async updateDob(param: UpdateDobParam, payload: UpdateDobDto): Promise<UpdateDobRes> {
    const { sessionId } = param;
    return this.cartAppService.updateDob(sessionId, payload);
  }

  /**
   * lấy thông tin tiền khách hàng cần trả cho đơn trả góp
   */
  async getTotalBill(body: GetTotalBillDto): Promise<GetTotalBillRes> {
    const { orderCode, ticketCode, paymentToday } = body;

    let totalPayment = 0;
    const order = await this.oMSService.getOneOrder(orderCode);

    const totalAmountDeposit = order?.orderPaymentCreate?.find(
      (e) => e.paymentCode === order?.paymentRequestCode,
    )?.paymentAmount;

    // const paymentListES = await this.paymentGatewayService.getListRedis([order?.paymentRequestCode]);
    const paymentListES = await this.paymentGatewayService.getPaymentRedis({
      paymentCodes: [order?.paymentRequestCode],
    });
    Logger.log(`[PAYMENT] paymentListES: ${JSON.stringify(paymentListES)}`);

    const arrReferenceOfCash: Array<any> = JSONPath({
      path: `$[*].detail[*][?(@ && @.referenceId === '${ticketCode}')]`,
      json: paymentListES,
    });
    const arrReferenceElse: Array<any> = JSONPath({
      path: `$[*].detail[*][*][?(@ && @.referenceId === '${ticketCode}')]`,
      json: paymentListES,
    });
    const arrReference = [...arrReferenceOfCash, ...arrReferenceElse];
    Logger.log(`[PAYMENT] arrReference: ${JSON.stringify(arrReference)}`);

    const totalDaThanhToan = arrReference?.reduce((acc, cur) => acc + cur?.amount || 0, 0);

    if (paymentToday?.totalPaymentToday && order?.orderAttribute === 4) {
      const totalTienDataTHanhToan = paymentListES.reduce((acc, cur) => acc + getDepositDetailAmount(cur?.detail), 0);
      return {
        totalPayment: +Number(paymentToday?.totalPaymentToday).toFixed(2),
        totalAmountDeposit,
        isFinalPayment: false,
        isPaymentPartialSuccess: paymentToday?.totalPaymentToday === totalTienDataTHanhToan,
      };
    }

    if (order?.orderAttribute === 7) {
      const arrSku: string[] = JSONPath({
        json: order,
        path: '$.details[*]..itemCode',
      });
      const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({ listSku: arrSku });
      const phase1 = osrDepositAmountBySku?.find((e) => e?.phaseId === 1);
      const isPhase1 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase1?.fromDate).format('YYYY-MM-DD'),
        moment(phase1?.toDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );
      if (isPhase1 && phase1 && order?.orderStatus !== OrderStatus.FinishDeposit) {
        const totalTienDataTHanhToan = paymentListES.reduce((acc, cur) => acc + getDepositDetailAmount(cur?.detail), 0);
        let isPaymentPartialSuccess = totalTienDataTHanhToan === body?.preOrderDepositAmount?.totalDepositAmount;
        // cọc 0 đồng thì check transaction
        if (body?.preOrderDepositAmount?.totalDepositAmount === 0) {
          const arrCash = JSONPath({
            json: paymentListES,
            path: '$.[*].detail.cash[*]',
          });
          if (arrCash?.length) {
            isPaymentPartialSuccess = true;
          } else {
            isPaymentPartialSuccess = false;
          }
        }

        return {
          totalPayment: +Number(body?.preOrderDepositAmount?.totalDepositAmount).toFixed(2),
          totalAmountDeposit,
          isFinalPayment: false,
          isPaymentPartialSuccess: isPaymentPartialSuccess,
        };
      }

      const phase2 = osrDepositAmountBySku?.find((e) => e.phaseId === 2);
      const isPhase2 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
        moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );
      if (isPhase2 && phase2) {
        const totalTienDataTHanhToan = paymentListES.reduce((acc, cur) => acc + getDepositDetailAmount(cur?.detail), 0);
        return {
          totalPayment: Math.max(0, totalAmountDeposit - (order?.totalDeposit || 0)),
          totalAmountDeposit,
          isFinalPayment: true,
          isPaymentPartialSuccess: totalTienDataTHanhToan === totalAmountDeposit,
        };
      }
    }

    if (!order)
      return {
        totalPayment,
        totalAmountDeposit,
        isFinalPayment: false,
        isPaymentPartialSuccess: totalDaThanhToan === totalPayment,
      };
    // case không có ticketCode
    // lấy totalPayment từ paymentCreate
    totalPayment = totalAmountDeposit;
    if (!ticketCode) {
      return {
        totalPayment,
        totalAmountDeposit,
        isFinalPayment: false,
        isPaymentPartialSuccess: totalDaThanhToan === totalPayment,
      };
    }

    const {
      indications,
      schedules,
      orderCode: orderCodeInTicket,
      orderCodeOld,
    } = await this.examinationCoreService.getTicket({ ticketCode });
    // case có ticketCode
    if (order?.orderAttribute === 4 && orderCodeInTicket && !orderCodeOld) {
      Logger.log(`==================== ORDER ATTRIBUTE 4 ====================`);
      if (order?.details?.length) {
        const orderDetails = order?.details?.find((e) => e.itemCode === ITEM_VACCINE_SO1);
        Logger.log(`orderDetails: ${JSON.stringify(orderDetails)}`);

        // lấy danh sách Attachment in order
        const arrAttachmentCode = orderDetails?.detailAttachments?.map((e) => e.orderDetailAttachmentCode);
        Logger.log(`[ORDER] arrAttachmentCode: ${JSON.stringify(arrAttachmentCode)}`);

        const schedulesFilter = schedules?.filter((e) => arrAttachmentCode.includes(e.orderDetailAttachmentCode));
        Logger.log(`[ScheduleFilter] schedulesFilter: ${JSON.stringify(schedulesFilter)}`);

        const minDateSchedule = _.min(schedulesFilter?.map((e) => e.appointmentDate));
        Logger.log(`minDate: ${JSON.stringify(minDateSchedule)}`);

        const scheduleFilterSameDate = schedulesFilter?.filter((e) =>
          isSameDate(new Date(e.appointmentDate), new Date(minDateSchedule)),
        );
        Logger.log(`[ScheduleFilter] scheduleFilterSameDate: ${JSON.stringify(scheduleFilterSameDate)}`);

        const arrAttachmentCodeFilter = scheduleFilterSameDate?.map((e) => e.orderDetailAttachmentCode);
        Logger.log(`[ScheduleFilter] arrAttachmentCodeFilter: ${JSON.stringify(arrAttachmentCodeFilter)}`);

        const orderDetailAttachmentOrder = orderDetails?.detailAttachments?.filter((e) =>
          arrAttachmentCodeFilter.includes(e.orderDetailAttachmentCode),
        );
        Logger.log(`[orderFilterFinal] orderDetailAttachmentOrder: ${JSON.stringify(orderDetailAttachmentOrder)}`);
        const totalNeedDeposit = orderDetailAttachmentOrder?.reduce(
          (acc, cur) => acc + cur?.price - cur?.discountPromotion - cur?.discount,
          0,
        );
        // xử lý cho trường hợp đã thanh toán 1 nữa r. h thanh toán thêm
        const tienDaThanhToan1Phan = order?.orderPaymentCreate?.reduce((prev, curr) => {
          return curr?.paymentStatus === 4 ? prev + curr?.paymentAmount : prev;
        }, 0);

        totalPayment = +Math.max(totalNeedDeposit + order?.serviceFee + order?.totalDeposit - tienDaThanhToan1Phan, 0);

        return {
          totalPayment,
          totalAmountDeposit,
          isFinalPayment: false,
          isPaymentPartialSuccess: totalDaThanhToan === totalPayment,
        };
      }
      Logger.log(`==================== END ORDER ATTRIBUTE 4 ====================`);
    }
    Logger.log(`==================== ORDER ATTRIBUTE 0 OR NULL ====================`);
    if (indications?.length) {
      // lấy ra những thằng indications có trang thái là 5
      let indicationStatus5 = indications?.filter((e) => e?.status === 5);

      if (!indicationStatus5?.length) {
        const arrOrderAttachmentCode = indications?.map((e) => e?.orderDetailAttachmentCode);
        // check trong schedule có status = 5
        indicationStatus5 = schedules?.filter(
          (e) => arrOrderAttachmentCode.includes(e?.orderDetailAttachmentCode) && e?.status === 5,
        ) as any;
      }

      if (!indicationStatus5?.length) {
        return {
          totalPayment,
          totalAmountDeposit,
          isFinalPayment: false,
          isPaymentPartialSuccess: totalDaThanhToan === totalPayment,
        };
      }

      const orderDetailAttachmentCodes = indicationStatus5?.map((e) => e.orderDetailAttachmentCode);
      if (!orderDetailAttachmentCodes?.length)
        return {
          totalPayment,
          totalAmountDeposit,
          isFinalPayment: false,
          isPaymentPartialSuccess: totalDaThanhToan === totalPayment,
        };
      const arrDetailAttachments = [];

      // call oms của đơn này
      const { orders } = await this.oMSService.getOrderByOrderAttachmentCode({
        listAttachmentCode: orderDetailAttachmentCodes,
      });

      Logger.log(`orders: ${JSON.stringify(orders)}`);

      const arrPath: Array<DetailAttachment> = JSONPath({
        path: '$[*].details[*].detailAttachments[*]',
        json: orders,
      });

      Logger.log(`arrPath: ${JSON.stringify(arrPath)}`);

      const arrPathFilter = arrPath.filter((e) => orderDetailAttachmentCodes.includes(e.orderDetailAttachmentCode));
      arrDetailAttachments.push(...arrPathFilter);

      Logger.log(`arrDetailAttachments: ${JSON.stringify(arrDetailAttachments)}`);

      let totalBillInjection: number = arrDetailAttachments.reduce((acc, cur) => {
        return acc + cur?.price - cur?.discount - cur?.discountPromotion;
      }, 0);
      totalBillInjection = +totalBillInjection.toFixed(2);

      let totalOrder = 0;
      if (orderCodeOld && orderCodeInTicket && orderCodeOld !== orderCodeInTicket) {
        totalOrder = order?.totalBill;
      }
      totalPayment = totalBillInjection + totalOrder;
      // tính lại tổng tiền đã thanh toán vì Payment k muốn sync remainingAmount
      // Tính lại tổng tiền thanh vì có payment k có referenceId
      const totalDaThanhToan2 = paymentListES?.reduce((acc, cur) => acc + getDepositDetailAmount(cur?.detail), 0);
      const remainingAmount = paymentListES?.at(0)?.total - totalDaThanhToan2;

      return {
        totalPayment,
        totalAmountDeposit: paymentListES?.at(0)?.total,
        // cover cho trường hợp lần cuối thanh toán nếu đã thanh toán 1 phần r mà isPayment = true thì là lần cuối || phải tự tính
        // Hotfix ngày 21/09/2024
        isFinalPayment:
          totalDaThanhToan === totalPayment ? paymentListES?.at(0)?.isPayment : totalPayment === remainingAmount,
        isPaymentPartialSuccess: totalDaThanhToan === totalPayment,
        totalDeposit: orders?.at(0)?.totalDeposit,
      };
    }
    Logger.log(`==================== END ORDER ATTRIBUTE 0 || NULL ====================`);
    return {
      totalPayment,
      totalAmountDeposit,
      isFinalPayment: false,
      isPaymentPartialSuccess: totalDaThanhToan === totalPayment,
    };
  }

  /**
   * https://reqs.fptshop.com.vn/browse/FV-13280
   */
  async getPromotionCartTotal(payload: ReqPromotionCartTotalDto) {
    const data = await this.cartAppService.getPromotionCartTotal(payload);
    return data;
  }

  /**
   * Select promotion
   */
  async selectedPromotioCartTotal(payload: SelectedPromotionCartTotalDto) {
    const data = await this.cartAppService.selectsPromotionCartTotal(payload);
    return data;
  }

  /**
   * Thay thế quà khuyến mãi
   */
  async giftReplaceCartTotal(payload: GiftReplacePromotionCartTotalDto) {
    const data = await this.cartAppService.giftReplaceCartTotal(payload);
    return data;
  }

  async getVoucherCartTotal(queryPromotionDto: ReqPromotionCartTotalDto) {
    const kmTong = await this.cartAppService.getPromotionCartTotal(queryPromotionDto);
    const total = kmTong?.voucherGen?.vouchersValid.reduce((acc, cur) => acc + cur?.amount, 0);
    return {
      totalVoucher: total,
    };
  }
}
