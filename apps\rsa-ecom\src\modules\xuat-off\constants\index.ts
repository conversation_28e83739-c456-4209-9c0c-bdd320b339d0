import { OrderStatus } from 'vac-nest-oms';

export enum OrderTypeXuatOff {
  // Đơn kéo về
  PullOrder = 0,
  // Đơn nhập tay
  HandEntryForm = 1,
}

export enum XuatOffStatus {
  Pending = 1, // Chờ xử lý
  ApprovedByLeaderOrManager = 2, // Đã duyệt lần 1
  ApprovedByNVVH = 3, // Đã duyệt lần 2
  Rejected = 4, // Từ chối
  Cancelled = 5, // Đã hủy
}

export enum EXOSteps {
  APPROVE_1ST = 1,
  APPROVE_2ST = 2,
}

export const XUAT_OFF_STATUS: Record<XuatOffStatus, string> = {
  [XuatOffStatus.Pending]: 'Chờ xử lý',
  [XuatOffStatus.ApprovedByLeaderOrManager]: 'Đã duyệt lần 1',
  [XuatOffStatus.ApprovedByNVVH]: 'Đã duyệt lần 2',
  [XuatOffStatus.Rejected]: 'Từ chối',
  [XuatOffStatus.Cancelled]: 'Đã hủy',
};

export const XUAT_OFF_ACTION = {
  CREATE: 'tạo mới',
  UPDATE: 'cập nhật',
  APPROVE: 'duyệt',
} as const;

export type XuatOffActionType = keyof typeof XUAT_OFF_ACTION;

export const ORDER_STATUS: Record<OrderStatus, string> = {
  [OrderStatus.Confirmed]: 'Chưa hoàn tất',
  [OrderStatus.Cancel]: 'Đã hủy',
  [OrderStatus.FullReturn]: 'Trả nguyên đơn',
  [OrderStatus.Completed]: 'Hoàn tất',
  [OrderStatus.PartialReturn]: 'Trả một phần',
  [OrderStatus.FinishDeposit]: 'Hoàn tất cọc',
};

export const LIST_STATUS_VERIFY_ORDER = [OrderStatus.Completed, OrderStatus.PartialReturn];

export enum XuatOffType {
  MANUAL = 1,
  AUTO_SATELLITE = 2,
}
