import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiHeader, ApiTags } from '@nestjs/swagger';
import { ClassErrorResponse, Public } from '@shared';
import { PostVaccinationService } from './post-vaccination.service';
import { SendNotificationFinishVaccineDto } from './dto/send-notification-finish-vaccine.dto';
import { ApiKey } from '@shared/common/decorators/api-key.decorator';

@Controller({ path: 'post-vaccination', version: '1' })
@ApiTags('Post Vaccination (Sau tiêm chủng)')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
export class PostVaccinationController {
  constructor(private readonly postVaccineService: PostVaccinationService) {}

  @Public()
  @ApiHeader({
    name: 'api-key',
    required: true,
  })
  @ApiKey()
  @HttpCode(HttpStatus.OK)
  @Post('notification/finish-vaccination')
  async sendFinishVaccinationNotification(@Body() dto: SendNotificationFinishVaccineDto) {
    return this.postVaccineService.finishVaccination(dto);
  }
}
