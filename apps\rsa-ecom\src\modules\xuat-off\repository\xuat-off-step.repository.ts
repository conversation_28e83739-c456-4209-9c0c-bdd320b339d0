import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AbstractRepository } from '@shared/database/typeorm/abstract.repository';
import { EntityManager, Repository } from 'typeorm';
import { XuatOffStepInfoEntity } from '../entities';

@Injectable()
export class XuatOffStepInfoRepository extends AbstractRepository<XuatOffStepInfoEntity> {
  protected readonly logger = new Logger(XuatOffStepInfoRepository.name);
  constructor(
    @InjectRepository(XuatOffStepInfoEntity)
    private readonly repository: Repository<XuatOffStepInfoEntity>,
    private readonly entityManager: EntityManager,
  ) {
    super(repository, entityManager);
  }
}
