import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiExtraModels, ApiTags, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { RecommendationCommonService } from '@libs/modules/recommendation-common/services/recommendation-common.service';
import { ClassErrorResponse, CustomHeaders } from '@shared';
import { RecommendationSuggestDto } from '@frt/nestjs-api/dist/regimen-app-affiliate-api';

@Controller({ path: 'recommendations', version: '1' })
@ApiTags('Recommendations')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@CustomHeaders()
export class RecommendationsController {
  constructor(private readonly recommendationsService: RecommendationCommonService) {}

  @Get('customer-by-phone')
  async getCustomerByPhone(@Query('phoneNumber') phoneNumber: string) {
    return this.recommendationsService.getCustomerByPhone(phoneNumber);
  }

  @Get('advice-scripts-by-sku')
  @ApiOperation({ summary: 'Lấy kịch bản tư vấn theo SKU vaccine' })
  @ApiQuery({ name: 'sku', required: true, type: String, description: 'Mã SKU của vaccine' })
  async getAdviceScriptBySku(@Query('sku') sku: string) {
    return this.recommendationsService.getAdviceScriptBySku(sku);
  }

  @Post('suggest')
  @ApiOperation({ summary: 'Lấy gợi ý theo SKU vaccine' })
  async suggest(@Body() recommendationSuggestDto: RecommendationSuggestDto) {
    return this.recommendationsService.suggest(recommendationSuggestDto);
  }
}
