import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class ItemCreateMonitor {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  monitorId: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  isLeaveEarly?: boolean;

  @ApiProperty()
  @IsOptional()
  @Expose()
  note?: string;
}

export class CreateMonitorRes {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  ticketCode: string;

  @ApiProperty({ isArray: true, type: ItemCreateMonitor })
  @IsOptional()
  @Expose()
  arrMonitor: ItemCreateMonitor[];
}

export class CreateHealthMonitorRes {
  @ApiProperty()
  @Expose()
  isSuccess?: boolean;
}
