import { HttpStatus, Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import moment from 'moment';
import { CustomerCoreService, ValidateOtpDto } from 'vac-nest-customer-core';
import {
  CreatePersonDto,
  FamilyService as FamilyCoreService,
  IdentificationDto,
  UpdatePersonDto,
} from 'vac-nest-family';
import { IAMCoreService } from 'vac-nest-iam';
import { OsrService } from 'vac-nest-osr';
import { GetCustomerTagDto, GetCustomerTagRes, VaccineCarCoreService } from 'vac-nest-vaccine-car';
import {
  ErrorCode,
  IError,
  OrderChannels,
  SystemException,
  concurrentPromise,
  isDateBSmallerDateA,
  isSameDate,
  parseDateTimeZone,
  parsePhoneNumber,
} from '../../../../../shared/src';
import { FilesService } from '../../files/services/files.service';
import { CreateManyPersonDto, ItemImageDto, detailsDto } from '../dto';
import {
  ATTR_OTP,
  CUSTOMER_TAG_CODE_VACCINE_FPT,
  CUSTOMER_TAG_NEW_CLIENT_VACCINE,
  CUSTOMER_TAG_VACCINE_FPT,
} from '../enum';

import { CreateFamilyPackageRes } from 'vac-nest-family-package';
import { GetApplicationByChanel, PERMISSION, TENANT } from '@shared/enum/auth';
import { InsideService } from 'vac-nest-inside';

@Injectable()
export class CustomersRuleService {
  constructor(
    private readonly customerCoreService: CustomerCoreService,
    private readonly iamCoreService: IAMCoreService,
    private readonly filesService: FilesService,
    private readonly familyCoreService: FamilyCoreService,
    private readonly osrCoreService: OsrService,
    private readonly carCoreService: VaccineCarCoreService,
    private readonly insideService: InsideService,
  ) {}

  /**
   * @TODO case xác nhận sdt bằng otp
   */
  async verifyChangePhoneWithOtp(payload: { otp: string; major: string; phoneNumber: string }) {
    const payloadValidOtp: ValidateOtpDto = {
      phoneNumber: parsePhoneNumber(payload?.phoneNumber, '84'),
      otp: payload?.otp,
      fromSystem: ATTR_OTP.FromSystem,
      major: payload?.major,
    };

    const { isValid } = await this.customerCoreService.validateOtp(payloadValidOtp);
    if (!isValid) {
      const exception: IError = {
        code: ErrorCode.RSA_VERIFY_USER,
        message: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        details: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    return true;
  }

  /**
   * @TODO case xác nhận sdt bằng inside
   */
  async verifyChangePhoneWithInsideSM(payload: {
    token: string;
    shopCode: string;
    application: string;
    insideCode: string;
    otp: string;
    orderChannel?: string;
  }) {
    const { token, shopCode, application, insideCode, otp, orderChannel } = payload;
    if (!token) {
      const exception: IError = {
        code: ErrorCode.UNAUTHORIZED,
        message: ErrorCode.getError(ErrorCode.UNAUTHORIZED),
        details: ErrorCode.getError(ErrorCode.UNAUTHORIZED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.UNAUTHORIZED);
    }

    // gọi api lấy danh sách User với type là permission ==> FE check
    // check nếu tồn tại userName trong danh sách permission
    // call api verify otp

    const permissonException: IError = {
      code: ErrorCode.RSA_USER_SM,
      message: ErrorCode.getError(ErrorCode.RSA_USER_SM),
      details: ErrorCode.getError(ErrorCode.RSA_USER_SM),
      validationErrors: null,
    };

    if (orderChannel && OrderChannels.RSA_ECOM.includes(orderChannel)) {
      // check permission in rsa ecom.
      const roles = await this.iamCoreService.getUserRoles({ applicationId: application, token });
      const roleCodes = (roles || []).map((role) => role.name);
      if (roleCodes.every((role) => !['Ecom_Manager', 'Ecom_Sale_leader', 'Ecom_Sale'].includes(role))) {
        throw new SystemException(permissonException, HttpStatus.FORBIDDEN);
      }
    }
    // cmt bỏ get nhân viên shop để verify inside chỉ verify otp
    // const listUserRPF = await this.iamCoreService.getUserRPFByShopCode({
    //   tenantId: TenantId_LongChau,
    //   rpfNames: [RPF_NAME_PERMISSION.SMApprove],
    //   shopCodes: [shopCode],
    //   applicationId: application,
    //   type: TypeGetUserRPFByShopCodeEnum.PERMISSION,
    // });

    // if (!listUserRPF || !listUserRPF?.find((user) => user?.employeeCode === insideCode)) {
    //   const exception: IError = {
    //     code: ErrorCode.RSA_USER_SM,
    //     message: ErrorCode.getError(ErrorCode.RSA_USER_SM),
    //     details: ErrorCode.getError(ErrorCode.RSA_USER_SM),
    //     validationErrors: null,
    //   };
    //   throw new SystemException(exception, HttpStatus.FORBIDDEN);
    // }

    const resultVerifyOTPUser = await this.iamCoreService.verifyOtp({
      function: 'Change PhoneNumber',
      otp,
      system: 1,
      token,
      userName: insideCode,
    });

    if (!resultVerifyOTPUser) {
      const exception: IError = {
        code: ErrorCode.RSA_VERIFY_USER,
        message: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        details: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    return true;
  }

  /**
   * @TODO case xác nhận với giấy tờ tùy thân
   */
  async verifyChangePhoneWithIdentification(payload: {
    personId: string;
    arrImages: ItemImageDto[];
    modifiedByStaffCode: string;
  }) {
    const { arrImages, personId, modifiedByStaffCode } = payload;

    const arrIdentifications: IdentificationDto[] = [];
    if (arrImages?.length >= 0) {
      const keys = arrImages?.map((img) => {
        return img.key;
      });

      const { links } = await this.filesService.getS3Presign({ urls: keys });

      if (links?.length) {
        let idx = 0;
        for (const link of links) {
          arrIdentifications[idx].imageUrl = link;
          arrIdentifications[idx].identificationType = arrImages?.at(0)?.identificationType;
          arrIdentifications[idx].personId = personId;
          arrIdentifications[idx].createdBy = modifiedByStaffCode;
          ++idx;
        }
      }
    }

    // lưu hình ảnh vào db
    if (arrIdentifications?.length) {
      await this.familyCoreService.identificationCreate(arrIdentifications);
    }

    return true;
  }

  /**
   * @TODO verify email người fpt
   * @param lcvIds
   * @returns
   */
  async _verifyEmailFpter(lcvIds: string[]): Promise<{ phoneRegister: string; email: string; isFpter: boolean }> {
    // isFpter phục vụ cho việc check có phải là người fpt
    if (!lcvIds?.length)
      return {
        phoneRegister: '',
        email: '',
        isFpter: false,
      };

    // call family lấy list email
    const persons = await this.familyCoreService.getPrimaryShortPerson(lcvIds);

    const emails = persons?.filter((i) => i?.email)?.map((item) => item?.email) || [];

    if (!emails?.length)
      return {
        phoneRegister: '',
        email: '',
        isFpter: false,
      };

    // call OSR api verifyEmail để lấy ra email của người fpt
    try {
      const listEmailVerify = await this.osrCoreService.verifyEmailVaccine({ emails });
      const emailFpt = listEmailVerify?.find((email) => email?.isValid);
      if (!emailFpt)
        return {
          phoneRegister: '',
          email: '',
          isFpter: false,
        };

      const { phoneNumber, email } = persons?.find((person) => person?.email === emailFpt.email);

      return {
        email,
        phoneRegister: phoneNumber,
        isFpter: true,
      };
    } catch (error) {
      return {
        phoneRegister: '',
        email: '',
        isFpter: false,
      };
    }
  }

  // case for update customer
  async _verifyEmailUpdatePerson(email: string) {
    try {
      const listEmailVerify = await this.osrCoreService.verifyEmailVaccine({ emails: [email] });
      if (!listEmailVerify?.at(0)?.isValid) return false;

      return true;
    } catch (error) {
      return false;
    }
  }

  // verify check tag KH người fpt để hiển thị logo ngoài web
  async _verifyCustomerTag(params: GetCustomerTagDto): Promise<{
    isFpter: boolean;
    customerTags: GetCustomerTagRes[];
  }> {
    const customerTags = await this.carCoreService.getCustomerTags(params);

    const filterFptVaccine = customerTags?.filter(
      (i) => i?.tagId?.trim() === CUSTOMER_TAG_VACCINE_FPT && i?.code?.trim() === CUSTOMER_TAG_CODE_VACCINE_FPT,
    );

    if (!filterFptVaccine?.length)
      return {
        isFpter: false,
        customerTags,
      };

    return {
      isFpter: true,
      customerTags,
    };
  }

  async verifyOtpInactivePerson(payload: {
    token: string;
    insideCode: string;
    otp: string;
    orderChannel?: string; // Optional parameter
    shopCode?: string; // You'll need to provide the shopCode parameter
  }) {
    if (payload.orderChannel && OrderChannels.RSA.includes(payload.orderChannel)) {
      // Check if the orderChannel is RSA and handle accordingly
      const resultVerifyASM = await this.verifyInsideSMByPermission({
        token: payload.token,
        shopCode: payload.shopCode,
        functionName: 'Inactive person',
        insideCode: payload.insideCode,
        otp: payload.otp,
        orderChannel: payload.orderChannel,
        arrPermissionSM: [PERMISSION.ASM_PERMISSION],
      });

      return resultVerifyASM;
    }

    const resultVerifyOTPUser = await this.iamCoreService.verifyOtp({
      function: 'Inactive person',
      otp: payload.otp,
      system: 1,
      token: payload.token,
      userName: payload.insideCode,
    });

    if (!resultVerifyOTPUser) {
      const exception: IError = {
        code: ErrorCode.RSA_VERIFY_USER,
        message: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        details: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    return true;
  }

  /**
   * @TODO check rule tạo nhiều khách hàng
   */

  async _checkRuleCreateManyPerson(payload: CreateManyPersonDto) {
    // check rule dành cho đối tượng tiêm
    const result1 = this._checkRuleCreatePerson(payload);
    return this._checkRuleDuplicateGuardian(result1);
  }

  /**
   * rule check với đối tượng tiêm
   */
  private _checkRuleCreatePerson(payload: CreateManyPersonDto) {
    // check rule trùng thông tin
    // Họ và tên
    // Ngày tháng năm sinh
    // Địa chỉ 3 cấp: Xã/Phường; Quận/Huyện; Tỉnh/Thành phố
    const result = this._checkRuleDuplicateInformation(payload);

    // check rule trùng sdt đối tượng tiêm với người giám hộ
    const result2 = this._checkRuleDuplicatePhoneWithLCV(result);
    return this._checkRulePhoneNumberIdentical(result2);
  }

  /**
   * rule SDT đã tồn tại trong LCV
   */
  private _checkRulePhoneNumberIdentical(data: CreatePersonDto[]) {
    if (!data?.length) return [];

    // group theo sdt
    const groupByPhoneNumber = _.groupBy(data, 'phoneNumber');
    const duplicatePhoneNumber = _.pickBy(groupByPhoneNumber, (group) => group?.length > 1);
    const phones = (duplicatePhoneNumber && Object?.keys(duplicatePhoneNumber)) || [];
    if (phones?.length) {
      data?.map((item) => {
        if (phones?.includes(item?.phoneNumber)) {
          item.phoneNumber = '';
        }
      });
    }
    return data;
  }

  /**
   * rule trùng họ tên
   * ngày tháng năm sinh
   * địa chỉ 3 cấp: Xã/Phường; Quận/Huyện; Tỉnh/Thành phố
   */
  private _checkRuleDuplicateInformation(payload: CreateManyPersonDto) {
    const { persons } = payload;
    if (!persons?.length) return [];

    const dupItems = [];
    persons?.forEach((item, index) => {
      _.forEach(persons, (otherItem, otherIndex) => {
        if (index !== otherIndex) {
          if (
            item?.name?.trim() === otherItem?.name?.trim() &&
            parseDateTimeZone(item?.dateOfBirth, '+07:00') === parseDateTimeZone(otherItem?.dateOfBirth, '+07:00') &&
            item?.frequentlyWardCode?.trim() === otherItem?.frequentlyWardCode?.trim() &&
            item?.frequentlyDistrictCode?.trim() === otherItem?.frequentlyDistrictCode?.trim() &&
            item?.frequentlyProvinceCode?.trim() === otherItem?.frequentlyProvinceCode?.trim()
          ) {
            const message = ErrorCode.getError(ErrorCode.RSA_SYNC_TCQG_MULTIP_PROFILE_DUP)?.replace(
              `{profile}`,
              item?.name,
            );
            throw new SystemException(
              {
                code: ErrorCode.RSA_SYNC_TCQG_MULTIP_PROFILE_DUP,
                message: message,
                details: message,
                validationErrors: null,
              },
              HttpStatus.BAD_REQUEST,
            );
          }
        }
      });
    });

    if (!dupItems?.length) return persons;

    return persons?.filter(
      (item) =>
        !dupItems.some(
          (item2) =>
            item?.name?.trim() === item2?.name?.trim() &&
            parseDateTimeZone(item?.dateOfBirth, '+07:00') === parseDateTimeZone(item2?.dateOfBirth, '+07:00') &&
            item?.frequentlyWardCode?.trim() === item2?.frequentlyWardCode?.trim() &&
            item?.frequentlyDistrictCode?.trim() === item2?.frequentlyDistrictCode?.trim() &&
            item?.frequentlyProvinceCode?.trim() === item2?.frequentlyProvinceCode?.trim(),
        ),
    );
  }

  /**
   * check rule trùng người giám hộ
   */
  private _checkRuleDuplicateGuardian(persons: CreatePersonDto[]) {
    if (!persons?.length) return [];

    let personConcat = [];
    persons?.map((person) => {
      if (!person?.familyProfileDetails?.length) return;

      personConcat = _.concat(personConcat, person?.familyProfileDetails);
    });

    if (!personConcat?.length) return persons;
    const groupedByPhoneNumber = _.groupBy(personConcat, 'phoneNumber');
    const duplicates = _.pickBy(groupedByPhoneNumber, (group) => group?.length > 1);
    const duplicatePhoneNumbers = Object.keys(duplicates);

    persons?.map((person) => {
      if (!person?.familyProfileDetails?.length) return;

      personConcat = _.concat(personConcat, person?.familyProfileDetails);
    });

    persons?.map((person) => {
      if (!person?.familyProfileDetails?.length) return;
      person['familyProfileDetails'] = _.remove(
        person?.familyProfileDetails,
        (item) => !duplicatePhoneNumbers?.includes(item?.phoneNumber?.trim()),
      );
    });

    return persons;
  }

  /**
   * check rule trùng sdt của người giám hộ với đối tượng tiệm
   * cho sdt đối tượng tiêm rỗng
   * loại trừ người giám hộ có cùng sdt đối tượng tiêm
   */
  private _checkRuleDuplicatePhoneWithLCV(data: CreatePersonDto[]) {
    if (!data?.length) return [];

    data?.map((person) => {
      if (!person?.familyProfileDetails?.length) return;
      // nếu trùng person trùng số dt với người giám hộ thì giữ lại person
      person.familyProfileDetails = person?.familyProfileDetails?.map((familyProfileDetail) => {
        return {
          ...familyProfileDetail,
          phoneNumber: familyProfileDetail?.phoneNumber === person?.phoneNumber ? '' : familyProfileDetail?.phoneNumber,
        };
      });
      const familyProfileDetailsFromTCQG = person?.familyProfileDetails;
      person.familyProfileDetails = person?.familyProfileDetails?.filter(
        (x) =>
          x?.name?.trim() !== '' && // loại trừ người giám hộ không có sdt
          x?.phoneNumber?.trim() !== '', // loại trừ người giám hộ không có name
      );

      if (person?.dateOfBirth) {
        const agePerson = moment().diff(person?.dateOfBirth, 'years');
        // nếu nhỏ hơn 12 tuổi, người tiềm có số dt, và không có người giám hộ nào hết thì tự tạo 1 người giám hộ
        if (agePerson < 12 && person.phoneNumber && !person.familyProfileDetails.length) {
          const newName = _.orderBy(familyProfileDetailsFromTCQG, ['isGuardian'], ['desc']).find((f) => f.name);
          const basePerson = {
            name: newName ? newName?.name : `M_${person?.name}`,
            phoneNumber: person?.phoneNumber,
            dateOfBirth: newName?.dateOfBirth || '',
            isGuardian: true,
            identityCard: '',
            type: newName?.type ? newName?.type : 3, // khác
            gender: newName?.gender ? newName?.gender : 2, // khác,
          } as any;
          person.familyProfileDetails = [basePerson];
          person.phoneNumber = '';
        }
      }
    });

    return data;
  }

  /**
   * đánh tag khách hàng mới chủ hộ và thành viên
   */
  async createTagForCustomer(lcvId: string) {
    try {
      let isCreateTag = true;
      const tagCustomer = await this.carCoreService.getCustomerTags({ vaccinationCode: lcvId });
      if (tagCustomer?.length) {
        // check nếu lcvID đó đã đánh tag KH mới thì gán isCreateTag = false không cần phải đánh tag lại
        const tagNewCustomer = tagCustomer?.find(
          (i) => (i?.tagId).toLowerCase() === CUSTOMER_TAG_NEW_CLIENT_VACCINE.toLowerCase(),
        );
        if (tagNewCustomer) isCreateTag = false;
      }
      // đánh tag khách hàng mới khi tạo member
      if (isCreateTag) {
        await this.carCoreService.createdCustomerTags({
          vaccinationCode: lcvId,
          tagId: CUSTOMER_TAG_NEW_CLIENT_VACCINE,
        });
      }
    } catch (error) {}
  }

  /**
   * @description check rule tạo hoặc cập nhật phụ nữ mang thai
   */
  async checkCreateAndUpdatePregnancy(details: detailsDto[]) {
    if (!details?.length) return true;

    // sort by theo số lần mang thai
    details = _.orderBy(details, 'pregnancyNumber', 'asc');

    for (let i = 0; i < details.length; i++) {
      // trùng số lần mang thai
      if (details[i]?.pregnancyNumber === details[i + 1]?.pregnancyNumber) {
        const message = ErrorCode.getError(ErrorCode.RSA_DUP_PREGNANCY_NUMBER)?.replace(
          '{pregnancyNumber}',
          details[i]?.pregnancyNumber.toString(),
        );
        throw new SystemException(
          {
            code: ErrorCode.RSA_DUP_PREGNANCY_NUMBER,
            message: message,
            details: message,
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }

      // Ngày sinh/dự sinh/sảy thai không được sớm hơn ngày mang thai
      const isValidDate = isDateBSmallerDateA(details[i]?.estimateDate, details[i]?.pregnancyDate);
      if (isValidDate) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_ESTIMATE_DATE_SMALLER_PREGNANCY_DATE,
            message: ErrorCode.getError(ErrorCode.RSA_ESTIMATE_DATE_SMALLER_PREGNANCY_DATE),
            details: ErrorCode.getError(ErrorCode.RSA_ESTIMATE_DATE_SMALLER_PREGNANCY_DATE),
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    // rule trùng ngày số lần mang thai
    // rule ngày lần mang thai n+1 nhỏ hơn n
    for (let i = details.length - 1; i > 0; i--) {
      const isSame = isSameDate(details[i]?.pregnancyDate, details[i - 1]?.pregnancyDate);
      const isSameAndSmallDate = isDateBSmallerDateA(details[i]?.pregnancyDate, details[i - 1]?.pregnancyDate);
      if (isSame) {
        const message = ErrorCode.getError(ErrorCode.RSA_DUP_SAME_PREGNANCY_DATE)?.replace(
          '{pregnancyDate}',
          moment(details[i]?.pregnancyDate).utcOffset(7).format('DD-MM-YYYY'),
        );
        throw new SystemException(
          {
            code: ErrorCode.RSA_DUP_SAME_PREGNANCY_DATE,
            message: message,
            details: message,
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }

      if (isSameAndSmallDate) {
        const message = ErrorCode.getError(ErrorCode.RSA_DUP_PREGNANCY_DATE)
          ?.replace('{pregnancyNumber2}', details[i]?.pregnancyNumber.toString())
          ?.replace('{pregnancyNumber1}', details[i - 1]?.pregnancyNumber.toString());
        throw new SystemException(
          {
            code: ErrorCode.RSA_DUP_PREGNANCY_DATE,
            message: message,
            details: message,
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    return true;
  }

  /**
   * @link https://reqs.fptshop.com.vn/browse/FV-12256
   * check owner trong family-package thì không cho đổi SĐT và nhỏ hơn 18 tuổi
   */
  async checkOwnerFamilyPackage(payload?: {
    person?: UpdatePersonDto;
    resFamily?: CreateFamilyPackageRes; // family package
    isDeletePhone?: boolean; // true nếu như gọi api xóa sdt
    isUpdate?: boolean;
  }) {
    const { resFamily, isUpdate, person, isDeletePhone = false } = payload;
    let isRangeAge = false;

    if (!!isUpdate) {
      const agePerson = person?.dateOfBirth ? moment().diff(person?.dateOfBirth, 'years') : person?.from;
      isRangeAge = agePerson < 18;
    }

    // nếu lcvId === owner thì không cho update nhỏ hơn 18 tuổi
    if (resFamily?.owner === person.lcvId && (isRangeAge || isDeletePhone)) {
      const msg = isDeletePhone
        ? ErrorCode.getError(ErrorCode.RSA_OWNER_NOT_DELETE_PHONE_FAMILY_PACKAGE)
        : ErrorCode.getError(ErrorCode.RSA_OWNER_NOT_UPDATE_FAMILY_PACKAGE);
      const exception: IError = {
        code: ErrorCode.RSA_OWNER_NOT_UPDATE_OR_DELETE_PHONE_FAMILY_PACKAGE,
        message: msg,
        details: msg,
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }
  }

  /**
   * @TODO case xác nhận sdt bằng inside
   */
  async verifyInsideSMByPermission(payload: {
    token: string;
    shopCode: string;
    functionName: string;
    insideCode: string;
    otp: string;
    orderChannel?: string;
    arrPermissionSM: string[];
  }) {
    const { token, shopCode, functionName, insideCode, otp, orderChannel, arrPermissionSM } = payload;
    if (!token) {
      const exception: IError = {
        code: ErrorCode.UNAUTHORIZED,
        message: ErrorCode.getError(ErrorCode.UNAUTHORIZED),
        details: ErrorCode.getError(ErrorCode.UNAUTHORIZED),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.UNAUTHORIZED);
    }

    // get danh sách shop
    const [arrPermission, arrShop] = await concurrentPromise(
      this.iamCoreService.getUserRPFByUserName({
        applicationId: GetApplicationByChanel(orderChannel),
        tenantId: TENANT.VACCINE,
        userName: insideCode,
        type: '2',
      }),
      this.insideService.getShopByUserName({
        UserName: insideCode,
      }),
    );
    const isTRue = arrPermission?.find((item) => arrPermissionSM.includes(item));
    const isInShop = arrShop?.find((item) => item?.warehouseCodeB1 === shopCode);
    if (!isTRue) {
      const exception: IError = {
        code: ErrorCode.RSA_RULE_ASM,
        message: ErrorCode.getError(ErrorCode.RSA_RULE_ASM)?.replace('{{userName}}', insideCode),
        details: ErrorCode.getError(ErrorCode.RSA_RULE_ASM)?.replace('{{userName}}', insideCode),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }
    if (!isInShop) {
      const exception: IError = {
        code: ErrorCode.RSA_RULE_ASM_SHOP,
        message: ErrorCode.getError(ErrorCode.RSA_RULE_ASM_SHOP)?.replace('{{userName}}', insideCode),
        details: ErrorCode.getError(ErrorCode.RSA_RULE_ASM_SHOP)?.replace('{{userName}}', insideCode),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    const resultVerifyOTPUser = await this.iamCoreService.verifyOtp({
      function: functionName,
      otp,
      system: 1,
      token,
      userName: insideCode,
    });

    if (!resultVerifyOTPUser) {
      const exception: IError = {
        code: ErrorCode.RSA_VERIFY_USER,
        message: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        details: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    return true;
  }
}
