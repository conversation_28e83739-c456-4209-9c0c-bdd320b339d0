import { Module, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AuthGuard,
  BadRequestExceptionFilter,
  DatabaseConfigService,
  envValidator,
  HttpExceptionFilter,
  LoggingInterceptor,
  RedisConfigService,
  TimeoutInterceptor,
  TransformInterceptor,
  WinstonLoggerConfigService,
} from '@shared';
import { HistoryModule } from 'modules/modules/modules/history/history.module';
import { WinstonModule } from 'nest-winston';
import { ScheduleRequestsModule } from './modules/schedule-requests/schedule-requests.module';
import { CallCenterModule } from './modules/call-center/call-center.module';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { ModulesModule } from 'modules/modules';
import { EmployeeSchedulingModule } from './modules/employee-scheduling/employee-scheduling.module';
import { EmployeeModule } from './modules/employee/employee.module';
import { ProductsModule } from 'modules/modules/modules/products/products.module';
import { ShiftsModule } from './modules/shifts/shifts.module';
import { HealthzModule } from '@shared/modules/healthz/health.module';
import { GroupsModule } from './modules/groups/groups.module';
import { GroupMembersModule } from './modules/group-members/group-members.module';
import { ScreenLevel2sModule } from './modules/screen-level-2s/screen-level-2s.module';
import { FilesModule } from 'modules/modules/modules/files/files.module';
import { S3Module } from 'ict-nest-s3';
import { S3ConfigService } from '@shared/config/s3.config';
import { CustomersModule } from 'modules/modules/modules/customers/customers.module';
import { FamilyModule } from 'modules/modules/modules/family/family.module';
import { AdministrativeModule } from 'modules/modules/modules/administrative/administrative.module';
import { CartsModule } from 'modules/modules/modules/carts/carts.module';
import { SchedulesModule } from 'modules/modules/modules/schedules/schedules.module';
import { ConsultantsModule } from 'modules/modules/modules/consultants/consultants.module';
import { RegimensModule } from 'modules/modules/modules/regimens/regimens.module';
import { JourneyModule } from 'modules/modules/modules/journey/journey.module';
import { LoyaltyModule } from 'modules/modules/modules/loyalty/loyalty.module';
import { ReasonsModule } from 'modules/modules/modules/reasons/reasons.module';
import { OrdersModule as OrdersRsaModule } from 'modules/modules/modules/orders/orders.module';
import { TicketModule } from 'modules/modules/modules/ticket/ticket.module';
import { EcomOrderModule } from './modules/ecom-order/ecom-order.module';
import { PaymentModule } from './modules/payment/payment.module';
import { VouchersModule } from 'modules/modules/modules/vouchers/vouchers.module';
import { OrdersModule } from './modules/orders/orders.module';
import { InvoiceModule } from 'modules/modules/modules/invoice/module.service';
import { WorkerModule } from './modules/worker/worker.module';
import { OsrModule } from 'modules/modules/modules/osr/osr.module';
import { ScriptingModule } from './modules/scripting/scripting.module';
import { GameUtopModule } from 'modules/modules/modules/game-utop/game-utop.module';
import { InsideWrapModule } from './modules/inside-wrap/inside-wrap.module';
import { SurveysModule as LibSurveysModule } from 'modules/modules/modules/surveys/surveys.module';
import { ShopsModule } from 'modules/modules/modules/shops/shops.module';
import { EmailOtpModule } from 'modules/modules/modules/email-otp/email-otp.module';
import { AllExceptionsFilter } from '@shared/common/filters/all-exception.filter';
import { AutoGenerateModule } from './modules/auto-generate/auto-generate.module';
import { NotesModule } from 'modules/modules/modules/notes/notes.module';
import { NotificationModule } from 'modules/modules/modules/notification';
import { SurveysModule } from './modules/surveys/surveys.module';
import { AppointmentReminderModule } from './modules/appointment-reminder/appointment-reminder.module';
import { PrintCenterModule } from 'modules/modules/modules/print-center/printCenter.module';
import { DepositCancelECOMModule } from './modules/deposit-cancel/depost-cancel.module';
import { CampaignModule } from './modules/campaign/campaign.module';
import { CartPricingModule } from 'modules/modules/modules/cart-pricing/cartPricing.module';
import { MultiDoseModule as RSAMultiDoseModule } from 'modules/modules/modules/multi-dose/multi-dose.module';
import { MultiDoseModule } from './modules/multi-dose/multi-dose.module';
import { CarePlanInjectionModule } from './modules/care-plan-injection/care-plan-injection.module';
import { OrderPromissingWrapModule } from '@libs/modules/order-promissing-wrap/order-promissing-wrap.module';
import { CashbackModule } from 'modules/modules/modules/cashback/cashback.module';
import { CallCenterV2Module } from './modules/call-center-v2/call-center.module';
import { MonitorStcRsa } from './modules/monitor-stc-rsa/monitor-stc-rsa.module';
import { XuatOffModule } from './modules/xuat-off/xuat-off.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validate: envValidator,
    }),
    WinstonModule.forRootAsync({
      useClass: WinstonLoggerConfigService,
    }),
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfigService,
    }),
    RedisModule.forRootAsync({
      useClass: RedisConfigService,
    }),
    S3Module.forRootAsync({
      useClass: S3ConfigService,
    }),
    DepositCancelECOMModule,
    GroupsModule,
    GroupMembersModule,
    ScreenLevel2sModule,
    ModulesModule,
    ScheduleRequestsModule,
    CallCenterModule,
    EmployeeSchedulingModule,
    EmployeeModule,
    CustomersModule,
    FamilyModule,
    AdministrativeModule,
    CartsModule,
    CartPricingModule,
    OrdersModule,
    ProductsModule,
    SchedulesModule,
    ConsultantsModule,
    ShiftsModule,
    RegimensModule,
    JourneyModule,
    OrderPromissingWrapModule,
    TicketModule,
    ReasonsModule,
    HealthzModule.forRootAsync(['http-checker', 'typeOrm-checker']), // http-checker,typeOrm-checker, mongo-checke
    EcomOrderModule,
    FilesModule,
    S3Module,
    LoyaltyModule,
    VouchersModule,
    OrdersRsaModule,
    InvoiceModule,
    NotificationModule,
    PaymentModule,
    WorkerModule,
    OsrModule,
    EmailOtpModule,
    ScriptingModule,
    GameUtopModule,
    InsideWrapModule,
    SurveysModule,
    LibSurveysModule,
    ShopsModule,
    AutoGenerateModule,
    NotesModule,
    AppointmentReminderModule,
    PrintCenterModule,
    CampaignModule,
    MultiDoseModule,
    RSAMultiDoseModule,
    CarePlanInjectionModule,
    CashbackModule,
    HistoryModule,
    CallCenterV2Module,
    MonitorStcRsa,
    XuatOffModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useValue: new ValidationPipe({
        whitelist: false,
        skipMissingProperties: false,
        transform: true,
      }),
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TimeoutInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: BadRequestExceptionFilter,
    },
  ],
})
export class AppModule {}
