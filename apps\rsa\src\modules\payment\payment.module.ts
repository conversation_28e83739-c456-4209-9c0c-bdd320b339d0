import { Module } from '@nestjs/common';
import { PaymentGatewayModule } from 'vac-nest-payment-gateway';
import { PaymentController } from './controllers/payment.controller';
import { PaymentService } from './services/payment.service';
import { DepositCancelModule } from '../../../../../libs/modules/src/modules/deposit-cancel/depositCancel.module';
import { OMSModule } from 'vac-nest-oms';
import { OrdersModule } from '@libs/modules/orders/orders.module';

@Module({
  imports: [PaymentGatewayModule, DepositCancelModule, OMSModule, OrdersModule],
  controllers: [PaymentController],
  providers: [PaymentService],
})
export class PaymentModule {}
