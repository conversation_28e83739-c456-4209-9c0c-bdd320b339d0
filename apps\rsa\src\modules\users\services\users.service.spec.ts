import { GetEmployeeWorkingInfoRes, InsideService } from 'vac-nest-inside';
import { UsersService } from './users.service';
import { Test } from '@nestjs/testing';
import { GetEmployeeWorkingInfoDto } from '../dto';

describe('UsersService', () => {
  let service: UsersService;
  let insideService: InsideService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: InsideService,
          useValue: {
            getEmployeeWorkingInfo: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    insideService = module.get<InsideService>(InsideService);
  });

  describe('getListEmployeeWorkingInfo', () => {
    it('should return unique employees with job titles in jobCodeForSale', async () => {
      const getEmployeeWorkingInfoDto: GetEmployeeWorkingInfoDto = { shopCode: 'shop1' };
      const jobCodeForSale = ['SALE1', 'SALE2'];
      process.env.INSIDE_JOB_CODE_SALE = jobCodeForSale.join(',');

      const mockEmployees: GetEmployeeWorkingInfoRes[] = [
        { employeeCode: 'E1', employeeName: 'John Doe', jobTitleCode: 'SALE3' },
        { employeeCode: 'E1', employeeName: 'John Doe', jobTitleCode: 'SALE1' },
        { employeeCode: 'E2', employeeName: 'Jane Doe', jobTitleCode: 'SALE2' },
      ];

      jest.spyOn(insideService, 'getEmployeeWorkingInfo').mockResolvedValue(mockEmployees);

      const result = await service.getListEmployeeWorkingInfo(getEmployeeWorkingInfoDto);

      expect(result).toEqual([
        { employeeCode: 'E1', employeeName: 'John Doe', jobTitleCode: 'SALE1' },
        { employeeCode: 'E2', employeeName: 'Jane Doe', jobTitleCode: 'SALE2' },
      ]);
    });

    it('should return an empty array if no employees match job titles in jobCodeForSale', async () => {
      const getEmployeeWorkingInfoDto: GetEmployeeWorkingInfoDto = { shopCode: 'shop1' };
      process.env.INSIDE_JOB_CODE_SALE = 'SALE1,SALE2';

      const mockEmployees = [{ employeeCode: 'E1', employeeName: 'John Doe', jobTitleCode: 'OTHER' }];

      jest.spyOn(insideService, 'getEmployeeWorkingInfo').mockResolvedValue(mockEmployees);

      const result = await service.getListEmployeeWorkingInfo(getEmployeeWorkingInfoDto);

      expect(result).toEqual([]);
    });

    it('should return unique employees even if there are duplicates', async () => {
      const getEmployeeWorkingInfoDto: GetEmployeeWorkingInfoDto = { shopCode: 'shop1' };
      process.env.INSIDE_JOB_CODE_SALE = 'SALE1';

      const mockEmployees = [
        { employeeCode: 'E1', employeeName: 'John Doe', jobTitleCode: 'SALE1' },
        { employeeCode: 'E1', employeeName: 'John Doe', jobTitleCode: 'SALE1' },
      ];

      jest.spyOn(insideService, 'getEmployeeWorkingInfo').mockResolvedValue(mockEmployees);

      const result = await service.getListEmployeeWorkingInfo(getEmployeeWorkingInfoDto);

      expect(result).toEqual([{ employeeCode: 'E1', employeeName: 'John Doe', jobTitleCode: 'SALE1' }]);
    });
  });
});
