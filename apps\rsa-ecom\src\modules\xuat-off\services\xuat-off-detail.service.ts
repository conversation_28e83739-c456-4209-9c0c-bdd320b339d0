import { BadRequestException, forwardRef, Inject, Injectable, LoggerService, NotFoundException } from '@nestjs/common';
import { UserDto } from '@shared';
import * as _ from 'lodash';
import moment from 'moment';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { In, Not } from 'typeorm';
import { GetOneOrderLibResponse, OMSService } from 'vac-nest-oms';
import { XuatOffStatus } from '../constants';
import { UpdateXuatOffDetailDto, XuatOffDetailResponseDto } from '../dto';
import { CancelXuatOffDetailDto, CreateXuatOffDetailDto } from '../dto/xuat-off-detail';
import { XuatOffEntity } from '../entities';
import { XuatOffDetailEntity } from '../entities/xuat-off-detail.entity';
import { XuatOffDetailRepository, XuatOffRepository } from '../repository';
import { XuatOffStepInfoService } from './xuat-off-step.service';
import { XuatOffService } from './xuat-off.service';

@Injectable()
export class XuatOffDetailService {
  constructor(
    private xuatOffRepository: XuatOffRepository,
    private xuatOffDetailRepository: XuatOffDetailRepository,

    private readonly omsService: OMSService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER)
    private readonly logger: LoggerService,

    private xuatOffStepInfoService: XuatOffStepInfoService,
    @Inject(forwardRef(() => XuatOffService))
    private readonly xuatOffService: XuatOffService,
  ) {}

  assignXuatOffDetail(
    xuatOff: XuatOffEntity,
    payload: CreateXuatOffDetailDto,
    order: GetOneOrderLibResponse,
    user: UserDto,
  ) {
    const employees = order?.employees;
    const newDetail = new XuatOffDetailEntity({
      xoCode: xuatOff?.xoCode,
      orderCodeShop: order?.orderCode || payload?.orderCodeShop,
      orderShopCreateDate:
        moment(employees?.find((e) => e?.step === 1)?.createdDate).toDate() || payload?.orderShopCreateDate,
      orderShopCompleteDate:
        moment(employees?.find((e) => e?.step === 2)?.createdDate).toDate() || payload?.orderShopCompleteDate,
      customerPhone: order?.phone || payload?.customerPhone,
      customerName: order?.custName || payload?.customerName,
      type: payload?.type,
      status: payload?.status || XuatOffStatus.Pending,
      createdBy: user?.employee_code,
      createdByName: user?.full_name,
    });
    xuatOff.details = [...xuatOff.details, newDetail];
  }

  async create(createDto: CreateXuatOffDetailDto, user: UserDto): Promise<XuatOffDetailResponseDto> {
    try {
      const [order, xuatOff] = await Promise.all([
        this.omsService.getOneOrder(createDto?.orderCodeShop),
        this.xuatOffRepository.mustGetXuatOffByXOCode(createDto?.xoCode, { details: true }),
      ]);
      this.assignXuatOffDetail(xuatOff, createDto, order, user);
      await xuatOff.save();

      return xuatOff;
    } catch (error) {
      throw new BadRequestException(`Failed to create XuatOffDetail: ${error.message}`);
    }
  }

  async findAll(): Promise<XuatOffDetailEntity[]> {
    return await this.xuatOffDetailRepository.find({}, {});
  }

  async findOne(id: string): Promise<XuatOffDetailEntity> {
    const detail = await this.xuatOffDetailRepository.findOne({ id });
    if (!detail) {
      throw new NotFoundException(`XuatOffDetail with ID ${id} not found`);
    }
    return detail;
  }

  async update(id: string, updateDto: UpdateXuatOffDetailDto, user: UserDto): Promise<XuatOffDetailEntity> {
    const detail = await this.findOne(id);
    if (!detail?.id) {
      throw new BadRequestException('XuatOffDetail not found');
    }

    Object.assign(detail, _.omit(updateDto, ['id', 'xoCode']));
    detail.modifiedBy = user?.employee_code;
    detail.modifiedByName = user?.full_name;
    await this.xuatOffDetailRepository.save(detail);
    return detail;
  }

  async remove(id: string): Promise<void> {
    const detail = await this.findOne(id);
    await this.xuatOffDetailRepository.remove(detail);
  }

  async findByXoId(xoId: string): Promise<XuatOffDetailEntity[]> {
    return await this.xuatOffDetailRepository.find({}, { xuatOff: { id: xoId }, isDelete: false });
  }

  updateXuatOffDetail(xuatOff: XuatOffEntity, user: UserDto, details: UpdateXuatOffDetailDto[]) {
    const updateIds = details.filter((detail) => detail?.id).map((detail) => detail?.id);

    // Map existing details not in updateIds with xoId = null
    const detailsToRemove = xuatOff.details
      .filter((detail) => !updateIds.includes(detail?.id))
      .map(
        (detail) =>
          new XuatOffDetailEntity({
            ...detail,
            isDelete: true,
            deletedDate: detail?.deletedDate ? detail?.deletedDate : new Date(),
            modifiedBy: user?.employee_code,
            modifiedByName: user?.full_name,
          }),
      );

    xuatOff.details = [
      ...details.map((detail) => {
        if (detail?.id) {
          const existingDetail = xuatOff.details.find((d) => d.id === detail.id);
          return new XuatOffDetailEntity({
            ...existingDetail,
            ..._.pickBy(detail, _.identity),
            modifiedBy: user?.employee_code,
            modifiedByName: user?.full_name,
          });
        }
        return new XuatOffDetailEntity({
          ...detail,
          status: XuatOffStatus.Pending,
          xoCode: xuatOff?.xoCode,
          createdBy: user?.employee_code,
          createdByName: user?.full_name,
          modifiedBy: user?.employee_code,
          modifiedByName: user?.full_name,
        });
      }),
      ...detailsToRemove,
    ];
  }

  async findByOrderCodeShops(orderCodes: string[], excludeXoCode?: string): Promise<XuatOffDetailEntity[]> {
    const details = await this.xuatOffDetailRepository.find(
      {},
      {
        orderCodeShop: In(orderCodes),
        status: Not(In([XuatOffStatus.Cancelled, XuatOffStatus.Rejected])),
        isDelete: false,
        ...(excludeXoCode && { xuatOff: { xoCode: Not(excludeXoCode) } }),
      },
    );

    this.logger.log(
      {
        message: `Xuat off Details !== 4 or !== 5`,
        fields: {
          info: `Xuat off Details !== 4 or !== 5`,
          method: `GET`,
          url: `Xuat off Details`,
          bodyReq: JSON.stringify(details),
          queryReq: JSON.stringify(orderCodes),
          paramsReq: '{}',
          headers: '{}',
          dataRes: JSON.stringify(details),
        },
      },
      false,
    );

    return details;
  }

  async cancelXuatOffDetail(payload: CancelXuatOffDetailDto, user: UserDto): Promise<void> {
    const { orderCode } = payload;
    const xuatOffDetail = await this.xuatOffDetailRepository.findOne(
      {
        orderCodeShop: payload?.orderCode,
        status: Not(In([XuatOffStatus.Cancelled, XuatOffStatus.Rejected])),
      },
      {
        xuatOff: { details: true, steps: true },
      },
    );

    if (!xuatOffDetail) {
      this.logger.log(
        {
          message: `Xuat off Detail not found or already cancelled/rejected`,
          fields: {
            info: `Xuat off Detail not found or already cancelled/rejected`,
            method: `PATCH`,
            url: `cancel-by-order-code`,
            bodyReq: JSON.stringify({ orderCode }),
            queryReq: '{}',
            paramsReq: JSON.stringify({ orderCode }),
            headers: '{}',
            dataRes: 'null',
          },
        },
        false,
      );
      return;
    }

    const xuatOff = await xuatOffDetail.xuatOff;
    this.xuatOffStepInfoService.assignXuatOffStep(
      xuatOff,
      user,
      XuatOffStatus.Cancelled,
      payload?.notes || `Trả hàng đơn: ${orderCode}`,
    );
    const detail = xuatOff?.details?.find((xoDetail) => xoDetail.orderCodeShop === orderCode);

    detail.modifiedBy = user?.employee_code;
    detail.modifiedByName = user?.full_name;
    detail.status = XuatOffStatus.Cancelled;

    this.xuatOffService.updateFinalXuatOffStatus(xuatOff, XuatOffStatus.Cancelled);

    await xuatOff.save();

    this.logger.log(
      {
        message: `Xuat off Detail cancelled successfully`,
        fields: {
          info: `Xuat off Detail cancelled successfully`,
          method: `PATCH`,
          url: `cancel-by-order-code`,
          bodyReq: JSON.stringify({ orderCode }),
          queryReq: '{}',
          paramsReq: JSON.stringify({ orderCode }),
          headers: '{}',
          dataRes: JSON.stringify(xuatOffDetail),
        },
      },
      false,
    );
  }
}
