import { Body, Controller, HttpCode, HttpStatus, Post, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOkResponse, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FtelService } from './ftel.service';
import { FtelSignInDto, FtelSignInResponseDto } from './dto/sign-in.dto';
import { generalSchema } from '@shared';
import { CreateLogFtelRecordDto } from 'vac-nest-rsa-integration';

@ApiBearerAuth('defaultJWT')
@Controller({ path: 'ftel', version: '1' })
@ApiTags('Ftel')
@ApiExtraModels(FtelSignInDto, FtelSignInResponseDto)
export class FtelController {
  constructor(private readonly ftelService: FtelService) {}

  @Post('sign-in')
  @ApiOperation({
    summary: 'Get token Ftel',
  })
  @ApiOkResponse({
    description: 'Token Ftel',
    schema: generalSchema(FtelSignInResponseDto, 'object'),
  })
  async signIn(@Body() dto: FtelSignInDto, @Req() req: any) {
    return await this.ftelService.signIn(dto, req.headers['shop-code']);
  }

  @Post('log-record')
  @ApiOperation({
    summary: 'Save log record fci',
  })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
    type: CreateLogFtelRecordDto,
  })
  createLogFtelRecords(@Body() payload: CreateLogFtelRecordDto) {
    return this.ftelService.createLogFtelRecords(payload);
  }
}
