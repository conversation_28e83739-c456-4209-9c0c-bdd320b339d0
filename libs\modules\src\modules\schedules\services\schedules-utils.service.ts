import { Inject, Injectable } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { STATUS_CALENDAR, STATUS_PAYMENT } from '@shared/enum/payment';
import { Request } from 'express';
import * as _ from 'lodash';
import { VaccineHistoryDetailDto } from 'vac-nest-history';
import { RegimenService } from 'vac-nest-regimen';
import { SourceId } from '../constants';
import { OutPutHistoryScheduleRes } from '../dto';
import { ItemSchedule } from 'vac-nest-schedule-engine-app';
import moment from 'moment';
import { parseDateTimeZone } from '@shared';
import { ScheduleItem } from 'vac-nest-schedule';
import { EnmTicketType, ExaminationCoreService, TicketDetailRes } from 'vac-nest-examination';
import { PIMAppService } from 'vac-nest-pim-app';
import { GetBannedSkuListItem } from 'vac-nest-osr/dist/dto/get-banned-sku-list.dto';
import { IsBannedItem } from '../dto/is-sku-banned.dto';

@Injectable()
export class SchedulesUtilsService {
  shopCode: string;
  constructor(
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly regimenCoreService: RegimenService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly pimAppService: PIMAppService,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
  }

  /**
   * @TODO map field res cho created/updated/list
   */
  async mapFieldHistory(data: VaccineHistoryDetailDto[]) {
    const output = [];

    const regimenDetails: {
      regimentId?: string;
      scheduleType?: string;
      maxInjections?: number;
    }[] = [];

    const arrDiseaseGroup: {
      diseaseGroupId: string;
      diseaseGroupName: string;
      diseaseName: string;
    }[] = [];

    const arrRegimeId = data?.filter((regimen) => regimen?.regimenId)?.map((regimen) => regimen?.regimenId) || [];

    if (arrRegimeId?.length) {
      const regimens = await this.regimenCoreService.getRegimenByIds({ regimenIds: _.uniq(arrRegimeId) });

      if (regimens?.length) {
        for (const detail of regimens) {
          regimenDetails.push({
            regimentId: detail?.id,
            scheduleType: detail?.scheduleType,
            maxInjections: detail?.maxInjections,
          });
        }
      }
    }

    const diseaseGroupIds = data?.filter((item) => item?.diseaseGroupId)?.map((i) => i?.diseaseGroupId) || [];

    if (diseaseGroupIds?.length) {
      const itemsDiseaseGroup = await this.regimenCoreService.getListGroupByDisease({
        diseaseGroupIds: _.uniq(diseaseGroupIds),
      });

      if (itemsDiseaseGroup?.length) {
        for (const disease of itemsDiseaseGroup) {
          arrDiseaseGroup.push({
            diseaseGroupId: disease?.diseaseGroupId,
            diseaseGroupName: disease?.diseaseGroupName,
            diseaseName: disease?.diseaseName,
          });
        }
      }
    }

    data?.map((item) => {
      if (
        item?.isQualify === true ||
        (item?.isQualify === false && ![SourceId?.LCVId, SourceId?.TCQG].includes(item?.sourceId))
      ) {
        const itemRegimen = regimenDetails?.find((regimen) => regimen?.regimentId === item?.regimenId);
        const disease = arrDiseaseGroup?.find((i) => i?.diseaseGroupId === item?.diseaseGroupId);

        const result: OutPutHistoryScheduleRes = {
          ...item,
          injections: item?.injection,
          appointmentDate: item?.vaccinatedDate,
          statusPayment: STATUS_PAYMENT.DA_THANH_TOAN,
          statusAppointment: STATUS_CALENDAR.DA_TIEM,
          customerNote: '',
          createdByName: '',
          note: '',
          isScheduleFromHistory: true,
          isPaid: true,
          scheduleType: itemRegimen?.scheduleType || '',
          disease: item?.sku ? disease?.diseaseGroupName || '' : item?.disease, // nếu có sku thì lấy diseaseGroupName còn k thì lấy disease
        };

        // loại bỏ 2 property này ra khỏi response vì lúc ban đầu đã define injections vs appointmentDate
        delete result['injection'];
        delete result['vaccinatedDate'];

        output.push(result);
      }
    });

    /** sort vaccineDate */
    if (!output?.length)
      return {
        itemGroup: [],
        items: [],
      };

    const itemGroup = this._groupHistoryByDiseaseGroupId(output);
    const items = this._sortVaccineDateForHistory(output);

    return {
      itemGroup,
      items,
    };
  }

  private _groupHistoryByDiseaseGroupId(items: OutPutHistoryScheduleRes[]) {
    const res: { diseaseId?: string; diseaseName?: string; vaccines?: OutPutHistoryScheduleRes[] }[] = [];

    const groupByData = _.groupBy(items, (i) => (i?.diseaseGroupId ? `${i?.diseaseGroupId}` : `${i?.disease}`));

    if (!Object?.keys(groupByData)?.length) return res;

    Object?.keys(groupByData)?.forEach((diseaseGroupId) => {
      const dataByGroupDisease = groupByData?.[diseaseGroupId];
      const sortVaccineDate = dataByGroupDisease?.sort((a, b) => {
        return +new Date(b?.appointmentDate) - +new Date(a?.appointmentDate);
      });

      res.push({
        diseaseId: dataByGroupDisease?.at(0)?.['diseaseGroupId'],
        diseaseName: dataByGroupDisease?.at(0)?.['disease'],
        vaccines: sortVaccineDate,
      });
    });

    return res;
  }

  private _sortVaccineDateForHistory(items: OutPutHistoryScheduleRes[]) {
    const res = items?.sort((a, b) => {
      return +new Date(b?.appointmentDate) - +new Date(a?.appointmentDate);
    });

    return res;
  }

  calculateAndOverwriteInjection(arrayHistory) {
    // Group vaccination records by SKU
    const groupedBySKU = _.groupBy(
      arrayHistory.filter((x) => x.isQualify),
      (i) => (i?.sku ? `${i?.sku}` : `${i?.disease}`),
    );

    // Iterate over each group
    const finalResult = _.flatMap(groupedBySKU, (records) => {
      // Sort records by vaccinated date
      const sortedRecords = _.sortBy(records, 'vaccinatedDate');

      // Add new field 'injectionNew' with the index in the array
      const recordsWithIndex = sortedRecords.map((record, index) => ({
        ...record,
        injectionsOld: record.injection || record.injections,
        injections: index + 1, // Index starts from 1
        injection: index + 1, // Index starts from 1
      }));

      return recordsWithIndex;
    });
    return [...finalResult, ...arrayHistory.filter((x) => !x.isQualify)];
  }

  calculateAndOverwriteSchedule(arraySchedule) {
    // Group vaccination records by SKU
    const groupedBySKU = _.groupBy(arraySchedule, 'sku');

    // Iterate over each group
    const finalResult = _.flatMap(groupedBySKU, (records) => {
      // Sort records by vaccinated date
      const sortedRecords = _.sortBy(records, ['isHistory', 'appointmentDate']);

      // Add new field 'injectionNew' with the index in the array
      const recordsWithIndex = sortedRecords.map((record, index) => ({
        ...record,
        injectionsOld: record.injections || record.injection,
        injections: index + 1, // Index starts from 1
        injection: index + 1, // Index starts from 1
        injectionDebug: {
          injectionsOld: record.injections || record.injection,
          injections: index + 1, // Index starts from 1
          injection: index + 1, // Index starts from 1
        },
      }));

      return recordsWithIndex;
    });
    return finalResult;
  }

  async mapFieldSchedule(schedules: ScheduleItem[]) {
    if (!schedules.length) {
      return {
        items: [],
        schedulesFromShop: [],
        schedulesFromEcomNeedHandle: [],
      };
    }

    const regimenIds = schedules?.map?.((item) => item?.regimenId).filter((x) => x);

    let regimens = [];
    if (regimenIds.length) {
      regimens = await this.regimenCoreService.getRegimenByIds({ regimenIds: _.uniq(regimenIds) });
    }

    // const listTicketOpen = await this._getOpenTickets(schedules?.[0]?.lcvId);

    const arrSku = schedules?.map((item) => item?.sku);
    const { listProduct } = await this.pimAppService.getListProductBySkuNoRule(arrSku);

    const items = schedules?.map?.((item) => {
      const itemRegimen = regimens?.find((regimen) => regimen?.id === item?.regimenId);
      const isLateAppointment = this._checkLateAppointment(item?.appointmentDate);
      const itemProduct = listProduct?.find((product) => product?.sku === item?.sku);

      const res = {
        ...item,
        scheduleType: itemRegimen?.scheduleType || '',
        textLateAppointment: isLateAppointment ? 'Trễ hẹn' : '',
        statusPayment: item?.isPaid ? 1 : 0,
        statusAppointment: item?.status,
        vaccineName: item.skuName,
        isMultiDose: itemProduct?.isMultiDose || false,
      } as any;
      // if (!moment(item?.appointmentDate).isAfter(moment()) && listTicketOpen && listTicketOpen?.length) {
      //   res.listTicketOpen = listTicketOpen || [];
      //   res.message = 'Không thể chỉnh sửa lịch hẹn do khách hàng đang có phiếu khám mở';
      // }

      return res;
    });

    return {
      ...this._groupSchedule(items),
      items,
    };
  }

  /**
   * @TODO check rule hiển thị text "trễ hẹn"
   */
  public _checkLateAppointment(vaccineDate?: string | Date) {
    return (
      (vaccineDate &&
        parseDateTimeZone(new Date(vaccineDate), '+07:00', 'YYYY-MM-DD') <
          parseDateTimeZone(new Date(), '+07:00', 'YYYY-MM-DD')) ||
      false
    );
  }

  private _groupSchedule(items: ItemSchedule[]) {
    const [schedulesFromEcomNeedHandle, schedulesFromShop] = _.partition(items, (i) =>
      this._checkIsEcomScheduleNeedHandle(i),
    );

    return {
      schedulesFromShop: _.map(
        _.groupBy(schedulesFromShop, (s) => moment(s.appointmentDate).format('DD/MM/YYYY')),
        (data, date) => {
          const isLateAppointment = this._checkLateAppointment(data?.[0]?.appointmentDate);
          return { date, data, textLateAppointment: isLateAppointment ? 'Trễ hẹn' : '', isLateAppointment };
        },
      ),
      schedulesFromEcomNeedHandle: _.map(
        _.groupBy(schedulesFromEcomNeedHandle, (s) => moment(s.appointmentDate).format('DD/MM/YYYY')),
        (data, date) => {
          const isLateAppointment = this._checkLateAppointment(data?.[0]?.appointmentDate);
          return { date, data, textLateAppointment: isLateAppointment ? 'Trễ hẹn' : '', isLateAppointment };
        },
      ),
    };
  }

  async _getOpenTickets(lCVId: string): Promise<string[]> {
    // lấy danh sách ticket của lcvid
    const ticketByLcvIds = await this.examinationCoreService.searchByLcsIds({
      lCVIds: [lCVId],
    });

    const indicationStatuses = [1, 2, 3, 4, 5, 6];
    const nonIndicationStatuses = [2, 3, 4, 5, 6];

    const openTickets = ticketByLcvIds
      ?.filter(
        (item) =>
          (item?.ticketType === EnmTicketType.INDICATION && indicationStatuses.includes(item?.status)) ||
          (item?.ticketType === EnmTicketType.NOT_INDICATION && nonIndicationStatuses.includes(item?.status)),
      )
      ?.map((x) => x?.ticketCode);
    return openTickets;
  }

  private _checkIsEcomScheduleNeedHandle(schedule: ItemSchedule) {
    if (!schedule) return false;
    // 7:  FromWebRSAEcom  8: FromAppRSAEcom
    const isFromEcom = [7, 8].includes(schedule.sourceId);
    const hasOrderInfo = schedule.orderCode && schedule.orderDetailAttachmentCode;

    return isFromEcom && hasOrderInfo && !schedule?.isPaid;
  }

  isSkuBanned(listSkuBanned: GetBannedSkuListItem[], sku: string): IsBannedItem {
    const nowCompare = moment().utcOffset(7);
    const bannedFound = listSkuBanned.find((banned) => banned.itemCode === sku && banned.phaseId === 2);
    if (!bannedFound || !bannedFound?.isSellRestricted) {
      return {
        isBanned: false,
        bannedItem: null,
      };
    }

    return {
      isBanned: nowCompare.isBetween(
        moment(bannedFound.sellRestrictFromDate?.split('T')?.at(0)).utcOffset(7),
        moment(bannedFound.sellRestrictToDate?.split('T')?.at(0)).utcOffset(7),
      ),
      bannedItem: bannedFound,
    };
  }
}
