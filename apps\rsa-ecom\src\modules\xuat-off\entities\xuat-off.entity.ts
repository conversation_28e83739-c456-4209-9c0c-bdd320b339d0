import { AbstractEntity } from '@shared/database/typeorm/abtract.entity';
import { Column, CreateDateColumn, Entity, OneToMany } from 'typeorm';
import { XuatOffDetailEntity } from './xuat-off-detail.entity';
import { XuatOffStepInfoEntity } from './xuat-off-step-info.entity';
import { XuatOffType } from '../constants';

@Entity({ name: 'xuat_off' })
export class XuatOffEntity extends AbstractEntity<XuatOffEntity> {
  @Column({ name: 'xo_code', type: 'varchar', length: 100 })
  xoCode?: string;

  @Column({ name: 'order_code_ecom', type: 'varchar', length: 100, nullable: true })
  orderCodeEcom?: string;

  @Column({ name: 'order_ecom_create_date', type: 'timestamp', nullable: true })
  orderEcomCreateDate: Date;

  @Column({ name: 'order_ecom_push_date', type: 'timestamp', nullable: true })
  orderEcomPushDate: Date;

  @Column({ name: 'order_ecom_complete_date', type: 'timestamp', nullable: true })
  orderEcomCompleteDate: Date;

  @Column({ type: 'text', array: true, default: () => "'{}'" })
  images: string[];

  @Column({ type: 'text', array: true, default: () => "'{}'" })
  files: string[];

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ name: 'reason_code', type: 'varchar', length: 100, nullable: true })
  reasonCode: string;

  @Column({ name: 'reason_name', type: 'varchar', length: 100, nullable: true })
  reasonName?: string;

  @Column({ type: 'int', nullable: true })
  status?: number; // 1: Chờ xử lý - 2: Đã duyệt lần 1 - 3: Đã duyệt lần 2 - 4: Từ chối - 5: Đã hủy

  @Column({ type: 'int', nullable: true, default: () => XuatOffType.MANUAL })
  type?: number;

  @Column({ name: 'is_delete', default: false })
  isDelete?: boolean;

  @Column({ name: 'customer_phone', type: 'varchar', length: 20, nullable: true })
  customerPhone: string;

  @Column({ name: 'customer_name', type: 'varchar', length: 100, nullable: true })
  customerName: string;

  @Column({ name: 'created_by', type: 'varchar', length: 100 })
  createdBy?: string;

  @Column({ name: 'created_by_name', type: 'varchar', length: 100 })
  createdByName?: string;

  @Column({ name: 'modified_by', type: 'varchar', length: 100, nullable: true })
  modifiedBy?: string;

  @Column({ name: 'modified_by_name', type: 'varchar', length: 100, nullable: true })
  modifiedByName?: string;

  @OneToMany(() => XuatOffDetailEntity, (detail) => detail.xuatOff, {
    cascade: ['insert', 'update', 'soft-remove'],
  })
  details?: XuatOffDetailEntity[];

  @OneToMany(() => XuatOffStepInfoEntity, (step) => step.xuatOff, {
    cascade: ['insert', 'update', 'soft-remove'],
  })
  steps?: XuatOffStepInfoEntity[];

  @Column({ name: 'user_roles', type: 'text', array: true, default: () => "'{}'" })
  userRoles: string[];

  @Column({ name: 'order_ecom_created_by', type: 'varchar', length: 100, nullable: true })
  orderEcomCreatedBy?: string;

  @Column({ name: 'order_ecom_created_by_name', type: 'varchar', length: 100, nullable: true })
  orderEcomCreatedByName?: string;
}
