import { Controller, Get, HttpCode, HttpStatus, Param } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, CustomHeaders, generalSchema, Public } from '@shared';
import {
  DSMSService,
  GetDataCountryRes,
  GetDistrictRes,
  GetNationRes,
  GetProvinceRes,
  GetShopVaccineRes,
  GetWardRes,
} from 'vac-nest-dsms';
import _ from 'lodash';
import { FindAllShopRes } from '../dto';
import { AdministrativeService } from '../services/administrative.service';

@Controller({ path: 'administrative', version: '1' })
@ApiTags('Administrative')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(
  ClassResponse,
  GetDistrictRes,
  GetNationRes,
  GetProvinceRes,
  GetWardRes,
  GetShopVaccineRes,
  GetDataCountryRes,
)
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@CustomHeaders()
export class AdministrativeController {
  constructor(
    private readonly dsmsService: DSMSService,
    private readonly administrativeService: AdministrativeService,
  ) {}

  @Public()
  @Get('get-provinces')
  @ApiOperation({
    summary: 'Lấy tỉnh thành',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách tỉnh thành',
    schema: generalSchema(GetProvinceRes, 'object'),
  })
  getProvinces() {
    return this.dsmsService.getProvince();
  }

  @Public()
  @Get('get-districts/:provinceCode')
  @ApiOperation({
    summary: 'Lấy quận huyện',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(GetDistrictRes, 'object'),
  })
  getDistricts(@Param('provinceCode') provinceCode: string) {
    return this.dsmsService.getDistrict({ province: provinceCode });
  }

  @Public()
  @Get('get-wards/:districtCode')
  @ApiOperation({
    summary: 'Lấy Phường xã',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách Phường xã',
    schema: generalSchema(GetWardRes, 'object'),
  })
  getWards(@Param('districtCode') districtCode: string) {
    return this.dsmsService.getWard({ district: districtCode });
  }

  @Public()
  @Get('get-nations')
  @ApiOperation({
    summary: 'Lấy quốc gia',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(GetNationRes, 'object'),
  })
  getNations() {
    return this.dsmsService.getNation();
  }

  @Get('get-all-shop-vaccine')
  @ApiOperation({
    summary: 'Lấy cơ sở tiêm chủng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách cơ sở tiêm chủng',
    schema: generalSchema(GetNationRes, 'object'),
  })
  getAllShopVaccine() {
    const shopType = 'VACCINE';
    return this.dsmsService.getAllShopVaccine(shopType);
  }

  @Get('get-all-shop-vaccine-v2')
  @ApiOperation({
    summary: 'Lấy cơ sở tiêm chủng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách cơ sở tiêm chủng',
    schema: generalSchema(GetNationRes, 'object'),
  })
  async getAllShopVaccineV2() {
    const shopType = 'VACCINE';
    const allShop = await this.dsmsService.getAllShopVaccine(shopType);
    allShop.items = _.sortBy(allShop.items, 'code');
    return allShop;
  }

  @Get('get-country')
  @ApiOperation({
    summary: 'Lấy danh sách quốc gia',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách quốc gia',
    schema: generalSchema(GetDataCountryRes, 'object'),
  })
  getCountry() {
    return this.dsmsService.getCountry();
  }

  @Get('find-all-shop')
  @ApiOperation({
    summary: 'Lấy tất cả shop',
  })
  @ApiOkResponse({
    description: 'Lấy danh sách shop',
    schema: generalSchema(FindAllShopRes, 'array'),
  })
  findAllShop() {
    return this.administrativeService.findAllShop();
  }
}
