import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AbstractRepository } from '@shared/database/typeorm/abstract.repository';
import { EntityManager, FindOptionsRelations, Repository } from 'typeorm';
import { XuatOffEntity } from '../entities';

@Injectable()
export class XuatOffRepository extends AbstractRepository<XuatOffEntity> {
  protected readonly logger = new Logger(XuatOffRepository.name);
  constructor(
    @InjectRepository(XuatOffEntity)
    private readonly repository: Repository<XuatOffEntity>,
    private readonly entityManager: EntityManager,
  ) {
    super(repository, entityManager);
  }

  async mustGetXuatOffById(id: string, relations?: FindOptionsRelations<XuatOffEntity>): Promise<XuatOffEntity> {
    const xuatOff = await this.findOne({ id }, { ...relations });
    if (!xuatOff) {
      this.logger.error(`XuatOff with id ${id} not found`);
      throw new NotFoundException(`XuatOff with id ${id} not found`);
    }
    return xuatOff;
  }

  async mustGetXuatOffByXOCode(
    xoCode: string,
    relations?: FindOptionsRelations<XuatOffEntity>,
  ): Promise<XuatOffEntity> {
    const xuatOff = await this.findOne({ xoCode }, { ...relations });
    if (!xuatOff) {
      this.logger.error(`XuatOff with xo code ${xoCode} not found`);
      throw new NotFoundException(`XuatOff with xo code ${xoCode} not found`);
    }
    return xuatOff;
  }
}
