import { ApiProperty, OmitType } from '@nestjs/swagger';
import { CreateXuatOffStepInfoDto } from './create-xuat-off-step-info.dto';

export class UpdateXuatOffStepInfoDto extends OmitType(CreateXuatOffStepInfoDto, ['xoCode']) {}

export class XuatOffStepInfoRes {
  @ApiProperty()
  id: string;

  @ApiProperty()
  xoId: string;

  @ApiProperty()
  xoCode: string;

  @ApiProperty()
  employeeCode: string;

  @ApiProperty()
  employeeName: string;

  @ApiProperty()
  step: number;

  @ApiProperty()
  notes: string;

  @ApiProperty()
  createdDate: Date;

  @ApiProperty()
  modifiedDate: Date;
}
