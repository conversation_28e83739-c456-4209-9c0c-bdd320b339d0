import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateXuatOffStepInfoDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  xoCode: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  step: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateXuatOffStepInfoDto extends OmitType(CreateXuatOffStepInfoDto, ['xoCode']) {}

export class XuatOffStepInfoRes {
  @ApiProperty()
  id: string;

  @ApiProperty()
  xoId: string;

  @ApiProperty()
  xoCode: string;

  @ApiProperty()
  employeeCode: string;

  @ApiProperty()
  employeeName: string;

  @ApiProperty()
  step: number;

  @ApiProperty()
  notes: string;

  @ApiProperty()
  createdDate: Date;

  @ApiProperty()
  modifiedDate: Date;
}
