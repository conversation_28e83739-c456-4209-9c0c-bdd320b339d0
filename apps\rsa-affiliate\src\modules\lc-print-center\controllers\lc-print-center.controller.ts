import { Body, Controller, Get, HttpCode, HttpStatus, Post, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, generalSchema, Public } from '@shared';
import { PrintCenterRes } from 'lc-nest-print-center';
import { LcPrintCenterService } from '../services/lc-print-center.service';
import { GetPrinterResponse, GetPrinterDto } from '../dto';
import { PriceListDto } from '@libs/modules/print-center/dto';
import { VaccineBarcodeRes } from 'vac-nest-print-center';

@ApiTags('LC Print Center')
@Controller({ path: 'lc-print-center', version: '1' })
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@ApiExtraModels(GetPrinterResponse, PrintCenterRes)
export class LcPrintCenterController {
  constructor(private readonly lcPrintCenter: LcPrintCenterService) {}

  @Get('printer')
  @ApiOkResponse({
    description: 'Danh sách máy in',
    schema: generalSchema(GetPrinterResponse, 'object'),
  })
  @ApiOperation({
    summary: 'Lấy danh sách máy in của Long Châu',
  })
  @Public()
  async getPrintCenter(@Query() query: GetPrinterDto) {
    return this.lcPrintCenter.getPrinter(query);
  }

  @Post('price-list')
  @ApiOperation({
    summary: 'In bảng giá tham khảo dịch vụ vaccine',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin file pdf, image',
    schema: generalSchema(VaccineBarcodeRes, 'object'),
  })
  printPriceList(@Body() body: PriceListDto) {
    return this.lcPrintCenter.printPriceList(body);
  }
}
