import { BaseEntity } from '@shared';
import { Column, Entity } from 'typeorm';

@Entity({
  name: 'transfer_injection',
  orderBy: { created_at: 'DESC' },
})
export class TransferInjection extends BaseEntity {
  @Column()
  shopCode: string;

  @Column({
    comment: 'PersonId người nhận',
  })
  personId: string;

  @Column({
    comment: 'sku',
  })
  sku: string;

  @Column({
    comment: 'lcvid người gửi',
  })
  lcvIdOld: string;

  @Column({
    comment: 'lcvid người nhận',
  })
  lcvIdNew: string;

  @Column()
  updatedBy: string;

  @Column({
    comment: 'false: error',
  })
  isSuccess: boolean;
}
