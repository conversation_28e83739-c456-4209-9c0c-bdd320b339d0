import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { Version } from '../entities/version.entity';

export class CreateVersionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  version: string;

  @ApiProperty()
  @IsEnum([1, -1])
  @IsNotEmpty()
  status: number;

  @ApiProperty()
  @IsEnum([1, 9, 10, 11])
  @IsNotEmpty()
  type: number;

  @ApiProperty()
  @IsString()
  versionCode: string;

  @ApiProperty()
  @IsString()
  note: string;
}

export class CreateVersionResponse extends Version {}
