import { GetListPaymentMethodRes } from 'vac-nest-payment-gateway/dist/dto';
import { FilterPaymentMethodDto } from '../dto/filter-payment-method.dto';
import { ALLOWED_PAYMENT_METHOD } from '../constants';
import { PaymentMethod } from 'vac-nest-payment-portal';

export function filterPaymentMethod(
  paymentMethods: GetListPaymentMethodRes[],
  conditions: FilterPaymentMethodDto[] = ALLOWED_PAYMENT_METHOD,
) {
  const allowPaymentMethodIds = conditions.map((condition) => condition.allowedPaymentMethodId);
  const allowPaymentMethods = paymentMethods.filter((method) => {
    return allowPaymentMethodIds.includes(method.id);
  });

  return allowPaymentMethods.map((method) => {
    const allowedVendorIds =
      conditions.find((condition) => condition.allowedPaymentMethodId === method.id)?.allowedVendors || 'ALL';

    if (allowedVendorIds === 'ALL') {
      return method;
    }

    return {
      ...method,
      vendors: method.vendors.filter((vendor) => {
        return (allowedVendorIds as any).includes(vendor.id);
      }),
    };
  });
}

export const convertPaymentMethodToName = (paymentMethod: PaymentMethod) =>
  Object.keys(PaymentMethod)[Object.values(PaymentMethod).indexOf(paymentMethod)];
