import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationModule, ShortLinkModule } from 'vac-nest-notification';
import { RSAIntegrationModule } from 'vac-nest-rsa-integration';
import { SurveysController } from './controllers/surveys.controller';
import { AutoGenerateModule } from 'apps/rsa-ecom/src/modules/auto-generate/auto-generate.module';
import { NotiSurveys } from './entitys/noti-surveys.entity';
import { ElasticSearchModule } from '@shared';
import { SurveysService } from './services/surveys.service';
import { NotificationModule as LocalNotificationModule } from 'modules/modules/modules/notification';
import { InsideModule } from 'vac-nest-inside';
import { OsrModule } from 'vac-nest-osr';
import { VacStorefrontModule } from 'vac-nest-storefront';
import { FamilyModule } from 'vac-nest-family';
import { RegimenModule } from 'vac-nest-regimen';
import { SMSSurveysService } from './services/sms.service';
import { SurveysOcrController } from './controllers/surveys-ocr.controller';
import { SurveysOcrService } from './services/surveys-ocr.service';
import { EmployeeSchedulingModule } from 'apps/rsa-ecom/src/modules/employee-scheduling/employee-scheduling.module';
import { EmployeeModule } from 'apps/rsa-ecom/src/modules/employee/employee.module';
import { VacCoreAssignJobModule } from 'vac-nest-assign-job';
import { RsaLcApiModule } from '@frt/nestjs-api';
import { ScheduleD7Controller } from './controllers/schedule-d7.controller';
import { ScheduleD7Service } from './services/schedule-d7.service';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { JourneyModule as JourneyCoreModule } from 'vac-nest-journey';

@Module({
  imports: [
    TypeOrmModule.forFeature([NotiSurveys]),
    ElasticSearchModule,
    RSAIntegrationModule,
    NotificationModule,
    AutoGenerateModule,
    LocalNotificationModule,
    InsideModule,
    OsrModule,
    ShortLinkModule,
    VacStorefrontModule,
    FamilyModule,
    RegimenModule,
    EmployeeSchedulingModule,
    EmployeeModule,
    VacCoreAssignJobModule,
    RsaLcApiModule,
    ExaminationCoreModule,
    JourneyCoreModule,
  ],
  controllers: [SurveysController, SurveysOcrController, ScheduleD7Controller],
  providers: [SurveysService, SMSSurveysService, SurveysOcrService, ScheduleD7Service],
  exports: [SurveysService],
})
export class SurveysModule {}
