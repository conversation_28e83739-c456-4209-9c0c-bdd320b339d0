import { Inject, Injectable } from '@nestjs/common';
import {
  CheckTransactionVaccineMBankDto,
  CreatedQRMBankDto,
  PayloadGetListBank,
  PaymentGatewayService,
  ServiceType,
  VerifyVoucherGotitLibDto,
  VerifyVoucherUrboxLibDto,
} from 'vac-nest-payment-gateway';
import { ALLOWED_PAYMENT_METHOD } from '../constants';
import { filterPaymentMethod } from '../utils';
import { GetListPaymentPayVaccineDto } from '../dto/get-list-payment-pay-vaccine.dto';
import { concurrentPromise, OrderChannels } from '@shared';
import { RsaBeV2Service } from 'vac-nest-rsa-be-v2';
import { OMSService } from 'vac-nest-oms';
import { PayloadCancelQRMBankDto, PayloadCreatedQRMBankDto } from 'apps/rsa/src/modules/payment/dto';
import {
  PaymentFromSystem,
  PaymentInitMethod,
  PaymentPartnerType,
  PaymentQRCodeType,
  PaymentTerminalId,
} from 'apps/rsa/src/modules/payment/constants';
import { CheckBillDto } from '../dto';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';

@Injectable()
export class PaymentService {
  constructor(
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly omsService: OMSService,
    private readonly rsaBeV2Service: RsaBeV2Service,
    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  async getListPaymentMethod() {
    const paymentMethods = await this.paymentGatewayService.getListPaymentMethod({
      serviceTypeId: ServiceType.Vaccine,
    });
    const paymentMethodsConvert = paymentMethods?.map((paymentItem) => {
      if (paymentItem?.id === 17) {
        return {
          ...paymentItem,
          name: 'Thanh toán tại Trung tâm Tiêm chủng', //FV-10283
        };
      }
      return paymentItem;
    });
    return filterPaymentMethod(paymentMethodsConvert, ALLOWED_PAYMENT_METHOD);
  }

  /**
   * @TODO lấy danh sách phương thức chi tiền
   */
  async getPaymentPayVaccine(getPaymentPayVaccineDto: GetListPaymentPayVaccineDto) {
    const { orderCode, type } = getPaymentPayVaccineDto;
    let res = await this.paymentGatewayService.getListPaymentPayVaccine();

    if (!res) {
      return {
        items: [],
      };
    }
    res?.forEach((item) => {
      if (item?.id === 1) {
        item?.detail?.forEach((e) => {
          e['disabled'] = false;
        });
      }
      if (item?.id === 2) {
        item['disabled'] = false;
      }
    });
    /**
     * @TODO Nếu kênh ecom thì trả disable hết chỉ trả ck nếu có tiền không thì chỉ có
     */
    const [getTotalTien, order] = await concurrentPromise(
      this.rsaBeV2Service.getDepositCancelDetail(orderCode),
      this.omsService.getOneOrder(orderCode),
    );
    // hủy cọc
    if (type === 1 && (order?.orderAttribute === 7 || OrderChannels.RSA_AFFILIATE.includes(order?.orderChanel))) {
      if (!getTotalTien?.['totalRefund']) {
        res?.forEach((item) => {
          if (item?.id === 1) {
            item?.detail?.forEach((e) => {
              if (e?.id === 1 && !OrderChannels.RSA_AFFILIATE.includes(order?.orderChanel)) {
                e['disabled'] = true;
              }
            });
          }
          if (item?.id === 5) {
            item['disabled'] = true;
          }
        });
      }
    }
    // chi tiền
    if (type === 2) {
      res = res.filter((item) => item?.id === 1);
    }
    return {
      items: res,
    };
  }

  async checkBill(checkBillDto: CheckBillDto) {
    const { total, cash, transfer, card, voucher, cod, ewallet, point, topUp, payAtShop } = checkBillDto;

    const totalBill = +total || 0;
    const cashBill = +cash || 0;
    const transferBill = +transfer || 0;
    const cardBill = +card || 0;
    const voucherBill = +voucher || 0;
    const codBill = +cod || 0;
    const eWalletBill = +ewallet || 0;
    const rewardPoints = +point || 0;
    const topUpBill = +topUp || 0;
    const payAtShopBill = +payAtShop || 0;

    const totalPriceMethodNoChange = transferBill + voucherBill + cardBill + topUpBill;
    const totalPriceMethodRemain = totalBill - totalPriceMethodNoChange;

    //f.sell point payment
    const pointFriendSell =
      rewardPoints - Math.floor(voucherBill / 1000) > 0 ? rewardPoints - Math.floor(voucherBill / 1000) : 0;

    //price for order deposit
    const depositPrice = Number(Math.round((totalBill * 0.3) / 1000) * 1000);

    // Total price
    if (totalPriceMethodRemain < 0) {
      return {
        total: totalPriceMethodNoChange,
        remain: 0,
        voucher: voucher,
        depositPrice: depositPrice,
        depositRemain: 0,
        point: pointFriendSell,
      };
    }

    let totalCustomerPay = cashBill + transferBill + cardBill + codBill + voucherBill + topUpBill + payAtShopBill;
    let totalCustomerPayRemain = totalCustomerPay - totalBill;
    // E-wallet with voucher
    if (eWalletBill) {
      totalCustomerPay = eWalletBill + voucherBill;
      totalCustomerPayRemain = totalCustomerPay - totalBill;
    }

    const dataRes: any = {
      // Total don't calculate with voucher
      total: totalCustomerPay,
      remain: totalCustomerPayRemain,
      voucher: voucher,
      depositPrice: depositPrice,
      depositRemain: totalCustomerPayRemain,
      point: pointFriendSell,
      payAtShop: payAtShopBill,
    };

    return dataRes;
  }

  /**
   * @TODO tạo QR code MBank
   */
  async createQRCodeMBank(body: PayloadCreatedQRMBankDto) {
    const payloadGenQRCodeMBank: CreatedQRMBankDto = {
      ...body,
      shopCode: body?.shopCode,
      terminalID: 'LONGCHAU1',
      qrcodeType: 4,
      initMethod: 12,
      partnerType: 2,
      fromSystem: 'mRSA',
    };
    return await this.paymentGatewayService.createdQRMBank(payloadGenQRCodeMBank);
  }

  /**
   * @TODO hủy QR code MBank
   */
  async cancelQRCodeMBank(body: PayloadCancelQRMBankDto) {
    return await this.paymentGatewayService.cancelQRMBank(body);
  }

  /**
   * @TODO check transaction vaccine payer MBank
   */
  async checkTransactionMBank(body: CheckTransactionVaccineMBankDto) {
    return await this.paymentGatewayService.checkTransactionVaccinePayedMBank(body);
  }

  /**
   * @TODO get list bank
   */
  async getListBankVaccine(param: PayloadGetListBank) {
    const result = (await this.paymentGatewayService.getListBankVaccine(param)) || [];

    if (result?.length > 0) {
      result?.sort((a, b) => {
        if (a?.bankCode?.trim() === 'MBbank' && b?.bankCode?.trim() !== 'MBbank') return -1;
        if (a?.bankCode?.trim() !== 'MBbank' && b?.bankCode?.trim() === 'MBbank') return 1;
        return (a?.bankCode as any) - (b?.bankCode as any);
      });
    }

    return result;
  }

  /**
   * @TODO verifyVoucherGotit
   */
  async verifyVoucherGotit(param: VerifyVoucherGotitLibDto) {
    return this.paymentGatewayService.verifyVoucherGotit(param);
  }

  /**
   * @TODO verifyVoucherGotit
   */
  async verifyVoucherUrbox(param: VerifyVoucherUrboxLibDto) {
    const orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    return this.paymentGatewayService.verifyVoucherUrboxV2({ ...param, channel: orderChannel });
  }
}
