import { HttpException, HttpStatus, Injectable, Inject } from '@nestjs/common';
import { OsrService, UpdateBrandByIdDto } from 'vac-nest-osr';
import {
  SearchListCashbackByQueryDto,
  FinishCashbackReceiptDto,
  CheckCashbackDto,
  ResendCardDto,
  CreateCardDto,
  UpdateCashbackDto,
} from '../dto';
import {
  STATUS_CASHBACK,
  STATUS_CASHBACK_DISPLAY,
  CashbackOrderStatus,
  WEEKDAY,
  MESSAGE,
  ENUM_CARD_PRODUCT,
  CashbackCardType,
  MESSAGE_GET_TRANSACTION_PIS,
  GetTransactionStatusType,
  MESSAGE_NOTIFICATION,
} from '../constants';
import { ErrorCode, IError, SystemException, parsePhoneNumber } from '@shared';
import { OrderUtilsService } from '../../orders/services/order-utils.services';
import { CustomerCoreService, ValidateOtpDto } from 'vac-nest-customer-core';
import { ATTR_OTP, MAJOR_OTP } from '../../customers/enum';
import {
  PisPartnerIntegrationApiService,
  GetCardProductsPisDto,
  CreateCardPisDto,
  GetTransactionInfoPisDto,
  CardPisResponse,
} from '@frt/nestjs-api/dist/pis-partner-integration-api';
import { RsaIctApiService, GetTransactionInfoRes } from '@frt/nestjs-api/dist/rsa-ict-api';
import moment from 'moment';
import { NotificationService } from '@libs/modules/notification';

@Injectable()
export class CashbackService {
  constructor(
    private readonly osrService: OsrService,
    private readonly orderUtilsService: OrderUtilsService,
    private readonly customerCoreService: CustomerCoreService,
    private readonly pisPartnerIntegrationApiService: PisPartnerIntegrationApiService,
    private readonly rsaIctApiService: RsaIctApiService,
    private readonly notificationService: NotificationService,
  ) {}

  private handleStatusCashbackDisplay(status: STATUS_CASHBACK) {
    switch (status) {
      case STATUS_CASHBACK.NOT_CASHBACK:
        return STATUS_CASHBACK_DISPLAY.NOT_CASHBACK;
      case STATUS_CASHBACK.CANCEL:
        return STATUS_CASHBACK_DISPLAY.CANCEL;
      case STATUS_CASHBACK.COMPLETED:
        return STATUS_CASHBACK_DISPLAY.COMPLETED;
      default:
        return '';
    }
  }

  private async verifyCashbackOtp(payload: { otp: string; major: string; phoneNumber: string }) {
    const payloadValidOtp: ValidateOtpDto = {
      phoneNumber: parsePhoneNumber(payload?.phoneNumber, '84'),
      otp: payload?.otp,
      fromSystem: ATTR_OTP.FromSystem,
      major: payload?.major,
    };

    const { isValid } = await this.customerCoreService.validateOtp(payloadValidOtp);
    if (!isValid) {
      const exception: IError = {
        code: ErrorCode.RSA_VERIFY_USER,
        message: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        details: ErrorCode.getError(ErrorCode.RSA_VERIFY_USER),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    return true;
  }

  async searchListCashBackByQuery(searchListCashbackByQueryDto: SearchListCashbackByQueryDto) {
    const resCashback = await this.osrService.searchListCashBackByQuery(searchListCashbackByQueryDto);
    return resCashback;
  }

  async finishCashbackReceipt(finishCashbackReceiptDto: FinishCashbackReceiptDto) {
    // Step 0: vaidate otp
    await this.verifyCashbackOtp({
      otp: finishCashbackReceiptDto?.otp,
      major: MAJOR_OTP.Cashback || '',
      phoneNumber: finishCashbackReceiptDto?.phoneNumber,
    });
    // End validate Otp
    // Step 1: từ Số điện thoại và cashBackReceiptCode --> Find ra cashback item
    const resCashbackFromPhone = await this.osrService.getListCashBackByPhone([finishCashbackReceiptDto.phoneNumber]);
    const findCashbackItem = resCashbackFromPhone?.find(
      (item) => item?.cashBackReceiptCode === finishCashbackReceiptDto?.cashBackReceiptCode,
    );
    // 1: Chưa cashback
    // 2: Hủy
    // 4: Hoàn tất cashback
    if (!findCashbackItem) {
      throw new HttpException(
        {
          code: ErrorCode.RSA_CASHBACK_STATUS,
          message: `Không tìm thấy phiếu cashback hoặc phiếu cashback status không hợp lệ`,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    if (findCashbackItem?.status === 2 || findCashbackItem?.status === 4) {
      throw new HttpException(
        {
          code: ErrorCode.RSA_CASHBACK_STATUS,
          message: `Phiếu chi tiền đã ${this.handleStatusCashbackDisplay(
            findCashbackItem.status,
          )} nên không tiếp tục cashback. Vui lòng kiểm tra lại trạng thái phiếu`,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // Step 2: tạo paymet+ finish payment
    const { depositAll } = await this.orderUtilsService.createPaymentAndDepositAllForCashback(
      {
        orderCode: findCashbackItem?.details?.[0]?.orderCode,
        totalRefund: findCashbackItem?.cashBackAmount || 0,
        modifiedBy: finishCashbackReceiptDto?.modifiedBy || '',
        cashBackReceiptCode: finishCashbackReceiptDto?.cashBackReceiptCode,
      },
      true,
      findCashbackItem?.shopCode,
    );

    // Step 3: Call api hoàn tất giao dịch cashback
    return await this.osrService.finishCashbackReceipt({
      id: finishCashbackReceiptDto?.id,
      paymentCode: depositAll?.paymentCode,
      modifiedBy: finishCashbackReceiptDto?.modifiedBy,
      // reasonCashBack: '',
      modifiedByName: finishCashbackReceiptDto?.modifiedByName,
    });
  }

  async getCashbackByPhoneList(phoneList: string[]) {
    if (phoneList.length === 1) {
      const resCashback = await this.osrService.getListCashBackByPhone([phoneList[0]]);
      if (resCashback?.length) {
        const cardProducts = await this.getCardProducts();
        const mobileCarrier = cardProducts?.[0]?.products?.find(
          (item) => item.brandUniqueId === resCashback?.[0]?.brandUniqueId,
        );
        return [
          {
            ...resCashback?.[0],
            brandImage: mobileCarrier?.image || '',
            sku: mobileCarrier?.skus || '',
          },
        ];
      }
    }
    const resCashback = await this.osrService.getListCashBackByPhone(phoneList);
    return resCashback;
  }

  async getCardProducts() {
    const getCardProductsDto: GetCardProductsPisDto = {
      groupUniqueId: ENUM_CARD_PRODUCT.groupUniqueId,
      shopCode: ENUM_CARD_PRODUCT.shopCode,
    };
    const cardProducts = await this.pisPartnerIntegrationApiService.getCardProducts(getCardProductsDto);
    return [cardProducts]; // Wrap in array to match old API structure
  }

  async checkCashback(params: CheckCashbackDto) {
    const todayDayOfWeek = moment().utcOffset(7).day();
    const isDayInWeek = WEEKDAY?.includes(todayDayOfWeek);
    const listPhoneNumber = [params?.phoneNumber];
    const checkOrderCashback = await this.osrService.searchCashbackSuggestByPhoneNumber(listPhoneNumber);
    if (checkOrderCashback?.length) {
      const order = checkOrderCashback.find(
        (item) => item?.status === CashbackOrderStatus.DON_HANG_DANG_XU_LY && item?.isCashBack,
      );
      if (order) {
        const checkReceipt = await this.osrService.getListCashBackByPhone(listPhoneNumber);
        if (!checkReceipt?.length) {
          if (isDayInWeek) {
            return {
              id: order?.id,
              isSuggest: order?.isCashBack,
              message: MESSAGE.DAY_IN_WEEK,
              showTelcoButton: true,
              isSelectedMobileCarrier: !!order?.brandUniqueId,
            };
          } else {
            return {
              id: order?.id,
              isSuggest: order?.isCashBack,
              message: MESSAGE.DAY_IN_WEEKEND,
              showTelcoButton: false,
              isSelectedMobileCarrier: !!order?.brandUniqueId,
            };
          }
        }
      }
    }
    return {
      id: '',
      isSuggest: false,
      message: '',
      showTelcoButton: false,
      isSelectedMobileCarrier: false,
    };
  }

  async saveMobileCarrier(body: UpdateCashbackDto) {
    const cardProducts = await this.getCardProducts();
    const mobileCarrier = cardProducts?.[0]?.products?.find((item) => item.brandUniqueId === body?.brandUniqueId);
    if (body?.isUpdateReceipt) {
      await this.osrService.updateBrandInfoCashbackReceiptById({
        ...body,
        type: CashbackCardType.CASHBACK_CARD,
      });

      return [
        {
          brandUniqueId: body?.brandUniqueId,
          brandName: body?.brandName || '',
          modifiedBy: body?.modifiedBy || '',
          brandImage: mobileCarrier?.image || '',
        },
      ];
    } else {
      const updateBrand = await this.osrService.updateBrandById(body);
      if (updateBrand) {
        return [
          {
            ...updateBrand,
            brandImage: mobileCarrier?.image || '',
          },
        ];
      }
      return [];
    }
  }

  async getSuggestCashbackByPhoneNumber(body: CheckCashbackDto, data = null) {
    const cashbackSuggest = data
      ? [data]
      : await this.osrService.searchCashbackSuggestByPhoneNumber([body?.phoneNumber]);
    if (cashbackSuggest?.length) {
      const order = cashbackSuggest.find(
        (item) => item?.status === CashbackOrderStatus.DON_HANG_DANG_XU_LY && item?.isCashBack,
      );
      const cardProducts = await this.getCardProducts();
      const mobileCarrier = cardProducts?.[0]?.products?.find((item) => item?.brandUniqueId === order?.brandUniqueId);
      return [
        {
          ...order,
          brandImage: mobileCarrier?.image || '',
          sku: mobileCarrier?.skus || '',
        },
      ];
    }
    return cashbackSuggest;
  }

  private checkTransactioinStatus(status: number) {
    if (status === GetTransactionStatusType.PROCESSING) {
      return MESSAGE_GET_TRANSACTION_PIS.PROCESSING;
    }
    return MESSAGE_GET_TRANSACTION_PIS.FAILED;
  }

  async resendCard(body: ResendCardDto) {
    const receipts = await this.osrService.getListCashBackByPhone([body?.phoneNumber]);
    const receipt = receipts?.at(0);
    let network: string, serialCode: string, cardValue: string;
    if (receipt && receipt?.transactionCode && receipt?.transactionStatus === GetTransactionStatusType.COMPLETED) {
      let transaction: any;

      try {
        // Thử call PIS trước
        const transactionInfoDto: GetTransactionInfoPisDto = {
          transactioncode: receipt.transactionCode,
        };
        const transactionInfo = await this.pisPartnerIntegrationApiService.getTransactionInfo(transactionInfoDto);
        transaction = transactionInfo;
        serialCode = transaction?.detail?.cards?.at(0).pin;
      } catch (error) {
        // Chỉ fallback về ICT khi gặp mã lỗi PISPartnerIntegration:404
        if (error?.response?.code === ErrorCode.PIS_PARTNER_INTEGRATION_404) {
          const transactionInfos = await this.rsaIctApiService.getTransactionInfo({
            transactioncodes: [receipt.transactionCode],
          });
          transaction = transactionInfos?.data?.at(0);
          serialCode = transaction?.detail?.cards?.at(0).pin;
        } else {
          // Nếu là lỗi khác, throw lại error gốc
          throw error;
        }
      }

      cardValue = receipt?.cashBackAmount;
      network = receipt?.brandName;
    } else {
      const messageError = this.checkTransactioinStatus(receipt?.transactionStatus);
      if (messageError) {
        return {
          isSuccess: false,
          message: messageError,
        };
      }
    }
    // else {
    //   const cardProducts = await this.getCardProducts();
    //   const mobileCarrier = cardProducts?.[0].products?.find((item) => item.brandUniqueId === receipt?.brandUniqueId);
    //   const skuMobileCarrier = mobileCarrier?.skus?.find((item) => item.denomination === receipt.cashBackAmount);
    //   // create card
    //   const createCard = await this.rsaIctApiService.createCard({
    //     ...body,
    //     sku: skuMobileCarrier?.skuCode,
    //     quantity: 1,
    //   });

    //   const transactionInfos = await this.rsaIctApiService.getTransactionInfo({
    //     transactioncodes: [createCard?.data?.transactions?.at(0)?.transactionCode],
    //   });
    //   const transaction = transactionInfos?.data?.at(0);

    //   // save transactionCode, transactionStatus OSR
    //   await this.osrService.finishCashbackReceipt({
    //     id: receipt?.id,
    //     modifiedBy: body?.modifiedBy || '',
    //     modifiedByName: body?.modifiedByName || '',
    //     transactionCode: transaction?.transactionCode || '',
    //     transactionStatus: transaction?.transactionStatus,
    //   });

    //   const messageError = this.checkTransactioinStatus(transaction?.transactionStatus);
    //   if (messageError) {
    //     return {
    //       isSuccess: false,
    //       message: messageError,
    //     };
    //   }

    //   network = mobileCarrier?.brandName;
    //   cardValue = receipt?.cashBackAmount;
    //   serialCode = transaction?.detail?.cards?.at(0).pin;
    // }

    await this.notificationService.sendNotifications(
      [
        {
          TemplateId: process.env.NOTIFICATION_CASHBACK_CARD_TEMPLATE_ID,
          To: [body?.phoneNumber],
          Cc: [],
          Bcc: [],
          Param: {
            Title: {},
            Content: {
              network: network,
              serialCode: serialCode,
              cardValue: cardValue,
            },
            ContentFailOver: {},
            ExtraProperties: {},
          },
        },
      ],
      true,
    );

    return {
      isSuccess: true,
      message: MESSAGE_NOTIFICATION.SUCCESS,
    };
  }

  async createCard(payload: CreateCardDto) {
    const { phoneNumber, brandUniqueId, modifiedBy, modifiedByName } = payload;

    const [resCashback, cardProducts] = await Promise.all([
      this.osrService.getListCashBackByPhone([phoneNumber]),
      this.getCardProducts(),
    ]);

    const mobileCarrier = cardProducts?.[0]?.products?.find((item) => item.brandUniqueId === brandUniqueId);
    const skuMobileCarrier = mobileCarrier?.skus?.find(
      (item) => item.denomination === resCashback?.at(0)?.cashBackAmount,
    );

    const createCardDto: CreateCardPisDto = {
      phoneNumber: phoneNumber,
      sku: skuMobileCarrier?.skuCode,
      quantity: 1,
      requestId: resCashback?.at(0)?.cashBackReceiptCode || '',
    };
    const createCard = await this.pisPartnerIntegrationApiService.createCard(createCardDto);

    let isSendMessage = false;
    let pin = '';

    if (createCard?.transactionStatus === GetTransactionStatusType.PROCESSING && createCard?.transactionCode) {
      // Nếu đang processing, gọi lại API để lấy thông tin giao dịch 3 lần nếu vẫn processing
      const transactionInfos = await this.getTransactionInfoWithRetry(createCard?.transactionCode, 3);

      if (transactionInfos?.transactionStatus === GetTransactionStatusType.COMPLETED) {
        isSendMessage = true;
        pin = transactionInfos?.detail?.cards?.at(0).pin;
      }
    } else if (createCard?.transactionStatus === GetTransactionStatusType.COMPLETED) {
      isSendMessage = true;
      pin = createCard?.detail?.cards?.at(0).pin;
    }

    if (isSendMessage) {
      // Lưu thông tin giao dịch vào OSR status processing
      await this.osrService.finishCashbackReceipt({
        id: resCashback?.at(0)?.id,
        modifiedBy: modifiedBy,
        modifiedByName: modifiedByName,
        transactionCode: createCard?.transactionCode,
        transactionStatus: GetTransactionStatusType.COMPLETED,
      });

      this.notificationService.sendNotifications(
        [
          {
            TemplateId: process.env.NOTIFICATION_CASHBACK_CARD_TEMPLATE_ID,
            To: [phoneNumber],
            Cc: [],
            Bcc: [],
            Param: {
              Title: {},
              Content: {
                network: mobileCarrier?.brandName || '',
                serialCode: pin,
                cardValue: resCashback?.at(0)?.cashBackAmount,
              },
              ContentFailOver: {},
              ExtraProperties: {},
            },
          },
        ],
        true,
      );

      return {
        isSuccess: true,
        transactionCode: createCard?.transactionCode,
        transactionStatus: GetTransactionStatusType.COMPLETED,
        message: MESSAGE_NOTIFICATION.SUCCESS,
      };
    } else {
      // Lưu thông tin giao dịch vào OSR status processing
      await this.osrService.finishCashbackReceipt({
        id: resCashback?.at(0)?.id,
        modifiedBy: modifiedBy,
        modifiedByName: modifiedByName,
        transactionCode: createCard?.transactionCode || '',
        transactionStatus: GetTransactionStatusType.FAILED,
      });
      return {
        isSuccess: false,
        transactionCode: createCard?.transactionCode || '',
        transactionStatus: GetTransactionStatusType.FAILED,
        message: MESSAGE_GET_TRANSACTION_PIS.FAILED,
      };
    }
  }

  private async getTransactionInfoWithRetry(transactionCode: string, maxRetries = 3): Promise<CardPisResponse> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const transactionInfoDto: GetTransactionInfoPisDto = {
        transactioncode: transactionCode,
      };
      const transactionInfo = await this.pisPartnerIntegrationApiService.getTransactionInfo(transactionInfoDto);

      if (transactionInfo?.transactionStatus === GetTransactionStatusType.PROCESSING) {
        if (attempt < maxRetries) {
          continue;
        }
      }

      return transactionInfo;
    }
  }
}
