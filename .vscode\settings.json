{"cSpell.words": ["bằng", "biosignal", "c<PERSON><PERSON>", "ch<PERSON><PERSON>", "ch<PERSON><PERSON>", "công", "createby", "c<PERSON>ng", "cust", "đang", "danh", "d<PERSON><PERSON>", "Dtos", "d<PERSON>ng", "duoc", "<PERSON><PERSON><PERSON><PERSON>", "Ecom", "Familycv", "<PERSON><PERSON><PERSON>", "FTEL", "h<PERSON><PERSON>", "Healthz", "h<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Infor", "ispaid", "<PERSON><PERSON><PERSON><PERSON>", "KHAM", "khám", "khong", "kh<PERSON>ng", "k<PERSON><PERSON><PERSON>", "lcvid", "<PERSON><PERSON><PERSON>", "LCVID", "lia<PERSON><PERSON>", "l<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "lo<PERSON><PERSON>", "lòng", "Matetial", "ngoài", "nguoi", "nhóm", "persion", "ph<PERSON><PERSON>", "ph<PERSON>n", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Presign", "Promissing", "reget", "<PERSON><PERSON><PERSON>", "schdule", "tcqg", "TEMPLATEID", "thái", "th<PERSON>nh", "thay", "thêm", "theo", "THEO", "thông", "th<PERSON><PERSON><PERSON>", "tiem", "tiêm", "<PERSON><PERSON><PERSON><PERSON>", "Tính", "to<PERSON>", "trạng", "trên", "tr<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "từng", "typeorm", "unbook", "Unbook", "updateby", "Utop", "waitting"]}