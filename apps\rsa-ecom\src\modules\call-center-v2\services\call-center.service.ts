import { Inject, Injectable, Logger } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import { Request } from 'express';
import moment from 'moment/moment';
import { plainToInstance } from 'class-transformer';
import _ from 'lodash';

import {
  GetRecordReq,
  LogEventReq,
  BeRSAIntegrationService,
  GetRecordBySessionReq,
  ClearMisscallDto,
  RECORD_TYPE_ENUM,
  RecordBySessionRes,
} from '@frt/nestjs-api/dist/be-rsa-integration-api';
import { REDIS_CALL_CENTER_CALL_LOG, REDIS_CALL_CENTER_CALL_LOG_EXPIRED_TIME } from '../call-center.constant';
import { CALL_EVENT, CALL_TYPE, DIALOG_STATE } from '../call-center.enum';
import { CallLogDto, Filter<PERSON>allLog, GetMissCallDto, SignRequestDto, UpdateNoteCallLogDto } from '../dto';
import { CallLogService } from './call-log.service';
import { TelcoService } from '@frt/nestjs-api';
import { CallDetailRes } from '../dto/call-detail';
import { CallType, Status } from 'vac-nest-telco';

@Injectable()
export class CallCenterService {
  private readonly logger = new Logger(CallCenterService.name);

  constructor(
    private readonly integrationService: BeRSAIntegrationService,
    private readonly callLogService: CallLogService,
    @Inject(REQUEST) private readonly request: Request,
    @InjectRedis(process.env.REDIS_CONNECTION)
    private readonly redis: Redis,
    private readonly telCoService: TelcoService,
  ) {}

  private getAuthorizationHeader(headers: Record<string, string>): { Authorization: string } {
    return {
      Authorization: this.request.headers['authorization'],
      ...headers,
    };
  }

  private async handleApiCall<T>(apiCall: () => Promise<T>): Promise<T | null> {
    try {
      return await apiCall();
    } catch (error) {
      this.logger.error('info', error?.message);
      return null;
    }
  }

  async signIn(payload: SignRequestDto) {
    const rs = await this.handleApiCall(() =>
      this.integrationService.login(payload, {
        headers: this.getAuthorizationHeader({
          Tenant: payload.tenant,
        }),
      }),
    );
    return rs?.data;
  }

  async getMissCall(filter: GetMissCallDto) {
    const rs = await this.handleApiCall(() =>
      this.integrationService.getMisscall(filter, {
        headers: this.getAuthorizationHeader({
          Tenant: filter.tenant,
        }),
      }),
    );

    return rs?.data;
  }

  async clearMisscall(payload: ClearMisscallDto) {
    const rs = await this.handleApiCall(() => this.integrationService.clearMisscall(payload));

    return rs?.data;
  }

  async getRecord(payload: GetRecordReq) {
    const rs = await this.handleApiCall(() => this.integrationService.getRecord(payload));

    return rs?.data;
  }

  async getRecordBySessions(payload: GetRecordBySessionReq) {
    const rs = await this.handleApiCall(() => this.integrationService.getRecordBySessions(payload));

    return rs?.data;
  }

  async saveLogEventCall(payload: LogEventReq, config?: { headers?: Record<string, string> }) {
    const rs = await this.handleApiCall(() => this.integrationService.saveLogEventCall(payload, config));

    return rs?.data;
  }

  async createCallLog(data: CallLogDto) {
    const now = moment();
    const hashKey = `${REDIS_CALL_CENTER_CALL_LOG}_${data.sessionId}`;
    const keyEvent = `${hashKey}:${data.callEvent}:${data.fromPhone}:${data.toPhone}`;
    const sessionKeyEvent = `${data.sessionId}_${data.fromPhone}_${data.toPhone}`;

    try {
      await this.saveLogEventCall(
        {
          name: data?.nameEvent,
          type: data?.callEvent,
          senderId: data?.toPhone,
          applicationId: data?.fromPhone,
          sessionId: data?.sessionId,
          userName: data?.insideAccount,
        },
        {
          headers: this.getAuthorizationHeader({}),
        },
      );
    } catch (error) {
      this.logger.error('info', error?.message);
      return {
        callID: null,
        sessionId: data.sessionId,
        sessionKeyEvent,
        callEvent: data.callEvent,
        callType: data.callType,
        isBlockRequest: true,
      };
    }

    switch (data.callEvent) {
      case CALL_EVENT.CALL_INCOMING:
      case CALL_EVENT.CALL_OUTGOING:
      case CALL_EVENT.CALL_TRANSFER_ACCEPTED: {
        if (!(await this.redis.set(keyEvent, Date.now(), 'EX', 2, 'NX'))) {
          return {
            callID: null,
            sessionId: data.sessionId,
            sessionKeyEvent,
            callEvent: data.callEvent,
            callType: data.callType,
            isBlockRequest: true,
          };
        }

        const firstLog = await this.redis.get(sessionKeyEvent);
        if (firstLog) {
          const insertDataAnswer: CallLogDto = JSON.parse(firstLog) as CallLogDto;
          await this.callLogService.updateCallLogDBAndES(
            {
              ...insertDataAnswer,
              callEvent: data.callEvent,
              nameEvent: data.nameEvent,
              dialogState: data.dialogState,
              isFirstLog: false,
              note: data.note || '',
              callType: data.callType,
              startTime: data.startTime,
              isHidden: data.isHidden,
              listRefSessionId: data.listRefSessionId,
            },
            sessionKeyEvent,
          );
        } else {
          data.isFirstLog = true;
          const nowTime = now.toISOString();
          const createLog = await this.callLogService.createCallLog({
            ...data,
            callID: null,
            sessionId: sessionKeyEvent,
            note: data.note || '',
            duration: 0,
            endTime: nowTime,
            startTime: nowTime,
            isHidden: data.isHidden,
            listRefSessionId: data.listRefSessionId,
          });

          await this.redis.set(
            sessionKeyEvent,
            JSON.stringify(createLog),
            'EX',
            REDIS_CALL_CENTER_CALL_LOG_EXPIRED_TIME,
          );
        }
        break;
      }
      case CALL_EVENT.CALL_ANSWERED: {
        if (!(await this.redis.set(keyEvent, Date.now(), 'EX', 2, 'NX'))) {
          return {
            callID: null,
            sessionId: data.sessionId,
            sessionKeyEvent,
            callEvent: data.callEvent,
            callType: data.callType,
            isBlockRequest: true,
            listRefSessionId: data.listRefSessionId,
          };
        }

        data.startTime = now.toISOString();
        const firstLogAnswer = await this.redis.get(sessionKeyEvent);
        if (!firstLogAnswer) {
          return {
            callID: null,
            sessionId: sessionKeyEvent,
            callEvent: data.callEvent,
            keyEvent,
            firstLog: firstLogAnswer,
          };
        }

        const insertDataAnswer: CallLogDto = JSON.parse(firstLogAnswer) as CallLogDto;
        await this.callLogService.updateCallLogDBAndES(
          {
            ...insertDataAnswer,
            callEvent: data.callEvent,
            nameEvent: data.nameEvent,
            dialogState: data.dialogState,
            recordId: data.recordId || '',
            isFirstLog: false,
            note: data.note || '',
            endTime: data.endTime,
            fromPhone: data.fromPhone,
            toPhone: data.toPhone,
            callType: insertDataAnswer.callType === CALL_TYPE.TRANSFERRED ? insertDataAnswer.callType : data.callType,
            startTime: data.startTime,
            isHidden: data.isHidden,
            listRefSessionId: data.listRefSessionId,
          },
          sessionKeyEvent,
        );
        break;
      }
      case CALL_EVENT.CALL_HANGUP:
      case CALL_EVENT.CALL_REJECTED: {
        if (!(await this.redis.set(keyEvent, Date.now(), 'EX', 2, 'NX'))) {
          return {
            callID: null,
            sessionId: data.sessionId,
            sessionKeyEvent,
            callEvent: data.callEvent,
            nameEvent: data.nameEvent,
            callType: data.callType,
            isBlockRequest: true,
          };
        }

        data.endTime = now.toISOString();
        const firstLog = await this.redis.get(sessionKeyEvent);
        if (!firstLog) {
          return {
            callID: null,
            sessionId: sessionKeyEvent,
            callEvent: data.callEvent,
            keyEvent,
            firstLog: firstLog,
          };
        }

        const insertData: CallLogDto = JSON.parse(firstLog) as CallLogDto;
        const startTime = moment(insertData?.startTime);
        const duration = now.valueOf() - startTime.valueOf();

        const dataCall = {
          ...insertData,
          callID: null,
          callEvent: data.callEvent,
          nameEvent: data.nameEvent,
          dialogState: data.dialogState,
          recordId: data.recordId || '',
          isFirstLog: false,
          note: data.note || '',
          endTime: data.endTime,
          duration: Math.round(duration / 1000),
          fromPhone: data.fromPhone,
          toPhone: data.toPhone,
          callType: data.callType,
          isHidden: data.isHidden,
          listRefSessionId: data.listRefSessionId,
        };

        await this.callLogService.updateDurationCallLogDB(dataCall, sessionKeyEvent);
        break;
      }
    }
  }

  async filterCallLog(filter: FilterCallLog) {
    try {
      return this.callLogService.filter(filter);
    } catch (error) {
      this.logger.error('info', error?.message);
      return true;
    }
  }

  async updateNoteInCallLog(data: UpdateNoteCallLogDto, sessionId: string) {
    try {
      return this.callLogService.updateNoteCallLogDB(data, sessionId);
    } catch (error) {
      this.logger.error('info', error?.message);
      return true;
    }
  }

  async getDetailCallBySession(sessionId: string) {
    try {
      // Lấy sessionId gốc bằng cách tách chuỗi và lấy phần tử đầu tiên
      const originSessionId = sessionId?.split('_')?.[0];

      // Gọi service để lấy chi tiết cuộc gọi từ telco với sessionId gốc
      let callTelcoDetails = await this.telCoService.getCallDetailBySession({
        sessionId: [originSessionId],
        type: RECORD_TYPE_ENUM.ALL,
      });

      // Xác định callId: nếu có nhiều hơn 1 cuộc gọi thì lấy groupCallId, ngược lại lấy sessionId
      const callId = callTelcoDetails.length > 1 ? callTelcoDetails[0]?.groupCallId : callTelcoDetails[0]?.sessionId;

      // Đồng bộ callId với danh sách log
      await this.syncCallIdToListLog(callId, callTelcoDetails);

      // Lấy danh sách sessionId từ callTelcoDetails
      const sessionIds = callTelcoDetails?.map((call) => call.sessionId);

      // Lấy log cuộc gọi dựa trên danh sách sessionId
      const { items: callLogs } = await this.callLogService.getLogsBySessionIds(sessionIds);

      // lấy lại call từ telco để đảm bảo có đủ thông tin
      const callTelcoDetailsWithLogs = (
        await Promise.all(
          _.uniqBy(callLogs, 'sessionId')
            .filter((callLog) => callTelcoDetails.some((call) => call.sessionId !== callLog.sessionId?.split('_')[0]))
            .map((callLog) =>
              this.telCoService.getCallDetailBySession({
                sessionId: [callLog.sessionId?.split('_')[0]],
                type: RECORD_TYPE_ENUM.ALL,
              }),
            ),
        )
      ).flat();

      // Cập nhật lại callTelcoDetails với thông tin từ callLogs
      callTelcoDetails = _.uniqBy(
        [...callTelcoDetails, ...callTelcoDetailsWithLogs],
        (item) =>
          `${item.sessionId}_${item.phoneIn}_${item.phoneOut}_${item.extension}_${item.sourceExtension}_${item.callType}`,
      );

      // Xử lý chi tiết cho từng cuộc gọi
      const callDetail = callTelcoDetails.map((call) => {
        // Khởi tạo số điện thoại với giá trị mặc định
        let phoneNumberIn = call.phoneIn;
        let phoneNumberOut = call.phoneOut;

        // Xử lý cho cuộc gọi chuyển tiếp mở rộng và nội bộ
        if ([CallType.ExtendedTransfer, CallType.Internal].includes(call.callType)) {
          // Đối với cuộc gọi đến: Sử dụng sourceExtension nếu hợp lệ (>0), ngược lại dùng phoneIn
          phoneNumberIn = Number(call.sourceExtension) > 0 ? call.sourceExtension : call.phoneIn;
          // Đối với cuộc gọi đi: Sử dụng extension nếu hợp lệ (>0), ngược lại dùng phoneOut
          phoneNumberOut = Number(call.extension) > 0 ? call.extension : call.phoneOut;
        }

        // Xử lý cuộc gọi đi
        if (call.callType === CallType.CallOut) {
          // Với cuộc gọi đi: Sử dụng extension nếu hợp lệ, ngược lại dùng phoneIn
          phoneNumberIn = Number(call.extension) > 0 ? call.extension : call.phoneIn;
        }

        // Xử lý cuộc gọi đến
        if (call.callType === CallType.CallIn) {
          // Với cuộc gọi đến: Sử dụng extension nếu hợp lệ, ngược lại dùng phoneOut
          phoneNumberOut = Number(call.extension) > 0 ? call.extension : call.phoneOut;
        }

        // Tạo sessionId mới bằng cách kết hợp sessionId và số điện thoại
        const sessionConverted = `${call.sessionId}_${phoneNumberIn}_${phoneNumberOut}`;

        // Tìm log cuộc gọi tương ứng với sessionId đã chuyển đổi
        let callLog = callLogs?.find((log) => log.sessionId === sessionConverted);

        // Khởi tạo trạng thái cuộc gọi mặc định là chưa kết nối
        let telcoDialogState = DIALOG_STATE.NOT_CONNECTED;

        // Nếu trạng thái là đã xử lý hoặc chuyển tiếp thì đổi thành đã kết nối
        if ([Status.Handled, Status.Transfer].includes(+call?.status)) {
          telcoDialogState = DIALOG_STATE.CONNECTED;
        }

        // Xử lý cho trường hợp cuộc gọi không thành công
        if (!callLog && call?.status === Status.Failed) {
          callLog = callLogs?.find((log) => log.sessionId?.startsWith(call?.sessionId));
          if (callLog) {
            call.phoneIn = call?.phoneIn && Number(call?.phoneIn) > 0 ? call?.phoneIn : callLog?.fromPhone || null;
            call.phoneOut = call?.phoneOut && Number(call?.phoneOut) > 0 ? call?.phoneOut : callLog?.toPhone || null;
          }
        }

        if (callLog) {
          // Remove the callLog from callLogs to avoid duplicate processing
          const index = callLogs.indexOf(callLog);
          if (index > -1) {
            callLogs.splice(index, 1);
          }
        }

        // Trả về đối tượng chứa thông tin chi tiết cuộc gọi
        return {
          // Các thông tin cơ bản
          id: callLog?.id,
          insideCode: callLog?.insideCode,
          note: callLog?.note,
          sessionId: callLog?.sessionId || sessionConverted,
          insideAccount: callLog?.insideAccount,
          insideName: callLog?.insideName,
          dialogState: callLog?.dialogState || telcoDialogState,

          // Thông tin về cuộc gọi
          duration: call?.duration ? call?.duration / 1000 : callLog?.duration || 0,
          groupCallId: call?.groupCallId,
          callType: call?.callType,
          timeStart: call?.timeStart,
          timeEnd: call?.timeEnd,
          phoneIn: call?.phoneIn,
          phoneOut: call?.phoneOut,
          extension: call?.extension,
          sourceExtension: call?.sourceExtension,
          statusCode: call?.statusCode,

          // Thông tin ghi âm
          recordings:
            callLog?.dialogState !== DIALOG_STATE.NOT_CONNECTED
              ? call?.recordings.map((recording) => ({
                  ...recording,
                  sessionId: callLog?.sessionId || sessionConverted,
                }))
              : [],
        };
      });

      // Filter calls with ID and update them
      const callsWithId = callDetail?.filter((call) => call.id);
      if (callsWithId?.length > 0) {
        await Promise.all(
          callsWithId?.map(async (call) => {
            if (call?.timeStart && call?.timeEnd && call?.duration) {
              await this.callLogService.updateCallLogDBAndES(
                {
                  startTime: new Date(call.timeStart).toISOString(),
                  endTime: new Date(call.timeEnd).toISOString(),
                  duration: call.duration,
                },
                call.sessionId,
              );
            }
          }),
        );
      }

      callLogs?.forEach((callLog) => {
        const mappedCall = {
          // Thông tin cơ bản
          id: callLog?.id,
          insideCode: callLog?.insideCode,
          note: callLog?.note,
          sessionId: callLog?.sessionId,
          insideAccount: callLog?.insideAccount,
          insideName: callLog?.insideName,
          dialogState: callLog?.dialogState,
          duration: callLog?.duration,

          // Thông tin về cuộc gọi
          groupCallId: callLog?.callID,
          callType: callLog?.callType,
          timeStart:
            typeof callLog?.startTime === 'string' ? new Date(callLog.startTime).getTime() : callLog?.startTime,
          timeEnd: typeof callLog?.endTime === 'string' ? new Date(callLog.endTime).getTime() : callLog?.endTime,
          phoneIn: callLog?.fromPhone,
          phoneOut: callLog?.toPhone,
          // Thông tin ghi âm
          recordings:
            callDetail.find((call) => call.sessionId.split('_')[0] === callLog.sessionId?.split('_')[0])?.recordings ??
            [],
        };

        callDetail.push(mappedCall);
      });

      const callDetailSort = callDetail?.sort((prev, curr) => curr?.timeStart - prev?.timeStart);
      const callDetailInstance = plainToInstance(CallDetailRes, callDetail);

      const recordingsMap = new Map();
      callDetailSort?.forEach((call) => {
        call.recordings?.forEach((recording) => {
          if (!recordingsMap.has(recording.url)) {
            recordingsMap.set(recording.url, recording);
          }
        });
      });

      const sortedRecordings = Array.from(recordingsMap.values()).sort((a, b) => a.startedStamp - b.startedStamp);

      return {
        callData: callDetailInstance,
        extraData: {
          recordings: sortedRecordings,
        },
      };
    } catch (error) {
      this.logger.error('info', error?.message);
    }
  }

  private async syncCallIdToListLog(callId: string, listLog: RecordBySessionRes[]) {
    try {
      await Promise.all(listLog.map((log) => this.callLogService.updateCallIdToLogBySessionId(callId, log.sessionId)));
    } catch (error) {
      this.logger.error('info', error?.message);
      return;
    }
  }
}
