import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseInterceptors } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { BlockAdjustOrderInterceptor, ClassErrorResponse, CustomHeaders, generalSchema } from '@shared';
import {
  CancelOrderDto,
  ContinueBuyingRes,
  CreateOrderResponse,
  EmployeeInfoContinueBuyingDto,
  PlaceOrderDto,
  UpdateStatusOrderManyDto,
  UpdateStatusOrderResponse,
} from '../dto';
import { OrdersFamilyPackageService } from '../services/orders-family-package.service';
import { CancelOrderLibResponse } from 'vac-nest-oms';
import { GetOrdersByLcvidsAttributesConvertDto } from '../dto/get-list-order-by-lcvids-attributes.dto';

@Controller({ path: 'orders-family-package', version: '1' })
@ApiTags('Order Family package')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(CreateOrderResponse, UpdateStatusOrderResponse)
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@CustomHeaders()
export class OrdersFamilyPackageController {
  constructor(private readonly ordersFamilyPackageService: OrdersFamilyPackageService) {}

  @Post('place')
  @ApiOperation({
    summary: 'Tạo đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(CreateOrderResponse, 'object'),
  })
  placeOrder(@Body() placeOrder: PlaceOrderDto) {
    return this.ordersFamilyPackageService.placeOrder(placeOrder);
  }

  @Put('place')
  @ApiOperation({
    summary: 'Cập nhật đơn hàng dành cho lễ tân',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(CreateOrderResponse, 'object'),
  })
  updatePlaceOrder(@Body() placeOrderDto: PlaceOrderDto) {
    return this.ordersFamilyPackageService.updatePlaceOrder(placeOrderDto);
  }

  @Post('update-status-order-deposit-many')
  @ApiOperation({
    summary: 'Cập nhật trạng thái đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Cập nhật trạng thái đơn hàng',
    schema: generalSchema(UpdateStatusOrderResponse, 'object'),
  })
  updatedStatusOrderDepositMany(@Body() body: UpdateStatusOrderManyDto) {
    return this.ordersFamilyPackageService.updateStatusOrderMany(body);
  }

  @Post('cancel-order')
  @ApiOperation({
    summary: 'Hủy đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả hủy đơn hàng',
    schema: generalSchema(CancelOrderLibResponse, 'object'),
  })
  cancelOrder(@Body() body: CancelOrderDto) {
    return this.ordersFamilyPackageService.cancelOrder(body);
  }

  @Get('continue-buying/:orderCode')
  @ApiOperation({
    summary: 'Tiếp tục đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả hủy đơn hàng',
    schema: generalSchema(ContinueBuyingRes, 'object'),
  })
  @UseInterceptors(BlockAdjustOrderInterceptor)
  continueBuying(@Param('orderCode') orderCode: string, @Query() payload: EmployeeInfoContinueBuyingDto) {
    return this.ordersFamilyPackageService.continueBuying(orderCode, false, payload);
  }

  // Get list order aff and order ecom by lcvids
  @Post('ordersinfor/search-by-lcvids-attributes')
  @ApiOperation({
    summary: 'Lấy danh sách đơn hàng theo lcvIds của đơn aff và ecom',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả đơn hàng',
  })
  getOrderByLcvidsAttributes(@Body() body: GetOrdersByLcvidsAttributesConvertDto) {
    return this.ordersFamilyPackageService.getOrderByLcvidsAttributes(body.lcvIds, true);
  }

  // save-incentive-order
  @Post('save-incentive-order')
  @ApiOperation({
    summary: 'Lưu đơn hàng incentive vào redis',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kết quả save-incentive-order',
  })
  saveIncentiveOrderRedis(@Body() body: GetOrdersByLcvidsAttributesConvertDto) {
    return this.ordersFamilyPackageService.saveIncentiveOrderRedis(body.lcvIds);
  }
}
