import { HttpException, HttpStatus, Inject, Injectable, Logger, LoggerService } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { parseDateTimeZone } from '@shared/utilities/function-time-zone';
import { Request } from 'express';
import * as _ from 'lodash';
import { OrdersService } from 'modules/modules/modules/orders/services/orders.service';
import moment from 'moment';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { CartAppService, GetCartLibResponse, OrderAttribute } from 'vac-nest-cart-app';
import { WHS_CODE_NORMAL } from 'vac-nest-customer-core';
import {
  AssignRoomDtoV2,
  ChangeRoomDto,
  ChangeStatusTicketDto,
  ClinicType,
  CreateTicketDto,
  CreateTicketImageDto,
  CreateTicketNoteDto,
  CreateTicketRes,
  DeleteTicketImageDto,
  EnmAssignRule,
  EnmStatusTicket,
  ExaminationCoreService,
  HealthCheck,
  HealthCheckQuestion,
  ItemTicket,
  MaterialAttachmentRes,
  PaymentType,
  SearchTicketDto,
  TicketDetailAdjustDto,
  TicketDetailRes,
  TicketRes,
  UpdateInjectionRouteAndPositionDto,
  UpdateMaterialAttachmentDto,
  UpdateTrackingStatusDto,
  mapCountStatus,
  EnmTicketType,
} from 'vac-nest-examination';
import { FamilyService, GetPersonByIdRes } from 'vac-nest-family';
import {
  GetInventoryProductItemByShopSkuCustomResponse,
  IMSService,
  getWhsTail,
  whsCodeWithShopCodeWhsType,
} from 'vac-nest-ims';
import { CancelBookParams, IMSBookingService } from 'vac-nest-ims-booking';
import { GetOrdersInfoDto, JourneyService } from 'vac-nest-journey';
import { MonitorCoreService } from 'vac-nest-monitor';
import { CashbackType, EnmMaterialType, OsrService, verifyBarcodeDto } from 'vac-nest-osr';
import { EcomDisplay, OMSService, OrderPaymentCreate, OrderStatus, GetOneOrderLibResponse } from 'vac-nest-oms';
import { PaymentGatewayService } from 'vac-nest-payment-gateway';
import { ScheduleCoreService } from 'vac-nest-schedule';
import {
  CancelVoucherByPaymentCodeDto,
  UnbookVoucherByPaymentCodeDto,
  VoucherCoreService,
} from 'vac-nest-voucher-core';
import { FilesService } from '../../../../../../libs/modules/src/modules/files/services/files.service';
import { ErrorCode, OrderChannels, SystemException } from '../../../../../../libs/shared/src';
import { Channel, OrderChanel, SystemBook, TransType, VacOrderStatus } from '../../../../../shared/src/enum';
import { DAY_IN_WEEK, MONITOR_IN_MINUTES, ScreenId, STATUS_INDICATION, STATUS_TICKET } from '../constants';
import {
  CancelTicketBodyDto,
  CancelTicketResponseDto,
  ChangeStatusCompeteInjectionDto,
  CompleteInjectionMonitorDto,
  CreateTicketDtos,
  DoctorCancelTicketDto,
  GetTicketRedisTVDto,
  OnlyCancelTicketDto,
  ReExaminationDto,
  StartExaminingDto,
  TicketMapPersonResDto,
} from '../dto';
import { TicketUtilsService } from './ticket-utils.service';
import { OrderUtilsService } from '../../orders/services/order-utils.services';
import { PIMAppService } from 'vac-nest-pim-app';
import { VacCoreAssignJobService } from 'vac-nest-assign-job';
import { System } from 'vac-nest-loyalty-rule';
import { LoyaltyAppService } from 'vac-nest-loyalty-app';
import Redis from 'ioredis';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { RsaEcomService } from 'vac-nest-rsa-ecom';
import { VacOrderInjectionService } from 'vac-nest-order-injection';
import { IError } from '@shared';
import { RegimenService } from 'vac-nest-regimen';
import { GetPreOrdersInfoDto } from '../dto/get-order-pre-order.dto';
import { JSONPath } from 'jsonpath-plus';
import { NotificationService } from '@libs/modules/notification';
import {
  CamundaSpeakerApiService,
  GetTicketsRequestDto,
  TicketResponseDto,
  VacOrderCoreApiService,
} from '@frt/nestjs-api';
import { UpdateTrackingStatusRequestDto } from '../dto/update-tracking-status-request.dto';
// import { CustomersService } from '../../customers/services/customers.service';
// import { MAJOR_OTP, TYPE_OTP } from '../../customers/enum';

// import { RegimenService } from 'vac-nest-regimen';
@Injectable()
export class TicketService {
  shopCode: string;
  orderChannel: string;

  constructor(
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly familyService: FamilyService,
    private readonly monitorCoreService: MonitorCoreService,
    private readonly ticketUtilsService: TicketUtilsService,
    private readonly journeyCoreService: JourneyService,
    private readonly imsService: IMSService,
    private readonly filesService: FilesService,
    private readonly ordersService: OrdersService,
    private readonly orderUtilsService: OrderUtilsService,
    private readonly omsService: OMSService,
    private readonly scheduleCoreService: ScheduleCoreService,
    private readonly paymentCoreService: PaymentGatewayService,
    private readonly voucherCoreService: VoucherCoreService,
    private readonly imsBookingService: IMSBookingService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly cartAppService: CartAppService,
    private readonly pimAppService: PIMAppService,
    private readonly vacCoreAssignJobService: VacCoreAssignJobService,
    private readonly loyaltyAppService: LoyaltyAppService,
    @InjectRedis(process.env.REDIS_CONNECTION)
    private readonly redis: Redis,
    private rsaEcomService: RsaEcomService,
    private readonly vacOrderInjectionService: VacOrderInjectionService,
    private readonly regimenService: RegimenService,
    private readonly osrService: OsrService,
    private readonly notificationService: NotificationService,
    private readonly camundaSpeakerApiService: CamundaSpeakerApiService,
    private readonly vacOrderCoreApiService: VacOrderCoreApiService,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
    this.orderChannel = (this.req.headers?.['order-channel'] as string) || '';
  }

  changeRoom(room: ChangeRoomDto) {
    return this.examinationCoreService.changeRoom(room);
  }

  private mapPhoneToMatchingAttachmentCodes(
    orders: GetOneOrderLibResponse[] = [],
    oDACs: string[] = [],
  ): Map<string, string[]> {
    const result = new Map<string, string[]>();

    if (!Array.isArray(orders) || !Array.isArray(oDACs)) {
      return result;
    }

    for (const order of orders) {
      const phoneNumber = order?.phone;
      if (!phoneNumber) continue;

      const attachments: string[] = _.compact(
        JSONPath({
          path: '$.details[*].detailAttachments[*].orderDetailAttachmentCode',
          json: order ?? {},
        }) ?? [],
      );

      if (!Array.isArray(attachments)) continue;

      const matchedCodes = attachments.filter((code) => oDACs.includes(code));

      if (matchedCodes.length > 0) {
        const existing = result.get(phoneNumber) || [];
        result.set(`${phoneNumber}_${order?.orderCode}`, [...existing, ...matchedCodes]);
      }
    }

    return result;
  }

  private async handleCashBack(listAttachmentCode: string[]) {
    try {
      const todayDayOfWeek = moment().format('dddd');
      const isDayInWeek = DAY_IN_WEEK?.includes(todayDayOfWeek);
      if (!isDayInWeek) {
        return undefined;
      }
      let { orders } = await this.omsService.getOrderByOrderAttachmentCode({
        listAttachmentCode,
      });
      // Lọc ra những order có orderChanel = affiliate
      orders = orders?.filter((e) => OrderChannels.RSA_AFFILIATE.includes(e['orderChanel']));
      if (!orders?.length) {
        return undefined;
      }
      const listPhoneNumber = orders?.map((item) => item?.phone);

      const lisCashbackTicket = await this.osrService.searchCashbackSuggestByPhoneNumber(listPhoneNumber);
      const resCashback = await this.osrService.getCashBackByListAttachmentCode(listAttachmentCode, {
        isSuggest: true,
        isTracking: true,
      });

      const oDACWithPhones = this.mapPhoneToMatchingAttachmentCodes(orders, listAttachmentCode);
      const listCashBack = lisCashbackTicket?.filter((e) => e?.status === 1 && e?.isCashBack);
      listCashBack?.forEach((item) => {
        const caskbackFind = resCashback?.find(
          (e) => e?.phoneNumber === item?.phoneNumber && e.orderCode === item?.orderVAC,
        );
        item.suggestAmount = caskbackFind?.cashBackAmount || 0;
        item['custName'] = caskbackFind?.custName || '';
      });

      // Chỉ lấy cashback hợp lệ
      return {
        cashback: listCashBack?.filter((cashback) => cashback?.suggestAmount > 0) || [],
        oDACWithPhones,
      };
    } catch (error) {
      Logger.error({
        fields: {
          info: '[handleCashBack] handle error',
          errors: error,
        },
      });
      return undefined;
    }
  }

  /**
   * @TODO tạo phiếu khám
   * @param payload
   * @returns
   */
  async createTicket(body: CreateTicketDtos) {
    const { items } = body;
    body.placeOrder.createTicketDto = items;
    const orderCode = body?.items?.at(0)?.orderCode;
    const ticketCode = body?.items?.at(0)?.ticketCode;
    const sessionId = body?.placeOrder?.sessionId;
    const shopCode = this.req.headers?.['shop-code'] as string;

    // check existed ticket in today
    const { items: getOpeningTickets } = await this.examinationCoreService.searchTicketExcludeDetail({
      personIds: body.items?.map(({ personId }) => personId),
      statuses: [
        EnmStatusTicket.CHO_KHAM,
        EnmStatusTicket.DANG_KHAM,
        EnmStatusTicket.CHECKED_UP,
        EnmStatusTicket.WAITING_PAYMENT,
        EnmStatusTicket.WAITING_INJECT,
        EnmStatusTicket.INJECTING,
        EnmStatusTicket.WAIT_FOR_FOLLOW,
      ],
      fromDate: moment().utcOffset(7).startOf('d').toDate(),
      toDate: moment().utcOffset(7).endOf('d').toDate(),
    });
    const indicationTicketOpenings = getOpeningTickets?.filter(
      ({ ticketType }) => ticketType === EnmTicketType.INDICATION,
    );
    if (indicationTicketOpenings?.length) {
      throw new HttpException(
        {
          code: ErrorCode.RSA_HAVE_TICKET_IS_OPEN,
          message: ErrorCode.getError(ErrorCode.RSA_HAVE_TICKET_IS_OPEN).replace(
            '{ticketCode}',
            indicationTicketOpenings.map((ticket) => ticket.ticketCode)?.toString(),
          ),
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    const orderChannel = this.req['headers']?.['order-channel'] as string;
    this.orderUtilsService.blockAdjustTicketWithAnotherVaccine({
      orderChannel,
      createTicketDto: items,
    });

    this.orderUtilsService.filterDupTicketSchedule(items);
    let getCart: GetCartLibResponse | null = null;
    if (sessionId) {
      getCart = await this.cartAppService.getCart({ sessionId, shopCode: shopCode });
      await this.orderUtilsService.validateSellRestrictProducts(
        getCart?.listCartSelected,
        ErrorCode.RSA_SKU_SELL_RESTRICT_AT_CREATE_TICKET,
        items,
      );
    }

    let getTicket: TicketDetailRes[] = [];

    if (ticketCode) {
      getTicket = [await this.examinationCoreService.getTicket({ ticketCode })];
    }

    let createTicket: TicketDetailRes[] | CreateTicketRes[] = null;

    if (orderCode && sessionId && getTicket?.length) {
      Logger.log(`[order vs session vs ticket] orderCode: ${orderCode} & sessionId: ${sessionId}`);
      // update order

      // xử lý đơn Web/App xuống shop.
      let getOrder: GetOneOrderLibResponse = null;
      try {
        getOrder = await this.ordersService.updatePlaceOrder(orderCode, body?.placeOrder as any);
      } catch (error) {
        Logger.log({
          fields: {
            info: '[updatePlaceOrder] handle error',
            errors: error,
          },
        });
        // nếu nguồn WebAPp thì đi update không cần update payment
        getOrder = await this.omsService.getOneOrder(orderCode);

        if (
          error?.response?.code === ErrorCode.RSA_ORDER_ADJUST_LESS_MONEY &&
          OrderChannels.WEB_APP.includes(getOrder?.orderChanel)
        ) {
          // đi adjust không adjust payment
          const dto = await this.orderUtilsService.getAdjustDto(body?.placeOrder as any, getCart, getOrder);
          getOrder = await this.omsService.updateOrder(orderCode, {
            ...dto,
            orderID: getOrder?.orderID,
            orderVersion: getOrder?.orderVersion,
            orderCode: orderCode,
            modifiedBy: body?.placeOrder?.employeeCode,
            modifiedByName: body?.placeOrder?.employeeName,
          });
        }
      }
      // const getOrder = await this.ordersService.updatePlaceOrder(orderCode, body?.placeOrder as any);
      const updateTicketDto = await this.ticketUtilsService.createAdjustTicketDtoV2([body?.items?.at(0)], getOrder);
      const ticketRes = await this.examinationCoreService.adjustTicket(
        {
          ticketCode,
        },
        {
          ...getTicket?.at(0),
          ...updateTicketDto?.at(0),
          clinicId: null,
          clinicName: '',
          status: EnmStatusTicket.CHO_KHAM,
          ticketType: body?.items?.at(0)?.ticketType,
        },
      );
      const updateTickets = [ticketRes as any];
      const assignRoomData: AssignRoomDtoV2 = {
        ticketCodes: updateTickets?.map((e) => e.ticketCode),
        shopCode: updateTickets?.at(0)?.shopCode,
        roomType: ClinicType.PK,
        rule: EnmAssignRule.ASSIGN_EXAMINATION,
        modifiedBy: updateTickets?.at(0)?.createdBy,
      };

      const assignRooms = await this.examinationCoreService.assignRoomV2(assignRoomData);
      for (const room of assignRooms) {
        await this.examinationCoreService.adjustTicket({ ticketCode: room.ticketCode }, room);
      }

      return assignRooms;
    } else if (getTicket?.length) {
      Logger.log(`[có ticket] ticketCode: ${ticketCode}`);
      createTicket = [
        await this.examinationCoreService.adjustTicket(
          {
            ticketCode,
          },
          {
            ...getTicket?.at(0),
            clinicId: null,
            clinicName: '',
            status: EnmStatusTicket.CHO_KHAM,
            ticketType: body?.items?.at(0)?.ticketType,
          },
        ),
      ];
    } else {
      if (orderCode && sessionId) {
        Logger.log(`[order vs session] orderCode: ${orderCode} & sessionId: ${sessionId}`);
        let getOrder = await this.omsService.getOneOrder(orderCode);
        const indicationStatus5 = body?.items?.at(0)?.indications?.find((indication) => indication?.status === 5);
        if ([8, 7].includes(+getOrder?.orderAttribute) && indicationStatus5) {
          throw new SystemException(
            {
              code: ErrorCode.RSA_ERROR_RULE_DON_MIX,
            },
            HttpStatus.FORBIDDEN,
          );
        }
        try {
          const updateOrder = await this.ordersService.updatePlaceOrder(orderCode, body?.placeOrder as any);
          getOrder = updateOrder;
        } catch (error) {
          Logger.log({
            fields: {
              info: '[updatePlaceOrder] handle error',
              errors: error,
            },
          });
        }
        // await this.ticketUtilsService.createAdjustTicketDtoV2([body?.items?.at(0)], getOrder);
      }

      createTicket = await this.ticketUtilsService.createTicketUtil(items, getCart?.headerData?.journeyId || '');
    }

    const assignRoomData: AssignRoomDtoV2 = {
      ticketCodes: createTicket?.map((e) => e.ticketCode),
      shopCode: createTicket?.at(0)?.shopCode,
      roomType: ClinicType.PK,
      rule: EnmAssignRule.ASSIGN_EXAMINATION,
      modifiedBy: createTicket?.at(0)?.createdBy,
    };

    const assignRooms = await this.examinationCoreService.assignRoomV2(assignRoomData);
    for (const room of assignRooms) {
      await this.examinationCoreService.adjustTicket({ ticketCode: room.ticketCode }, room);
    }
    return assignRooms;
  }

  /**
   * @TODO lấy chi tiết phiều khám
   * @param payload
   * @returns
   */
  async getTicket(ticketCode: string, screenId?: string) {
    const data = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });

    let healthCheckList: HealthCheck[] = null;
    let healthCheckQuestionList: HealthCheckQuestion[] = null;

    if ((!data?.healthCheckQuestions?.length || !data?.healthChecks?.length) && data?.personId) {
      const personInfor = await this.familyService.getPersonById(data.personId);

      if (personInfor?.dateOfBirth) {
        const { biosignal, question } = await this.monitorCoreService.searchQuestion({
          birthday: personInfor?.dateOfBirth,
          gender: personInfor?.gender,
        });

        healthCheckList = biosignal?.map((entry) => {
          const healthCheckData: HealthCheck = {
            ticketId: data?.id,
            biosignalId: entry?.id,
            biosignalValue: '',
            fieldName: entry?.fieldName || '',
            doctorNote: '',
            displayName: entry?.displayName || '',
            stt: entry?.stt || 0,
            unit: entry?.unit,
            isRequired: entry?.isRequired,
            inputType: entry?.inputType,
            createdDate: null,
            createdBy: '',
            modifiedDate: null,
            modifiedBy: '',
          };
          return healthCheckData;
        });
        healthCheckQuestionList = question?.map((entry) => {
          const healthCheckQuestionData: HealthCheckQuestion = {
            ticketId: data.id,
            stt: entry?.stt || 0,
            questionId: entry?.id,
            questionContext: entry?.questionContext,
            answer: false,
            note: entry?.note,
            createdDate: null,
            createdBy: '',
            modifiedDate: null,
            modifiedBy: '',
          };
          return healthCheckQuestionData;
        });
      }
    }

    if (data?.image?.length >= 0) {
      const key_Image = data?.image?.map((img) => {
        return img.image;
      });

      const { links } = await this.filesService.getS3Presign({ urls: key_Image });

      let idx = 0;
      for (const item of data?.image) {
        item.url = links?.at(idx);
        ++idx;
      }
    }

    // get sku trong indications
    if (data && data?.indications?.length) {
      const skus = data?.indications?.map((i) => i?.sku);
      const { listProduct } = await this.pimAppService.getListProductBySku(skus);
      data.indications = data?.indications?.map((i) => {
        let image = null;
        if (screenId === ScreenId.NURSE_DETAIL) {
          image = data?.image?.filter((img) => img?.orderDetailAttachmentCode === i?.orderDetailAttachmentCode);
        }
        return {
          ...i,
          isMultiDose: listProduct?.find((p) => p?.sku === i?.sku)?.isMultiDose || false,
          image: image?.length ? image : null,
        };
      });
    }

    return {
      ...data,
      healthChecks: data?.healthChecks && data?.healthChecks?.length ? data?.healthChecks : healthCheckList,
      healthCheckQuestions:
        data?.healthCheckQuestions && data?.healthCheckQuestions?.length
          ? data?.healthCheckQuestions
          : healthCheckQuestionList,
    };
  }

  /**
   * @TODO tìm kiếm danh sách phiếu khám dành cho bác sĩ
   */
  async searchTicket(payload: SearchTicketDto) {
    const { keyWord, fromDate, toDate, shopCode } = payload;
    /**
     * @TODO Không còn dùng count nua. Hotfix 20240119
     */
    // gọi api count status để lấy count của status
    // const payloadCountStatus: CountStatusTicketDto = {
    //   fromDate: payload?.fromDate,
    //   toDate: payload?.toDate,
    //   shopCode: payload?.shopCode,
    // };
    // const countStatus = await this.examinationCoreService.countStatusTicket({
    //   fromDate,
    //   toDate,
    //   shopCode,
    // });
    // const mapStatusCount = mapCountStatus(countStatus);
    // gọi api search family để lấy persionId

    let personIds: string[] = [];
    if (keyWord) {
      const persons = await this.familyService.searchPersonFamily({ keyword: keyWord });
      personIds = persons?.items?.map((person) => person.id) || [];
    }

    const searchTicket = _.isEmpty(personIds) && keyWord ? keyWord : '';
    const payloadSearchTicket = {
      ...payload,
      personIds: personIds,
    };

    if (keyWord) {
      payloadSearchTicket.keyWord = searchTicket;
    }

    const resTicket = await this.examinationCoreService.searchTicketExcludeDetail(payloadSearchTicket);

    if (resTicket?.totalCount === 0) {
      return {
        totalCount: 0,
        items: [],
        countStatus: [],
      };
    }

    // sort phiếu khám cho màn màn hình TV
    if (typeof payload?.roomType !== 'undefined') {
      // resTicket['items'] = resTicket?.items?.filter((item) => item?.status !== STATUS_TICKET.CHO_THEO_DOI);
      resTicket['items'] = _.orderBy(resTicket['items'], ['status'], ['desc']);
    }

    const data = await this.commonDataResponseSearchAndGet(resTicket?.items);

    return {
      ...data,
      totalCount: resTicket.totalCount,
      countStatus: [],
    };
  }

  /**
   * @TODO tìm kiếm phiếu khám cho màn màn hình TV
   */
  async searchTicketRedis(payload: SearchTicketDto) {
    return this.examinationCoreService.searchTicketRedis(payload);
  }

  /**
   * @TODO hàm dùng chung trả về response cho get danh sách với search phiếu khám
   */

  async commonDataResponseSearchAndGet(ticketList: TicketRes['items']) {
    const lcvIds = ticketList.map((person) => person?.lcvId);
    // gọi lên family lấy thông tin khách hàng
    const resFamily = await this.familyService.getFamilyForReportSymptom(lcvIds);
    // change đầu api call family để lấy isHost

    const dataMapTicketPerson = this.mapDataGetInforPerson(ticketList, resFamily);

    return {
      totalCount: dataMapTicketPerson.length,
      items: dataMapTicketPerson,
    };
  }

  /**
   * @TODO map lấy thông tin person qua ticket
   * @param tickets
   * @param persons
   * @returns
   */
  mapDataGetInforPerson(tickets: ItemTicket[], persons: any[]) {
    let outPut: TicketMapPersonResDto[] = [];
    _.forEach(tickets, (ticket: ItemTicket) => {
      const itemPerson = persons.find((person) => person?.lcvId === ticket?.lcvId);
      const itemIsHost = itemPerson?.familyProfileDetails && itemPerson?.familyProfileDetails?.find((i) => i?.isHost);

      const itemTicket = {
        ...ticket,
        personName: itemPerson?.name ?? '',
        dateOfBirth: itemPerson?.dateOfBirth ?? '',
        gender: itemPerson?.gender ?? 0,
        phoneNumber: itemPerson?.phoneNumber ?? null,
        guardianName: itemIsHost ? itemIsHost?.name : '',
        guardianPhone: itemIsHost ? itemIsHost?.phoneNumber : '',
        nationalVaccineCode: itemPerson?.nationalVaccineCode?.length > 0 ? itemPerson?.nationalVaccineCode : null,
      };
      outPut = [...outPut, itemTicket];
    });

    return outPut;
  }

  /**
   * @TODO change status ticket
   */
  async changeStatusTicket(body: ChangeStatusTicketDto) {
    const data = await this.examinationCoreService.changeStatusTicket(body);

    let healthCheckList: HealthCheck[] = null;
    let healthCheckQuestionList: HealthCheckQuestion[] = null;

    if ((!data?.healthCheckQuestions?.length || !data?.healthChecks?.length) && data?.personId) {
      const personInfor = await this.familyService.getPersonById(data.personId);

      if (personInfor?.dateOfBirth) {
        const { biosignal, question } = await this.monitorCoreService.searchQuestion({
          birthday: personInfor?.dateOfBirth,
          gender: personInfor?.gender,
        });
        healthCheckList = biosignal?.map((entry) => {
          const healthCheckData: HealthCheck = {
            ticketId: data?.id,
            biosignalId: entry?.id,
            biosignalValue: '',
            fieldName: entry?.fieldName || '',
            doctorNote: '',
            unit: entry?.unit,
            displayName: entry?.displayName || '',
            stt: entry?.stt || 0,
            isRequired: entry?.isRequired,
            inputType: entry?.inputType,
            createdDate: null,
            createdBy: '',
            modifiedDate: null,
            modifiedBy: '',
          };
          return healthCheckData;
        });
        healthCheckQuestionList = question?.map((entry) => {
          const healthCheckQuestionData: HealthCheckQuestion = {
            ticketId: data.id,
            stt: entry?.stt || 0,
            questionId: entry?.id,
            questionContext: entry?.questionContext,
            answer: false,
            note: entry?.note,
            createdDate: null,
            createdBy: '',
            modifiedDate: null,
            modifiedBy: '',
          };
          return healthCheckQuestionData;
        });
      }
    }

    return {
      ...data,
      healthChecks: data?.healthChecks && data?.healthChecks?.length ? data?.healthChecks : healthCheckList,
      healthCheckQuestions:
        data?.healthCheckQuestions && data?.healthCheckQuestions?.length
          ? data?.healthCheckQuestions
          : healthCheckQuestionList,
    };
  }

  /**
   * @TODO tìm kiếm danh sách phiếu khám dành cho dược sĩ
   */
  async searchTicketNursing(payload: SearchTicketDto) {
    // gọi api search family để lấy persionId
    const { keyWord } = payload;

    // tìm kiếm ticket theo tên, sdt, tcqg
    let personIds: string[] = [];
    if (keyWord) {
      const persons = await this.familyService.searchPersonFamily({ keyword: keyWord });
      personIds = persons?.items?.map((person) => person.id) || [];
    }

    const searchTicket = _.isEmpty(personIds) && keyWord ? keyWord : '';
    const payloadSearchTicket = {
      ...payload,
      personIds: personIds,
    };

    if (keyWord) {
      payloadSearchTicket.keyWord = searchTicket;
    }

    const { items, totalCount } = await this.examinationCoreService.searchTicketExcludeDetail(payloadSearchTicket);

    if (totalCount === 0) {
      return {
        totalCount: 0,
        items: [],
      };
    }

    const data = await this.commonDataResponseSearchAndGet(items);

    return {
      ...data,
      totalCount: totalCount,
    };
  }

  createTicketNote(createTicketNoteDto: CreateTicketNoteDto) {
    return this.examinationCoreService.createTicketNote(createTicketNoteDto);
  }

  getTicketNoteByPersonId(personId: string) {
    return this.examinationCoreService.getTicketNoteByPersonId(personId);
  }

  async adjustTicket(body: TicketDetailAdjustDto) {
    // getTicket về check version trước có thông tin indication vs schdule hay k?
    const getTicket = await this.examinationCoreService.getTicket({ ticketCode: body?.ticketCode });
    if ((getTicket?.indications?.length || getTicket?.schedules?.length) && !body?.schedules?.length) {
      throw new SystemException({ code: ErrorCode.RSA_TICKET_EMPTY }, HttpStatus.FORBIDDEN);
    }

    // if (body?.orderCode && body?.indications?.length) {
    //   const arrOrderDetailAttachmentCode = body?.indications?.map(
    //     (indication) => indication?.orderDetailAttachmentCode,
    //   );
    //   const arrCompact = _.compact(arrOrderDetailAttachmentCode);
    //   if (arrOrderDetailAttachmentCode?.length !== arrCompact?.length) {
    //     throw new SystemException({ code: ErrorCode.RSA_ORDER_ATTACHMENT_CODE }, HttpStatus.FORBIDDEN);
    //   }
    // }

    if (body?.orderCode) {
      const getOrder = await this.omsService.getOneOrder(body?.orderCode);
      const updateTicketDto = await this.ticketUtilsService.createAdjustTicketDtoV2([body as any], getOrder);
      return this.examinationCoreService.adjustTicket({ ticketCode: body?.ticketCode }, updateTicketDto?.at(0) as any);
    }
    return this.examinationCoreService.adjustTicket(
      { ticketCode: body?.ticketCode },
      { ...body, journeyId: getTicket?.journeyId || '' },
    );
  }

  /**
   * @TODO thay đổi trạng thái đã tiêm
   */
  async changeStatusCompleteInjection(body: ChangeStatusCompeteInjectionDto) {
    const { ticketCode, injectingNursingCode, injectingNursingName, indications } = body;
    // lấy thông tin phiếu khám với ticketCode
    const infoTicket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });

    if (!infoTicket) return {};

    const infoAdjustTicket = await this.examinationCoreService.updateCompleteStatus({
      ticketCode: ticketCode,
      supervisedNursingCode: injectingNursingCode,
      supervisedNursingName: injectingNursingName,
      modifiedBy: injectingNursingCode,
      modifiedByName: injectingNursingName,
    });

    if (!infoAdjustTicket) return {};

    return { ...infoAdjustTicket };
  }

  /**
   * @TODO hoàn thành theo dõi sau tiêm
   */
  async completeInjectionMonitor(body: CompleteInjectionMonitorDto) {
    const { ticketCode, supervisedNursingCode, supervisedNursingName } = body;
    // lấy thông tin phiếu khám với ticketCode
    const infoTicket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });

    if (!infoTicket) return {};

    const infoAdjustTicket = await this.examinationCoreService.updateCompleteStatus({
      ticketCode: ticketCode,
      supervisedNursingCode,
      supervisedNursingName,
      modifiedBy: supervisedNursingCode,
      modifiedByName: supervisedNursingName,
    });

    if (!infoAdjustTicket) return {};

    // remove ticket ra ngoài queue
    // try {
    //   await this.vacCoreAssignJobService.deleteTicketFromMonitor({ ticketId: ticketCode });
    // } catch (error) {}

    return { ...infoAdjustTicket };
  }

  async startExamining(startExaminingDto: StartExaminingDto) {
    const ticketUpdate = await this.changeStatusTicket({
      ticketCode: startExaminingDto?.ticketCode,
      status: startExaminingDto?.status,
      modifiedBy: startExaminingDto?.modifiedBy,
    });

    return this.adjustTicket({
      ...ticketUpdate,
      doctorCode: startExaminingDto?.modifiedBy,
      doctorName: startExaminingDto?.modifiedByName,
    });
  }

  /**
   * @description lấy danh sách đơn gộp nhóm
   */
  async getOrderInfoService(body: GetOrdersInfoDto) {
    if (!_.isEmpty(body?.keyWord)) {
      body.shopCode = '';
    }
    const { totalCount, items } = await this.journeyCoreService.getOrderInfo(body);

    let persons: GetPersonByIdRes[] = [];

    if (items?.length) {
      const lcvIds: string[] = [];
      items?.map((item) => {
        if (item?.lcvId !== null) {
          lcvIds.push(item?.lcvId);
        }
      });
      persons = await this.familyService.getManyByLcvId({ lcvId: _.uniq(lcvIds) });
    }

    items?.map((item) => {
      const itemPerson = persons?.find((person) => person?.lcvId === item?.lcvId);
      item['nationalVaccineCode'] =
        itemPerson?.nationalVaccineCode?.length > 0 ? itemPerson?.nationalVaccineCode : null;
    });

    return {
      totalCount: totalCount,
      items: items,
    };
  }

  /**
   * @description Lấy danh sách đơn hàng đặt cọc ở màn hình profile.
   */
  async getPreOrderInfo({ lcvId }: GetPreOrdersInfoDto, orderChannel: string) {
    const preOrderFiltersRes = await this.journeyCoreService.getPreOrderInfoByLcvId(lcvId);
    const preOrders = [];

    // https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=158239180
    switch (OrderChannels.RSA_ECOM.includes(orderChannel)) {
      case true:
        preOrders.push(
          ...preOrderFiltersRes.filter((item) => {
            return (
              (OrderChannels.RSA.includes(item.orderChanel) && item.orderStatus === OrderStatus.FinishDeposit) ||
              (OrderChannels.RSA_ECOM.includes(item.orderChanel) &&
                item.orderStatus === OrderStatus.Confirmed &&
                item.ecomDisplay === EcomDisplay.AtShop) ||
              (OrderChannels.RSA_ECOM.includes(item.orderChanel) && item.orderStatus === OrderStatus.FinishDeposit)
            );
          }),
        );
        break;
      default:
        preOrders.push(
          ...preOrderFiltersRes.filter((item) => item.ecomDisplay || OrderChannels.RSA.includes(item.orderChanel)),
        );
        break;
    }
    const setSKU = new Set<string>();

    const detailPreOrders = preOrders.map((item) => {
      const details = (item.details || []).filter((detail) => detail.isPromotion !== 'Y');
      const allDetailAttachments = details?.flatMap((detail) => detail?.detailAttachments);
      const skuInfo = details?.at(0)?.detailAttachments?.at(0);
      const skuItemCode = skuInfo?.itemCode;
      const skuItemName = skuInfo?.itemName;
      setSKU.add(skuItemCode);

      const groupBySKU = _.groupBy(allDetailAttachments, 'itemCode');
      const skuAndTotalQuantity = _.mapValues(groupBySKU, (attachmentByItem) => _.sumBy(attachmentByItem, 'quantity'));
      return {
        ...item,
        skuItemCode,
        skuItemName,
        totalQuantity: skuAndTotalQuantity[skuItemCode] || item.totalQuantity,
      };
    });

    const regimens = [];
    const listItemCodes = _.uniq(
      detailPreOrders.map((item) => {
        return item?.skuItemCode;
      }),
    );

    if (listItemCodes.length) {
      regimens.push(...(await this.regimenService.getListDiseaseGroupBySku(listItemCodes)));
    }

    /**
     *
     */
    const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({
      listSku: [...setSKU],
    });

    return detailPreOrders.map((item) => {
      let isAbleToContinuePreOrder = false;
      const diseaseGroupInfo = regimens.find((regimen) => regimen?.sku === item?.skuItemCode);
      const phase2 = osrDepositAmountBySku?.find((e) => e.phaseId === 2 && e?.itemCode === item?.skuItemCode);
      const isPhase2 = moment(moment().format('YYYY-MM-DD')).isBetween(
        moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
        moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
        undefined,
        '[]',
      );
      if (phase2 && isPhase2 && item?.orderStatus === OrderStatus.FinishDeposit) {
        isAbleToContinuePreOrder = true;
      }

      if (
        item?.orderPaymentCreate?.length === 1 &&
        item?.orderPaymentCreate?.at(0)?.paymentStatus === 4 &&
        item?.orderAttribute === OrderAttribute.PRE_ORDER &&
        OrderChannels.RSA_ECOM.includes(this.req.headers['order-channel'] as string)
      ) {
        isAbleToContinuePreOrder = true;
      }
      // check payment hoàn tất thì = totalDeposit
      const orderCreatePayment: OrderPaymentCreate[] =
        JSONPath({
          json: item,
          path: '$.orderPaymentCreate[?(@ && @.paymentType === 1 && @.paymentStatus === 4)]',
        }) || [];

      const totalDeposit = orderCreatePayment?.reduce((acc, payment) => {
        return acc + (payment?.paymentAmount || 0);
      }, 0);

      return {
        ...item,
        totalDeposit,
        diseaseGroupInfo: diseaseGroupInfo,
        nameDisplay: `${diseaseGroupInfo?.diseaseGroupName || ''} - ${diseaseGroupInfo?.skuName || ''}`,
        isAbleToContinuePreOrder,
      };
    });
  }

  /**
   * @TODO
   * Lấy danh sách thuốc hạn sốt
   * Lấy danh sách hình của thuốc hạ sốt
   * Check tồn thuốc hạ sôt
   */
  async getListMatetial() {
    let listMaterial = await this.ticketUtilsService.getListMaterial([EnmMaterialType.Antipyretic]);

    const listSkuMaterial = listMaterial?.map((material) => material.sku);

    const productInfo = await this.ticketUtilsService.getListImageBySku(listSkuMaterial);

    const lstInventory = await this.imsService.getListStockMedicAtShop({
      skuCodes: listSkuMaterial,
      shopCodes: [this.shopCode],
    });

    listMaterial = listMaterial?.map((material) => {
      const inventoryFind = lstInventory?.inventories?.find(
        (inventory) =>
          inventory?.sku === material?.sku &&
          getWhsTail(inventory?.whsCode) === material?.whsCode &&
          inventory?.unitCode === material?.unitCode &&
          inventory?.quantityAvailable >= material?.quantity,
      );

      const productFind = productInfo?.find((product) => product?.sku === material?.sku);
      return { ...material, isAvailable: !!inventoryFind, image: productFind?.image ?? '' };
    });

    return listMaterial;
  }

  async updateMaterialAttachment(payload: UpdateMaterialAttachmentDto) {
    return this.examinationCoreService.updateMaterialAttachment(payload);
  }

  async checkInventoryMaterial(materialDetail: MaterialAttachmentRes) {
    if (materialDetail?.sku) {
      const lstInventory = await this.imsService.getListStockMedicAtShop({
        skuCodes: [materialDetail?.sku],
        shopCodes: [this.shopCode],
      });
      const inventoryFind = lstInventory?.inventories?.find(
        (inventory) =>
          inventory?.sku === materialDetail?.sku &&
          getWhsTail(inventory?.whsCode) === materialDetail?.whsCode &&
          inventory?.unitCode === materialDetail?.unitCode &&
          inventory?.quantity >= materialDetail?.quantity,
      );
      if (!inventoryFind) return { isAvailable: false };
      else return { isAvailable: true };
    }
  }

  async createTicketImage(body: CreateTicketImageDto) {
    return this.examinationCoreService.createTicketImage(body);
  }

  async deleteTicketImage(body: DeleteTicketImageDto) {
    return this.examinationCoreService.deleteTicketImage(body);
  }

  async cancelTicket(body: CancelTicketBodyDto): Promise<CancelTicketResponseDto> {
    const { ticketCode, reasonName, modifiedBy, modifiedByName } = body;
    const ticket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });

    // chỉ hủy phiếu khi trạng thái phiếu là chờ khám và đang khám
    if (![STATUS_TICKET.CHO_TIEM, STATUS_TICKET.DANG_TIEM].includes(ticket?.status)) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_CANCEL_TICKET_TIEM_NOT_ALLOWED,
        },
        HttpStatus.FORBIDDEN,
      );
    }
    /**
     * @TODO IMS
     */
    const cancelBookParams: CancelBookParams = {
      shopCode: this.shopCode,
      systemBook: SystemBook.VACCINE,
      transNum: ticketCode,
      transType: TransType.TICKET,
      updatedBy: modifiedBy,
    };
    try {
      await this.imsBookingService.cancelBook(cancelBookParams);
    } catch (error) {
      Logger.error({
        fields: {
          info: '[cancelBook] handle error',
          errors: error,
        },
      });
    }
    // _.forEach(_.get(ticket, 'indications', []), (element) => {
    //   const booking: CreateBookingDto = {
    //     docEntry: 0,
    //     docType: 1,
    //     systemType: 14,
    //     itemCode: element.sku,
    //     whsCode: whsCodeWithShopCodeWhsType(this.shopCode, WHS_CODE_NORMAL),
    //     shopCode: this.shopCode,
    //     qty_order: 1,
    //     createDate: parseDateTimeZone(new Date(), '+07:00'),
    //     updateDate: parseDateTimeZone(new Date(), '+07:00'),
    //     createby: modifiedBy,
    //     updateby: modifiedBy,
    //     status: 1,
    //     bookType: 1,
    //     orderCode: _.get(ticket, 'ticketCode', '') || _.get(ticket, 'orderCode', ''),
    //     unitCode: +element.unitCode,
    //   };
    //   createBookingDto.push(booking);
    // });

    // change status to Cancel
    const data = await this.examinationCoreService.changeStatusTicket({
      ticketCode: _.get(ticket, 'ticketCode', ''),
      status: EnmStatusTicket.CANCEL_INJECT,
      modifiedBy: modifiedBy,
    });

    // cancel SO2
    const orderCodes = _.compact(_.uniq(ticket?.indications?.map((e) => e.orderCodeCounter)));
    if (!orderCodes?.length) return data;
    const { orders } = await this.omsService.getListOrderES({ orderCode: orderCodes });
    for (const order of orders) {
      if ([OrderStatus.Cancel, OrderStatus.Completed].includes(order.orderStatus)) continue;
      await this.omsService.cancelOrder({
        orderCode: order.orderCode,
        orderCancelChannel: 14,
        orderID: order.orderID,
        modifiedBy: modifiedBy,
        modifiedByName: modifiedByName,
        reason: 1,
        reasonCancel: 'Hủy phiếu khám: ' + reasonName,
        systemDate: moment().format(),
      });
    }
    return data;
  }

  async reExamination(reExaminationDto: ReExaminationDto) {
    const { ticketCode, reasonName, modifiedBy, modifiedByName } = reExaminationDto;
    const ticket = await this.examinationCoreService.getTicket({ ticketCode: ticketCode });

    if (
      ![EnmStatusTicket.CHECKED_UP, EnmStatusTicket.WAITING_INJECT, EnmStatusTicket.INJECTING]?.includes(ticket?.status)
    ) {
      throw new SystemException({ code: ErrorCode.RSA_RE_EXAMINATION_CODE }, HttpStatus.FORBIDDEN);
    }

    if (ticket?.orderCode) {
      const orderDetail = await this.omsService.getOneOrder(ticket?.orderCode);
      if (orderDetail?.orderStatus === OrderStatus.Cancel || orderDetail?.orderStatus === OrderStatus.Completed) {
        throw new SystemException({ code: ErrorCode.RSA_RE_EXAMINATION_CODE }, HttpStatus.FORBIDDEN);
      }
    }

    const ticketDetail = await this.examinationCoreService.changeStatusTicketReverse({
      ticketCode,
      status: EnmStatusTicket.DANG_KHAM,
      modifiedBy,
    });

    // Nhã book cho trường hợp tồn còn 1. và đi khám lại
    /**
     * @TODO IMS
     */
    const cancelBookParams: CancelBookParams = {
      shopCode: this.shopCode,
      systemBook: SystemBook.VACCINE,
      transNum: ticketCode,
      transType: TransType.TICKET,
      updatedBy: modifiedBy,
    };
    try {
      await this.imsBookingService.cancelBook(cancelBookParams);
    } catch (error) {
      Logger.error({
        fields: {
          info: '[cancelBook] handle error',
          errors: error,
        },
      });
    }

    const orderCodes = _.compact(_.uniq(ticket?.indications?.map((e) => e.orderCodeCounter)));
    if (!orderCodes?.length) return ticketDetail;
    const { orders } = await this.omsService.getListOrderES({ orderCode: orderCodes });
    for (const order of orders) {
      if ([OrderStatus.Cancel, OrderStatus.Completed].includes(order.orderStatus)) continue;
      await this.omsService.cancelOrder({
        orderCode: order.orderCode,
        orderCancelChannel: 14,
        orderID: order.orderID,
        modifiedBy: modifiedBy,
        modifiedByName: modifiedByName,
        reason: 1,
        reasonCancel: reasonName,
        systemDate: moment().format(),
      });
    }
    return ticketDetail;
  }

  /**
   * @TODO bác sĩ hủy phiếu khám
   */
  async doctorCancelTicket(body: DoctorCancelTicketDto) {
    const { ticketCode, modifiedBy, reasonNote, modifiedByName } = body;

    /**
     * @TODO PETER_20240119
     */
    // await this.imsService.cancelBookByTransactionCode({ transactionCode: ticketCode });

    // lấy chi tiết phiếu
    const ticketDetail = await this.examinationCoreService.getTicket({ ticketCode });

    // chỉ hủy phiếu khi trạng thái phiếu là chờ khám và đang khám
    if (![STATUS_TICKET.CHO_KHAM, STATUS_TICKET.DANG_KHAM].includes(ticketDetail?.status)) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_CANCEL_TICKET_NOT_ALLOWED,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    /**
     * @TODO IMS
     */
    const cancelBookParams: CancelBookParams = {
      shopCode: this.shopCode,
      systemBook: SystemBook.VACCINE,
      transNum: ticketCode,
      transType: TransType.TICKET,
      updatedBy: modifiedBy,
    };
    try {
      await this.imsBookingService.cancelBook(cancelBookParams);
    } catch (error) {
      Logger.error({
        fields: {
          info: '[cancelBook] handle error',
          errors: error,
        },
      });
    }

    if (!ticketDetail?.orderCode) {
      // case chưa có orderCode
      // call hủy phiếu khám
      await this.examinationCoreService.changeStatusTicket({
        ticketCode: ticketCode,
        status: STATUS_TICKET.HUY,
        modifiedBy: modifiedBy,
      });

      return {
        isCancel: true, // hủy phiếu thành công
      };
    }

    // case có orderCode
    const orderDetail = await this.omsService.getOneOrder(ticketDetail?.orderCode);

    // case chưa thanh toán
    if (orderDetail?.orderStatus === OrderStatus.Confirmed) {
      try {
        // gọi api hủy đơn
        await this.omsService.cancelOrder({
          orderID: orderDetail?.orderID,
          orderCode: orderDetail?.orderCode,
          orderCancelChannel: +orderDetail?.orderChanel || Channel.Offline,
          reason: 1,
          reasonCancel: reasonNote,
          modifiedBy: modifiedBy,
          modifiedByName: modifiedByName,
          systemDate: moment().format(),
        });

        // unbook clinic ticket
        await this.orderUtilsService.cancelBook(this.shopCode, modifiedBy, ticketCode);

        for (const orderPayment of orderDetail?.orderPaymentCreate) {
          try {
            const payloadCancelVoucher: CancelVoucherByPaymentCodeDto = {
              fromSystem: 'RSA',
              orderCode: orderDetail?.orderCode,
              paymentCode: orderPayment?.paymentCode,
              phoneNumber: orderDetail?.phone,
              shopCode: orderDetail?.shopCode,
              reason: 'Huỷ voucher khi huỷ đơn hàng',
            };
            await this.voucherCoreService.cancelVoucherByPaymentCode(payloadCancelVoucher);

            const unbookVoucherDto: UnbookVoucherByPaymentCodeDto = {
              fromSystem: 'RSA',
              phoneNumber: orderDetail.phone,
              orderCode: orderDetail.orderCode,
              paymentCode: orderPayment?.paymentCode,
              shopCode: orderDetail.shopCode,
            };
            await this.voucherCoreService.unbookVoucherByPaymentCode(unbookVoucherDto);

            try {
              await this.loyaltyAppService.cancelExchangePoint(orderDetail?.custCode, {
                system: System,
                orderCode: orderDetail.orderCode,
                paymentCode: orderPayment?.paymentCode,
                phoneNumber: orderDetail?.phone,
                shopCode: orderDetail?.shopCode,
                reason: 'Huỷ voucher loyalty',
              });
            } catch (error) {
              Logger.error({
                fields: {
                  info: '[CancelExchangePointLoyalty] handle error',
                  errors: error,
                },
              });
            }
          } catch (error) {
            Logger.error({
              fields: {
                info: '[cancelVoucher] handle error',
                errors: error,
              },
            });
          }
        }

        // hủy payment
        // await this.paymentCoreService.cancelPaymentRequestCode(orderDetail?.paymentRequestCode);
      } catch (error) {}

      // hủy phiếu khám
      await this.examinationCoreService.changeStatusTicket({
        ticketCode: ticketCode,
        status: STATUS_TICKET.HUY,
        modifiedBy: modifiedBy,
      });

      return {
        isCancel: true, // hủy phiếu thành công
      };
    }

    throw new SystemException(
      {
        code: ErrorCode.RSA_CANCEL_TICKET_FOR_DOCTOR,
      },
      HttpStatus.FORBIDDEN,
    );
  }

  /**
   * @ticket FV-8350
   *      Docs: https://confluence.fptshop.com.vn/pages/viewpage.action?pageId=158250657
   * update tracking status
   */
  async updateTrackingStatus(payload: UpdateTrackingStatusRequestDto) {
    const trackingKey = `RSA:TRACKING:${payload?.ticketCode}`;
    if (!(await this.redis.set(trackingKey, Date.now(), 'EX', 5, 'NX'))) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_DOUBLE_CLICK,
          message: 'Vui lòng thử lại sau 5s!',
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }
    const { indication } = payload;
    const item = {
      sku: _.get(indication?.[0], 'sku', undefined),
      position: _.get(indication?.[0], 'position', undefined),
      lotNumber: _.get(indication?.[0], 'lotNumber', undefined),
      lotDate: _.get(indication?.[0], 'lotDate', undefined),
      productItemCode: _.get(indication?.[0], 'productItemCode', undefined),
    };
    // Chặn hoàn tất tiêm nếu đã trả hàng
    const getTicket = await this.examinationCoreService.getTicket({ ticketCode: payload?.ticketCode });
    const arrOrderDetailAttachmentCode = _.uniq(
      _.compact(
        JSONPath({
          path: '$.indications[*].orderDetailAttachmentCode',
          json: getTicket,
        }),
      ),
    );

    const getVacOrderDetails = await this.vacOrderCoreApiService.searchByOrderDetailAttachmentCodes(
      arrOrderDetailAttachmentCode,
    );

    const arrVacOrderInjections = _.uniq(
      _.compact(
        JSONPath({
          path: '$[*].orderInjectionDetails[*].orderInjections[*]',
          json: getVacOrderDetails,
        }),
      ),
    );
    getTicket?.indications?.forEach((indicationItem) => {
      const vacOrderFind = arrVacOrderInjections?.find(
        (orderInjection) => orderInjection?.orderDetailAttachmentCode === indicationItem?.orderDetailAttachmentCode,
      );

      let message = '';
      let exception: IError = null;

      switch (true) {
        case vacOrderFind?.status === VacOrderStatus.DA_TIEM:
          message = ErrorCode.getError(ErrorCode.RSA_BLOCK_DA_TIEM)?.replace('{sku}', indicationItem?.sku);
          exception = {
            code: ErrorCode.RSA_BLOCK_DA_TIEM,
            message: message,
            details: message,
            validationErrors: null,
          };
          throw new SystemException(exception, HttpStatus.FORBIDDEN);

        case vacOrderFind?.status === VacOrderStatus.DA_TRA_HANG:
          message = ErrorCode.getError(ErrorCode.RSA_BLOCK_DA_TRA_HANG)?.replace('{sku}', indicationItem?.sku);
          exception = {
            code: ErrorCode.RSA_BLOCK_DA_TRA_HANG,
            message: message,
            details: message,
            validationErrors: null,
          };
          throw new SystemException(exception, HttpStatus.FORBIDDEN);

        case !vacOrderFind:
          message = ErrorCode.getError(ErrorCode.RSA_BLOCK_FINISH_TICKET)?.replace('{sku}', indicationItem?.sku);
          exception = {
            code: ErrorCode.RSA_BLOCK_FINISH_TICKET,
            message: message,
            details: message,
            validationErrors: null,
          };
          throw new SystemException(exception, HttpStatus.FORBIDDEN);

        default:
          break;
      }
    });

    if (!_.isEmpty(item) && item?.sku) {
      let inventoryData: GetInventoryProductItemByShopSkuCustomResponse = null;
      try {
        inventoryData = await this.imsService.getInventoryProductItemByShopSku({
          skus: [item.sku],
          productItems: [item.productItemCode],
          shopCode: this.shopCode,
          whsCodes: [whsCodeWithShopCodeWhsType(this.shopCode, WHS_CODE_NORMAL)],
        });
      } catch (error) {}
      const productItem = _.maxBy(inventoryData?.items?.[0]?.productItems, 'unitLevel');
      if (!_.isEmpty(productItem)) {
        if (productItem?.quantity <= 0) {
          throw new SystemException(
            {
              code: ErrorCode.RSA_IMS_OUT_OF_STOCK,
              message: `Tồn kho Lô sản xuất ${item?.lotNumber}-${item?.lotDate} không đủ, vui lòng chọn lô sản xuất khác`,
            },
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    }

    const { ticketCode } = payload;
    const links = (await this.mappingIndicationWithLinkId(ticketCode)) || [];
    if (links && links?.length) {
      payload.indication = payload.indication.map((i) => {
        const currLink = links?.find((link) => link?.sku === i?.sku);
        return { ...i, linkId: currLink?.linkId || '' };
      });
    }

    // check valid barCode
    if (indication?.length) {
      const barCodes: verifyBarcodeDto[] = [];
      // gọi pim check vaccine đa liều dựa vào sku
      const skus = _.uniq(indication?.map((i) => i?.sku));
      let pimProducts = [];
      try {
        const { listProduct } = await this.pimAppService.getListProductBySku(skus, this.shopCode);
        pimProducts = [...listProduct];
      } catch (error) {}

      indication?.map((i) => {
        // nếu có thông tin pim thì đi find theo sku
        const productBySku = pimProducts?.length ? pimProducts?.find((p) => p?.sku === i?.sku) : null;
        // case là vaccine đa liều nhưng không truyền barcode => báo lỗi
        if (productBySku?.isMultiDose && !i?.barCode) {
          throw new SystemException(
            {
              code: ErrorCode.RSA_UPDATE_TRACKING_MULTIDOSE_VALID_BARCODE,
              message: ErrorCode.getError(ErrorCode.RSA_UPDATE_TRACKING_MULTIDOSE_VALID_BARCODE)
                ?.replace('{sku}', productBySku?.sku)
                ?.replace('{skuName}', productBySku?.name),
              details: ErrorCode.getError(ErrorCode.RSA_UPDATE_TRACKING_MULTIDOSE_VALID_BARCODE)
                ?.replace('{sku}', productBySku?.sku)
                ?.replace('{skuName}', productBySku?.name),
            },
            HttpStatus.FORBIDDEN,
          );
        } else if (productBySku?.isMultiDose && i?.barCode) {
          // case là vaccine đa liều , truyền barcode => push vào mảng để đi verify barCode từ OSR
          barCodes.push({
            barCode: i?.barCode,
            sku: i?.sku,
            shopCode: this.shopCode,
            unitCode: i?.unitCode,
            unitName: i?.unitName,
            quantity: 1,
          });
        }
      });

      // call OSR verify barcode
      if (barCodes?.length) {
        await this.osrService.verifyBarcode(barCodes);
      }
    }

    // FV-9610
    const listPerson = await this.familyService.getListPrimaryPerson([getTicket?.lcvId]);
    const { name } = listPerson?.[0];
    const listAttachmentCode = getTicket?.indications?.map(
      (itemIndication) => itemIndication?.orderDetailAttachmentCode,
    );

    const cashbackInfo = await this.handleCashBack(listAttachmentCode);
    const cashback = cashbackInfo?.cashback;
    const oDACWithPhones = cashbackInfo?.oDACWithPhones;

    // update tracking images
    if (payload?.images?.length) {
      await this.examinationCoreService.createTicketImage({
        createdBy: payload?.modifiedBy,
        ticketCode: payload?.ticketCode,
        images: payload?.images || [],
      });
    }

    const result = await this.examinationCoreService.updateTrackingStatus({
      ...payload,
      ...(cashback?.length && {
        cashbackPhoneCardInfo: cashback?.map((cashbackDetail) => {
          const orderDetailAttachmentCodes = oDACWithPhones.get(
            `${cashbackDetail?.phoneNumber}_${cashbackDetail?.orderVAC}`,
          );
          return {
            brandName: cashbackDetail?.brandName,
            brandUniqueId: cashbackDetail?.brandUniqueId,
            type: CashbackType.CASHBACK,
            orderDetailAttachmentCode: listAttachmentCode?.find((code) => orderDetailAttachmentCodes?.includes(code)),
          };
        }),
      }),
    });

    // đưa ticketCode vào queue
    // try {
    //   await Promise.all([
    //     this.vacCoreAssignJobService.addTicketToQueue({
    //       ticketId: payload?.ticketCode,
    //       monitorInMinutes: MONITOR_IN_MINUTES,
    //       shopCode: this.shopCode,
    //       customerName: name,
    //     }),
    //   ]);
    // } catch (error) {}
    return {
      item: {
        ...result,
        cashback: cashback?.length
          ? cashback.map((cashbackSuggest) => {
              return {
                phoneNumber: cashbackSuggest?.phoneNumber || '',
                custName: cashbackSuggest?.['custName'] || '',
                brandName: cashbackSuggest?.brandName || '',
                cashBackAmount: cashbackSuggest?.suggestAmount || 0,
              };
            })
          : [],
      },
    };
  }

  async updateInjectionRouteAndPosition(payload: UpdateInjectionRouteAndPositionDto) {
    return await this.examinationCoreService.updateInjectionRouteAndPosition(payload);
  }

  async mappingIndicationWithLinkId(ticketCode: string) {
    const ticket = await this.examinationCoreService.getTicket({ ticketCode });
    const orderDetailAttachmentCodes = _.map(ticket?.indications || [], 'orderDetailAttachmentCode');
    if (!orderDetailAttachmentCodes.length) {
      return [];
    }
    let rs: any = ticket?.indications || [];
    const { orders } = await this.omsService.getOrderByOrderAttachmentCode({
      listAttachmentCode: orderDetailAttachmentCodes,
    });

    const orderCodes = _.map(orders || [], 'orderCode');
    if (!orderCodes.length) {
      return [];
    }

    rs = rs.map((indication) => {
      const orderCode = orders.find((order) =>
        order?.details?.find((orderDetail) =>
          orderDetail?.detailAttachments?.find(
            (detailAttachments) =>
              detailAttachments?.orderDetailAttachmentCode === indication?.orderDetailAttachmentCode,
          ),
        ),
      );
      return { ...indication, orderCode: orderCode?.orderCode || '' };
    });

    const { items: scheduleRequests } = await this.rsaEcomService.getScheduleRequestByOrderCodes(orderCodes);

    if (!scheduleRequests || !scheduleRequests?.length) {
      return [];
    }

    rs = rs.map((indication) => {
      const link = scheduleRequests?.find((s) => s?.orderCode === indication?.orderCode);
      return { ...indication, linkId: link?.idLink };
    });

    return rs;
  }

  wordingErrorMessage(errorCode) {
    switch (errorCode) {
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10012:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10012);
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10013:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10013);
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10014:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10014);
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10016:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10016);
      case ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10017:
        return ErrorCode.getError(ErrorCode.PAYMENT_GATEWAY_CANCEL_ORDER_10017);
      default:
        return '';
    }
  }

  async onlyCancelTicket(payload: OnlyCancelTicketDto) {
    const orderData = await this.omsService.getOneOrder(payload?.orderCodeOld);

    // Chặn khong cho Hủy đơn nếu đã thanh toán. Payment check rule
    if (orderData?.orderStatus === OrderStatus.Confirmed || orderData?.orderStatus === OrderStatus.Completed) {
      const arrPaymentCode = _.uniq(
        _.compact(
          orderData.orderPaymentCreate.map((e) => {
            if (e.paymentType === PaymentType.THU) return e.paymentCode;
          }),
        ),
      );

      const cancelPayment = await this.paymentCoreService.cancelPaymentVaccine({
        paymentCodeWithShopCodes: arrPaymentCode?.map((entry) => ({
          paymentCode: entry,
          shopCode: orderData?.shopCode,
        })),
        cancelBy: payload?.modifiedBy,
        fromSystem: 'BE RSA',
        phoneNumber: orderData?.phone,
        orderCode: payload?.orderCodeOld,
        referenceId: payload?.ticketCode || '',
      });

      const cancelPaymentFailure = cancelPayment?.processedPayments?.filter((process) => !process?.isCancelled);

      if (cancelPaymentFailure?.length) {
        const exception: IError = {
          code: cancelPaymentFailure?.at(0)?.errorCode,
          message:
            this.wordingErrorMessage(cancelPaymentFailure?.at(0)?.errorCode) || cancelPaymentFailure?.at(0)?.message,
          details:
            this.wordingErrorMessage(cancelPaymentFailure?.at(0)?.errorCode) || cancelPaymentFailure?.at(0)?.message,
          validationErrors: cancelPaymentFailure?.map((error) => ({
            message: this.wordingErrorMessage(error?.errorCode) || error?.message,
          })),
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }
    }

    return this.examinationCoreService.changeStatusTicket({
      ticketCode: payload?.ticketCode,
      status: EnmStatusTicket.HUY,
      modifiedBy: payload?.modifiedBy,
      note: payload?.note,
    });
  }

  /**
   * @description lấy danh sách đơn gộp nhóm
   */
  async getOrderInfoEcomService(body: GetOrdersInfoDto) {
    if (OrderChannels.RSA_AFFILIATE.includes(this.orderChannel)) {
      if (!_.isEmpty(body?.keyWord)) {
        body.shopCodeCreate = '';
      }
    }

    if (body.ecomDisplay?.find((_item) => +_item === EcomDisplay.AtOnline)) {
      body.ecomDisplay.push(EcomDisplay.Save.toString());
    }

    const { totalCount, items } = await this.journeyCoreService.getOrderInfoEcom({ ...body });
    return {
      totalCount: totalCount,
      items: items || [],
    };
  }

  /**
   * @description lấy danh sách đơn gộp nhóm
   */
  async getOrderInfoAffiliateService(body: GetOrdersInfoDto) {
    if (!_.isEmpty(body?.keyWord)) {
      body.shopCodeCreate = '';
    }
    const { totalCount, items } = await this.journeyCoreService.getOrderInfoAffiliate({ ...body });
    return {
      totalCount: totalCount,
      items: items || [],
    };
  }

  async getTicketsRedis(payload: GetTicketRedisTVDto): Promise<{ totalCount: number; items: TicketResponseDto[] }> {
    const { skipCount = 0, maxResultCount = 10 } = payload;
    const res = await this.camundaSpeakerApiService.getTicketsRedis(payload);
    const items = res?.slice(skipCount, skipCount + maxResultCount) || [];
    return {
      totalCount: res?.length || 0,
      items,
    };
  }
}
