import { Module } from '@nestjs/common';
import { PaymentGatewayModule } from 'vac-nest-payment-gateway';
import { PaymentService } from './services/payment.service';
import { PaymentController } from './controllers/payment.controller';
import { OsrModule } from 'vac-nest-osr';
import { RsaBeV2Module } from 'vac-nest-rsa-be-v2';
import { OMSModule } from 'vac-nest-oms';

@Module({
  imports: [PaymentGatewayModule, OMSModule, RsaBeV2Module],
  controllers: [PaymentController],
  providers: [PaymentService],
})
export class PaymentModule {}
