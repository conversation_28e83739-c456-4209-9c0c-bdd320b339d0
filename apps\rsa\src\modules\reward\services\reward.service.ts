import { Injectable, Inject, NotFoundException, BadRequestException } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import e, { Request } from 'express';
import {
  RewardTransactionResponseDto,
  RewardApiService,
  RewardTransactionFilterRequestDto,
  PagedRewardTransactionResponseDto,
  RewardTransactionDetailDto,
  RewardTypeEnum,
} from '@frt/nestjs-api/dist/reward-api';
import {
  RewardPointRuleCalculatorByQuantityRequest,
  RewardPointRuleApiService,
} from '@frt/nestjs-api/dist/reward-point-rule-api/src';
import { RewardPointRuleCalculatorResDtoList } from '../dto/RewardPointRuleCalculatorResDto';

@Injectable()
export class RewardService {
  shopCode: string;

  constructor(
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly rewardApiService: RewardApiService,
    private readonly rewardPointRuleApiService: RewardPointRuleApiService,
  ) {
    this.shopCode = (this.req.headers?.['shop-code'] as string) || '';
  }

  async searchRewardTransactions(
    params: RewardTransactionFilterRequestDto,
  ): Promise<PagedRewardTransactionResponseDto> {
    try {
      const result = await this.rewardApiService.searchRewardTransactions(params);

      if (result?.items) {
        result.items = result.items.map((item: RewardTransactionResponseDto) => {
          const totalRewardValueByType: { rewardType: number; totalRewardValue: number }[] = [];
          if (Array.isArray(item.details)) {
            const rewardTypeMap = new Map<number, number>();
            item.details.forEach((detail: RewardTransactionDetailDto) => {
              const type = detail.rewardType;
              const value = detail.rewardValue || 0;
              rewardTypeMap.set(type, (rewardTypeMap.get(type) || 0) + value);
            });
            rewardTypeMap.forEach((totalRewardValue, rewardType) => {
              totalRewardValueByType.push({ rewardType, totalRewardValue });
            });
          }
          return {
            ...item,
            totalRewardValueByType,
          };
        });
      }

      return result;
    } catch (error) {
      throw error;
    }
  }

  async calculateRewardTransactionInCartBySku(
    payload: RewardPointRuleCalculatorByQuantityRequest[],
  ): Promise<RewardPointRuleCalculatorResDtoList> {
    const rewardPointEstimation = await this.rewardPointRuleApiService.calculateRewardPointRuleByQuantity(payload);
    const items: RewardPointRuleCalculatorResDtoList['items'] = [];
    const rewardTypeTotals = new Map<number, number>();

    if (rewardPointEstimation?.length) {
      for (const item of rewardPointEstimation) {
        // Tính tổng điểm theo rewardType
        const currentTotal = rewardTypeTotals.get(item?.rewardType) || 0;
        rewardTypeTotals.set(item?.rewardType, currentTotal + (item?.rewardValue || 0));

        items.push({
          ...item,
          displayRewardPoint: {
            rewardType: item?.rewardType,
            rewardValue: item?.rewardValue,
            displayValue:
              item?.rewardType === RewardTypeEnum.MONEY
                ? `H${((item?.rewardValue || 0) / 1000).toLocaleString()}`
                : `D${item?.rewardValue || 0}`,
          },
        });
      }
    }

    // Chuyển đổi Map thành array
    const totalRewardPointByType = Array.from(rewardTypeTotals.entries()).map(([rewardType, totalRewardValue]) => ({
      rewardType,
      rewardValue: totalRewardValue || 0,
      displayValue:
        rewardType === RewardTypeEnum.MONEY
          ? `H${((totalRewardValue || 0) / 1000).toLocaleString()}`
          : `D${totalRewardValue || 0}`,
    }));

    return {
      items,
      totalRewardPointByType,
    };
  }
}
