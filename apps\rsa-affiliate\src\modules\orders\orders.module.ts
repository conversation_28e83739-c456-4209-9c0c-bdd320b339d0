import { OrdersModule as OrdersRsaModule } from 'modules/modules/modules/orders/orders.module';
import { FamilyModule } from 'vac-nest-family';
import { InsideModule } from 'vac-nest-inside';
import { OMSModule } from 'vac-nest-oms';
import { OsrModule } from 'vac-nest-osr';
import { PaymentGatewayModule } from 'vac-nest-payment-gateway';
import { RegimenModule } from 'vac-nest-regimen';
import { RSAIntegrationModule } from 'vac-nest-rsa-integration';

import { Module } from '@nestjs/common';
import { RedisService } from '@shared';

import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';
import { CartAppModule } from 'vac-nest-cart-app';

@Module({
  imports: [
    OrdersRsaModule,
    OMSModule,
    InsideModule,
    PaymentGatewayModule,
    OsrModule,
    RSAIntegrationModule,
    RegimenModule,
    FamilyModule,
    CartAppModule,
  ],
  controllers: [OrdersController],
  providers: [OrdersService, RedisService],
  exports: [OrdersService],
})
export class OrdersModule {}
