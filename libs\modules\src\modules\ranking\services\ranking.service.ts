import { CreateFamilyPackageResponse } from '@libs/modules/customers/dto';
import { CustomersFamilyPackageService } from '@libs/modules/customers/services/customers-family-package.service';
import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { CartAppService } from 'vac-nest-cart-app';
import { FamilyService } from 'vac-nest-family';
import { FamilyPackageService } from 'vac-nest-family-package';
import { FamilyRuleService } from 'vac-nest-family-rule';
import {
  ListPersonAttributeItemRes,
  SuggestRankingDto,
  SuggestRankingForCartPayload,
  SuggestRankingRes,
  TransfromRanking,
} from '../dto';

@Injectable()
export class RankingService {
  constructor(
    private readonly familyService: FamilyService,
    private readonly familyRuleService: FamilyRuleService,
    private readonly familyPackageService: FamilyPackageService,
    private readonly customersFamilyPackageService: CustomersFamilyPackageService,
  ) {}

  async suggestRanking(suggestRankingdto: SuggestRankingDto): Promise<SuggestRankingRes> {
    // await this.familyRuleService.checkExistPersonInGroup({
    //   lcvId: suggestRankingdto?.groupPersonJunction?.map((e) => e.lcvId),
    // });

    const lcvIds = suggestRankingdto?.groupPersonJunctions?.map((e) => e?.lcvId);

    // Lấy thông tin suggest ranking từ family rule
    const suggestRankingData = await this.familyRuleService.suggestByListPerson({
      lcvId: lcvIds,
      groupTypeCode: suggestRankingdto?.groupTypeCode || 0,
      groupTypeName: suggestRankingdto?.groupTypeName || '',
    });
    // Lấy thông tin person từ family
    const persons = await this.familyService.getManyByLcvId({ lcvId: lcvIds });

    // Gắng thông tin ranking vào response
    const suggestRankingRes: SuggestRankingRes = {
      ...suggestRankingdto,
      ranking: plainToInstance(TransfromRanking, suggestRankingData, {
        excludeExtraneousValues: true,
        exposeUnsetFields: false,
      }),
    };

    // Trả ra thông tin còn thiếu cho FE
    suggestRankingRes?.groupPersonJunctions?.forEach((person) => {
      const personFind = persons?.find((p) => p?.lcvId === person.lcvId);
      const listPersonAttributeItem: ListPersonAttributeItemRes[] = [];
      suggestRankingData?.listPersonAttribute?.forEach((p) => {
        if (p?.lcvId === person?.lcvId) {
          listPersonAttributeItem?.push({ attributeCode: p?.attributeCode, attributeValue: p?.attributeValue });
        }
      });

      person.name = personFind?.name;
      person.phoneNumber = personFind?.phoneNumber;
      person.personId = personFind?.id;
      person.listPersonAttribute = listPersonAttributeItem;
    });

    return suggestRankingRes;
  }

  async suggestRankingForCart(payload: SuggestRankingForCartPayload): Promise<CreateFamilyPackageResponse> {
    const { lcvId, ranking } = payload;

    const res = await this.familyPackageService.getGroupFamilyPackage({
      lcvIds: [lcvId],
    });

    const detailGroup = await this.customersFamilyPackageService.mapInfoDetailGroup({ ...res?.at(0), code: '' });

    detailGroup.groupPersonJunctions?.forEach((personJuntion) => {
      personJuntion['isAccumulated'] = !ranking?.lcvIdsForNextRank?.includes(personJuntion?.lcvId);
      const personAttributeInCart = ranking?.listPersonAttribute?.filter(
        (attribute) => attribute?.lcvId === personJuntion?.lcvId,
      );
      personJuntion.personAttribute = personAttributeInCart || personJuntion.personAttribute;
    });

    detailGroup.groupAttribute = payload?.ranking?.groupAttribute || detailGroup?.groupAttribute;

    return {
      ...detailGroup,
      message: ranking?.message,
      code: res?.at(0)?.code,
    };
  }
}
