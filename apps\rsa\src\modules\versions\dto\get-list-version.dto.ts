import { ApiProperty } from '@nestjs/swagger';
import { Version } from '../entities/version.entity';
import { Expose, Transform, Type } from 'class-transformer';

export class GetListVersionDto {
  @ApiProperty({ required: false })
  @Expose()
  @Type(() => Number)
  type: number;

  @ApiProperty({ required: false })
  @Expose()
  @Type(() => Number)
  status: number;

  @ApiProperty({ required: false })
  @Expose()
  @Type(() => Number)
  @Transform(({ value }) => value || 1)
  page: number;

  @ApiProperty({ required: false })
  @Expose()
  @Type(() => Number)
  @Transform(({ value }) => value || 10)
  perPage: number;
}

export class GetListVersionResponse {
  versions: Version[];
}
