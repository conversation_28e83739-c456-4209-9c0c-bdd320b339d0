import { Logger } from '@nestjs/common';
import _ from 'lodash';
import { GetEvaluationRequestRes, ImageERRes } from 'vac-nest-examination';
import { FamilyService, GetPersonByIdRes } from 'vac-nest-family';
import { GetAgeRangeResponseDto, RegimenItem, RegimenService } from 'vac-nest-regimen';
import { concurrentPromise, concurrentPromiseThrowError } from '../../../../../shared/src';
import { FilesService } from '../../files/services/files.service';
import { AddPropertiesDto } from '../dto/add-properties.dto';
import { JSONPath } from 'jsonpath-plus';
import { calculateTimeDifference } from 'vac-commons';

export abstract class EvaluationRequestAdapter {
  private readonly logger = new Logger(EvaluationRequestAdapter.name);

  constructor(
    protected readonly regimenService: RegimenService,
    protected readonly familyService: FamilyService,
    protected readonly filesService: FilesService,
  ) {}

  async addPropertiesToEvaluationRequest<
    T extends GetEvaluationRequestRes | Array<GetEvaluationRequestRes>,
    K extends keyof AddPropertiesDto,
  >(evaluationRequest: T, keys: readonly K[]): Promise<T> {
    const evaluationRequestDeepClone: T = JSON.parse(JSON.stringify(evaluationRequest));
    const regimenIds: Array<string> = JSONPath({
      json: evaluationRequestDeepClone,
      path: '$..details[*].regimenId',
    });
    const lcvIds: Array<string> = JSONPath({
      json: evaluationRequestDeepClone,
      path: '$..lcvId',
    });

    const promises: [Promise<RegimenItem[]>, Promise<GetPersonByIdRes[]>, Promise<Array<GetAgeRangeResponseDto>>] = [
      regimenIds?.length && keys.includes('regimen' as K)
        ? this.regimenService.getRegimenByIds({
            regimenIds: _.flattenDeep(_.uniq(regimenIds)),
          })
        : new Promise((resolve) => resolve([])),
      lcvIds?.length && keys.includes('family' as K)
        ? this.familyService.getManyByLcvId({ lcvId: _.flattenDeep(_.uniq(lcvIds)) })
        : new Promise((resolve) => resolve([])),
      this.regimenService.getAgeRanges(),
    ];

    const [regimens, families, ageRanges] = await concurrentPromise(...promises);

    const mapperRes = async (evaluationRequestEntry: GetEvaluationRequestRes) => {
      evaluationRequestEntry?.details?.forEach((detail) => {
        const regimen = regimens?.find((regimenEntry) => regimenEntry.id === detail.regimenId);
        detail['regimen'] = regimen;
      });
      const familyFind = families?.find((family) => family.lcvId === evaluationRequestEntry.lcvId);
      if (familyFind?.familyProfileDetails?.length) {
        familyFind['familyProfileDetails'] = familyFind['familyProfileDetails']?.filter((e) => e.isHost === true);
      }
      evaluationRequestEntry['family'] = familyFind;
      for (const imageEntry of evaluationRequestEntry['images']) {
        const { links } = await this.filesService.getS3Presign({ urls: [imageEntry.image] });
        imageEntry['url'] = links[0];
      }
      if (evaluationRequestEntry['family']) {
        evaluationRequestEntry['family']['customerAge'] = calculateTimeDifference(
          evaluationRequestEntry?.['family']?.dateOfBirth,
          evaluationRequestEntry?.['family']?.from,
          evaluationRequestEntry?.['family']?.to,
          ageRanges,
          evaluationRequestEntry?.['family']?.ageUnitCode,
        );
      }
      return evaluationRequestEntry;
    };

    if (Array.isArray(evaluationRequest)) {
      const evaluationRequestArray = evaluationRequestDeepClone as Array<GetEvaluationRequestRes>;
      for (const evaluationRequestEntry of evaluationRequestArray) {
        await mapperRes(evaluationRequestEntry);
      }
    } else {
      const evaluationRequestEntry = evaluationRequestDeepClone as GetEvaluationRequestRes;
      await mapperRes(evaluationRequestEntry);
    }
    return evaluationRequestDeepClone;
  }
}
