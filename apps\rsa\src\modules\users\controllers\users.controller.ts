import { Controller, Get, Param, Query } from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON><PERSON>, ApiBearerAuth, ApiExtraModels, ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { GetListShopByEmployeeRes, InsideService } from 'vac-nest-inside';
import { ClassResponse } from '@shared';
import { CustomHeaders, generalSchema } from '@shared';
import { GetEmployeeWorkingInfoDto, GetListEmployeeShiftDateDto, GetListEmployeeShiftDateRes } from '../dto';
import { UsersService } from '../services/users.service';

@Controller({ path: 'users', version: '1' })
@ApiTags('Users')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(ClassResponse, GetListShopByEmployeeRes, GetListEmployeeShiftDateRes)
@CustomHeaders()
export class UsersController {
  constructor(private readonly insideService: InsideService, private readonly usersService: UsersService) {}

  @Get('get-shop-by-employee/:employeeCode')
  @ApiOperation({
    summary: 'L<PERSON>y danh sách shop bằng employee',
  })
  @ApiOkResponse({
    schema: generalSchema(GetListShopByEmployeeRes, 'object'),
  })
  getListShopByEmployee(@Param('employeeCode') employeeCode: string) {
    return this.insideService.getListShopByEmployee(employeeCode);
  }

  @Get('get-infor-employee-shift-date')
  @ApiOperation({
    summary: 'Lấy danh sách nhân viên theo ngày làm việc',
  })
  @ApiOkResponse({
    schema: generalSchema(GetListEmployeeShiftDateRes, 'object'),
  })
  getInforEmployeeShiftDate(@Query() getListEmployeeShiftDateDto: GetListEmployeeShiftDateDto) {
    return this.usersService.getInforEmployeeShiftDate(getListEmployeeShiftDateDto);
  }

  @Get('get-employee-working-info')
  @ApiOperation({
    summary: 'Lấy danh sách nhân viên v2',
  })
  @ApiOkResponse({
    schema: generalSchema(GetListEmployeeShiftDateRes, 'object'),
  })
  getListEmployeeWorkingInfo(@Query() getListEmployeeShiftDateDto: GetEmployeeWorkingInfoDto) {
    return this.usersService.getListEmployeeWorkingInfo(getListEmployeeShiftDateDto);
  }
}
