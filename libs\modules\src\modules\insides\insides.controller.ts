import { Controller, Get, HttpCode, HttpStatus, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, generalSchema, Public } from '@shared';
import {
  CheckEmployeeShowPopupDto,
  CheckEmployeeShowPopupQueryParamsDto,
  GetEmployeeByShopCodeLCDto,
  GetListEmployeeByShopCodeRes,
  GetListShopByEmployeeRes,
  GetUserShopInfoDto,
} from 'vac-nest-inside';
import { InsidesService } from './insides.service';
import { getShopVaccineByShopLCDto, resGetShopVTByEmployee, resShopVaccineByShopLC } from './dto/insides.dto';

@Controller({ path: 'insides', version: '1' })
@ApiTags('Insides')
@ApiBearerAuth()
@ApiExtraModels(
  ClassResponse,
  GetListShopByEmployeeRes,
  GetListEmployeeByShopCodeRes,
  resGetShopVTByEmployee,
  resShopVaccineByShopLC,
  CheckEmployeeShowPopupDto,
)
export class InsidesController {
  constructor(private readonly insidesService: InsidesService) {}

  @Get('employee-sale-ecom')
  @Public()
  findAll() {
    return this.insidesService.findAll();
  }

  @Get('get-shop-by-employeeCode-lc')
  @Public()
  @ApiOperation({
    summary: 'Thông tin shop theo nhân viên',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin shop theo nhân viên',
    schema: generalSchema(resGetShopVTByEmployee, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  getEmployeeLC(@Query() params: GetUserShopInfoDto) {
    return this.insidesService.getEmployeeInfoLC(params);
  }

  @Get('get-employee-by-shopCode-lc')
  @Public()
  @ApiOperation({
    summary: 'Thông tin nhân viên theo shop',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin nhân viên theo shop',
    schema: generalSchema(GetListEmployeeByShopCodeRes, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  getEmployeeByShopCodeLC(@Query() params: GetEmployeeByShopCodeLCDto) {
    return this.insidesService.getEmployeeByShopCodeLC(params);
  }

  @Get('get-shop-vaccine-by-shop-code-lc')
  @Public()
  @ApiOperation({
    summary: 'Thông tin shop vaccine',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin shop vaccine',
    schema: generalSchema(resShopVaccineByShopLC, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  getShopVaccineByShopLC(@Query() params: getShopVaccineByShopLCDto) {
    return this.insidesService.getShopVaccineByShopCodeLC(params);
  }

  @Get('check-employee-show-popup')
  @Public()
  @ApiOperation({
    summary: 'Thông tin popup của employee',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin popup của employee',
    schema: generalSchema(CheckEmployeeShowPopupDto, 'array'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    type: ClassErrorResponse,
  })
  checkEmployeeShowPopup(@Query() params: CheckEmployeeShowPopupQueryParamsDto) {
    return this.insidesService.checkEmployeeShowPopup(params);
  }
}
