import { Controller, Get, Post, Body, Query, Param, HttpCode, HttpStatus } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiOkResponse,
  getSchemaPath,
  ApiExtraModels,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { RewardService } from '../services/reward.service';

import { RewardTransactionFilterRequestDto } from '@frt/nestjs-api/dist/reward-api';
import { RewardTransactionPagedResponseDto } from '../dto';
import {
  RewardPointRuleCalculatorRequestDto,
  RewardPointRuleCalculatorResDtoList,
} from '../dto/RewardPointRuleCalculatorResDto';

@ApiTags('Reward')
@Controller({ path: 'reward', version: '1' })
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(RewardTransactionPagedResponseDto, RewardPointRuleCalculatorResDtoList)
export class RewardController {
  constructor(private readonly rewardService: RewardService) {}

  @Post('search-reward-transactions')
  @ApiOperation({ summary: 'Tìm kiếm giao dịch thưởng với bộ lọc' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thành công',
    schema: {
      type: 'object',
      properties: {
        data: {
          $ref: getSchemaPath(RewardTransactionPagedResponseDto),
        },
      },
    },
  })
  searchRewardTransactions(@Body() params: RewardTransactionFilterRequestDto) {
    return this.rewardService.searchRewardTransactions(params);
  }

  @Post('calculate-reward-point')
  @ApiOperation({ summary: 'Tính toán điểm thưởng' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thành công',
    schema: {
      type: 'object',
      properties: {
        data: {
          $ref: getSchemaPath(RewardPointRuleCalculatorResDtoList),
        },
      },
    },
  })
  calculateRewardPoint(@Body() payload: RewardPointRuleCalculatorRequestDto) {
    return this.rewardService.calculateRewardTransactionInCartBySku(payload.items);
  }
}
