import { Modu<PERSON> } from '@nestjs/common';
import { CommonController } from './controllers/common.controller';
import { CommonService } from './services/common.service';
import { MonitorCoreModule } from 'vac-nest-monitor';
import { CustomerCoreModule } from 'vac-nest-customer-core';
import { OrdersModule } from 'modules/modules/modules/orders/orders.module';

@Module({
  imports: [MonitorCoreModule, CustomerCoreModule, OrdersModule],
  controllers: [CommonController],
  providers: [CommonService],
})
export class CommonModule {}
