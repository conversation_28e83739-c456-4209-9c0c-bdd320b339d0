import { Module } from '@nestjs/common';
import { <PERSON>ward<PERSON>ontroller } from './controllers/reward.controller';
import { RewardService } from './services/reward.service';
import { RewardApiModule } from '@frt/nestjs-api/dist/reward-api';
import { RewardPointRuleApiModule } from '@frt/nestjs-api';

@Module({
  imports: [RewardApiModule, RewardPointRuleApiModule],
  controllers: [RewardController],
  providers: [RewardService],
  exports: [RewardService],
})
export class RewardModule {}
