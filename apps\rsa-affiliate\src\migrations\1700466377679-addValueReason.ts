import { MigrationInterface, QueryRunner } from 'typeorm';

export class addValueReason1700466377679 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '1', '1', 'Bị hủy do yêu cầu của khách hàng')`,
    );
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '1', '1', 'Bị hủy do sai lệch hàng tồn kho')`,
    );
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '1', '1', 'Bị hủy do thay đổi phương thức giao hàng')`,
    );
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '1', '1', '<PERSON>ị hủy do thông tin không chính xác về đơn đặt hàng')`,
    );
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '2', '1', 'Khách có nhu cầu huỷ cọc chi tiền')`,
    );
    await queryRunner.query(
      `INSERT INTO reasons ("type", "status", "name") VALUES( '2', '1', 'Thay đổi thông tin chi tiết sản phẩm')`,
    );
    await queryRunner.query(`INSERT INTO reasons ("type", "status", "name") VALUES( '2', '1', 'Lý do khác')`);
  }
  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('');
  }
}
