import { MigrationInterface, QueryRunner } from 'typeorm';

export class TransferInjection1712132896176 implements MigrationInterface {
  name = 'TransferInjection1712132896176';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "transfer_injection" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "shopCode" character varying NOT NULL, "personId" character varying NOT NULL, "sku" character varying NOT NULL, "lcvIdOld" character varying NOT NULL, "lcvIdNew" character varying NOT NULL, "updatedBy" character varying NOT NULL, "isSuccess" boolean NOT NULL, CONSTRAINT "PK_a4ca00bb57f08d94a205ad02851" PRIMARY KEY ("id")); COMMENT ON COLUMN "transfer_injection"."personId" IS 'PersonId người nhận'; COMMENT ON COLUMN "transfer_injection"."sku" IS 'sku'; COMMENT ON COLUMN "transfer_injection"."lcvIdOld" IS 'lcvid người gửi'; COMMENT ON COLUMN "transfer_injection"."lcvIdNew" IS 'lcvid người nhận'; COMMENT ON COLUMN "transfer_injection"."isSuccess" IS 'false: error'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "transfer_injection"`);
  }
}
