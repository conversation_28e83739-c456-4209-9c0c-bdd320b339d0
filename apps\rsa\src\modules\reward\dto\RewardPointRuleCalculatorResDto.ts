import {
  RewardPointRuleCalculatorByQuantityRequest,
  RewardPointRuleCalculatorByQuantityResponseItem,
} from '@frt/nestjs-api';
import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class DisplayRewardPointByType {
  @ApiProperty({ description: 'Type of the reward', example: 1 })
  @Expose()
  rewardType: number;

  @ApiProperty({ description: 'Value of the reward', example: 1000 })
  @Expose()
  rewardValue: number;

  @ApiProperty({ description: 'Display value for the reward', example: 'H1' })
  @Expose()
  displayValue: string;
}

export class RewardPointRuleCalculatorResDto extends RewardPointRuleCalculatorByQuantityResponseItem {
  @ApiProperty({ type: DisplayRewardPointByType, description: 'List of display reward points by type' })
  @Expose()
  @Type(() => DisplayRewardPointByType)
  displayRewardPoint: DisplayRewardPointByType;
}

export class RewardPointRuleCalculatorResDtoList {
  @ApiProperty({
    type: RewardPointRuleCalculatorResDto,
    isArray: true,
    description: 'List of reward point rule calculator responses',
  })
  @Expose()
  @Type(() => RewardPointRuleCalculatorResDto)
  items: RewardPointRuleCalculatorResDto[];

  @ApiProperty({
    type: DisplayRewardPointByType,
    description: 'Total reward points grouped by type, e.g. { "1": 1000, "2": 500 }',
    isArray: true,
  })
  @Expose()
  totalRewardPointByType: DisplayRewardPointByType[];
}

export class RewardPointRuleCalculatorRequestDto {
  @ApiProperty({
    description: 'List of reward point rule calculator requests',
    type: RewardPointRuleCalculatorByQuantityRequest,
    isArray: true,
  })
  @Expose()
  @Type(() => RewardPointRuleCalculatorByQuantityRequest)
  items: RewardPointRuleCalculatorByQuantityRequest[];
}
