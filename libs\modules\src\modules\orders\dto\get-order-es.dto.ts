import { ApiProperty, PickType } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { OrdersInfo } from 'vac-nest-journey';
import { CreateOrderRes, DetailLib } from 'vac-nest-oms';

export class PaymentInfoForClient {
  @ApiProperty({ description: 'Tổng tiền sau giảm giá | Tổng cộng' })
  @Expose()
  totalBill: number;

  @ApiProperty({ description: 'Tổng tiền đơn hàng | Tiền sản phẩm' })
  @Expose()
  total: number;

  @ApiProperty({ description: 'Tổng tiền giảm giá | Giảm giá trực tiếp' })
  @Expose()
  totalDiscount: number;

  @ApiProperty({ description: 'Tổng tiền giảm giá voucher | Giảm giá voucher' })
  @Expose()
  totalDiscountVoucher: number;

  @ApiProperty({ description: 'T<PERSON>t cả tiền giảm giá bao gồm voucher | Tổng giảm giá' })
  @Expose()
  totalDiscountAll: number;

  @ApiProperty({ description: 'Phí lưu trữ' })
  @Expose()
  serviceFee: number;

  @ApiProperty({ description: 'Tổng tiền khách đã trả' })
  @Expose()
  depositedAmount: number;

  @ApiProperty({ description: 'Số tiền cần trả thêm' })
  @Expose()
  remainingAmount: number;

  @ApiProperty({ description: 'Tồng tiền sau giảm giá và voucher' })
  @Expose()
  @IsOptional()
  totalBillAfterVoucher?: number;

  @ApiProperty({ description: 'Tổng tiền đặt cọc' })
  @Expose()
  @IsOptional()
  totalDeposit?: number;
}

export class PaymentMethodDto {
  @ApiProperty({ description: 'Type thu/chi : 1 - thu, 2 - chi' })
  @Expose()
  @IsOptional()
  type?: number;

  @ApiProperty({ description: 'Ngày thanh toán' })
  @Expose()
  @IsOptional()
  dateOfPayment?: Date | string;

  @ApiProperty({ description: 'Số tiền' })
  @Expose()
  @IsOptional()
  amount?: number;

  @ApiProperty({ description: 'Phương thức thanh toán' })
  @Expose()
  @IsOptional()
  paymentMethod?: string;

  @ApiProperty({ description: 'Chi tiết thanh toán' })
  @Expose()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Nhân viên thu/chi' })
  @Expose()
  @IsOptional()
  employeeName?: string;

  @ApiProperty({ description: 'Nhân viên thu/chi' })
  @Expose()
  @IsOptional()
  employeeCode?: string;

  @ApiProperty({ description: 'Hình ảnh' })
  @Expose()
  @IsOptional()
  url?: string;
}

export class GetOrderEsRes extends CreateOrderRes {
  @ApiProperty()
  @Expose()
  paymentInfo: PaymentInfoForClient;

  @ApiProperty()
  @Expose()
  totalDiscountAdjustment?: number;

  @ApiProperty()
  @Expose()
  totalDirectDiscount?: number;

  @ApiProperty()
  @Expose()
  isCompleted: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isHasContract?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isHasInvoice?: boolean;

  @ApiProperty({ type: PaymentMethodDto, isArray: true })
  @Expose()
  @IsOptional()
  paymentMethods?: PaymentMethodDto[];

  @ApiProperty({ description: 'Trường Pre Order để tiếp tục đơn' })
  @Expose()
  isAbleToContinuePreOrder?: boolean;

  @ApiProperty({ description: 'Phase PreOrder' })
  @Expose()
  preOrderPhaseId?: string;

  @ApiProperty({ description: 'Flag xử lý đơn vệ tinh' })
  @Expose()
  isAbleToContinueDepositOrder?: boolean;

  @ApiProperty({})
  @Expose()
  immediateVaccines?: any[];

  @ApiProperty({})
  @Expose()
  subsequentVaccines?: any[];

  @ApiProperty({})
  @Expose()
  appointmentSchedulesOrder?: any[];
}
export class GetGiftDeliveryRes extends PickType(OrdersInfo, [
  'giftDeliveryPerson',
  'giftDeliveryStatus',
  'giftDeliveryTime',
]) {
  @ApiProperty({ isArray: true, type: DetailLib })
  @IsOptional()
  @Expose()
  arrGift?: Partial<DetailLib>[];
}

export class GetGiftByOrderFromEcom extends DetailLib {
  @ApiProperty()
  @IsOptional()
  @Expose()
  ticketCode?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  journeyId?: string;
}

export class GetGiftByOrderFromEcomRes {
  @ApiProperty({ isArray: true, type: GetGiftByOrderFromEcom })
  @IsOptional()
  @Expose()
  arrGift?: Partial<GetGiftByOrderFromEcom>[];
}
