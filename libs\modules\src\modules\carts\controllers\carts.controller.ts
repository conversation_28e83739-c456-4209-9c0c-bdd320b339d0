import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Patch, Post, Put, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ClassErrorResponse, ClassResponse, CustomHeaders, generalSchema } from '@shared';
import {
  AddDiscountAdjustmentDto,
  AddExtraDataDto,
  AddListCartItemDto,
  AdjustOrderLibDto,
  GetCartConfirmLibResponse,
  GetCartLibDto,
  GiftReplaceCartAppDto as GiftReplaceDtoLib,
  GiftReplacePromotionCartTotalDto,
  ReqPromotionCartTotalDto,
  ResPromotionCartTotal,
  SelectedPromotionCartTotalDto,
  SelectedPromotionDto,
  SkuReplaceDto,
  UpdateDobDto,
  UpdateListCartItemRedisDto,
} from 'vac-nest-cart-app';
import {
  GetMultipleGiftReplaceDto,
  GetMultipleGiftReplaceResponseDto,
  GiftReplaceDto,
  GiftReplaceResponse,
  PromotionService,
} from 'vac-nest-promotion';
import {
  AddCartItemDto,
  CheckRuleAddCartDto,
  ConfirmPushingCartDto,
  ConfirmPushingCartRes,
  DeleteCartItemDto,
  GenericSessionDto,
  GenericSessionResponse,
  GetCartDto,
  GetCartResponse,
  GetConfirmedCartParam,
  GetSessionByOrderCodeTicketCodeDto,
  GetSessionByOrderCodeTicketCodeRes,
  GetTotalBillDto,
  GetTotalBillRes,
  MergeCartToAnonymous,
  MergeCartToCustomer,
  OrderPreviewResDto,
  UpdateCartItemDto,
  UpdateDobParam,
} from '../dto';
import { AddVoucherRSADto } from '../dto/add-voucher-rsa.dto';
import { CartsService } from '../services/carts.service';

@Controller({ path: 'cart', version: '1' })
@ApiTags('Cart')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(
  ClassResponse,
  GenericSessionResponse,
  GetCartResponse,
  ConfirmPushingCartRes,
  OrderPreviewResDto,
  GiftReplaceResponse,
  GetSessionByOrderCodeTicketCodeRes,
  GetMultipleGiftReplaceDto,
  GetMultipleGiftReplaceResponseDto,
  GetTotalBillRes,
  ResPromotionCartTotal,
)
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
@CustomHeaders()
export class CartsController {
  constructor(private readonly cartsService: CartsService, private readonly promotionService: PromotionService) {}

  @Post('generic-session')
  @ApiOperation({
    summary: 'Tạo session cho giỏ hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Danh sách sản phẩm bởi keyword',
    schema: generalSchema(GenericSessionResponse, 'object'),
  })
  genericSession(@Body() genericSessionDto: GenericSessionDto) {
    return this.cartsService.genericSession(genericSessionDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Lấy thông tin mới nhất của giỏ hàng theo sessionId',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  getCart(@Query() getCartDto: GetCartDto) {
    return this.cartsService.getCart(getCartDto);
  }

  @Post()
  @ApiOperation({
    summary: 'Thêm sản phẩm vào giỏ hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  addCartItem(@Body() addCartItemDto: AddCartItemDto) {
    return this.cartsService.addCartItem(addCartItemDto);
  }

  @Post(':id/discount-adjustment')
  @ApiOperation({
    summary: 'Thêm điều chỉnh giá vào giỏ hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  addDiscountAdjustment(@Param('id') id: string, @Body() addDiscountAdjustmentDto: AddDiscountAdjustmentDto) {
    return this.cartsService.addDiscountAdjustment(id, addDiscountAdjustmentDto);
  }

  @Delete('/discount-adjustment/:id')
  @ApiOperation({
    summary: 'Thêm điều chỉnh giá vào giỏ hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  removeDiscountAdjustment(@Param('id') id: string, @Body() getCart: GetCartLibDto) {
    return this.cartsService.removeDiscountAdjustment(id, getCart);
  }

  @Post('list-cart-item')
  @ApiOperation({
    summary: 'Thêm danh sách sản phẩm vào giỏ hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  addListCartItem(@Body() addListCartItemDto: AddListCartItemDto) {
    return this.cartsService.addListCartItem(addListCartItemDto);
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật sản phẩm trong giỏ hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  updateCartItem(@Param('id') id: string, @Body() updateCartItemDto: UpdateCartItemDto) {
    return this.cartsService.updateCartItem(id, updateCartItemDto);
  }

  @Patch('list-cart-item')
  @ApiOperation({
    summary: 'Cập nhật danh sách thông tin trong redis',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  updateListCartItemRedis(@Body() updateListCartItemRedisDto: UpdateListCartItemRedisDto) {
    return this.cartsService.updateListCartItemRedis(updateListCartItemRedisDto);
  }

  @Delete()
  @ApiOperation({
    summary: 'Xoá sản phẩm trong giỏ hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  deleteCartItem(@Body() deleteCartItemDto: DeleteCartItemDto) {
    return this.cartsService.deleteCartItem(deleteCartItemDto);
  }

  @Post('merge-to-customer')
  @ApiOperation({
    summary: 'Gộp giỏ hàng hiện tại vào giỏ hàng của khách',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  mergeCartToCustomer(@Body() mergeCartToCustomer: MergeCartToCustomer) {
    return this.cartsService.mergeCartToCustomer(mergeCartToCustomer);
  }

  @Post('merge-to-anonymous')
  @ApiOperation({
    summary: 'Gộp giỏ hàng của khách vào giỏ hàng ẩn danh',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  mergeCartToAnonymous(@Body() mergeCartToAnonymous: MergeCartToAnonymous) {
    return this.cartsService.mergeCartToAnonymous(mergeCartToAnonymous);
  }

  @Post('voucher')
  @ApiOperation({
    summary: 'Thêm voucher vào giỏ hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  addVoucher(@Body() addVoucherDto: AddVoucherRSADto) {
    return this.cartsService.addVoucher(addVoucherDto);
  }

  @Post('extra-data')
  @ApiOperation({
    summary: 'Thêm Extra Data',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  addExtraData(@Body() addExtraDataDto: AddExtraDataDto) {
    return this.cartsService.addExtraData(addExtraDataDto);
  }

  @Post('selected-promotion')
  @ApiOperation({
    summary: 'Chọn chương trình khuyến mãi',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  selectedPromotion(@Body() selectedPromotionDto: SelectedPromotionDto) {
    return this.cartsService.selectedPromotion(selectedPromotionDto);
  }

  @Post('/order/adjust')
  @ApiOperation({
    summary: 'Nút quay lại giỏ hàng hoặc xủ lý đơn hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  adjustCart(@Body() adjustOrderLibDto: AdjustOrderLibDto) {
    return this.cartsService.adjustCart(adjustOrderLibDto);
  }

  @Post('/confirm/pushing-cart')
  @ApiOperation({
    summary: 'Lấy thông tin màn hình xác nhận giỏ hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(ConfirmPushingCartRes, 'object'),
  })
  confirmPushingCart(@Body() confirmPushingCartDto: ConfirmPushingCartDto) {
    return this.cartsService.confirmPushingCart(confirmPushingCartDto);
  }

  /**
   * @TODO thông tin đơn hàng màn hình preview
   */
  @Get('preview-order/:journeyId')
  @ApiOperation({
    summary: 'Chi tiết đơn hàng cho màn preview',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin chi tiết đơn hàng',
    schema: generalSchema(OrderPreviewResDto, 'object'),
  })
  async orderPreview(@Param('journeyId') journeyId: string) {
    return this.cartsService.orderPreviewService(journeyId);
  }

  /**
   * @TODO Danh sách Thay thế quà
   */
  @Post('/get-gift-replace')
  @ApiOperation({
    summary: 'Danh sách Thay thế quà',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin quà thay thế',
    schema: generalSchema(GiftReplaceResponse, 'object'),
  })
  getGiftReplace(@Body() giftReplaceDto: GiftReplaceDto) {
    return this.cartsService.getGiftReplace(giftReplaceDto);
  }

  /**
   * @TODO Danh sách Thay thế quà
   */
  @Post('/get-multiple-gift-replace')
  @ApiOperation({
    summary: 'Danh sách Thay thế quà (nhiều quà)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin quà thay thế',
    schema: generalSchema(GetMultipleGiftReplaceResponseDto, 'object'),
  })
  getMultiple(@Body() giftReplaceDto: GetMultipleGiftReplaceDto) {
    return this.cartsService.getMultipleGiftReplace(giftReplaceDto);
  }

  @Post('/gift-replace')
  @ApiOperation({
    summary: 'Thay thế quà',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  giftReplace(@Body() giftReplaceDtoLib: GiftReplaceDtoLib) {
    return this.cartsService.giftReplace(giftReplaceDtoLib);
  }

  @Post('/sku-replace')
  @ApiOperation({
    summary: 'Thay thế sản phẩm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(GetCartResponse, 'object'),
  })
  skuReplace(@Body() skuReplaceDto: SkuReplaceDto) {
    return this.cartsService.skuReplace(skuReplaceDto);
  }

  /**
   * @TODO xem chi tiết theo ticket code
   */
  @Get('tickets/:ticketCode')
  @ApiOperation({
    summary: 'Xem chi tiết theo ticket code',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Xem chi tiết theo ticket code',
  })
  async getDetailsByTicketCode(@Param('ticketCode') ticketCode: string, @Query() getCartDto: GetCartDto) {
    return this.cartsService.getDetailsByTicketCode(ticketCode, getCartDto);
  }

  /**
   * @TODO Xem chi tiết theo order theo session id
   */
  @Get('orders')
  @ApiOperation({
    summary: 'Xem chi tiết theo order theo session id',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Xem chi tiết theo order theo session id',
  })
  async getOrderDetailsBySessionId(@Query() getCartDto: GetCartDto) {
    return this.cartsService.getOrderDetailsBySessionId(getCartDto);
  }

  /**
   * @TODO get confirmed cart by order code
   */
  @Get('get-confirmed-cart/:orderCode')
  @ApiOperation({
    summary: 'get confirmed cart by order code',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'get confirmed cart by order code',
    schema: generalSchema(GetCartConfirmLibResponse, 'object'),
  })
  async getConfirmedCart(@Param() param: GetConfirmedCartParam) {
    return this.cartsService.getConfirmedCart(param);
  }

  /**
   * @TODO lấy session cart từ orderCode và ticketCode
   */
  @Get('session-by-order-code-ticket-code')
  @ApiOperation({
    summary: 'get confirmed cart by order code',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'get confirmed cart by order code',
    schema: generalSchema(GetSessionByOrderCodeTicketCodeRes, 'object'),
  })
  async getSessionByOrderCodeTicketCode(@Query() query: GetSessionByOrderCodeTicketCodeDto) {
    return this.cartsService.getSessionByOrderCodeTicketCode(query);
  }

  @Post('check-rule-add-cart')
  @ApiOperation({
    summary: 'Check rule add cart và chỉ định mũi đã mua',
  })
  @HttpCode(HttpStatus.OK)
  async checkRuleAddCart(@Body() checkRuleAddCartDto: CheckRuleAddCartDto) {
    return this.cartsService.checkRuleAddCart(checkRuleAddCartDto);
  }

  @Patch(':sessionId/dob')
  @ApiOperation({
    summary: 'update dob',
  })
  @HttpCode(HttpStatus.OK)
  async updateDob(@Param() param: UpdateDobParam, @Body() payload: UpdateDobDto) {
    return this.cartsService.updateDob(param, payload);
  }

  /**
   * @TODO tổng tiền khách trả cho thanh toán trả chậm
   */
  @Post('total-bill')
  @ApiOperation({
    summary: 'Tổng tiền khách cần trả đơn trả chậm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin tổng tiền khách cần trả đơn trả chậm',
    schema: generalSchema(GetTotalBillRes, 'object'),
  })
  async getTotalBill(@Body() body: GetTotalBillDto) {
    return this.cartsService.getTotalBill(body);
  }

  /**
   * https://reqs.fptshop.com.vn/browse/FV-13280
   */
  @Get('get-promotion-cart-total')
  @ApiOperation({
    summary: 'Promotion cart total',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin promotion',
    schema: generalSchema(ResPromotionCartTotal, 'object'),
  })
  getPromotionCartTotal(@Query() queryPromotionDto: ReqPromotionCartTotalDto) {
    return this.cartsService.getPromotionCartTotal(queryPromotionDto);
  }

  @Post('selected-promotion-cart-total')
  @ApiOperation({
    summary: 'Chọn chương trình khuyến mãi',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả về cart hiện tại',
    schema: generalSchema(ResPromotionCartTotal, 'object'),
  })
  selectedPromotioCartTotal(@Body() payload: SelectedPromotionCartTotalDto) {
    return this.cartsService.selectedPromotioCartTotal(payload);
  }

  @Post('gift-replace-cart-total')
  @ApiOperation({
    description: 'Thay thế quà tặng khuyến mãi cho cart tổng gói gia đình',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trả ra danh sách sản phẩm của cart hiện tại',
    schema: generalSchema(ResPromotionCartTotal, 'object'),
  })
  giftReplaceCartTotal(@Body() payload: GiftReplacePromotionCartTotalDto): Promise<ResPromotionCartTotal> {
    return this.cartsService.giftReplaceCartTotal(payload);
  }

  @Get('get-voucher-cart-total')
  @ApiOperation({
    summary: 'Voucher cart total',
  })
  @HttpCode(HttpStatus.OK)
  getVoucherCartTotal(@Query() queryPromotionDto: ReqPromotionCartTotalDto) {
    return this.cartsService.getVoucherCartTotal(queryPromotionDto);
  }
}
