import { Injectable } from '@nestjs/common';
import _ from 'lodash';
import { CreateConsultantDto } from '@libs/modules/consultants/dto';
import { ConsultantsService as ConsultantsLiService } from '@libs/modules/consultants/services/consultants.service';

@Injectable()
export class ConsultantsService {
  constructor(private readonly consultantLibService: ConsultantsLiService) {}

  async createConsultant(payload: CreateConsultantDto) {
    return this.consultantLibService.createConsultant(payload);
  }
}
