import { CustomersRuleService } from './../../customers/services/customers-rule.service';
import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { CartAppService, TypeVaccineNow } from 'vac-nest-cart-app';
import { FamilyService } from 'vac-nest-family';
import {
  CreatedJourneyDto,
  CreatedJourneyStepDto,
  JourneyService,
  STEP_NAME,
  TRANSACTION_TYPE,
  TypeStepId,
} from 'vac-nest-journey';
import { PIMAppService } from 'vac-nest-pim-app';
import { RegimenService } from 'vac-nest-regimen';
import { CreateConsultantDto, ItemArrCartDto, ItemCartDto } from '../dto';
import { RegimensService } from '../../regimens/services/regimens.service';
import _ from 'lodash';
import {
  ErrorCode,
  IError,
  OrderChannels,
  SystemException,
  concurrentPromise,
  OrderChanel,
} from '../../../../../shared/src';
import { DetailRegimenCloseRes, VacOrderInjectionService } from 'vac-nest-order-injection';
import { JSONPath } from 'jsonpath-plus';
import { RSA_ECOM_HARD_DEFAULT_SHOP_CODE } from '@shared/common/hard-code';
import { ReturnHomeService } from 'vac-nest-return-home';
import moment from 'moment';
import { DSMSService } from 'vac-nest-dsms';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { CreatedJourneyConvertDto } from '../dto/create-journey.dto';
import { GetOneOrderLibResponse, OrderStatus } from 'vac-nest-oms';
import { ScheduleCoreService } from 'vac-nest-schedule';
import { InsideService } from 'vac-nest-inside';

@Injectable()
export class ConsultantsService {
  constructor(
    private readonly journeyService: JourneyService,
    private readonly cartAppService: CartAppService,
    private readonly pimCoreService: PIMAppService,
    private readonly regimenCoreService: RegimenService,
    private readonly customersRuleService: CustomersRuleService,
    private readonly familyCoreService: FamilyService,
    private readonly regimensService: RegimensService,
    private readonly returnHomeService: ReturnHomeService,
    private readonly vacOrderInjectionService: VacOrderInjectionService,
    private readonly dSMSService: DSMSService,
    private readonly scheduleCoreService: ScheduleCoreService,
    private readonly insideService: InsideService,

    @Inject(REQUEST)
    private readonly req: Request,
  ) {}

  checkConsultantForAffiliate(arrOrderAffiliate: Array<GetOneOrderLibResponse>) {
    const channel = (this.req['headers']?.['order-channel'] as string) || '';
    // if (OrderChannels.RSA_ECOM.includes(channel)) return; //FV-13572
    const removeOrderCurrent: Array<GetOneOrderLibResponse> = arrOrderAffiliate?.filter(
      (e) => e.orderStatus === OrderStatus.FinishDeposit,
    );
    if (removeOrderCurrent?.length) {
      if (OrderChannels.RSA_AFFILIATE.includes(channel)) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_AFF_CREATE_ORDER_AFFILIATE,
          },
          HttpStatus.FORBIDDEN,
        );
      } else if (OrderChannels.RSA.includes(channel)) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_CREATE_ORDER_AFFILIATE,
          },
          HttpStatus.FORBIDDEN,
        );
      } else if (OrderChannels.RSA_ECOM.includes(channel)) {
        //FV-13572
        throw new SystemException(
          {
            code: ErrorCode.RSA_ECOM_ALREADY_HAVE_AFF_ORDER_DEPOSIT_COMPLETED,
            message: ErrorCode.getError(ErrorCode.RSA_ECOM_ALREADY_HAVE_AFF_ORDER_DEPOSIT_COMPLETED),
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }
  }

  async createConsultant(payload: CreateConsultantDto) {
    /**
     * @TODO
     *   - Tạo Journey
     *   - Tạo session
     *   - Merge cart customer
     *   - Tạo Journey Step
     */
    const orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    const source = orderChannel;
    const isChannelRsaEcom = OrderChannels.RSA_ECOM.includes(orderChannel);
    const isChannelMRSA = [OrderChannels.RSA[1]].includes(orderChannel);

    // check exist order for channel mrsa offline
    if (isChannelMRSA) {
      const resOrderJourney = await this.journeyService.getOrderByLcvidsAttributes({
        lcvIds: [payload?.personIdSub],
        source: [
          OrderChanel.FromRSAEcom,
          OrderChanel.FromVaccineShopAffiliate,
          OrderChanel.FromMobileAffiliate,
          OrderChanel.FromRSAOffline,
          OrderChanel.FromAppEcom,
          OrderChanel.FromWebEcom,
        ],
        orderStatus: [OrderStatus.Confirmed, OrderStatus.FinishDeposit],
        pageNumber: 1,
        pageSize: 200,
      });
      if (resOrderJourney?.totalCount) {
        const orderCodes: string[] = _.compact(
          JSONPath({
            path: '$.items[*].orderCode',
            json: resOrderJourney,
          }) ?? [],
        );
        const orderCodesString: string = orderCodes.join(', ');
        throw new SystemException(
          {
            code: ErrorCode.M_RSA_OFFLINE_CONSULTANTS_NOT_ALLOWED_EXIST_ORDER,
            message: `${ErrorCode.getError(
              ErrorCode.M_RSA_OFFLINE_CONSULTANTS_NOT_ALLOWED_EXIST_ORDER,
            )} ${orderCodesString}`,
            details: `${ErrorCode.getError(
              ErrorCode.M_RSA_OFFLINE_CONSULTANTS_NOT_ALLOWED_EXIST_ORDER,
            )} ${orderCodesString}`,
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    // check CreateOrderForAffiliate
    const [arrOrderAffiliate, saveOrderSchedules] = await concurrentPromise(
      this.journeyService.getOrderAffiliateInfoByLcvId(payload?.personIdSub),
      isChannelRsaEcom ? this.scheduleCoreService.getSaveOrderSchedules([payload.personIdSub]) : null, // Chỉ check chặn trên RSA ecom
    );
    // FV-11444:
    const saveOrderSchedulesFilter = saveOrderSchedules?.items?.filter((item) => item?.status === 0) || [];
    if (saveOrderSchedulesFilter?.length) {
      const orderCode = saveOrderSchedulesFilter?.[0]?.orderCode;
      throw new SystemException(
        {
          code: ErrorCode.RSA_ECOM_CONSULTANT_NOT_ALLOWED_EXIST_ORDER_ECOM_DISPLAY_2,
          message: `${ErrorCode.getError(
            ErrorCode.RSA_ECOM_CONSULTANT_NOT_ALLOWED_EXIST_ORDER_ECOM_DISPLAY_2,
          )} ${orderCode}`,
          details: `${ErrorCode.getError(
            ErrorCode.RSA_ECOM_CONSULTANT_NOT_ALLOWED_EXIST_ORDER_ECOM_DISPLAY_2,
          )} ${orderCode}`,
          validationErrors: null,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    // End Fv11444

    // FV-16343 - bỏ lỗi ErrorCode.RSA_ECOM_ALREADY_HAVE_AFF_ORDER_DEPOSIT_COMPLETED Check ở tạo đơn
    try {
      this.checkConsultantForAffiliate(arrOrderAffiliate);
    } catch (error) {
      if (error instanceof SystemException) {
        if (error?.['response']?.code !== ErrorCode.RSA_ECOM_ALREADY_HAVE_AFF_ORDER_DEPOSIT_COMPLETED) {
          throw error;
        }
      }
    }
    const extraPayload: Pick<CreatedJourneyDto, 'shopCodeCreate' | 'shopNameCreate'> = {};
    if (payload.shopCode) {
      extraPayload.shopCodeCreate = payload.shopCode;
    }
    if (payload.shopName) {
      extraPayload.shopNameCreate = payload.shopName;
    }

    let employeeNameInside = '';
    if (payload?.createdBy && !payload?.createdByName) {
      const listEmployeeInfor = await this.insideService.getListShopByEmployee(payload?.createdBy);
      employeeNameInside = listEmployeeInfor?.shops?.at(0)?.employeeName;
    }

    const payloadCreatedJourney: CreatedJourneyConvertDto = {
      ...payload,
      createdBy: payload?.createdBy || '',
      createdByName: payload?.createdByName || employeeNameInside,
      ...extraPayload,
      stepIdCurrent: TypeStepId.TIEP_NHAN_THONG_TIN,
      step: {
        stepId: TypeStepId.TIEP_NHAN_THONG_TIN,
        stepName: STEP_NAME[TypeStepId.TIEP_NHAN_THONG_TIN] ?? '',
        transactionNums: '',
        transactionType: TRANSACTION_TYPE['NONE'], // tạo tư vấn transaction type = 0
      },
      source,
    };

    if (OrderChannels.RSA_AFFILIATE.includes(orderChannel)) {
      // get shop VT theo shopCode LC
      const shopVX = await this.dSMSService.getAllShopLC({
        params: {
          shopLCAround: payload.shopCode,
        },
      });

      // đặt cờ if true thì chặn false thì next
      let isAccessConsultants = false;
      // nếu có shop VT thì check thêm rule tất cả shop còn hiệu lực hay không
      if (shopVX?.length) {
        for (const itemShop of shopVX) {
          // check thời gian hiện tại có nằm trong khoảng thời gian của inside mà dsms trả về hay không
          isAccessConsultants = moment(moment().utcOffset(7).format('YYYY-MM-DD')).isBetween(
            moment(itemShop?.fromDate).utcOffset(7).format('YYYY-MM-DD'),
            moment(itemShop?.toDate).utcOffset(7).format('YYYY-MM-DD'),
            undefined,
            '[]',
          );
          if (isAccessConsultants) break;
        }
      }

      // nếu !shopVX?.length, isAccessConsultants = false thì báo lỗi
      if (!isAccessConsultants) {
        throw new SystemException(
          {
            code: ErrorCode.RSA_VALID_CONSULTANTS_AFFILIATE,
            message: ErrorCode.getError(ErrorCode.RSA_VALID_CONSULTANTS_AFFILIATE),
            details: ErrorCode.getError(ErrorCode.RSA_VALID_CONSULTANTS_AFFILIATE),
            validationErrors: null,
          },
          HttpStatus.FORBIDDEN,
        );
      }
    }

    //Start check rule kiểm tra xem khách có đơn trả hàng chưa xử lý không để thông báo
    const result = await this.journeyService.getOrderInfo({
      keyWord: payload?.personIdSub,
      orderStatus: [4, 5],
      pageSize: 1000,
    });

    const orderCodes = result?.items?.map((order) => order?.orderCode)?.filter((order) => order);
    if (orderCodes?.length) {
      const orderReturns = await this.returnHomeService.searchOrderReturn({ orderCodes, status: [1] });
      const orderReturnCodes = orderReturns?.filter((x) => x?.orderReturnCode)?.map((x) => x?.orderReturnCode);

      if (orderReturnCodes?.length) {
        const exception: IError = {
          code: ErrorCode.RSA_ERROR_CONSULTANTS_ORDER_RULE,
          message: ErrorCode.getError(ErrorCode.RSA_ERROR_CONSULTANTS_ORDER_RULE).replace(
            '{orderCode}',
            orderReturnCodes?.[0],
          ),

          details: ErrorCode.getError(ErrorCode.RSA_ERROR_CONSULTANTS_ORDER_RULE).replace(
            '{orderCode}',
            orderReturnCodes?.[0],
          ),
          validationErrors: null,
        };
        throw new SystemException(exception, HttpStatus.FORBIDDEN);
      }
    }

    //End check˝

    // Lấy dob của personSub (Người tiêm) gắn vào merge cart
    const personSub = (await this.familyCoreService.getListPrimaryPerson([payload?.personIdSub])).at(0);

    // get DOB
    const getDOB = await this.regimensService.getAgeRanges();

    const mappingUniKey = [personSub?.from, personSub?.to, personSub?.ageUnitCode].join('_');
    const isHaveDateOfBirth = mappingUniKey == '0_0_0';
    const dob = !isHaveDateOfBirth
      ? new Date(getDOB.find((e) => e.uniqkey == mappingUniKey)?.dob)
      : personSub?.dateOfBirth;

    const listLcvId = payload?.phones?.map((i) => i?.lcvId) || payload?.listPhoneNumber?.map((i) => i?.lcvId) || [];

    const { phoneRegister, email } = await this.customersRuleService._verifyEmailFpter(listLcvId);

    const dataJourney = await this.journeyService.postCreatedJourney(payloadCreatedJourney);
    const genericSession = await this.cartAppService.genericSession({ shopCode: payloadCreatedJourney?.shopCode });
    let cart = await this.cartAppService.mergeCartToCustomer({
      customerId: dataJourney?.custIdMain,
      sessionId: genericSession?.sessionId,
      phoneNumber: payload?.phoneNumber,
      phoneRegister,
      email,
      dob,
      vaccinationCode: personSub?.lcvId || '',
      createdBy: payloadCreatedJourney?.createdBy,
      createdByName: payloadCreatedJourney?.createdByName,
      journeyId: dataJourney?.id,
    });
    const createdJourneyStepDto: CreatedJourneyStepDto = {
      journeyId: dataJourney?.id,
      stepId: TypeStepId.TU_VAN,
      stepName: STEP_NAME[TypeStepId.TU_VAN] ?? '',
      createdBy: payloadCreatedJourney?.createdBy,
      transactionNums: genericSession?.sessionId,
      transactionType: TRANSACTION_TYPE['CART_SESSION'], // tạo tư vấn với session cart transaction type = 1
    };

    const dataStepJourney = await this.journeyService.postCreatedJourneyStep(createdJourneyStepDto);

    // add cart
    let arrItemCart = await this._mapArrCartItem(payload?.arrCartItem);
    // check Lịch hẹn vu vơ ko thêm vào giỏ hàng nếu phác đồ bị đóng
    const listRegimenClose = await this.vacOrderInjectionService.searchRegimenClose({ lcvId: [payload?.personIdSub] });
    const listRegimenCloseDetail: DetailRegimenCloseRes[] =
      JSONPath({
        path: '$.items[*].details[*]',
        json: listRegimenClose,
      }) || [];

    let isAddCartSuccess = true;
    const arrSku: string[] = [];
    arrItemCart?.forEach((itemCart) => {
      const isExistRegimenClose = listRegimenCloseDetail?.find((detail) => detail?.sku === itemCart?.itemCart);
      if (isExistRegimenClose) {
        isAddCartSuccess = false;
        arrSku.push(itemCart?.itemCart);
      }
    });

    if (OrderChannels.RSA_AFFILIATE.includes(orderChannel)) {
      await this.cartAppService.updateDob(cart.headerData.sessionId, {
        orderAttribute: 8,
      });
      cart = await this.cartAppService.getCart({
        sessionId: genericSession?.sessionId,
        phoneNumber: payload?.phoneNumber,
      });
    }

    arrItemCart = arrItemCart?.filter((itemCart) => !arrSku?.includes(itemCart?.itemCart));
    if (arrItemCart?.length > 0) {
      // check rule age when add list cart

      const arrRegimen: string[] = _.compact(
        _.uniq(
          arrItemCart?.map((e) => {
            if (!e.objectGroupId && _.min(e?.orderInjections) === 1) {
              return e?.regimenId;
            }
          }),
        ),
      );
      // if (arrRegimen?.length) {
      //   const checkRuleDto: CheckRuleCreateUpdateCartDto = {
      //     DOB: dob,
      //     items: arrRegimen?.map((e) => ({ regimenId: e })),
      //   };
      //   const checkRule = await this.orderRuleEngineService.checkRuleCreateUpdateCart(checkRuleDto);
      //   if (checkRule?.isRestrict) {
      //     const exception: IError = {
      //       code: checkRule?.rules?.at(0)?.type,
      //       message: checkRule?.rules?.at(0)?.message,
      //       details: checkRule?.rules?.at(0)?.message,
      //       validationErrors: null,
      //     };
      //     throw new SystemException(exception, HttpStatus.FORBIDDEN);
      //   }
      // }

      if (OrderChannels.RSA_ECOM.includes(orderChannel)) {
        const pimInfo = await this.pimCoreService.getListProductBySku(
          arrItemCart?.map((e) => e?.itemCart),
          RSA_ECOM_HARD_DEFAULT_SHOP_CODE,
        );
        if (pimInfo.listProduct.some((prod) => prod.isPreOrder)) {
          await this.cartAppService.updateDob(cart.headerData.sessionId, {
            orderAttribute: 7,
          });
          cart = await this.cartAppService.getCart({
            sessionId: genericSession?.sessionId,
            phoneNumber: payload?.phoneNumber,
          });
        }
        arrItemCart.forEach((e) => (e.startDateInjection = new Date()));
      }

      cart = await this.cartAppService.addListCartItem({
        sessionId: genericSession?.sessionId,
        listCartItemsInput: arrItemCart,
        hasValidate: false,
      });
    }

    return {
      ...genericSession,
      journeys: dataStepJourney,
      cart: cart || null,
      isAddCartSuccess,
    };
  }

  async _mapArrCartItem(arrCart: ItemCartDto[]) {
    let output: Partial<ItemArrCartDto>[] = [];

    if (!arrCart?.length) return output;

    // call product
    const skus = arrCart?.map((product) => product?.itemCart) || [];
    const { listProduct } = await this.pimCoreService.getListProductBySku(skus);

    listProduct?.map((product) => {
      const measures = product?.measures?.find((item) => item?.isSellDefault === true);
      const item = {
        itemCart: product?.sku,
        itemType: 1,
        unitCode: measures?.measureUnitId,
        quantity: 1,
        vaccinatedNow: TypeVaccineNow.LATER,
      };
      output.push(item);
    });

    // merge product
    output = output?.map((item) => {
      const findItem = arrCart?.find((x) => x.itemCart === item?.itemCart);
      item.lcvId = findItem?.lcvId;
      item.regimenId = findItem?.regimenId;
      item.orderInjections = findItem?.orderInjections;
      item.injectedVaccineCount = findItem?.injectedVaccineCount;
      item.vaccinatedNow = findItem?.vaccinatedNow;
      item.startDateInjection = findItem?.startDateInjection;
      item.diseaseGroup = findItem?.diseaseGroup;
      return { ...item, ...findItem, unitCode: findItem?.unitCode || item?.unitCode };
    });

    // call regiments
    const regimenIds = arrCart?.map((regimen) => regimen?.regimenId) || [];
    const listRegimens = await this.regimenCoreService.getRegimenByIds({ regimenIds });

    // merge regimen
    output?.map((item) => {
      const regimenFind = listRegimens?.find((regimen) => regimen?.id === item?.regimenId);
      item.requiredInjections = regimenFind?.requiredInjections;
      item.injectionDistanceNote = regimenFind?.note;
      item.idStartTime = regimenFind?.idStartTime || 0;
      item.startTime = regimenFind?.startTime || regimenFind?.scheduleType || '';
      item.objectGroupId = regimenFind?.objectGroupId || '';
      item.diseaseGroupId = regimenFind?.diseaseGroupId || '';
      item.scheduleType = regimenFind?.scheduleType || '';
    });

    return output;
  }
}
