import { Module, forwardRef } from '@nestjs/common';
import { EcomOrderModule } from 'apps/rsa-ecom/src/modules/ecom-order/ecom-order.module';
import { CartAppModule } from 'vac-nest-cart-app';
import { ExaminationCoreModule } from 'vac-nest-examination';
import { FamilyModule } from 'vac-nest-family';
import { IMSModule } from 'vac-nest-ims';
import { JourneyModule } from 'vac-nest-journey';
import { NotificationModule } from 'vac-nest-notification';
import { OMSModule } from 'vac-nest-oms';
import { VacOrderInjectionModule } from 'vac-nest-order-injection';
import { OsrModule } from 'vac-nest-osr';
import { PaymentGatewayModule } from 'vac-nest-payment-gateway';
import { PIMAppModule } from 'vac-nest-pim-app';
import { PricingModule } from 'vac-nest-pricing';
import { PromotionModule } from 'vac-nest-promotion';
import { RegimenModule } from 'vac-nest-regimen';
import { ScheduleCoreModule } from 'vac-nest-schedule';
import { VoucherCoreModule } from 'vac-nest-voucher-core';
import { CartsModule } from '../carts/carts.module';
import { DepositCancelModule } from '../deposit-cancel/depositCancel.module';
import { RegimensModule } from '../regimens/regimens.module';
import { TicketUtilsService } from '../ticket/services/ticket-utils.service';
import { OrdersController } from './controllers/orders.controller';
import { OrdersMasterDataController } from './controllers/orders.master-data.controller';
import { BusinessOrderService } from './services/business-order.services';
import { OrderUtilsService } from './services/order-utils.services';
import { OrdersService } from './services/orders.service';
import { VacHistoryModule } from 'vac-nest-history';
import { IMSBookingModule } from 'vac-nest-ims-booking';
import { TicketModule } from '../ticket/ticket.module';
import { LoyaltyAppModule } from 'vac-nest-loyalty-app';
import { SchedulesModule } from '../schedules/schedules.module';
import { CustomerCoreModule } from 'vac-nest-customer-core';
import { FilesModule } from '../files/files.module';
import { VacContractModule } from 'vac-nest-contract';
import { InvoiceAppModule } from 'vac-nest-invoice-app';
import { RedisModule, RedisService } from '../../../../shared/src';
import { OrderRedisService } from './services/order-redis.service';
import { OrderRedisController } from './controllers/order-redis.contreoller';
import { RsaEcomModule } from 'vac-nest-rsa-ecom';
import { ScheduleRequestsModule } from 'apps/rsa-ecom/src/modules/schedule-requests/schedule-requests.module';
import { InsideModule } from 'vac-nest-inside';
import { CustomersModule } from '../customers/customers.module';
import { OrderRuleEngineModule } from 'vac-nest-order-rule-engine';
import { LoggerModule } from 'vac-nest-logger';
import { OrdersFamilyPackageService } from './services/orders-family-package.service';
import { OrdersFamilyPackageController } from './controllers/orders-family-package.controller';
import { ReasonsModule } from '../reasons/reasons.module';
import { CamundaApiModule } from '@frt/nestjs-api';

@Module({
  imports: [
    OMSModule,
    CartAppModule,
    PaymentGatewayModule,
    PIMAppModule,
    ExaminationCoreModule,
    FamilyModule,
    RegimenModule,
    ScheduleCoreModule,
    NotificationModule,
    forwardRef(() => DepositCancelModule),
    IMSModule,
    PricingModule,
    VacOrderInjectionModule,
    OsrModule,
    VoucherCoreModule,
    PromotionModule,
    RegimensModule,
    EcomOrderModule,
    JourneyModule,
    VacHistoryModule,
    CartsModule,
    IMSBookingModule,
    LoyaltyAppModule,
    SchedulesModule,
    CustomerCoreModule,
    FilesModule,
    VacContractModule,
    InvoiceAppModule,
    RsaEcomModule,
    ScheduleRequestsModule,
    RedisModule,
    InsideModule,
    CustomersModule,
    OrderRuleEngineModule,
    LoggerModule,
    ReasonsModule,
    CamundaApiModule,
  ],
  controllers: [OrdersController, OrdersMasterDataController, OrderRedisController, OrdersFamilyPackageController],
  providers: [
    OrdersService,
    OrderUtilsService,
    TicketUtilsService,
    BusinessOrderService,
    RedisService,
    OrderRedisService,
    OrdersFamilyPackageService,
  ],
  exports: [OrdersService, OrderUtilsService, OrderRedisService, OrdersFamilyPackageService],
})
export class OrdersModule {}
