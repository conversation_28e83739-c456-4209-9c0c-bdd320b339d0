export const fulfillString = (str: string, param: any): string => {
  const properties: string[] = Object.getOwnPropertyNames(param);
  if (properties.length) {
    let returnValue = str;
    properties.forEach((element) => {
      returnValue = returnValue.replace(`{${element}}`, param[element] ? String(param[element]) : '');
    });
    // bo di phan thua regex: {[A-z]+}
    returnValue = returnValue.replace(/{[A-z]+}/g, '');
    return returnValue;
  }
  return str;
};
