import { Body, Controller, Get, HttpCode, HttpStatus, Post, Query, Req, UseInterceptors } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { BlockAdjustOrderInterceptor, ClassResponse, CustomHeaders, generalSchema, Public } from '@shared';
import {
  CancelHomePayLatterDto,
  CancelHomePayLatterRes,
  CancelPayooDto,
  CancelQRVpbDto,
  CheckTransactionBankTransferDto,
  CheckTransactionBankTransferRes,
  CheckTransactionHomPayLatterDto,
  CheckTransactionVaccineMBankDto,
  CheckTransactionVaccineMBankRes,
  CheckTransactionVaccinePayedDto,
  CheckTransactionVaccinePayedRes,
  CheckTransactionVaccinePayooDto,
  CheckTransactionVaccinePayooRes,
  CheckTransactionVpbDto,
  CheckTransactionVpbRes,
  CreatedQREwalletVaccineDto,
  CreatedQREwalletVaccineRes,
  CreatedQRMBankRes,
  CreateOrderPosVaccineDto,
  CreateOrderPosVaccineRes,
  CreateQRHomePayLatterDto,
  CreateQRHomePayLatterRes,
  CreateQRVpbDto,
  CreateQRVpbRes,
  DepositAllLibDto,
  DepositAllLibResponse,
  GetListBankLibResponse,
  GetListPaymentMethodRes,
  GetPaymentHistoryESLibDto,
  GetPaymentHistoryESLibResponse,
  InActiveQRDto,
  InActiveQRRes,
  PayloadGetListBank,
  PaymentGatewayService,
  VerifyVoucherGotitLibDto,
  VerifyVoucherGotitResponseLibDto,
  VerifyVoucherUrboxLibDto,
  VerifyVoucherUrboxResponseLibDto,
} from 'vac-nest-payment-gateway';
import {
  CheckBillDto,
  CheckBillRes,
  GetListPaymentPayVaccineRes,
  PayloadCancelQRMBankDto,
  PayloadCreatedQRMBankDto,
} from '../dto';
import { PaymentService } from '../services/payment.service';
import { GetListPaymentPayVaccineDto } from '../dto/get-list-payment-pay-vaccine.dto';
import { GetPaymentMethodDto } from '../dto/get-payment-method.dto';

@Controller({ path: 'payment-gateway', version: '1' })
@ApiTags('Payment')
@ApiExtraModels(
  ClassResponse,
  GetListPaymentMethodRes,
  CheckBillRes,
  GetListPaymentPayVaccineRes,
  DepositAllLibResponse,
  GetPaymentHistoryESLibResponse,
  CheckTransactionVaccinePayedRes,
  CreatedQREwalletVaccineRes,
  InActiveQRRes,
  CreateQRVpbRes,
  CheckTransactionVpbRes,
  CancelHomePayLatterRes,
  CreateQRHomePayLatterRes,
  CheckTransactionBankTransferRes,
  CreateOrderPosVaccineRes,
  CheckTransactionVaccinePayooRes,
  CreatedQRMBankRes,
  CheckTransactionVaccineMBankRes,
)
@CustomHeaders()
export class PaymentController {
  constructor(
    private readonly paymentService: PaymentService,
    private readonly paymentGatewayService: PaymentGatewayService,
  ) {}

  @Get('payment-method')
  @ApiOperation({
    summary: 'Lấy thông tin phương thức thanh toán',
  })
  @Public()
  @ApiOkResponse({
    description: 'Danh sách phương thức thanh toán',
    schema: generalSchema(GetListPaymentMethodRes, 'object'),
  })
  getListPaymentMethod(@Query() params: GetPaymentMethodDto) {
    return this.paymentService.getListPaymentMethod(params);
  }

  @Post('check-bill')
  @ApiOperation({
    summary: 'Check Bill',
  })
  @Public()
  @ApiOkResponse({
    description: 'Check Bill data',
    schema: generalSchema(CheckBillRes, 'object'),
  })
  checkBill(@Body() checkBillDto: CheckBillDto, @Req() req: any) {
    return this.paymentService.checkBill(checkBillDto, req.headers['order-channel']);
  }

  /**
   * @TODO lấy danh sách chi tiền
   */
  @Get('get-list-payment-pay-vaccine')
  @ApiOperation({
    summary: 'Lấy danh sách phương thức chi tiền',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin danh sách phương thức chi tiền',
    schema: generalSchema(GetListPaymentPayVaccineRes, 'object'),
  })
  getListPaymentPayVaccine(@Query() getPaymentPayVaccine: GetListPaymentPayVaccineDto) {
    return this.paymentService.getPaymentPayVaccine(getPaymentPayVaccine);
  }

  @Public()
  @Post('deposit-all')
  @ApiOperation({
    summary: 'Deposit all',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Chi tiết deposit',
    schema: generalSchema(DepositAllLibResponse, 'object'),
  })
  @UseInterceptors(BlockAdjustOrderInterceptor)
  depositAll(@Body() body: DepositAllLibDto) {
    return this.paymentGatewayService.depositAll(body);
  }

  @Public()
  @Get('es-history')
  @ApiOperation({
    summary: 'Lấy thông tin thanh toán',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin thanh toán',
    schema: generalSchema(GetPaymentHistoryESLibResponse, 'object'),
  })
  getPaymentHistoryES(@Query() param: GetPaymentHistoryESLibDto) {
    return this.paymentGatewayService.getPaymentHistoryES(param);
  }

  @Public()
  @Post('check-transaction-vaccine-payed')
  @ApiOperation({
    summary: 'Check Trans ví điện tử',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Check Trans ví điện tử',
    schema: generalSchema(CheckTransactionVaccinePayedRes, 'object'),
  })
  checkTransVaccinePayed(@Body() body: CheckTransactionVaccinePayedDto) {
    return this.paymentGatewayService.checkTransactionVaccinePayed(body);
  }

  @Public()
  @Post('create-qr-ewallet-vaccine')
  @ApiOperation({
    summary: 'Tạo QR code',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin tạo qr code',
    schema: generalSchema(CreatedQREwalletVaccineRes, 'object'),
  })
  createQREwalletVaccine(@Body() body: CreatedQREwalletVaccineDto) {
    return this.paymentGatewayService.createdQREwallet(body);
  }

  @Public()
  @Post('in-active')
  @ApiOperation({
    summary: 'Hủy QR code',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin hủy qr code',
    schema: generalSchema(InActiveQRRes, 'object'),
  })
  inActiveQR(@Body() body: InActiveQRDto) {
    return this.paymentGatewayService.inActiveQR(body);
  }

  @Public()
  @Post('cancel-qr-vpbank')
  @ApiOperation({
    summary: 'Hủy QR VPBank',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin hủy qr vpbank',
    schema: generalSchema(Boolean, 'boolean'),
  })
  cancelQRVpbank(@Body() body: CancelQRVpbDto) {
    return this.paymentGatewayService.cancelQRVpb(body);
  }

  @Public()
  @Post('create-qr-vpbank')
  @ApiOperation({
    summary: 'Tạo QR VPBank',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin hủy qr vpbank',
    schema: generalSchema(CreateQRVpbRes, 'object'),
  })
  createQRVpbank(@Body() body: CreateQRVpbDto) {
    return this.paymentGatewayService.createQRVpb(body);
  }

  @Public()
  @Post('check-transaction-qr-vpbank')
  @ApiOperation({
    summary: 'Check Transaction QR VPBank',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Transaction QR VPBank',
    schema: generalSchema(CheckTransactionVpbRes, 'array'),
  })
  checkTransactionQRVPBank(@Body() body: CheckTransactionVpbDto) {
    return this.paymentGatewayService.checkTransactionVpb(body);
  }

  @Public()
  @Post('cancel-qr-homePayLatter')
  @ApiOperation({
    summary: 'Hủy QR HomePayLatter',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Hủy QR HomePayLatter',
    schema: generalSchema(CancelHomePayLatterRes, 'object'),
  })
  cancelQRHomePayLatter(@Body() body: CancelHomePayLatterDto) {
    return this.paymentGatewayService.cancelHomePayLatter(body);
  }

  @Public()
  @Post('create-qr-homePayLatter')
  @ApiOperation({
    summary: 'Tạo QR HomePayLatter',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Tạo QR HomePayLatter',
    schema: generalSchema(CreateQRHomePayLatterRes, 'object'),
  })
  createQRHomePayLatter(@Body() body: CreateQRHomePayLatterDto) {
    return this.paymentGatewayService.createQRHomePayLatter(body);
  }

  @Public()
  @Post('check-transaction-qr-homepaylatter')
  @ApiOperation({
    summary: 'Check trans Home Paylater',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Transaction QR HomePayLatter',
    // schema: generalSchema(any, 'array'),
  })
  checkTransactionQRHomePayLatter(@Body() body: CheckTransactionHomPayLatterDto) {
    return this.paymentGatewayService.checkTransactionHomePayLatter(body);
  }

  @Public()
  @Get('check-transaction-bank-transfer')
  @ApiOperation({
    summary: 'Check trans chuyển khoản trước',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin Transaction bank transfer',
    schema: generalSchema(CheckTransactionBankTransferRes, 'array'),
  })
  checkTransactionBankTransfer(@Query() param: CheckTransactionBankTransferDto) {
    return this.paymentGatewayService.checkTransactionBankTransfer(param);
  }

  @Post('create-order-pos-vaccine')
  @ApiOperation({
    summary: 'Tạo đơn payoo',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Chi tiết đơn',
    schema: generalSchema(CreateOrderPosVaccineRes, 'object'),
  })
  createOrderPosVaccine(@Body() body: CreateOrderPosVaccineDto) {
    return this.paymentGatewayService.createOrderPosVaccine(body);
  }

  @Public()
  @Get('check-transaction-vaccine-payoo')
  @ApiOperation({
    summary: 'Check transaction payoo',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin payment',
    schema: generalSchema(CheckTransactionVaccinePayooRes, 'object'),
  })
  checkTransactionVaccinePayoo(@Query() param: CheckTransactionVaccinePayooDto) {
    return this.paymentGatewayService.checkTransactionVaccinePayoo(param);
  }

  @Public()
  @Post('cancel-order-payoo')
  @ApiOperation({
    summary: 'Hủy đơn payoo',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin hủy đơn',
    schema: generalSchema(true, 'boolean'),
  })
  cancelPayoo(@Body() body: CancelPayooDto) {
    return this.paymentGatewayService.cancelPayoo({ ...body, fromSystem: 'RSA' });
  }

  /**
   * @TODO tạo qr code cho MBank
   */
  @Public()
  @Post('create-qr-mbbank')
  @ApiOperation({
    summary: 'Tạo QR code MBank',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin QR code MBank',
    schema: generalSchema(CreatedQRMBankRes, 'object'),
  })
  createdQRMBank(@Body() body: PayloadCreatedQRMBankDto) {
    return this.paymentService.createQRCodeMBank(body);
  }

  /**
   * @TODO hủy qr code cho MBank
   */
  @Public()
  @Post('cancel-qr-mbbank')
  @ApiOperation({
    summary: 'Hủy QR code MBank',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin QR code MBank',
    schema: generalSchema(true, 'boolean'),
  })
  cancelQRMBank(@Body() body: PayloadCancelQRMBankDto) {
    return this.paymentService.cancelQRCodeMBank(body);
  }

  /**
   * @TODO check transaction vaccine payer MBank
   */
  @Public()
  @Post('check-transaction-vaccine-payed-mbbank')
  @ApiOperation({
    summary: 'Check transaction vaccine payed MBank',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin QR code MBank',
    schema: generalSchema(CheckTransactionVaccineMBankRes, 'object'),
  })
  checkTransactionVaccinePayedMBank(@Body() body: CheckTransactionVaccineMBankDto) {
    return this.paymentService.checkTransactionMBank(body);
  }

  @Public()
  @Get('get-banks')
  @ApiOperation({
    summary: 'Danh sách ngân hàng',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Thông tin danh sách ngân hàng',
    schema: generalSchema(GetListBankLibResponse, 'object'),
  })
  getListBank(@Query() param: PayloadGetListBank) {
    return this.paymentService.getListBankVaccine(param);
  }

  @Public()
  @Post('verify-voucher-gotit')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kiem tra voucher gotit',
    schema: generalSchema(VerifyVoucherGotitResponseLibDto, 'object'),
  })
  verifyVoucherGotit(@Body() param: VerifyVoucherGotitLibDto) {
    return this.paymentService.verifyVoucherGotit(param);
  }

  @Public()
  @Post('verify-voucher-urbox')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kiem tra voucher urbox',
    schema: generalSchema(VerifyVoucherUrboxResponseLibDto, 'object'),
  })
  verifyVoucherUrbox(@Body() param: VerifyVoucherUrboxLibDto) {
    return this.paymentService.verifyVoucherUrbox(param);
  }
}
