import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { CreateXuatOffDto } from './create-xuat-off.dto';
import { CreateXuatOffDetailDto } from './xuat-off-detail';

export class UpdateXuatOffDetailDto extends CreateXuatOffDetailDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  id?: string;
}
export class UpdateXuatOffDto extends CreateXuatOffDto {
  details?: UpdateXuatOffDetailDto[];
}
