import { Injectable, Logger } from '@nestjs/common';
import { EnumDistanceType, getStartDate, parseJson } from '@shared';
import _ from 'lodash';
import moment from 'moment';
import { CartAppService } from 'vac-nest-cart-app';
import { JourneyService } from 'vac-nest-journey';
import { DetailAttachment, DetailLib, OMSService, OrderStatus } from 'vac-nest-oms';
import { RegimenItem, RegimenService } from 'vac-nest-regimen';
import { PreProcessDto, PreProcessRes, Schedule } from '../dto/pre-process-data.dto';
import { FamilyService } from 'vac-nest-family';

@Injectable()
export abstract class ScriptingAdapter {
  constructor(
    protected readonly omsService: OMSService,
    protected readonly cartsService: CartAppService,
    protected readonly regimenService: RegimenService,
    protected readonly journeyService: JourneyService,
    protected readonly familyService: FamilyService,
  ) {}

  timeout(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async sleep(fn, ...args) {
    await this.timeout(3000);
    return fn(...args);
  }

  addDateInDefined(date: Date, distanceType: number, distanceValueByType: number) {
    const newDate = moment(date);
    switch (distanceType) {
      case EnumDistanceType.days:
        newDate.add(distanceValueByType, 'days');
        break;
      case EnumDistanceType.months:
        newDate.add(distanceValueByType, 'months');
        break;
      case EnumDistanceType.years:
        newDate.add(distanceValueByType, 'years');
        break;
      default:
        break;
    }
    return new Date(newDate.format());
  }

  getDistanceByOrderInjection(orderInjection: number, regimenInfo: RegimenItem) {
    if (!regimenInfo?.details?.length) return { distanceType: 0, distanceValueByType: 0, minDistance: 0 };
    let injection = orderInjection % regimenInfo?.details?.length;
    if (injection === 0) injection = regimenInfo?.details?.length;
    // div
    const detailFind = regimenInfo?.details?.find((e) => e.order === injection);
    if (!detailFind) return { distanceType: 0, distanceValueByType: 0, minDistance: 0 };
    const { distanceType, distanceValueByType, minDistance } = detailFind;
    if (injection === 1) return { distanceType: 0, distanceValueByType: 30, minDistance: 0 };
    return { distanceType, distanceValueByType, minDistance };
  }

  async preProcessData(orderCodes: string[]): Promise<Array<PreProcessRes>> {
    Logger.log('========================= START PRE PROCESS DATA =========================');
    const { orders } = await this.omsService.getListOrderES({ orderCode: orderCodes });
    const arrPreProcessDto: Array<PreProcessDto> = [];
    let arrRegimenId: Array<string> = [];
    for (const order of orders) {
      // if (order.orderStatus !== OrderStatus.Confirmed) {
      //   Logger.log(`${order.orderCode} - Order status not Confirmed with ${order.orderStatus}`);
      //   continue;
      // }
      // Cart confirm
      const cartConfirm = await this.cartsService.getCartConfirmByOrderCode(order.orderCode);
      const cartAppInfo = parseJson(cartConfirm?.cartAppInfo);
      if (!cartAppInfo) {
        Logger.log(`${order.orderCode} - Item not found`);
        continue;
      }
      arrPreProcessDto.push({ order, items: cartAppInfo['items'], headers: cartAppInfo['headers'] });
      arrRegimenId.push(...cartAppInfo['items'].map((item) => item.regimenId));
      Logger.log(`${order.orderCode} - SETImeOUt 500ms`);
      await this.timeout(500);
      Logger.log(`${order.orderCode} - SETImeOUt DONE`);
    }

    arrRegimenId = _.compact(_.uniq(arrRegimenId));

    Logger.log(`RegimenId: ${arrRegimenId}`);

    let arrRegimen: RegimenItem[] = [];
    if (arrRegimenId.length > 0)
      arrRegimen = await this.regimenService.getRegimenByIds({
        regimenIds: arrRegimenId,
      });
    const arrPreProcessRes: Array<PreProcessRes> = [];

    for (const preProcessDto of arrPreProcessDto) {
      const { order, items, headers } = preProcessDto;
      const journey = await this.journeyService.getJourneyByOrderCode({
        orderCode: order.orderCode,
      });
      if (!journey) {
        Logger.log(`${order.orderCode} - Journey not found`);
        continue;
      }
      const lcvId = items?.find((e) => e.lcvId)?.lcvId;
      if (!lcvId) {
        Logger.log(`${order.orderCode} - LcvId not found`);
        continue;
      }

      const family = await this.familyService.getManyByLcvId({ lcvId: [lcvId] });

      if (!family || !family[0]) {
        Logger.log(`${order.orderCode} - Family not found`);
        continue;
      }

      const preProcessResDto: PreProcessRes = {
        personId: family[0]?.id || '',
        journeyId: journey.id,
        modifiedBy: order?.modifiedBy || order?.createdBy,
        modifiedByName: order?.modifiedByName || order?.createdByName,
        orderCode: order?.orderCode,
        ticketInfor: [
          {
            custNoteOnline: '',
            createdBy: order?.modifiedBy || order?.createdBy,
            createdByName: order?.modifiedByName || order?.createdByName,
            shopCode: order?.shopCode,
            shopName: order?.shopName,
            ticketImages: [],
            lcvId: lcvId,
            orderCode: order?.orderCode,
            personId: family[0]?.id || '',
            saleEcomNote: '',
            sessionId: headers?.at(0)?.sessionId || '',
            ticketType: 1,
            indications: [],
            schedules: [],
          },
        ],
      };

      _.forEach(order?.details, (detail: DetailLib) => {
        _.forEach(detail?.detailAttachments, (attachmentEntry: DetailAttachment) => {
          const cartFind = items?.find(
            (item) => item?.itemCart === attachmentEntry.itemCode && item?.unitCode === attachmentEntry?.unitCode,
          );
          // Lấy ngày tiêm gần nhất
          if (!cartFind) {
            Logger.log(`${order?.orderCode} CartFind not found`);
            return;
          }
          const regimen = arrRegimen?.find((e) => e.id === cartFind?.regimenId);
          if (!regimen) {
            Logger.log(`${order?.orderCode} Regimen not found`);
            return;
          }
          const { distanceType, distanceValueByType } = this.getDistanceByOrderInjection(
            attachmentEntry?.orderInjection,
            regimen,
          );
          let getDate = null;
          if (_.min(cartFind.orderInjections) === attachmentEntry?.orderInjection) {
            getDate = getStartDate(cartFind?.startDateInjection, '+07:00');
          } else {
            const preOrderInjection: Schedule = _.maxBy(
              preProcessResDto?.ticketInfor[0]?.schedules?.filter((e) => e?.sku === attachmentEntry?.itemCode),
              'orderInjections',
            );
            if (preOrderInjection) {
              getDate = this.addDateInDefined(
                new Date(preOrderInjection?.appointmentDate),
                distanceType,
                distanceValueByType,
              );
            }
          }

          const scheduleDto: Schedule = {
            regimenId: regimen?.id,
            regimenName: regimen?.diseaseGroup?.name,
            sku: attachmentEntry?.itemCode,
            vaccineName: regimen?.vaccine?.name,
            appointmentDate: getDate,
            manufactor: regimen?.vaccine?.manufactor,
            orderInjections: attachmentEntry?.orderInjection,
            taxonomies: regimen?.vaccine?.manufactor,
            requireBeforeInjection: regimen?.vaccine?.requireBeforeInjection,
            unitCode: attachmentEntry?.unitCode,
            unitName: attachmentEntry?.unitName,
            scheduleType: regimen?.scheduleType,
            requiredInjections: regimen?.requiredInjections,
            sourceId: null,
            maxInjections: regimen?.maxInjections,
            status: 0,
            orderInjectionId: null,
            orderDetailAttachmentId: null,
            vacOrderCode: null,
            orderDetailAttachmentCode: null,
          };
          preProcessResDto?.ticketInfor[0]?.schedules.push(scheduleDto);
        });
      });

      if (preProcessResDto?.ticketInfor[0]?.schedules.length === 0) {
        Logger.log(`${order?.orderCode} Schedules not found`);
        continue;
      }
      arrPreProcessRes.push(preProcessResDto);
    }

    Logger.log('========================= END PRE PROCESS DATA =========================');
    return arrPreProcessRes;
  }

  abstract getTicketInfoByOrderCode(orderCode: Array<string>): Promise<Array<PreProcessRes>>;
  abstract processPushOrder(orderCode: Array<string>): Promise<true>;
}
