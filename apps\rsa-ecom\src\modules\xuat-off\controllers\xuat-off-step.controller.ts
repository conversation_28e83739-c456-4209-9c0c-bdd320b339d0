import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CurrentUser, generalSchema, UserDto } from '@shared';
import { UpdateXuatOffStepInfoDto, XuatOffStepInfoRes } from '../dto';
import { XuatOffEntity, XuatOffStepInfoEntity } from '../entities';
import { XuatOffStepInfoService } from '../services/xuat-off-step.service';
import { CreateXuatOffStepInfoDto } from '../dto/xuat-off-step';

@ApiTags('xuat-off-step')
@Controller({ path: 'xuat-off-step', version: '1' })
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(XuatOffStepInfoRes)
export class XuatOffStepController {
  constructor(private readonly xuatOffStepInfoService: XuatOffStepInfoService) {}

  @Post()
  @ApiOperation({ summary: 'Create new step' })
  @ApiOkResponse({
    description: 'create step successfully',
    schema: generalSchema(XuatOffStepInfoRes, 'object'),
  })
  async create(@Body() createDto: CreateXuatOffStepInfoDto, @CurrentUser() user: UserDto): Promise<XuatOffEntity> {
    return await this.xuatOffStepInfoService.create(createDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'Get all steps' })
  @ApiOkResponse({
    description: 'get step successfully',
    schema: generalSchema([XuatOffStepInfoRes], 'object'),
  })
  async findAll(): Promise<XuatOffStepInfoEntity[]> {
    return await this.xuatOffStepInfoService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get step by id' })
  @ApiOkResponse({
    description: 'get step successfully',
    schema: generalSchema(XuatOffStepInfoRes, 'object'),
  })
  async findOne(@Param('id', new ParseUUIDPipe()) id: string): Promise<XuatOffStepInfoEntity> {
    return await this.xuatOffStepInfoService.findOneById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update step' })
  @ApiOkResponse({
    description: 'Update step successfully',
    schema: generalSchema(XuatOffStepInfoRes, 'object'),
  })
  async update(@Param('id') id: string, @Body() updateDto: UpdateXuatOffStepInfoDto): Promise<XuatOffStepInfoEntity> {
    return await this.xuatOffStepInfoService.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete step' })
  @ApiOkResponse({
    description: 'Delete step successfully',
  })
  async remove(@Param('id') id: string): Promise<void> {
    return await this.xuatOffStepInfoService.remove(id);
  }
}
