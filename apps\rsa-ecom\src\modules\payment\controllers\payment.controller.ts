import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Query, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ClassResponse } from '@shared';
import { PaymentService } from '../services/payment.service';
import { Public } from '@shared';
import { generalSchema } from '@shared';
import {
  GetListPaymentMethodRes,
  VerifyVoucherGotitLibDto,
  VerifyVoucherGotitResponseLibDto,
  VerifyVoucherUrboxLibDto,
  VerifyVoucherUrboxResponseLibDto,
} from 'vac-nest-payment-gateway';
import { CheckBillDto, CheckBillRes, GetListPaymentMethodPreOrderRes, GetListPaymentPayVaccineRes } from '../dto';
import { SendSmsPaymentDto } from '../dto/send-sms-payment-link.dto';
import { GetListDepositAmountBySkuDto } from 'vac-nest-osr';
import { GetListPaymentPayVaccineDto } from '../dto/get-list-payment-pay-vaccine.dto';
import { GetPaymentMethodDto } from '../dto/get-payment-method.dto';

@Controller({ path: 'payment-gateway', version: '1' })
@ApiTags('Payment')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(ClassResponse, GetListPaymentMethodRes, CheckBillRes, GetListPaymentPayVaccineRes)
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Get('payment-method')
  @ApiOperation({
    summary: 'Lấy thông tin phương thức thanh toán',
  })
  @Public()
  @ApiOkResponse({
    description: 'Danh sách phương thức thanh toán',
    schema: generalSchema(GetListPaymentMethodRes, 'object'),
  })
  getListPaymentMethod(@Req() req: any, @Query() params: GetPaymentMethodDto) {
    return this.paymentService.getListPaymentMethod(params, req.headers['order-channel']);
  }

  @Get('get-list-payment-method-pre-order')
  @ApiOperation({
    summary: 'Lấy thông tin phương thức thanh toán cho pre-order',
  })
  @Public()
  @ApiOkResponse({
    description: 'Danh sách phương thức thanh toán pre-order',
    schema: generalSchema(GetListPaymentMethodPreOrderRes, 'object'),
  })
  getListPaymentMethodPreOrder(@Query() params: GetListDepositAmountBySkuDto, @Req() req: any) {
    return this.paymentService.getListPaymentMethodPreOrder(params, req.headers['order-channel']);
  }

  @Post('check-bill')
  @ApiOperation({
    summary: 'Check Bill',
  })
  @Public()
  @ApiOkResponse({
    description: 'Check Bill data',
    schema: generalSchema(CheckBillRes, 'object'),
  })
  checkBill(@Body() checkBillDto: CheckBillDto, @Req() req: any) {
    return this.paymentService.checkBill(checkBillDto, req.headers['order-channel']);
  }

  /**
   * @TODO lấy danh sách chi tiền
   */
  @Get('get-list-payment-pay-vaccine')
  @ApiOperation({
    summary: 'Lấy danh sách phương thức chi tiền',
  })
  @Public()
  @ApiOkResponse({
    description: 'Thông tin danh sách phương thức chi tiền',
    schema: generalSchema(GetListPaymentPayVaccineRes, 'object'),
  })
  getListPaymentPayVaccine(@Query() getPaymentPayVaccineDto: GetListPaymentPayVaccineDto) {
    return this.paymentService.getPaymentPayVaccine(getPaymentPayVaccineDto);
  }

  @Post('payment-link/send-vnpay')
  @ApiOperation({
    summary: 'Gửi link thanh toán VNPAY',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Gửi link thanh toán VNPAY',
  })
  sendVnpayLink(@Body() sendSmsAlepayDto: SendSmsPaymentDto, @Req() req: any) {
    return this.paymentService.sendVnpayLink(sendSmsAlepayDto, req.user);
  }

  @Post('payment-link/send-alepay')
  @ApiOperation({
    summary: 'Gửi link thanh toán alepay',
  })
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'link thanh toán',
  })
  sendAlepayLink(@Body() sendSmsAlepayDto: SendSmsPaymentDto, @Req() req: any) {
    return this.paymentService.sendAlepayLink(sendSmsAlepayDto, req.user);
  }

  @Post('payment-link/send-hpl')
  @ApiOperation({
    summary: 'Gửi link thanh toán home paylater',
  })
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'link thanh toán',
  })
  sendHPLLink(@Body() sendSmsHLPDto: SendSmsPaymentDto) {
    return this.paymentService.sendHPLLink(sendSmsHLPDto);
  }

  @Post('payment-link/send-zalopay')
  @ApiOperation({
    summary: 'Gửi link thanh toán zalopay',
  })
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'link thanh toán',
  })
  sendZalopayLink(@Body() sendSmsAlepayDto: SendSmsPaymentDto, @Req() req: any) {
    return this.paymentService.sendZalopayLink(sendSmsAlepayDto, req.user);
  }

  @Post('payment-link/send-momo')
  @ApiOperation({
    summary: 'Gửi link thanh toán momo',
  })
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'link thanh toán momo',
  })
  sendMomoLink(@Body() sendSmsMomoDto: SendSmsPaymentDto, @Req() req: any) {
    return this.paymentService.sendMomoLink(sendSmsMomoDto, req.user);
  }

  @Post('payment-link/send-transfer')
  @ApiOperation({
    summary: 'Gửi link thanh toán chuyển khoản',
  })
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'link thanh toán chuyển khoản',
  })
  sendTransferLink(@Body() sendSmsTransferDto: SendSmsPaymentDto) {
    return this.paymentService.sendTransferLink(sendSmsTransferDto);
  }

  @Get('payment-link/get-link-status-by-order/:orderCode')
  @ApiOperation({
    summary: 'Kiểm tra link thanh toán',
  })
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Trạng thái link thanh toán',
  })
  checkPaymentLink(@Param('orderCode') orderCode: string) {
    return this.paymentService.checkPaymentLink(orderCode);
  }

  @Public()
  @Post('verify-voucher-gotit')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kiem tra voucher gotit',
    schema: generalSchema(VerifyVoucherGotitResponseLibDto, 'object'),
  })
  verifyVoucherGotit(@Body() param: VerifyVoucherGotitLibDto) {
    return this.paymentService.verifyVoucherGotit(param);
  }

  @Public()
  @Post('verify-voucher-urbox')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Kiem tra voucher urbox',
    schema: generalSchema(VerifyVoucherUrboxResponseLibDto, 'object'),
  })
  verifyVoucherUrbox(@Body() param: VerifyVoucherUrboxLibDto) {
    return this.paymentService.verifyVoucherUrbox(param);
  }
}
