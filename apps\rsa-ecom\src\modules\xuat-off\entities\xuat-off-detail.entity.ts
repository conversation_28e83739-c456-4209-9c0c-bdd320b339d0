import { AbstractEntity } from '@shared/database/typeorm/abtract.entity';
import { AfterUpdate, BeforeUpdate, Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { OrderTypeXuatOff } from '../constants';
import { XuatOffEntity } from './xuat-off.entity';

@Entity({ name: 'xuat_off_detail' })
export class XuatOffDetailEntity extends AbstractEntity<XuatOffDetailEntity> {
  @Column({ name: 'xo_code', type: 'varchar', length: 100 })
  xoCode?: string;

  @Column({ name: 'order_code_shop', type: 'varchar', length: 100, nullable: true })
  orderCodeShop?: string;

  @Column({ name: 'order_shop_create_date', type: 'timestamp', nullable: true })
  orderShopCreateDate: Date;

  @Column({ name: 'order_shop_complete_date', type: 'timestamp', nullable: true })
  orderShopCompleteDate: Date;

  @Column({ name: 'customer_phone', type: 'varchar', length: 20, nullable: true })
  customerPhone: string;

  @Column({ name: 'customer_name', type: 'varchar', length: 100, nullable: true })
  customerName: string;

  @Column({ type: 'enum', enum: OrderTypeXuatOff, nullable: true })
  type: OrderTypeXuatOff;

  @Column({ name: 'is_delete', default: false })
  isDelete?: boolean;

  @Column({ type: 'int', nullable: true })
  status?: number;

  @Column({ name: 'reason_cancel', type: 'text', nullable: true })
  reasonCancel?: string;

  @Column({ name: 'created_by', type: 'varchar', length: 100 })
  createdBy?: string;

  @Column({ name: 'created_by_name', type: 'varchar', length: 100 })
  createdByName?: string;

  @Column({ name: 'modified_by', type: 'varchar', length: 100, nullable: true })
  modifiedBy?: string;

  @Column({ name: 'modified_by_name', type: 'varchar', length: 100, nullable: true })
  modifiedByName?: string;

  @ManyToOne(() => XuatOffEntity, (xuatOff) => xuatOff.details, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'xo_id' })
  xuatOff?: XuatOffEntity;
}
