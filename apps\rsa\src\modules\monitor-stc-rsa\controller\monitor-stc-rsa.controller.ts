import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { ApiBadRequestResponse, ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ClassErrorResponse, Public } from '@shared';
import _ from 'lodash';
import { MonitorStcService } from '@libs/modules/monitor-stc/services/monitor-stc.service';
import { GetMonitorStcDto } from '@libs/modules/monitor-stc/dto/get-monitor-stc.dto';
import { CalculateTempPricingDto } from '@libs/modules/monitor-stc/dto/calculate-temp-pricing.dto';

@Controller({ path: 'monitor-stc', version: '1' })
@ApiTags('Monitor STC')
@ApiBearerAuth('defaultJWT')
@ApiBadRequestResponse({
  description: 'Trả lỗi khi đầu vào bị sai',
  type: ClassErrorResponse,
})
export class MonitorStcRsaController {
  constructor(private readonly monitorStcService: MonitorStcService) {}

  @Public()
  @Post('/load')
  @ApiOperation({
    summary: 'Load màn hình stc',
  })
  @HttpCode(HttpStatus.OK)
  async loadMonitorStc(@Body() body: GetMonitorStcDto) {
    return await this.monitorStcService.loadVaccinationBook(body);
  }

  @Public()
  @Post('/pricing')
  @ApiOperation({
    summary: 'Tính giá tạm thời cho nhu cầu tư vấn',
  })
  @HttpCode(HttpStatus.OK)
  async pricing(@Body() body: CalculateTempPricingDto) {
    return await this.monitorStcService.calculateTempPricing(body);
  }
}
