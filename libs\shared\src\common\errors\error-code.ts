export class ErrorCode {
  static RSA_ORDER_STATUS_NOT_FINISH_DEPOSITED = 'RSA_ORDER_STATUS_NOT_FINISH_DEPOSITED:000403';
  static RSA_ORDER_STATUS_NOT_CONFIRMED = 'RSA_ORDER_STATUS_NOT_CONFIRMED:000403';
  static RSA_TICKET_EMPTY = 'RSA_INDICATION_BUTTON_EMPTY:000403';
  static RSA_EXAMINATION_TICKET_INDICATION_EMPTY = 'RSA_EXAMINATION_TICKET_INDICATION_EMPTY:000403';
  static RSA_ORDER_ATTACHMENT_CODE = 'RSA_ORDER_ATTACHMENT_CODE:000403';
  static RSA_CART_ORDER = 'RSA_CART_ORDER_ERROR:000403';
  static RSA_OSR_DATA_NOT_FOUND = 'RSA_OSR_DATA_NOT_FOUND:000400';
  static RSA_CANCEL_ORDER_FORBIDDEN = 'RSA_RSA_CANCEL_ORDER_FORBIDDEN:000403';
  static RSA_CHANGE_ORDER_TYPE_FAILED = 'RSA_CHANGE_ORDER_TYPE_FAILED:000403';
  static RSA_VOUCHER_OF_PRE_ORDER = 'RSA_VOUCHER_OF_PRE_ORDER:000403';
  static RSA_UPDATE_REGISTERED_FAILED = 'RSA_UPDATE_REGISTERED_FAILED:000400';
  static RSA_UNVERIFIED_VOUCHER_OTP = 'RSA_UNVERIFIED_VOUCHER_OTP:000403';
  static RSA_VOUCHER_PARTNER = 'RSA_VOUCHER_PARTNER:000403';
  static RSA_CONTRACT_NOT_FOUND = 'RSA_CONTRACT_NOT_FOUND:000400';
  static RSA_USER_SM = 'RSA_USER_SM:000403';
  static HEADER_VALIDATE = 'HEADER_VALIDATE:000400';
  static INTERNAL_ROTATION_EXISTED = 'INTERNAL_ROTATION_EXISTED:000403';
  static INTERNAL_ROTATION_ENOUGH_QUANTITY = 'INTERNAL_ROTATION_ENOUGH_QUANTITY:000403';
  static ORDER_RULE_DISCOUNT_ADJUSTMENT = 'ORDER_RULE_DISCOUNT_ADJUSTMENT:000403';
  static OCR_CAN_NOT_DETECT = 'OCR_CAN_NOT_DETECT';
  static RSA_VERIFY_USER = 'RSA_VERIFY_USER:000413';
  static RSA_DEPOSIT_EXTRA_PAYMENT = 'RSA_VERIFY_USER:000403';
  static RSA_CASE_REFUND_PAYMENT_DEPOSIT_NOT_FOUND = 'RSA_CASE_REFUND_PAYMENT_DEPOSIT_NOT_FOUND:001403';
  static RSA_CASE_REFUND_PAYMENT_DEPOSIT_AMOUNT_ZERO = 'RSA_CASE_REFUND_PAYMENT_DEPOSIT_AMOUNT_ZERO:004403';
  static RSA_DEPOSIT_CANCEL_NOT_FOUND = 'RSA_REFUND_CONFIRM_NOT_FOUND:000400';
  static RSA_SCHEDULE_MANUALLY_EXIST = 'RSA_SCHEDULE_MANUALLY_EXIST:000400';
  static RSA_SCHEDULE_MANUALLY_DATE = 'RSA_SCHEDULE_MANUALLY_EXIST:000401';
  static RSA_SCHEDULE_MANUALLY_DATE_GROUP = 'RSA_SCHEDULE_MANUALLY_DATE_GROUP:000402';
  static RSA_SCHEDULE_MANUALLY_DISEASE_GROUP = 'RSA_SCHEDULE_MANUALLY_DISEASE_GROUP:000401';
  static RSA_SCHEDULE_MANUALLY_PAID = 'RSA_SCHEDULE_MANUALLY_PAID:000400';
  static RSA_SCHEDULE_MANUALLY_PAID_DISEASE = 'RSA_SCHEDULE_MANUALLY_PAID_DISEASE:000400';
  static RSA_SCHEDULE_MANUALLY_REGIMEN = 'RSA_SCHEDULE_MANUALLY_REGIMEN:000400';
  static RSA_SCHEDULE_MANUALLY_ECOM = 'RSA_SCHEDULE_MANUALLY_ECOM:000400';
  static RSA_SCHEDULE_MANUALLY_DATE_DUP = 'RSA_SCHEDULE_MANUALLY_DUP:000402';
  static RSA_HISTORY_MANUALLY_DATE = 'RSA_HISTORY_MANUALLY_EXIST:000401';
  static RSA_HISTORY_MANUALLY_DATE_DUP = 'RSA_HISTORY_MANUALLY_DUP:000401';
  static RSA_SCHEDULE_MANUALLY_WRONG_STT = 'RSA_SCHEDULE_MANUALLY_EXIST:000402';
  static RSA_SCHEDULE_MANUALLY_WRONG_STT_CASE_1 = 'RSA_SCHEDULE_MANUALLY_EXIST:000403';
  static RSA_SCHEDULE_MANUALLY_WRONG_STT_CASE_2 = 'RSA_SCHEDULE_MANUALLY_EXIST:000405';
  static RSA_REFUND_CASH_TYPE_NOT_FOUND = 'RSA_REFUND_CASH:000400';
  static RSA_SYNC_TCQG_EXIST = 'RSA_SYNC_TCQG_EXIST:000400';
  static RSA_SYNC_PERSON_EXIST = 'RSA_SYNC_PERSON_EXIST:000401';
  static RSA_SYNC_PERSON_NOT_HAVE_DOB = 'RSA_SYNC_PERSON_NOT_HAVE_DOB:000400';
  static RSA_CHECK_DOUBLE = 'RSA_CHECK_DOUPLE:000402';
  static RSA_CHECK_SHOP_PHONE = 'RSA_CHECK_SHOP_PHONE:000400';
  static RSA_DEPOSIT_CANCEL_PHONE_NOT_FOUND = 'RSA_DEPOSIT_CANCEL:000400';
  static RSA_TYPE_CONFIRM_REFUND_CASH_NOT_FOUND = 'RSA_TYPE_CONFIRM_REFUND_CASH_NOT_FOUND:000400';
  static RSA_REFUND_REASON_CASH_NOT_FOUND = 'RSA_REFUND_REASON_CASH:000404';
  static RSA_DEPOSIT_CANCEL_PAYMENT_FAILED = 'RSA_DEPOSIT_CANCEL:000413';
  static RSA_DEPOSIT_CANCEL_FAILED = 'RSA_DEPOSIT_CANCEL:000403';
  static RSA_DETAIL_BARCODE = 'RSA_DETAIL_BARCODE:00403';
  static RSA_PRICING_NOT_FOUND = 'RSA_PRICING:00404';
  static RSA_UPLOAD_FAIL = 'RSA_UPLOAD:00500';
  static RSA_SHOP_CODE_CHANGING_ITEM_NOT_FOUND = 'RSA_SHOP_CODE_CHANGING_ITEM_NOT_FOUND:00404';
  static RSA_NO_SCHEDULE_TO_UPDATE = 'RSA_NO_SCHEDULE_TO_UPDATE:00404';
  static FAMILY_DUPLICATE = 'FAMILY_DUPLICATE:000400';
  static RSA_FTEL_UNAUTHORIZED = 'RSA_FTEL_UNAUTHORIZED:00500';
  static PRODUCT_EXIST = 'RSA_PRODUCT_EXIST:00409';
  static CART_EMPTY = 'RSA_CART_EMPTY:00403';
  static CHECK_IMEI_FORBIDDEN = 'RSA_CHECK_IMEI_ERROR:00403';
  static NOT_FOUND = 'RSA_ERROR:00404';
  static CONFLICT = 'RSA_ERROR:00409';
  static UNAUTHORIZED = 'RSA_ERROR:00401';
  static BAD_REQUEST = 'RSA_ERROR:00400';
  static INTERNAL_SERVER = 'RSA_ERROR:00500';
  static CORE_INTERNAL_SERVER = 'CORE_ERROR:00500';
  static INVALID_HOME_CREDIT_CONTRACT_SCHEME = 'RSA_HOMECREDIT_CONTRACT_SCHEME:00170';
  static INVALID_HOME_CREDIT_CONTRACT_SCHEME_PROPERTY = 'INVALID_HOME_CREDIT_CONTRACT_SCHEME_PROPERTY:00170';
  static PREORDER_COMBINE_WARRANTY = 'RSA_PREORDER_COMBINE_WARRANTY:000403';
  static IP15_CANCEL_ORDER = 'RSA_IP15_CANCEL_ORDER:000403';
  static SAMSUNG_INSTALLMENT_IMEI = 'SAMSUNG_INSTALLMENT_IMEI:000403';
  static RSA_ECOM_PHONE_IS_HANDLED = 'RSA_ECOM_CALL_RSA_ECOM_PHONE_IS_HANDLED:000400';
  static RSA_ECOM_INSIDE_ERROR = 'RSA_ECOM_INSIDE_ERROR:000400';
  static RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING = 'RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING:000403';
  static RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT = 'RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT:000404';
  static RSA_ECOM_ASSIGN_DATE_IS_IN_PAST = 'RSA_ECOM_ASSIGN_DATE_IS_IN_PAST:000400';
  static RSA_ECOM_INSERT_EMPLOYEE_DUPLICATE = 'RSA_ECOM_INSERT_EMPLOYEE_DUPLICATE:000400';
  static RSA_ECOM_ASSIGN = 'RSA_ECOM_ASSIGN:100404';
  static RSA_ECOM_ASSIGN_PRE_ORDER = 'RSA_ECOM_ASSIGN_PRE_ORDER:100404';
  static RSA_ECOM_CHECK_IN_PRE_ORDER = 'RSA_ECOM_CHECK_IN_PRE_ORDER:100404';
  static RSA_ECOM_PRE_ORDER_ASSIGN = 'RSA_ECOM_PRE_ORDER_ASSIGN:100404';
  static RSA_ECOM_ASSIGN_ROLE_SURVEY = 'RSA_ECOM_ASSIGN_ROLE_SURVEY:000400';
  static RSA_ECOM_ASSIGN_ROLE = 'RSA_ECOM_ASSIGN_ROLE:000400';
  static RSA_ECOM_ASSIGN_ROLE_PRE_ORDER = 'RSA_ECOM_ASSIGN_ROLE_PRE_ORDER:000400';
  static RSA_ECOM_STATUS_UPDATE_NOT_APPROVED = 'RSA_ECOM_STATUS_UPDATE_NOT_APPROVED:100400';
  static RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST = 'RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST:100400';
  static RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST = 'RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST:100404';
  static RSA_ECOM_EMPLOYEE_NOT_ONLINE = 'RSA_ECOM_EMPLOYEE_NOT_ONLINE:100404';
  static RSA_ECOM_ASSIGN_SURVEY_USER_NOT_CHECK_IN = 'RSA_ECOM_ASSIGN_SURVEY_USER_NOT_CHECK_IN:000400';
  static RSA_ECOM_ASSIGN_SURVEY_USER_NOT_ONLINE = 'RSA_ECOM_ASSIGN_SURVEY_USER_NOT_ONLINE:000400';
  static RSA_ECOM_ASSIGN_SURVEY_USER_ONLINE = 'RSA_ECOM_ASSIGN_SURVEY_USER_ONLINE:000400';
  static RSA_NOT_ENOUGH_STOCK = 'RSA_NOT_ENOUGH_STOCK:000403';
  static RSA_ECOM_GROUP_MEMBER_LV2_ALREADY_EXIST = 'RSA_ECOM_GROUP_MEMBER_LV2_ALREADY_EXIST:000429';
  static RSA_ECOM_GROUP_MEMBER_LV2_DOES_NOT_EXIST = 'RSA_ECOM_GROUP_MEMBER_LV2_ALREADY_EXIST:000430';
  static RSA_ECOM_GROUP_DOES_NOT_EXIST = 'RSA_ECOM_GROUP_DOES_NOT_EXIST:000404';
  static RSA_ECOM_YOUR_GROUP_LV2_NOT_FOUND = 'RSA_ECOM_YOUR_GROUP_LV2_NOT_FOUND:000404';
  static RSA_ECOM_YOU_ARE_NOT_ASSIGNED_GROUP_LV2 = 'RSA_ECOM_YOU_ARE_NOT_ASSIGNED_GROUP_LV2:000404';
  static RSA_ECOM_REQUEST_NOT_FOUND = 'RSA_ECOM_REQUEST_NOT_FOUND:000404';
  static RSA_ECOM_NOT_ALLOW_TO_EDIT = 'RSA_ECOM_NOT_ALLOW_TO_EDIT:000401';
  static RSA_ECOM_ASSIGN_GROUP_LV2_NOT_FOUND = 'RSA_ECOM_ASSIGN_GROUP_LV2_NOT_FOUND:000404';
  static RSA_ECOM_GEN_LINK_FAIL = 'RSA_ECOM_GEN_LINK_FAIL:000500';
  static RSA_ECOM_PAYMENT_NOT_FULL = 'RSA_ECOM_PAYMENT_NOT_FULL:000400';
  static RSA_ECOM_CALL_LOG_NOT_FOUND = 'RSA_ECOM_CALL_LOG_NOT_FOUND:000404';
  static RSA_NOT_FOUND_VERSION_TICKET_CODE = 'RSA_VERSION_TICKET_CODE_ERROR:00404';
  static RSA_ECOM_TICKET_NOT_FOUND = 'RSA_ECOM_TICKET_NOT_FOUND:000404';
  static RSA_ECOM_CART_NOT_FOUND = 'RSA_ECOM_CART_NOT_FOUND:000404';
  static RSA_ECOM_ORDER_NOT_FOUND = 'RSA_ECOM_ORDER_NOT_FOUND:000404';
  static RSA_ECOM_ORDER_ATT_SCHEDULE_NOT_FOUND = 'RSA_ECOM_ORDER_ATT_SCHEDULE_NOT_FOUND:000404';
  static RSA_ECOM_ORDER_PUSH_ORDER_NOT_ALLOWED = 'RSA_ECOM_ORDER_PUSH_ORDER_NOT_ALLOWED:000400';
  static RSA_ECOM_CONSULTANT_NOT_ALLOWED_EXIST_ORDER_ECOM_DISPLAY_2 =
    'RSA_ECOM_CONSULTANT_NOT_ALLOWED_EXIST_ORDER_ECOM_DISPLAY_2:000400';
  static RSA_ECOM_ORDER_CONTINUE_BUYING_NOT_ALLOWED = 'RSA_ECOM_ORDER_CONTINUE_BUYING_NOT_ALLOWED:000400';
  static RSA_ECOM_ORDER_SAVE_ORDER_NOT_ALLOWED = 'RSA_ECOM_ORDER_SAVE_ORDER_NOT_ALLOWED:000400';
  static RSA_ECOM_PROCESS_OTHER_CHANNEL = 'RSA_ECOM_PROCESS_OTHER_CHANNEL:000403';
  static RSA_ECOM_REQUEST_INVALID_ASSIGN = 'RSA_ECOM_REQUEST_INVALID_ASSIGN:000400';
  static RSA_ECOM_REQUEST_INVALID_ASSIGN_V2 = 'RSA_ECOM_REQUEST_INVALID_ASSIGN_V2:000400';
  static RSA_ECOM_REQUEST_INVALID_ASSIGN_V3 = 'RSA_ECOM_REQUEST_INVALID_ASSIGN_V3:000400';
  static RSA_ECOM_NO_RIGHT_TO_ADJUST_ORDER = 'RSA_ECOM_NO_RIGHT_TO_ADJUST_ORDER:000400';
  static RSA_RE_EXAMINATION_CODE = 'RSA_RE_EXAMINATION_CODE:000403';
  static RSA_ECOM_PAYMENT_STORED_NOT_FOUND = 'RSA_ECOM_PAYMENT_STORED_NOT_FOUND:000404';
  static RSA_CANCEL_TICKET_FOR_DOCTOR = 'RSA_RSA_CANCEL_TICKET_FOR_DOCTOR:000403';
  static RSA_CANCEL_TICKET_NOT_ALLOWED = 'RSA_RSA_CANCEL_TICKET_NOT_ALLOWED:000403';
  static RSA_CANCEL_TICKET_TIEM_NOT_ALLOWED = 'RSA_CANCEL_TICKET_TIEM_NOT_ALLOWED:000403';
  static RSA_ECOM_ORDER_CANCELED = 'RSA_ECOM_ORDER_CANCELED:000400';
  static RSA_ECOM_ORDER_PUSHED = 'RSA_ECOM_ORDER_PUSHED:000400';
  static RSA_VERIFY_FORMAT_PHONE_NUMBER = 'RSA_VERIFY_FORMAT_PHONE_NUMBER:000403';
  static RSA_HISTORY_STT_INJECTION = 'RSA_HISTORY_STT_INJECTION:000403';
  static RSA_HISTORY_STT_INJECTION_WRONG = 'RSA_HISTORY_STT_INJECTION_WRONG:000403';
  static RSA_HISTORY_STT_INJECTION_DATE_WRONG = 'RSA_HISTORY_STT_INJECTION_DATE_WRONG:000403';
  static RSA_HISTORY_STT_INJECTION_WRONG_EXITS = 'RSA_HISTORY_STT_INJECTION_WRONG_EXITS:000403';
  static RSA_HISTORY_DUP_INJECTION_DATE = 'RSA_HISTORY_DUP_INJECTION_DATE:000403';
  static RSA_SYNC_TCQG_DETAIL_ERROR = 'RSA_SYNC_TCQG_DETAIL_ERROR:000404';
  static RSA_PHONE_EMPTY = 'RSA_PHONE_EMPTY:000404';
  static RSA_ECOM_UNAUTHORIZED_API = 'RSA_ECOM_UNAUTHORIZED_API:000401';
  static RSA_REPLACE_SKU_ERROR = 'RSA_REPLACE_SKU_ERROR:000400';
  static RSA_SYSTEM_ERROR_UPDATE_RULE = 'RSA_SYSTEM_ERROR_UPDATE_RULE:00400';
  static RSA_REPLACE_SKU_EXITS_HISTORY_ERROR = 'RSA_REPLACE_SKU_EXITS_HISTORY_ERROR:000400';
  static RSA_REPLACE_SKU_EXITS_VAC_ORDER_ERROR = 'RSA_REPLACE_SKU_EXITS_VAC_ORDER_ERROR:000400';
  static RSA_CUSTOMER_TRANSFER_INJECTION = 'RSA_CUSTOMER_TRANSFER_INJECTION:000404';
  static RSA_LIMIT_UTOP_TRANSACTION = 'RSA_LIMIT_UTOP_TRANSACTION:000400';
  static RSA_LIMIT_UTOP_RESEND_LINK = 'RSA_LIMIT_UTOP_RESEND_LINK:000400';
  static RSA_INACTIVE_PERSON_NOT_VACCINATED = 'RSA_INACTIVE_PERSON_NOT_VACCINATED:000400';
  static RSA_INACTIVE_PERSON_IN_FAMILY_PACKAGE = 'RSA_INACTIVE_PERSON_IN_FAMILY_PACKAGE:000400';
  static RSA_INACTIVE_PERSON_EXIST_SCHEDULE_ECOM = 'RSA_INACTIVE_PERSON_EXIST_SCHEDULE_ECOM:000400';
  static RSA_INACTIVE_PERSON_JOURNEY_OPEN = 'RSA_INACTIVE_PERSON_JOURNEY_OPEN:000400';
  static RSA_INACTIVE_PERSON_EXIST_HISTORY = 'RSA_INACTIVE_PERSON_EXIST_HISTORY:000400';
  static RSA_SYNC_TCQG_MULTIP_PROFILE_DUP = 'RSA_SYNC_TCQG_MULTIP_PROFILE_DUP:000400';
  static RSA_LIMIT_TIME_SYNC_TCQG = 'RSA_LIMIT_TIME_SYNC_TCQG:000408';
  static RSA_HISTORY_SYNC_TCQG_LIMIT = 'RSA_HISTORY_SYNC_TCQG_LIMIT:000409';
  static RSA_SCHEDULE_MANUALLY_VALID_TICKET = 'RSA_SCHEDULE_MANUALLY_VALID_TICKET:000400';
  static RSA_UPDATE_SCHEDULE_MANUALLY_VALID_TICKET = 'RSA_UPDATE_SCHEDULE_MANUALLY_VALID_TICKET:000400';
  static RSA_BLOCK_CONTINUE_ORDER_EXIST_OPEN_TICKET = 'RSA_BLOCK_CONTINUE_IF_EXIST_TICKET:000400';
  static RSA_SCHEDULE_MANUALLY_CANCEL_VALID_TICKET = 'RSA_SCHEDULE_MANUALLY_CANCEL_VALID_TICKET:000400';
  static RSA_CONTRACT_INPROCESSING = 'RSA_CONTRACT_INPROCESSING:000403';
  static RSA_CONTRACT_SEND_MESSAGE = 'RSA_CONTRACT_SEND_MESSAGE:000403';
  static RSA_CONTRACT_EMPTY_PHONE_NUMBER = 'RSA_CONTRACT_EMPTY_PHONE_NUMBER:000403';
  static RSA_CREATE_CUSTOMER_RULE = 'RSA_CREATE_CUSTOMER_RULE:000408';
  static RCA_PRODUCT_OUT_OF_STOCK = 'RCA_PRODUCT_OUT_OF_STOCK:000400';
  static RSA_DUP_SCHEDULE = 'RSA_DUP_SCHEDULE:000404';
  static RSA_SKU_SELL_RESTRICT = 'RSA_SKU_SELL_RESTRICT:000403';
  static RSA_SKU_SELL_RESTRICT_AT_CREATE_TICKET = 'RSA_SKU_SELL_RESTRICT_AT_CREATE_TICKET:000403';
  static RSA_SKU_SELL_RESTRICT_AT_INDICATION = 'RSA_SKU_SELL_RESTRICT_AT_INDICATION:000403';
  static RSA_SKU_SELL_RESTRICT_AT_PLACE_ORDER = 'RSA_SKU_SELL_RESTRICT_AT_PLACE_ORDER:000403';
  static RSA_SCHEDULE_ALREADY_SEND_MESSAGE = 'RSA_SCHEDULE_ALREADY_SEND_MESSAGE:000403';
  static RSA_SCHEDULE_EMPTY_PHONE_NUMBER = 'RSA_SCHEDULE_EMPTY_PHONE_NUMBER:000403';
  static RSA_TICKET_4_IN_DATE = 'RSA_TICKET_4_IN_DATE:000403';
  static RSA_ERROR_TICKET_OPEN = 'RSA_ERROR_TICKET_OPEN:000400';
  static RSA_CUSTOMER_BLOCK_DELETE_PHONE = 'RSA_CUSTOMER_BLOCK_DELETE_PHONE:400';
  static RSA_CUSTOMER_BLOCK_DELETE_HOST_PERSON = 'RSA_CUSTOMER_BLOCK_DELETE_HOST_PERSON:400';
  static RSA_ERROR_PAY_LATER_ORDER_FINISH_DEPOSIT = 'RSA_ERROR_PAY_LATER_ORDER_FINISH_DEPOSIT:000403';
  static RSA_INACTIVE_PERSON_WAITING_PAID = 'RSA_INACTIVE_PERSON_WAITING_PAID:000400';
  static RSA_CHANGE_ORDER_ATTRIBUTE = 'RSA_CHANGE_ORDER_ATTRIBUTE:000403';
  static RSA_ERROR_EMPLOYEE_NOT_FOUND = 'RSA_ERROR_EMPLOYEE_NOT_FOUND:000400';
  static RSA_ERROR_CONSULTANTS_ORDER_RULE = 'RSA_ERROR_CONSULTANTS_ORDER_RULE_000403';
  static RSA_ECOM_SHOP_VE_TINH_REGION_NOT_FOUND = 'RSA_ECOM_SHOP_VE_TINH_REGION_NOT_FOUND:000404';
  static RSA_ECOM_SHOP_VE_TINH_EMPLOYEE_NOT_FOUND = 'RSA_ECOM_SHOP_VE_TINH_EMPLOYEE_NOT_FOUND:000404';
  static RSA_ECOM_BUSY_EMPLOYEE = 'RSA_ECOM_BUSY_EMPLOYEE:000400';
  static RSA_IMS_OUT_OF_STOCK = 'RSA_IMS_OUT_OF_STOCK:000400';
  static RSA_BLOCK_UNIT_WITH_REGIMEN = 'RSA_BLOCK_UNIT_WITH_REGIMEN:000403';
  static RSA_DOUBLE_CLICK = 'RSA_DOUBLE_CLICK:0004001';
  static RSA_INVALID_JOURNEY_ID = 'RSA_INVALID_JOURNEY_ID:000404';
  static RSA_PREVENTION_PRODUCT_ATTRIBUTE = 'RSA_PREVENTION_PRODUCT_ATTRIBUTE:000403';
  static RSA_BLOCK_SKU_BEXSERO = 'RSA_BLOCK_SKU_BEXSERO:000403';
  static RSA_CONTINUE_BUYING_RULE = 'RSA_CONTINUE_BUYING_RULE:000403';
  static RSA_SAVE_MOBILE_CARRIER_RULE = 'RSA_SAVE_MOBILE_CARRIER_RULE:000403';
  static RSA_ECOM_SAVE_MOBILE_CARRIER_RULE = 'RSA_ECOM_SAVE_MOBILE_CARRIER_RULE:000403';
  static RSA_EWALLET_RULE = 'RSA_EWALLET_RULE:000403';
  static RSA_VOUCHER_PARTNER_RULE = 'RSA_VOUCHER_PARTNER_RULE:000403';
  static RSA_UPDATE_HISTORY_DIFF_DAY = 'RSA_UPDATE_HISTORY_DIFF_DAY:000403';
  static RSA_UPDATE_HISTORY_THAN_ONE_DAY = 'RSA_UPDATE_HISTORY_THAN_ONE_DAY:000403';
  static RSA_ALREADY_DEPOSIT = 'RSA_ALREADY_DEPOSIT:000403';
  static PAYMENT_GATEWAY_CANCEL_ORDER_10012 = 'PaymentGateway:10012';
  static PAYMENT_GATEWAY_CANCEL_ORDER_10013 = 'PaymentGateway:10013';
  static PAYMENT_GATEWAY_CANCEL_ORDER_10014 = 'PaymentGateway:10014';
  static PAYMENT_GATEWAY_CANCEL_ORDER_10016 = 'PaymentGateway:10016';
  static PAYMENT_GATEWAY_CANCEL_ORDER_10017 = 'PaymentGateway:10017';
  static RSA_DUP_PREGNANCY_NUMBER = 'RSA_DUP_PREGNANCY_NUMBER:000403';
  static RSA_DUP_PREGNANCY_DATE = 'RSA_DUP_PREGNANCY_DATE:000403';
  static RSA_DUP_SAME_PREGNANCY_DATE = 'RSA_DUP_SAME_PREGNANCY_DATE:000403';
  static RSA_CONTRAINDICATION_PREGNANT_WOMEN = 'RSA_CONTRAINDICATION_PREGNANT_WOMEN:000403';
  static RSA_ESTIMATE_DATE_SMALLER_PREGNANCY_DATE = 'RSA_ESTIMATE_DATE_SMALLER_PREGNANCY_DATE:000403';
  static RSA_BLOCK_PLACE_WITH_CANCEL_STATUS = 'RSA_BLOCK_PLACE_WITH_CANCEL_STATUS:000403';
  static RSA_BLOCK_UPDATA_CART = 'RSA_BLOCK_UPDATA_CART:000403';
  static RSA_BLOCK_GARDASIL_4_FOR_NAM = 'RSA_BLOCK_GARDASIL_4_FOR_NAM:000403';
  static RSA_TRANSFER_0D = 'RSA_TRANSFER_0D:000403';
  static RSA_ECOM_ONLY_SUPPORT = 'RSA_ECOM_ONLY_SUPPORT:000403';
  static RSA_ACTIVE_PERSON_HAVE_FAMILY = 'RSA_ACTIVE_PERSON_HAVE_FAMILY:000403';
  static RSA_INACTIVE_ORDER = 'RSA_INACTIVE_ORDER:000403';
  static RSA_CART_ORDER_ORDER_STATUS_CONFIRM = 'RSA_CART_ORDER_ORDER_STATUS_CONFIRM:000403';
  static RSA_BARCODE_EXPIRY = 'RSA_BARCODE_EXPIRY:000403';
  static RSA_BLOCK_FINISH_TICKET = 'RSA_BLOCK_FINISH_TICKET:000403';
  static RSA_BLOCK_DA_TIEM = 'RSA_BLOCK_DA_TIEM:000403';
  static RSA_BLOCK_DA_TRA_HANG = 'RSA_BLOCK_DA_TRA_HANG:000403';
  static RSA_INACTIVE_ORDER_HOAN_TAT_COC = 'RSA_INACTIVE_ORDER_HOAN_TAT_COC:000403';
  static RSA_PRINT_MULTIDOSE_INVALID_PAYLOAD = 'RSA_PRINT_MULTIDOSE_INVALID_PAYLOAD:000403';
  static RSA_INVENTORY_BY_LODATE_MULTIDOSE = 'RSA_INVENTORY_BY_LODATE_MULTIDOSE:000403';
  static RSA_UPDATE_TRACKING_MULTIDOSE_VALID_BARCODE = 'RSA_UPDATE_TRACKING_MULTIDOSE_VALID_BARCODE:000403';
  //   static RSA_UPDATE_MANY_SCHEDULE_REGIMENT_SCREEN_SLIDEBAR = 'RSA_UPDATE_MANY_SCHEDULE_REGIMENT_SCREEN_SLIDEBAR:000403';
  static RSA_UPDATE_MANY_SCHEDULE_REGIMENT_SCREEN_SCHEDULE = 'RSA_UPDATE_MANY_SCHEDULE_REGIMENT_SCREEN_SCHEDULE:000403';
  static RSA_UPDATE_MANY_SCHEDULE_REGIMENT_SCREEN_SCHEDULE_SKU =
    'RSA_UPDATE_MANY_SCHEDULE_REGIMENT_SCREEN_SCHEDULE_SKU:000403';
  static RSA_UPDATE_MANY_SCHEDULE_SCREEN_SCHEDULE_DATE = 'RSA_UPDATE_MANY_SCHEDULE_SCREEN_SCHEDULE_DATE:000403';
  static PRE_ORDER_PHASE_ISSUES = 'PRE_ORDER_PHASE_ISSUES:000403';
  static RSA_AFFILIATE_AUTH_INSIDE = 'RSA_AFFILIATE_AUTH_INSIDE:000403';
  static RSA_ORDER_ADJUST_LESS_MONEY = 'RSA_ORDER_ADJUST_LESS_MONEY:000403';
  static RSA_ERROR_RULE_DON_MIX = 'RSA_ERROR_RULE_DON_MIX:000403';
  static RSA_CASHBACK_STATUS = 'RSA_CASHBACK_STATUS:000400';
  static RSA_VALID_CONSULTANTS_AFFILIATE = 'RSA_VALID_CONSULTANTS_AFFILIATE:000403';
  static RSA_AFFILIATE_BLOCK_CANCEL_ORDER_DEPOSIT = 'RSA_AFFILIATE_BLOCK_CANCEL_ORDER_DEPOSIT:000400';
  static RSA_AFFILIATE_BLOCK_CANCEL_ORDER_COMPLETED = 'RSA_AFFILIATE_BLOCK_CANCEL_ORDER_COMPLETED:000400';
  static RSA_PROCESS_ORDER_COMPLETED = `RSA_PROCESS_ORDER_COMPLETED:000400`;
  static RSA_HAVE_TICKET_IS_OPEN = 'RSA_HAVE_TICKET_IS_OPEN:000400';

  static RSA_AFF_CREATE_ORDER_AFFILIATE = 'RSA_AFF_CREATE_ORDER_AFFILIATE:000403';
  static RSA_CREATE_ORDER_AFFILIATE = 'RSA_CREATE_ORDER_AFFILIATE:000403';
  static ECOM_CREATE_ORDER_AFFILIATE_HAS_ZERO_DEPOSIT = 'ECOM_CREATE_ORDER_AFFILIATE_HAS_ZERO_DEPOSIT:000403';
  static ECOM_HANDEL_WEB_APP_TTOL_TT = 'ECOM_HANDEL_WEB_APP_TTOL_TT:000403';
  static RSA_ECOM_ALREADY_HAVE_AFF_ORDER_DEPOSIT_COMPLETED = 'RSA_ECOM_ALREADY_HAVE_AFF_ORDER_DEPOSIT_COMPLETED:000403';

  static RSA_ECOM_PAYMENT_INVALID = 'RSA_ECOM_PAYMENT_INVALID:000403';
  static RSA_ECOM_PAYMENT_NOT_FOUND = 'RSA_ECOM_PAYMENT_NOT_FOUND:000404';
  static RSA_ECOM_CONTINUE_BUYING_AFFILIATE = 'RSA_ECOM_CONTINUE_BUYING_AFFILIATE:000403';

  static RSA_ECOM_PUSH_ORDER_ERROR_001001 = 'RSA_ECOM_PUSH_ORDER_ERROR:001001';
  static WEB_APP_ORDER_NOT_YET_COMPLETE_400 = 'WEB_APP_ORDER_NOT_YET_COMPLETE_400';
  static WEB_APP_ORDER_HANDLE_BY_DOCTOR_400 = 'WEB_APP_ORDER_HANDLE_BY_DOCTOR_400';
  static SCHEDULE_BANNED_SKU = 'SCHEDULE_BANNED_SKU:000403';
  static RSA_BLOCK_ADJUST_TICKET_ANOTHER_VACCINE = 'RSA_BLOCK_ADJUST_TICKET_ANOTHER_VACCINE:000400';
  static RSA_CHAN_KHONG_MUI_TIEM_SAU = 'RSA_CHAN_KHONG_MUI_TIEM_SAU:000403';
  static SURVEY_IS_IN_PROCESSING = 'SURVEY_IS_IN_PROCESSING:000403';
  static SURVEY_ALREADY_ASSIGN = 'SURVEY_ALREADY_ASSIGN:000403';
  static RSA_OWNER_NOT_UPDATE_OR_DELETE_PHONE_FAMILY_PACKAGE =
    'RSA_OWNER_NOT_UPDATE_OR_DELETE_PHONE_FAMILY_PACKAGE:000403';
  static RSA_OWNER_NOT_UPDATE_FAMILY_PACKAGE = 'RSA_OWNER_NOT_UPDATE_FAMILY_PACKAGE:000403';
  static RSA_OWNER_NOT_DELETE_PHONE_FAMILY_PACKAGE = 'RSA_OWNER_NOT_DELETE_PHONE_FAMILY_PACKAGE:000403';
  static CHO_HOI_CHAN_EVALUATION_REQUEST = 'CHO_HOI_CHAN_EVALUATION_REQUEST:000403';
  static CUSTOMER_HAS_NOT_ENOUGH_INFO = 'CUSTOMER_HAS_NOT_ENOUGH_INFO:000403';
  static RSA_MISSING_ITEMCART_FOR_FB = 'RSA_MISSING_ITEMCART_FOR_FB:000403';
  static RSA_BLOCK_CREATE_UPDATE_ORDER_HAVE_TTTP = 'RSA_BLOCK_CREATE_UPDATE_ORDER_HAVE_TTTP:000403';
  static STOCK_NOT_ENOUGH = 'STOCK_NOT_ENOUGH:000400';
  static RULE_MAX_INJECTION_DISEASE = 'RULE_MAX_INJECTION_DISEASE:00403';
  static RSA_ECOM_EMPLOYEE_NOT_FOUND = 'RSA_ECOM_EMPLOYEE_NOT_FOUND:00403';

  static RSA_RULE_ASM = 'RSA_RULE_ASM:00403';

  static RSA_RULE_ASM_SHOP = 'RSA_RULE_ASM_SHOP:00403';
  static RSA_ORDER_ADJUST_SAME_MONEY = 'RSA_ORDER_ADJUST_SAME_MONEY:000403';
  static RSA_AFF_RESTRICTION_SKU = 'RSA_AFF_RESTRICTION_SKU:00403';

  static VOUCHER_RETURN_MONEY = 'VOUCHER_RETURN_MONEY:00403';
  static RSA_SKU_STOCK_RESTRICT_AT_PLACE_ORDER = 'RSA_SKU_STOCK_RESTRICT_AT_PLACE_ORDER:000403';
  static RSA_PREVENTION_PRODUCT_ATTRIBUTE_RARE_GOOD = 'RSA_PREVENTION_PRODUCT_ATTRIBUTE_RARE_GOOD:000403';
  static RSA_PREVENTION_PUSH_ORDER_RARE_GOOD = 'RSA_PREVENTION_PUSH_ORDER_RARE_GOOD:000403';
  static RSA_PREVENTION_SAVE_ORDER_RARE_GOOD = 'RSA_PREVENTION_SAVE_ORDER_RARE_GOOD:000403';
  static DIFFERENT_SHIFT_WORKING = 'DIFFERENT_SHIFT_WORKING:00400';
  static NOT_YET_ASSIGN_SHIFT = 'NOT_YET_ASSIGN_SHIFT:00400';
  static RSA_CUSTOMER_BLOCK_UPDATE_HOST_PERSON = 'RSA_CUSTOMER_BLOCK_UPDATE_HOST_PERSON:00400';
  static RSA_FAMILY_PACKAGE_NOT_FOUND = 'RSA_FAMILY_PACKAGE_NOT_FOUND:00400';
  static ONLINE_ORDER_NOTFOUND = 'ONLINE_ORDER_NOT_FOUND:000404';
  static ONLINE_ORDER_ASSIGNED = 'ONLINE_ORDER_ALREADY_ASSIGNED:000403';
  static ONLINE_ORDER_PROCESSED = 'ONLINE_ORDER_ALREADY_PROCESSED:000403';
  static ONLINE_ORDER_USER_ASSIGNED = 'ONLINE_ORDER_USER_ALREADY_ASSIGN:000403';
  static ONLINE_ORDER_USER_NOT_ONLINE = 'ONLINE_ORDER_USER_NOT_ONLINE:000400';
  static RSA_CHECK_ORDER_PRIORITY_PAYMENTED_AFFILIATE_WEB_APP =
    'RSA_CHECK_ORDER_PRIORITY_PAYMENTED_AFFILIATE_WEB_APP:000400';
  static ORDER_SHIFT_NOT_MATCH_IN_USER_SHIFT = 'ORDER_SHIFT_NOT_MATCH_IN_USER_SHIFT:000400';
  static RSA_CHECK_ORDER_PRIORITY_AFFILIATE = 'RSA_CHECK_ORDER_PRIORITY_AFFILIATE:000400';
  static RSA_CHECK_ORDER_PRIORITY_WEB_APP = 'RSA_CHECK_ORDER_PRIORITY_WEB_APP: 000400';
  static M_RSA_OFFLINE_CONSULTANTS_NOT_ALLOWED_EXIST_ORDER = 'M_RSA_OFFLINE_CONSULTANTS_NOT_ALLOWED_EXIST_ORDER:00403';
  static AFFILLIATE_ECOM_NOT_ALLOWED_CANCEL_ORDER = 'AFFILLIATE_ECOM_NOT_ALLOWED_CANCEL_ORDER:00403';
  static AFFILLIATE_ECOM_NOT_ALLOWED_CONTINUE_BUYING = 'AFFILLIATE_ECOM_NOT_ALLOWED_CONTINUE_BUYING:00403';

  static ECOM_PENDING_AFFILIATE_WITH_SAME_DISEASE = 'ECOM_PENDING_AFFILIATE_WITH_SAME_DISEASE:000403';

  // Xuất off đơn ecom
  static XO_CAN_CANCEL_ONLY_PENDING = 'XO_CAN_CANCEL_ONLY_PENDING:00403';
  static RSA_ECOM_XUAT_OFF_VERIFY_ECOM = 'RSA_ECOM_XUAT_OFF_VERIFY_ECOM:000403';
  static RSA_ECOM_XUAT_OFF_VERIFY_AT_SHOP = 'RSA_ECOM_XUAT_OFF_VERIFY_AT_SHOP:000403';
  static XO_APPROVE_ROLE_NOT_ALLOWED = 'XO_APPROVE_ROLE_NOT_ALLOWED:00403';
  static XO_INVALID_STATUS_TRANSITION = 'XO_INVALID_STATUS_TRANSITION:00406';
  static XO_UPDATE_ONLY_PENDING = 'XO_UPDATE_ONLY_PENDING:00403';
  static XO_EXIST = 'XO_EXIST:00409';
  static RSA_ECOM_XUAT_OFF_IS_ECOM = 'RSA_ECOM_XUAT_OFF_IS_ECOM:00403';
  static XO_DETAIL_EXIST = 'XO_DETAIL_EXIST:00409';
  static RSA_ECOM_XUAT_OFF_DATE_RANGE = 'RSA_ECOM_XUAT_OFF_DATE_RANGE:00403';
  static RSA_XO_INVALID_ORDER = 'RSA_XO_INVALID_ORDER:00403';
  static RSA_ECOM_XUAT_OFF_VERIFY_ORDER_STATUS = 'RSA_ECOM_XUAT_OFF_VERIFY_ORDER_STATUS:00403';
  static RSA_ECOM_XUAT_OFF_INVALID_DATE = 'RSA_ECOM_XUAT_OFF_INVALID_DATE:00403';
  static RSA_MISSING_OTP = 'RSA_MISSING_OTP:00403';

  static PIS_PARTNER_INTEGRATION_404 = 'PISPartnerIntegration:404';

  private static errorMap = ErrorCode.createErrorMap();

  static getError(code: string): string {
    if (this.errorMap.has(code)) {
      return this.errorMap.get(code);
    }
    return 'Error code has not been defined';
  }

  static defaultErrorCode() {
    return 'ERR:00000';
  }

  private static createErrorMap(): Map<string, string> {
    const errorCode = new Map();

    errorCode.set(
      this.RSA_CHAN_KHONG_MUI_TIEM_SAU,
      'Không thể chuyển đơn thành đơn từng phần khi không có mũi tiêm sau. Vui lòng check lại lịch tiêm của Khách hàng',
    );

    errorCode.set(
      this.ECOM_CREATE_ORDER_AFFILIATE_HAS_ZERO_DEPOSIT,
      'Khách hàng có đơn cọc vệ tinh 0đ. Vui lòng xử lý trước khi tạo đơn hàng mới',
    );

    errorCode.set(
      this.RSA_AFF_CREATE_ORDER_AFFILIATE,
      'Khách hàng đã có một đơn hàng đặt cọc từ shop vệ tinh ở trạng thái Hoàn tất cọc. Không được tạo mới một đơn hàng đặt cọc khác',
    );

    errorCode.set(
      this.RSA_ERROR_RULE_DON_MIX,
      'Không được chỉ định mũi tiêm trong đơn hàng Thanh toán từng phần khi đang xử lý đơn Đặt cọc/đơn Preorder!',
    );

    errorCode.set(
      this.RSA_TRANSFER_0D,
      'Không thể chi tiền chuyển khoản với đơn hàng chi 0đ. Vui lòng chọn hình thức khác!',
    );
    errorCode.set(
      this.RSA_INACTIVE_ORDER,
      'Khách hàng đang có đơn hàng {{orderCode}} hoàn tất cọc vui lòng hủy cọc trước khi vô hiệu hóa hồ sơ',
    );
    errorCode.set(this.RSA_PHONE_EMPTY, 'Khách hàng này thiếu thông tin số điện thoại! Vui lòng kiểm tra lại');
    errorCode.set(this.RSA_TICKET_4_IN_DATE, 'Không được hẹn quá 4 mũi trong cùng 1 ngày. Vui lòng thao tác lại.');
    errorCode.set(this.RSA_HISTORY_DUP_INJECTION_DATE, 'Bạn đã tiêm {vaccineName} trong ngày hôm nay rồi!');
    errorCode.set(
      this.RSA_ORDER_STATUS_NOT_FINISH_DEPOSITED,
      'Đơn hàng không phải là hoàn tất cọc. Vui lòng vào lại chi tiết đơn hàng!',
    );
    errorCode.set(this.RSA_ORDER_STATUS_NOT_CONFIRMED, 'Đơn hàng đã hoàn tất cọc. Vui lòng vào tính năng huỷ cọc!');
    errorCode.set(this.RSA_TICKET_EMPTY, 'Không có thông tin lịch tiêm! Vui lòng kiểm tra lại dữ liệu!');
    errorCode.set(this.RSA_EXAMINATION_TICKET_INDICATION_EMPTY, 'Không có lịch tiêm. Vui lòng kiểm tra lại dữ liệu!');
    errorCode.set(this.RSA_ORDER_ATTACHMENT_CODE, 'Có mã không có orderAttachmentCode. vui lòng kiểm tra lại dữ liệu!');
    errorCode.set(
      this.RSA_CART_ORDER,
      'Đơn hàng đã thanh toán. Nếu muốn bỏ mũi tiêm vui lòng vào tính năng huỷ cọc hoặc hủy đơn!',
    );
    errorCode.set(this.RSA_NOT_ENOUGH_STOCK, 'không đủ tồn!');
    errorCode.set(this.RSA_ECOM_PHONE_IS_HANDLED, 'Số điện thoại đang được xử lý!!!');
    errorCode.set(this.IP15_CANCEL_ORDER, 'Iphone 15 series không thể huỷ cọc trong giai đoạn này!!!');
    errorCode.set(this.PREORDER_COMBINE_WARRANTY, 'Vui lòng thêm combo bảo hành 1 đổi 1 vào giỏ hàng!');
    errorCode.set(this.RSA_OSR_DATA_NOT_FOUND, 'Đơn hàng không có sản phẩm pre order hoặc đã hết thời gian pre order');
    errorCode.set(this.RSA_CANCEL_ORDER_FORBIDDEN, 'Không thể huỷ đơn hàng vì đơn hàng đã được thanh toán');
    errorCode.set(this.RSA_CHANGE_ORDER_TYPE_FAILED, 'Đơn hàng trả góp không được chuyển đổi loại đơn hàng khác');
    errorCode.set(this.RSA_VOUCHER_OF_PRE_ORDER, 'Voucher không được sử dụng ở giai đoạn này');
    errorCode.set(this.RSA_UPDATE_REGISTERED_FAILED, 'Không thể cập nhật thông tin đăng ký');
    errorCode.set(this.RSA_UNVERIFIED_VOUCHER_OTP, 'Voucher trong giỏ hàng chưa được verify!');
    errorCode.set(
      this.RSA_VOUCHER_PARTNER,
      'Voucher đối tác không thể thêm từ giỏ hàng. Vui lòng thêm từ màn hình payment!',
    );
    errorCode.set(this.RSA_CONTRACT_NOT_FOUND, 'Không tìm thấy thông tin hợp đồng trả góp');
    errorCode.set(this.RSA_USER_SM, 'User không có quyền thực hiện chức năng');
    errorCode.set(this.HEADER_VALIDATE, 'Header Phải có shopcode và order-channel');
    errorCode.set(this.INTERNAL_ROTATION_ENOUGH_QUANTITY, 'Không thể thực hiện LCNB vì đơn hàng đủ số lượng');
    errorCode.set(this.INTERNAL_ROTATION_EXISTED, 'Đơn hàng đã có LCNB trước đó');
    errorCode.set(this.ORDER_RULE_DISCOUNT_ADJUSTMENT, 'Không thể thực hiện điều chỉnh giá');
    errorCode.set(this.OCR_CAN_NOT_DETECT, 'Không thể lấy thông tin từ hình cung cấp');
    errorCode.set(this.RSA_VERIFY_USER, 'User sai otp');
    errorCode.set(this.RSA_CASE_REFUND_PAYMENT_DEPOSIT_NOT_FOUND, 'Không tìm thấy thông tin thanh toán cọc');
    errorCode.set(this.RSA_CASE_REFUND_PAYMENT_DEPOSIT_AMOUNT_ZERO, 'Lỗi chi tiền 0 đồng');
    errorCode.set(this.RSA_DEPOSIT_CANCEL_NOT_FOUND, 'Không tìm thấy thông tin xác nhận hủy cọc');
    errorCode.set(this.RSA_REFUND_CASH_TYPE_NOT_FOUND, 'Không tìm thấy hình thức chi tiền');
    errorCode.set(this.RSA_DEPOSIT_CANCEL_PHONE_NOT_FOUND, 'Không tìm số điện thoại mua hàng');
    errorCode.set(this.RSA_TYPE_CONFIRM_REFUND_CASH_NOT_FOUND, 'Không tìm thấy hình thức chi tiền');
    errorCode.set(this.RSA_REFUND_REASON_CASH_NOT_FOUND, 'Không tìm thấy lý do hủy cọc trong hệ thống');
    errorCode.set(this.RSA_DEPOSIT_CANCEL_PAYMENT_FAILED, 'Không tìm thấy số tiền đặt cọc trong lịch sử thanh toán');
    errorCode.set(this.RSA_DEPOSIT_CANCEL_FAILED, 'Không phải đơn hàng đặc cọc hoặc chưa hoàn tất cọc');
    errorCode.set(this.RSA_DETAIL_BARCODE, 'Sản phẩm không đủ imei');
    errorCode.set(this.RSA_PRICING_NOT_FOUND, 'Không tìm thấy sản phẩm trong pricing');
    errorCode.set(this.RSA_UPLOAD_FAIL, 'Lỗi không thể đẩy file lên hệ thống');
    errorCode.set(this.PRODUCT_EXIST, 'Thông tin đã có trong giỏ hàng.');
    errorCode.set(this.CART_EMPTY, 'Thông tin trên giỏ hàng rỗng.');
    errorCode.set(this.CHECK_IMEI_FORBIDDEN, 'Sản phẩm đã được đặt một nơi khác! vui lòng chọn imei khác');
    errorCode.set(this.NOT_FOUND, 'Không tìm thấy thông tin trong hệ thống');
    errorCode.set(this.CONFLICT, 'Thông tin đã có trong hệ thống');
    errorCode.set(this.UNAUTHORIZED, 'API cần phải có token của người dùng');
    errorCode.set(this.RSA_ECOM_UNAUTHORIZED_API, 'API cần có key của người dùng');
    errorCode.set(this.BAD_REQUEST, 'Đầu vào không hợp lệ');
    errorCode.set(this.INTERNAL_SERVER, 'Lỗi hệ thống');
    errorCode.set(this.CORE_INTERNAL_SERVER, 'Lỗi hệ thống tích hợp');
    errorCode.set(this.INVALID_HOME_CREDIT_CONTRACT_SCHEME, 'Gói trả góp có thể không tìm thấy');
    errorCode.set(this.INVALID_HOME_CREDIT_CONTRACT_SCHEME_PROPERTY, 'Gói trả góp sai thông tin');
    errorCode.set(this.RSA_DEPOSIT_EXTRA_PAYMENT, 'Số tiền chi không hợp lệ');
    errorCode.set(this.RSA_FTEL_UNAUTHORIZED, 'Lỗi xác thực người dùng');
    errorCode.set(
      this.SAMSUNG_INSTALLMENT_IMEI,
      'IMEI trong giỏ hàng khác với thông tin trong hợp đồng. Vui lòng chọn lại',
    );
    errorCode.set(this.RSA_ECOM_INSIDE_ERROR, 'Nhân viên này không phải Nhân viên kinh doanh eCom Vắc xin');
    errorCode.set(this.RSA_ECOM_ASSIGNEE_EXISTING_REQUEST_PENDING, 'Nhân viên đang xử lý yêu cầu số {id}');
    errorCode.set(this.RSA_ECOM_ASSIGNEE_IS_NOT_IN_SHIFT, 'Nhân viên hiện không trong ca tiếp nhận yêu cầu');
    errorCode.set(this.RSA_ECOM_ASSIGN_DATE_IS_IN_PAST, 'Ngày cập nhật đang là ngày trong quá khứ.');
    errorCode.set(this.RSA_ECOM_INSERT_EMPLOYEE_DUPLICATE, 'Nhân viên đã được phân công vào nhóm khác.');
    errorCode.set(
      this.RSA_ECOM_ASSIGN,
      'Bạn sẽ không nhận được đơn hàng vì chưa được phân công! Vui lòng liên hệ quản lý.',
    );
    errorCode.set(
      this.RSA_ECOM_CHECK_IN_PRE_ORDER,
      'Bạn sẽ không nhận được yêu cầu vì chưa được phân công! Vui lòng liên hệ quản lý.',
    );
    errorCode.set(
      this.RSA_ECOM_ASSIGN_PRE_ORDER,
      'Nhân viên sẽ không nhận được yêu cầu vì chưa được phân công! Vui lòng phân công ca kíp.',
    );
    errorCode.set(this.RSA_ECOM_PRE_ORDER_ASSIGN, 'Nhân viên không thuộc nhóm Sale Pre-order. Vui lòng phân công lại');
    errorCode.set(this.RSA_ECOM_ASSIGN_ROLE_PRE_ORDER, 'Bạn không phải nhân viên Sale Pre-order, không thể checkin');
    errorCode.set(this.RSA_ECOM_ASSIGN_ROLE, 'Bạn không phải nhân viên sale ecom, không thể checkin');
    errorCode.set(this.RSA_ECOM_ASSIGN_ROLE_SURVEY, 'Bạn không phải nhân viên shop vệ tinh, không thể checkin');
    errorCode.set(this.RSA_ECOM_STATUS_UPDATE_NOT_APPROVED, 'Cập nhật trạng thái không được phép');
    errorCode.set(this.RSA_ECOM_NO_OWNER_SCHEDULE_REQUEST, 'Yêu cầu đang được xử lý bởi nhân viên khác');
    errorCode.set(this.RSA_ECOM_NOT_FOUND_SCHEDULE_REQUEST, 'Không tìm thấy yêu cầu');
    errorCode.set(this.RSA_ECOM_EMPLOYEE_NOT_ONLINE, 'Nhân viên không online');
    errorCode.set(this.RSA_ECOM_GROUP_MEMBER_LV2_ALREADY_EXIST, 'Nhân viên này đã được thêm vào nhóm trước đó');
    errorCode.set(this.RSA_ECOM_GROUP_DOES_NOT_EXIST, 'Không tìm thấy group yêu cầu');
    errorCode.set(this.RSA_SYNC_TCQG_EXIST, 'Mã TCQG đã được sử dụng ở 1 khách hàng khác, vui lòng kiểm tra lại');
    errorCode.set(this.RSA_SYNC_PERSON_EXIST, 'Khách hàng đã có mã TCQG, vui lòng kiểm tra lại');
    errorCode.set(this.RSA_SYNC_PERSON_NOT_HAVE_DOB, 'Không thể thêm mã TCQG vì khách hàng này chưa có ngày sinh');
    errorCode.set(this.RSA_CHECK_DOUBLE, 'Vui lòng kiểm tra lại sau {second}s!');
    errorCode.set(this.RSA_ECOM_GROUP_MEMBER_LV2_DOES_NOT_EXIST, 'Không tìm thấy nhóm này');
    errorCode.set(this.RSA_ECOM_YOUR_GROUP_LV2_NOT_FOUND, 'Không tìm thấy nhóm của bạn');
    errorCode.set(
      this.RSA_ECOM_YOU_ARE_NOT_ASSIGNED_GROUP_LV2,
      'Bạn không phải là thành viên trong nhóm được giao phụ trách yêu cầu này',
    );
    errorCode.set(
      this.RSA_ERROR_CONSULTANTS_ORDER_RULE,
      'Khách hàng đang có đơn trả hàng {orderCode} chưa hoàn tất.Vui lòng kiểm tra đơn trả hàng trước khi tiếp tục tư vấn!',
    );
    errorCode.set(this.RSA_ECOM_REQUEST_NOT_FOUND, 'Thông tin yêu cầu không tồn tại');
    errorCode.set(this.RSA_ECOM_NOT_ALLOW_TO_EDIT, 'Bạn không thể chỉnh sửa yêu cầu này');
    errorCode.set(this.RSA_ECOM_ASSIGN_GROUP_LV2_NOT_FOUND, 'Không tìm thấy nhóm được giao phụ trách yêu cầu');
    errorCode.set(this.RSA_ECOM_GEN_LINK_FAIL, 'Tạo link thanh toán không thành công');
    errorCode.set(this.RSA_ECOM_PAYMENT_NOT_FULL, 'Đơn hàng chưa thanh toán đủ tiền');
    errorCode.set(this.RSA_ECOM_CALL_LOG_NOT_FOUND, 'Không tìm thấy thông tin cuộc gọi {id}');
    errorCode.set(
      this.RSA_NOT_FOUND_VERSION_TICKET_CODE,
      'Phiên bản phiếu khám của bạn đang không phải mới nhất. Vui lòng tắt phiếu khám mở lại',
    );
    errorCode.set(this.RSA_ECOM_TICKET_NOT_FOUND, 'Không tìm thấy thông tin phiếu: {ticketCode}');
    errorCode.set(this.RSA_ECOM_CART_NOT_FOUND, 'Không tìm thấy thông tin giỏ hàng: {sessionId}');
    errorCode.set(this.RSA_ECOM_ORDER_NOT_FOUND, 'Không tìm thấy thông tin đơn hàng: {orderCode}');
    errorCode.set(this.RSA_ECOM_ORDER_ATT_SCHEDULE_NOT_FOUND, 'Không tìm thấy thông tin lịch hẹn trong đơn hàng');
    errorCode.set(
      this.RSA_ECOM_ORDER_PUSH_ORDER_NOT_ALLOWED,
      'Đơn chờ chưa đến ngày xử lý nhắc hẹn sớm, không được phép đẩy đơn',
    );
    errorCode.set(
      this.RSA_ECOM_CONSULTANT_NOT_ALLOWED_EXIST_ORDER_ECOM_DISPLAY_2,
      'Khách hàng đang có đơn chờ chưa hoàn thành. Vui lòng kiểm tra lại đơn',
    );
    errorCode.set(
      this.RSA_ECOM_ORDER_CONTINUE_BUYING_NOT_ALLOWED,
      'Lịch hẹn gần nhất trên hai ngày thì không được phép tiếp tục đơn',
    );
    errorCode.set(this.RSA_SCHEDULE_MANUALLY_EXIST, 'KH đã tồn tại lịch hẹn vắc xin {skuName} mũi {injection}');
    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_DATE,
      'KH đã có hẹn tiêm vắc xin {skuName} ngày {date}. Vui lòng kiểm tra lại',
    );

    errorCode.set(this.RSA_HISTORY_MANUALLY_DATE, 'KH đã có lịch sử tiêm {skuName} ngày {date}. Vui lòng kiểm tra lại');

    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_DATE_GROUP,
      'Khách hàng đã có lịch sử tiêm {groupName} - {skuName} ngày {date}. Vui lòng đặt lịch hẹn vào ngày khác',
    );
    errorCode.set(this.RSA_HISTORY_MANUALLY_DATE_DUP, 'Lịch sử tiêm mới đang bị trùng ngày. Vui lòng kiểm tra lại');

    errorCode.set(this.RSA_SCHEDULE_MANUALLY_WRONG_STT, 'Thứ tự mũi hẹn vắc xin {skuName} chưa hợp lý');
    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_WRONG_STT_CASE_1,
      'Khách hàng chưa có lịch hẹn hoặc lịch sử tiêm {skuName} mũi 1. Vui lòng kiểm tra lại',
    );
    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_WRONG_STT_CASE_2,
      'Ngày hẹn của {skuName} mũi {lastInjections} phải sau ngày hẹn của mũi {firstInjections}. Vui lòng kiểm tra lại',
    );

    errorCode.set(this.RSA_ECOM_REQUEST_INVALID_ASSIGN, 'Chỉ có thể phân công cho yêu cầu mới.');
    errorCode.set(
      this.RSA_ECOM_REQUEST_INVALID_ASSIGN_V2,
      'Không thể phân công cho yêu cầu đã tạo đơn hoặc khách hàng yêu cầu hủy.',
    );

    errorCode.set(this.RSA_ECOM_REQUEST_INVALID_ASSIGN_V3, 'Không thể phân công cho yêu cầu, trạng thái không hợp lệ.');

    errorCode.set(
      this.RSA_RE_EXAMINATION_CODE,
      'Khách hàng đã thanh toán đủ tiền. Vui lòng hủy tiêm phiếu này và tạo phiếu mới',
    );
    errorCode.set(this.RSA_ECOM_PAYMENT_STORED_NOT_FOUND, 'Không tìm thấy thông tin payment: {paymentCode}');
    errorCode.set(
      this.RSA_CANCEL_TICKET_FOR_DOCTOR,
      'Khách hàng đã thanh toán tiền, mời khách hàng ra quầy sale để nhận lại tiền',
    );
    errorCode.set(
      this.RSA_CANCEL_TICKET_NOT_ALLOWED,
      'Chỉ cho phép huỷ phiếu khám ở trạng thái Chờ khám hoặc Đang khám',
    );
    errorCode.set(
      this.RSA_CANCEL_TICKET_TIEM_NOT_ALLOWED,
      'Chỉ cho phép huỷ phiếu tiêm ở trạng thái Chờ tiêm hoặc Đang tiêm',
    );
    errorCode.set(
      this.RSA_SYSTEM_ERROR_UPDATE_RULE,
      'Ticket của bạn đang được xử lý và không được phép chỉnh sửa. Vui lòng kiểm tra lại!',
    );
    errorCode.set(this.RSA_ECOM_ORDER_CANCELED, 'Đơn hàng đã bị hủy. Không thể đẩy đơn.');
    errorCode.set(this.RSA_ECOM_ORDER_PUSHED, 'Đơn hàng đã được đẩy xuống shop');
    errorCode.set(this.RSA_VERIFY_FORMAT_PHONE_NUMBER, 'Số điện thoại không đúng định dạng');
    errorCode.set(this.RSA_SYNC_TCQG_DETAIL_ERROR, 'Có lỗi xảy ra khi đồng bộ mũi tiêm lên TCQG');

    errorCode.set(this.RSA_HISTORY_STT_INJECTION, 'Chưa có lịch sử tiêm {skuName} mũi 1. Vui lòng kiểm tra lại');

    errorCode.set(
      this.RSA_HISTORY_STT_INJECTION_WRONG,
      'Chưa có lịch sử tiêm {skuName} mũi {n}. Vui lòng kiểm tra lại',
    );

    errorCode.set(
      this.RSA_HISTORY_STT_INJECTION_DATE_WRONG,
      'Ngày tiêm {skuName} mũi {lastInjections} phải lớn hơn ngày tiêm mũi {firstInjections}. Vui lòng kiểm tra lại',
    );

    errorCode.set(
      this.RSA_HISTORY_STT_INJECTION_WRONG_EXITS,
      'Đã tồn tại lịch sử tiêm {skuName} mũi {n}. Vui lòng kiểm tra lại',
    );

    errorCode.set(this.RSA_SHOP_CODE_CHANGING_ITEM_NOT_FOUND, 'Không tìm thấy thông tin đơn hàng hoặc phiếu khám.');

    errorCode.set(
      this.RSA_REPLACE_SKU_ERROR,
      'Khách hàng đang có phiếu khám chưa hoàn thành. Vui lòng kiểm tra lại phiếu {ticket}',
    );

    errorCode.set(
      this.RSA_BLOCK_CONTINUE_ORDER_EXIST_OPEN_TICKET,
      'Khách hàng đang có phiếu khám {ticket} chưa hoàn tất. Vui lòng hoàn tất phiếu khám trước khi xử lý tiếp.',
    );

    errorCode.set(
      this.RSA_REPLACE_SKU_EXITS_HISTORY_ERROR,
      'Khách hàng {lcvId} đã có lịch sử tiêm vaccine nên không được chuyển mũi tiêm. Vui lòng kiểm tra lại',
    );

    errorCode.set(
      this.RSA_REPLACE_SKU_EXITS_VAC_ORDER_ERROR,
      'Khách hàng {lcvId} đang có mũi đã mua chưa tiêm vaccine nên không được chuyển mũi tiêm. Vui lòng kiểm tra lại',
    );

    errorCode.set(this.RSA_CUSTOMER_TRANSFER_INJECTION, 'Khách hàng không tồn tại');
    errorCode.set(this.RSA_LIMIT_UTOP_TRANSACTION, 'Đã vượt quá giới hạn số lần gửi link');
    errorCode.set(this.RSA_LIMIT_UTOP_RESEND_LINK, 'Đã đạt giới hạn số lần gửi lại link');
    errorCode.set(this.RSA_NO_SCHEDULE_TO_UPDATE, 'Không có lịch hẹn nào để cập nhật');
    errorCode.set(this.RSA_INACTIVE_PERSON_NOT_VACCINATED, 'Không thể vô hiệu hóa khách hàng có mũi đã mua chưa tiêm.');
    errorCode.set(
      this.RSA_INACTIVE_PERSON_IN_FAMILY_PACKAGE,
      'Không thể vô hiệu hóa hồ sơ do khách hàng đang thuộc một nhóm. Vui lòng xóa khỏi nhóm trước khi vô hiệu hóa.',
    );
    errorCode.set(
      this.RSA_INACTIVE_PERSON_NOT_VACCINATED,
      'Khách hàng có mũi chưa tiêm. Vui lòng trả hàng mũi đã mua để tiếp tục thao tác vô hiệu hóa hồ sơ!',
    );
    errorCode.set(
      this.RSA_INACTIVE_PERSON_EXIST_SCHEDULE_ECOM,
      'Không thể vô hiệu hóa khách hàng đang có đơn Ecom cần xử lý. Vui lòng hủy đơn ecom để tiếp tục thao tác vô hiệu hóa hồ sơ!',
    );
    errorCode.set(
      this.RSA_INACTIVE_PERSON_JOURNEY_OPEN,
      'KH có phiếu khám {ticketCode} chưa hoàn tất. Vui lòng hủy phiếu để vô hiệu hóa hồ sơ!',
    );
    errorCode.set(
      this.RSA_INACTIVE_PERSON_EXIST_HISTORY,
      'Hồ sơ khách hàng có lịch sử tiêm chủng nên không thể vô hiệu hóa tài khoản',
    );
    errorCode.set(
      this.RSA_SYNC_TCQG_MULTIP_PROFILE_DUP,
      'Khách hàng {profile} đang có nhiều mã TCQG, chỉ được chọn 1 mã để đồng bộ',
    );
    errorCode.set(
      this.RSA_LIMIT_TIME_SYNC_TCQG,
      'Lịch sử mới nhất chưa được cập nhật. Vui lòng bấm nút Kéo LS từ TCQG để xem lịch sử mới nhất',
    );
    errorCode.set(this.RSA_CREATE_CUSTOMER_RULE, 'Khách hàng <12 tuổi cần điền thông tin người giám hộ');

    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_VALID_TICKET,
      'Khách hàng đang có phiếu khám {ticketCode} chưa hoàn tất. Vui lòng đặt hẹn sau khi phiếu khám hoàn tất',
    );
    errorCode.set(
      this.RSA_UPDATE_SCHEDULE_MANUALLY_VALID_TICKET,
      'Khách hàng đang có phiếu khám {ticketCode} chưa hoàn tất. Vui lòng cập nhật lại lịch hẹn sau khi phiếu khám hoàn tất',
    );
    errorCode.set(this.RCA_PRODUCT_OUT_OF_STOCK, 'Không được bán sản phẩm có giá 0đ');

    errorCode.set(
      this.RSA_DUP_SCHEDULE,
      'Khách hàng có mũi tiêm chung {diseaseGroup}. Vui lòng cập nhật lại ngày hẹn và thử lại',
    );

    errorCode.set(
      this.RSA_SKU_SELL_RESTRICT,
      'Vắc xin {name} thuộc sản phẩm TẠM HẾT HÀNG/ KHÁCH ĐẶT MỚI LẤY, nên xảy ra lỗi',
    );

    errorCode.set(
      this.RSA_SKU_SELL_RESTRICT_AT_CREATE_TICKET,
      'Vắc xin {name} thuộc sản phẩm TẠM HẾT HÀNG/ KHÁCH ĐẶT MỚI LẤY, nên không thể tạo phiếu khám',
    );

    errorCode.set(
      this.RSA_SKU_SELL_RESTRICT_AT_INDICATION,
      'Vắc xin {name} thuộc sản phẩm TẠM HẾT HÀNG/ KHÁCH ĐẶT MỚI LẤY, nên không thể chỉ định tiêm',
    );

    errorCode.set(
      this.RSA_SKU_SELL_RESTRICT_AT_PLACE_ORDER,
      'Vắc xin  {name} thuộc sản phẩm TẠM HẾT HÀNG/ KHÁCH ĐẶT MỚI LẤY, nên không thể thanh toán',
    );

    errorCode.set(
      this.RSA_ECOM_SHOP_VE_TINH_REGION_NOT_FOUND,
      'Bạn sẽ không nhận được yêu cầu vì chưa được phân công! Vui lòng liên hệ quản lý.',
    );
    errorCode.set(this.RSA_ECOM_SHOP_VE_TINH_EMPLOYEE_NOT_FOUND, 'Không tìm thấy nhân viên thuộc shop vệ tinh.');
    errorCode.set(this.RSA_ECOM_EMPLOYEE_NOT_FOUND, 'Không tìm thấy nhân viên thông tin nhân viên.');
    errorCode.set(this.RSA_CONTRACT_INPROCESSING, 'Hợp đồng đang trong quá trình xử lý!');

    errorCode.set(this.RSA_CONTRACT_SEND_MESSAGE, 'Hợp đồng đang trong quá trình xử lý!');

    errorCode.set(this.FAMILY_DUPLICATE, 'KH đang thuộc gia đình này nên không thể chuyển vào gia đình này nữa!');

    errorCode.set(this.RSA_CHECK_SHOP_PHONE, 'Không được tạo khách hàng với SĐT shop');

    errorCode.set(
      this.RSA_HISTORY_SYNC_TCQG_LIMIT,
      'Chỉ được đồng bộ thông tin với cổng tiêm chủng quốc gia sau 10s nữa!',
    );

    errorCode.set(
      this.RSA_CONTRACT_EMPTY_PHONE_NUMBER,
      'Khách hàng không có số điện thoại nên không thể cấp giấy xác nhận tiêm chủng. Vui lòng bổ sung tại màn hình thông tin khách hàng!',
    );

    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_DATE_DUP,
      'Lưu ý: Khách hàng sẽ không nhận được tin nhắn nhắc hẹn {skuName} ngày {date} vì tin nhắn nhắc hẹn được gửi trước 2 ngày. Vui lòng chủ động liên hệ với khách hàng để nhắc hẹn.',
    );

    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_CANCEL_VALID_TICKET,
      'Khách hàng đang có phiếu khám {ticketCode} chưa hoàn tất. Vui lòng xóa lịch hẹn sau khi phiếu khám hoàn tất.',
    );

    errorCode.set(this.RSA_SCHEDULE_ALREADY_SEND_MESSAGE, 'Đã gửi voucher đến khách hàng. Vui lòng thử lại sau!');

    errorCode.set(
      this.RSA_SCHEDULE_EMPTY_PHONE_NUMBER,
      'Người giám hộ không có số điện thoại. Vui lòng bổ sung tại màn hình thông tin khách hàng!',
    );

    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_ECOM,
      'Khách hàng đã có lịch hẹn từ ecom ngày {date} của {vaccineName}, vui lòng đặt hẹn sau ngày {date}',
    );

    errorCode.set(this.RSA_ECOM_ASSIGN_SURVEY_USER_NOT_CHECK_IN, 'Nhân viên chưa checkin ngày hôm nay');
    errorCode.set(this.RSA_ECOM_ASSIGN_SURVEY_USER_NOT_ONLINE, 'Nhân viên không online');
    errorCode.set(
      this.RSA_ECOM_ASSIGN_SURVEY_USER_ONLINE,
      'Bạn không thể chỉnh sửa do nhân sự đang checkin trong ca làm việc',
    );

    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_PAID,
      'Khách hàng đã thanh toán cho lịch hẹn ngày {date} của {vaccineName}, vui lòng đặt hẹn sau ngày {date}',
    );

    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_PAID_DISEASE,
      'Bạn đang thêm lịch hẹn khác mã sản phẩm nhưng cùng nhóm bệnh, vui lòng thao tác lại.',
    );

    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_DISEASE_GROUP,
      'Khách hàng đã có lịch hẹn ngày {date} cho vaccine {skuName} cùng nhóm bệnh {groupName}. Vui lòng kiểm tra và đặt hẹn ngày khác.',
    );

    errorCode.set(
      this.RSA_SCHEDULE_MANUALLY_REGIMEN,
      'Vaccine {skuName} đang có nhiều lịch hẹn khác phác đồ. Vui lòng kiểm tra lại để đảm bảo lịch hẹn đúng.',
    );

    errorCode.set(
      this.RSA_ERROR_TICKET_OPEN,
      'Khách hàng đang có phiếu khám {ticketCode} chưa hoàn tất. Vui lòng thêm lịch sử tại màn hình phiếu khám của bác sĩ',
    );

    errorCode.set(
      this.RSA_CUSTOMER_BLOCK_DELETE_PHONE,
      'Bạn không được phép xóa SĐT của người giám hộ. Vui lòng kiểm tra lại!',
    );

    errorCode.set(
      this.RSA_CUSTOMER_BLOCK_DELETE_HOST_PERSON,
      'Không thể xóa thành viên {lcvId} vì họ đang là người giám hộ.',
    );

    errorCode.set(
      this.RSA_ERROR_PAY_LATER_ORDER_FINISH_DEPOSIT,
      'Đơn ở trạng thái Hoàn tất cọc không được đổi loại đơn.',
    );

    errorCode.set(
      this.RSA_INACTIVE_PERSON_WAITING_PAID,
      'Khách hàng có mũi trong đơn thanh toán từng phần. Vui lòng trả hàng mũi đã mua để tiếp tục thao tác vô hiệu hóa hồ sơ!',
    );

    errorCode.set(this.RSA_CHANGE_ORDER_ATTRIBUTE, 'Vui lòng không chuyển loại đơn hàng khi đơn hàng đã được tạo');
    errorCode.set(this.RSA_ECOM_NO_RIGHT_TO_ADJUST_ORDER, 'Bạn không có quyền xử lý đơn hàng này');
    errorCode.set(this.RSA_ERROR_EMPLOYEE_NOT_FOUND, 'Không tìm thấy khách hàng');
    errorCode.set(this.RSA_ECOM_BUSY_EMPLOYEE, 'Bạn không thể đổi nhóm do nhân sự đang checkin trong ca làm việc');
    errorCode.set(
      this.RSA_BLOCK_UNIT_WITH_REGIMEN,
      'Vắc xin HEBERBIOVAC phác đồ {scheduleType} phải được mua theo đơn vị tính {unitCode}. Vui lòng chọn lại đơn vị tính',
    );
    errorCode.set(
      this.RSA_INVALID_JOURNEY_ID,
      'Khách hàng đang có đơn chưa hoàn tất orderId. Vui lòng tìm kiếm và tiếp tục xử lý',
    );
    errorCode.set(
      this.RSA_PREVENTION_PRODUCT_ATTRIBUTE,
      'Vắc xin {name} thuộc sản phẩm TẠM HẾT HÀNG/ KHÁCH ĐẶT MỚI LẤY/ HÀNG KHAN HIẾM, nên không thể thêm mới lịch hẹn.',
    );
    errorCode.set(this.RSA_CONTINUE_BUYING_RULE, 'Đơn hàng đã hoàn tất thanh toán. Vui lòng kiểm tra lại.');

    errorCode.set(
      this.RSA_SAVE_MOBILE_CARRIER_RULE,
      'Đơn hàng đủ điều kiện được tặng thẻ nạp nên vui lòng vào trang thông tin khách hàng và chọn nhà mạng cho KH trước khi xử lý đơn cọc này.',
    );

    errorCode.set(
      this.RSA_ECOM_SAVE_MOBILE_CARRIER_RULE,
      'Khách hàng sẽ được tặng thẻ nạp. Vui lòng chọn nhà mạng trước khi tiếp tục xử lý đơn cọc này',
    );

    errorCode.set(
      this.RSA_BLOCK_SKU_BEXSERO,
      'Vaccine Bexsero chỉ được phép chỉ định đơn liều, không chỉ định cùng ngày với các vaccine khác',
    );

    errorCode.set(
      this.RSA_EWALLET_RULE,
      'Khách hàng đã áp dụng khuyến mãi khi thanh toán ví điện tử cho đơn hàng có giá trị {amount} nên không thể điều chỉnh đơn hàng nhỏ hơn số tiền này.',
    );

    errorCode.set(
      this.RSA_VOUCHER_PARTNER_RULE,
      'Không được điều chỉnh giá trị đơn hàng thấp hơn giá trị voucher đối tác đã sử dụng.',
    );

    errorCode.set(
      this.RSA_UPDATE_HISTORY_DIFF_DAY,
      'Lịch sử tiêm chỉ được cập nhật lùi lại 1 ngày và ngày cập nhật phải cùng tháng với ngày tiêm. Liên hệ 87333 khi muốn điều chỉnh thời gian khác',
    );

    errorCode.set(
      this.RSA_UPDATE_HISTORY_THAN_ONE_DAY,
      'Lịch sử tiêm đã được chỉnh sửa trước đó, không được cập nhật nhiều lần',
    );
    errorCode.set(this.RSA_ALREADY_DEPOSIT, 'Đơn hàng đã được thanh toán không thể hủy. Vui lòng hoàn tất thu tiền!');

    errorCode.set(this.PAYMENT_GATEWAY_CANCEL_ORDER_10012, 'Không thể hủy đơn hàng đã được hoàn tất thanh toán');
    errorCode.set(this.PAYMENT_GATEWAY_CANCEL_ORDER_10013, 'Không thể hủy đơn hàng đã được thanh toán Payoo');
    errorCode.set(this.PAYMENT_GATEWAY_CANCEL_ORDER_10014, 'Không thể hủy đơn hàng đã được thanh toán QR');
    errorCode.set(this.PAYMENT_GATEWAY_CANCEL_ORDER_10016, 'Không thể hủy đơn hàng đã tồn tại thanh toán');
    errorCode.set(
      this.PAYMENT_GATEWAY_CANCEL_ORDER_10017,
      'Không thể hủy đơn hàng đã được thanh toán chuyển khoản trước',
    );
    errorCode.set(
      this.RSA_DUP_PREGNANCY_NUMBER,
      'Đang khai báo trùng lần mang thai - Lần {pregnancyNumber}. Vui lòng kiểm tra lại và cập nhật đúng ngày mang thai',
    );

    errorCode.set(
      this.RSA_DUP_PREGNANCY_DATE,
      'Ngày mang thai lần thứ {pregnancyNumber2} đang sớm hơn ngày mang thai lần thứ {pregnancyNumber1}. Vui lòng kiểm tra lại và cập nhật đúng ngày mang thai.',
    );

    errorCode.set(
      this.RSA_DUP_SAME_PREGNANCY_DATE,
      'Đang khai báo trùng ngày mang thai - {pregnancyDate}. Vui lòng kiểm tra lại và cập nhật đúng ngày mang thai.',
    );

    errorCode.set(
      this.RSA_CONTRAINDICATION_PREGNANT_WOMEN,
      'Đang chỉ định tiêm ngay vắc xin {vaccineName} theo phác đồ {regimenName}. Vui lòng khai báo thông tin mang thai của khách hàng để đồng bộ đúng thông tin lên TCQG.',
    );

    errorCode.set(
      this.RSA_ESTIMATE_DATE_SMALLER_PREGNANCY_DATE,
      'Ngày sinh/dự sinh/sảy thai không được sớm hơn ngày mang thai. Vui lòng kiểm tra lại và cập nhật đúng ngày mang thai.',
    );
    errorCode.set(this.RSA_BLOCK_UPDATA_CART, 'Không thể mua thêm với đơn trả chậm đã thu tiền');

    errorCode.set(this.RSA_BLOCK_PLACE_WITH_CANCEL_STATUS, 'Không thể tiếp tục đơn do đơn hàng đã được hủy trước đó');

    errorCode.set(this.RSA_BLOCK_GARDASIL_4_FOR_NAM, 'Không tiêm GARDASIL 4 0.5ML cho khách hàng là nam giới');
    errorCode.set(this.RSA_ECOM_ONLY_SUPPORT, 'Chỉ cho phép nguồn gọi từ RSA ECOM');

    errorCode.set(this.RSA_BLOCK_PLACE_WITH_CANCEL_STATUS, 'Không thể tiếp tục đơn do đơn hàng đã được hủy trước đó');

    errorCode.set(this.RSA_ACTIVE_PERSON_HAVE_FAMILY, 'Khách hàng đang là chủ hộ nên không thể vô hiệu hóa hồ sơ.');

    errorCode.set(
      this.RSA_CART_ORDER_ORDER_STATUS_CONFIRM,
      'Không thể xóa hết sản phẩm ra khỏi giỏ hàng đã có mã đơn hàng.',
    );
    errorCode.set(this.RSA_BLOCK_FINISH_TICKET, 'Không tìm thấy sản phẩm {sku} trong hệ thống.');
    errorCode.set(this.RSA_BLOCK_DA_TIEM, 'Sản phẩm {sku} đã tiêm. Không thể hoàn tất tiêm phiếu này!');
    errorCode.set(this.RSA_BLOCK_DA_TRA_HANG, 'Sản phẩm {sku} đã trả hàng. Không thể hoàn tất tiêm phiếu này!');

    errorCode.set(
      this.RSA_INACTIVE_ORDER_HOAN_TAT_COC,
      'Khách hàng có đơn hoàn tất cọc {orderCode}. Vui lòng hủy cọc để vô hiệu hóa hồ sơ!',
    );

    errorCode.set(this.RSA_BARCODE_EXPIRY, 'Vaccine đa liều lọ số {barCode} đã hết thời hạn sử dụng.');

    errorCode.set(this.RSA_PRINT_MULTIDOSE_INVALID_PAYLOAD, 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.');

    errorCode.set(
      this.RSA_INVENTORY_BY_LODATE_MULTIDOSE,
      'Lô {lotDate} - {expiryDate} chỉ còn tồn {quantityExchange} Liều nên không thể mở lọ mới',
    );

    errorCode.set(
      this.RSA_UPDATE_TRACKING_MULTIDOSE_VALID_BARCODE,
      'Mũi tiêm vắc xin đa liều {sku} - {skuName} đang chưa được chọn mã lọ. Vui lòng tải lại trang và chọn mã lọ trước khi xác nhận tiêm.',
    );
    // errorCode.set(this.RSA_UPDATE_MANY_SCHEDULE_REGIMENT_SCREEN_SLIDEBAR, 'Không được chỉnh sửa.');
    errorCode.set(
      this.RSA_UPDATE_MANY_SCHEDULE_REGIMENT_SCREEN_SCHEDULE,
      'Khách hàng đang có phiếu khám {ticketCode} chưa hoàn tất, không chỉnh sửa lịch hẹn có cùng nhóm bệnh trong phiếu khám.',
    );

    errorCode.set(
      this.RSA_UPDATE_MANY_SCHEDULE_REGIMENT_SCREEN_SCHEDULE_SKU,
      'Khách hàng đang có phiếu khám {ticketCode} chưa hoàn tất, không chỉnh sửa phác đồ của mũi tiêm ngay trong phiếu khám.',
    );

    errorCode.set(
      this.RSA_UPDATE_MANY_SCHEDULE_SCREEN_SCHEDULE_DATE,
      'Khách hàng đang có phiếu khám {ticketCode} chưa hoàn tất, không chỉnh sửa ngày hẹn của lịch hẹn tiêm ngay trong phiếu khám.',
    );

    errorCode.set(this.PRE_ORDER_PHASE_ISSUES, 'Có lỗi xảy ra trong việc xác định phase đặt cọc.');
    errorCode.set(this.RSA_ECOM_PROCESS_OTHER_CHANNEL, 'Không thể xử lý đơn hàng không phải là đơn ECOM.');

    errorCode.set(
      this.RSA_AFFILIATE_AUTH_INSIDE,
      'Tài khoản của bạn không thuộc Shop vệ tinh nên không được quyền truy cập vào hệ thống.',
    );
    errorCode.set(
      this.RSA_ORDER_ADJUST_LESS_MONEY,
      'Khách hàng đã thanh toán toàn bộ giá trị của đơn đặt cọc. Vui lòng tạo phiếu khám vào gặp bác sĩ để được tư vấn thêm.',
    );

    errorCode.set(
      this.RSA_VALID_CONSULTANTS_AFFILIATE,
      'Shop đang đăng nhập không phải là shop vệ tinh nên không thể tư vấn.',
    );

    errorCode.set(
      this.RSA_ECOM_PAYMENT_INVALID,
      'Không thể hoàn tất đơn hàng do chưa có thông tin thanh toán . Vui lòng kiểm tra trạng thái thanh toán và cập nhật lại.',
    );
    errorCode.set(this.RSA_ECOM_PAYMENT_NOT_FOUND, 'Không tìm thấy thông tin thanh toán.');

    errorCode.set(
      this.RSA_AFFILIATE_BLOCK_CANCEL_ORDER_DEPOSIT,
      'Đơn hàng đã có phiếu khám {ticketCode}. Nên không được phép hủy cọc tại Shop vệ tinh.',
    );

    errorCode.set(
      this.RSA_AFFILIATE_BLOCK_CANCEL_ORDER_COMPLETED,
      'Đơn hàng đã được hoàn tất. Nên không được phép hủy cọc tại Shop vệ tinh.',
    );

    errorCode.set(this.RSA_PROCESS_ORDER_COMPLETED, 'Đơn hàng đã được {status}. Không thể xử lý tiếp.');
    errorCode.set(
      this.RSA_ECOM_CONTINUE_BUYING_AFFILIATE,
      'Khách hàng đã thanh toán đơn tại Shop vệ tinh không thể xử lý đơn trên Ecom.',
    );

    errorCode.set(
      this.RSA_HAVE_TICKET_IS_OPEN,
      'Khách hàng đang có phiếu {ticketCode}. Vui lòng kiểm tra lại với bác sĩ/ điều dưỡng trước khi tạo phiếu mới cho khách hàng!',
    );

    errorCode.set(
      this.RSA_ECOM_PUSH_ORDER_ERROR_001001,
      'Đơn đặt cọc từ shop vệ tinh không đươc phép chọn thanh toán tại cửa hàng.',
    );

    errorCode.set(
      this.WEB_APP_ORDER_NOT_YET_COMPLETE_400,
      'Đơn hàng từ Web/App Tiêm Chủng Long Châu khách hàng chưa hoàn tất đơn không thể xử lý',
    );

    errorCode.set(
      this.WEB_APP_ORDER_HANDLE_BY_DOCTOR_400,
      'Khách hàng đang được tiếp nhận bởi Bác sĩ không thể tiếp tục tư vấn tại Sale. Vui lòng kiểm tra lại.',
    );

    errorCode.set(this.RSA_ECOM_ORDER_SAVE_ORDER_NOT_ALLOWED, 'Không thể lưu đơn.');

    errorCode.set(
      this.SCHEDULE_BANNED_SKU,
      `Sản phẩm <sku> - <skuName> chỉ được bán theo đơn Pre-order, không được phép bán mới từ ngày <sellRestrictFromDate> đến <sellRestrictToDate>.`,
    );

    errorCode.set(
      this.RSA_BLOCK_ADJUST_TICKET_ANOTHER_VACCINE,
      `Không thể thanh toán/ tạo phiếu khám khi có lịch hẹn vắc xin thay thế. Vui lòng kiểm tra lại.`,
    );

    errorCode.set(
      this.SURVEY_IS_IN_PROCESSING,
      'Yêu cầu tư vấn đã được xử lý. Vui lòng tải lại trang và phân công tiếp!',
    );

    errorCode.set(
      this.SURVEY_ALREADY_ASSIGN,
      'Phiếu yêu cầu đã có Nhân viên tiếp nhận, vui lòng tải lại trang và thử lại.',
    );

    errorCode.set(
      this.RSA_OWNER_NOT_UPDATE_FAMILY_PACKAGE,
      'Khách hàng đang là chủ hộ của nhóm Gia đình là số 1 không thể xóa SĐT hoặc cập nhật nhỏ hơn 18 tuổi. Vui lòng kiểm tra lại.',
    );

    errorCode.set(
      this.RSA_OWNER_NOT_DELETE_PHONE_FAMILY_PACKAGE,
      'Khách hàng đang là người đại diện của nhóm không thể xóa SĐT của người đại diện. Vui lòng kiểm tra lại.',
    );

    errorCode.set(
      this.CHO_HOI_CHAN_EVALUATION_REQUEST,
      'Khách hàng <Name> đang có phiếu Yêu cầu Hội chẩn chưa hoàn tất. Vui lòng kiểm tra lại.',
    );
    errorCode.set(
      this.CUSTOMER_HAS_NOT_ENOUGH_INFO,
      'Khách hàng <Name> chưa có đủ thông tin để thực hiện tư vấn. Vui lòng kiểm tra lại.',
    );

    errorCode.set(
      this.RSA_MISSING_ITEMCART_FOR_FB,
      'Mỗi thành viên trong nhóm phải mua ít nhất 1 mũi để tạo đơn nhóm. Vui lòng kiểm tra lại.',
    );

    errorCode.set(
      this.RSA_BLOCK_CREATE_UPDATE_ORDER_HAVE_TTTP,
      'Không được chỉ định mũi tiêm trong đơn hàng Thanh toán từng phần khi đang xử lý đơn nhóm!',
    );

    errorCode.set(
      this.SURVEY_ALREADY_ASSIGN,
      'Phiếu yêu cầu đã có Nhân viên tiếp nhận, vui lòng tải lại trang và thử lại.',
    );

    errorCode.set(this.RSA_RULE_ASM, 'Người dùng {{userName}} không có quyền thực hiện chức năng.');
    errorCode.set(this.RSA_RULE_ASM_SHOP, 'Người dùng {{userName}} không thuộc shop.');
    errorCode.set(
      this.RSA_ORDER_ADJUST_SAME_MONEY,
      'Khách hàng không cần thanh toán đơn hàng có giá trị 0đ. Vui lòng tạo phiếu khám vào gặp bác sĩ để được tư vấn thêm.',
    );
    errorCode.set(
      this.RSA_AFF_RESTRICTION_SKU,
      process.env.MESSAGE_RESTRICTION ||
        'Hiện vắc xin Cúm khan hiếm tồn kho, chỉ nhận đặt cọc có thanh toán. Dự kiến có hàng sau 10/03.',
    );

    errorCode.set(
      this.VOUCHER_RETURN_MONEY,
      'Voucher {{voucherCodes}} không thể áp dụng do đã sử dụng hoặc bị hủy. Vui lòng kiểm tra lại hoặc bỏ áp dụng voucher.',
    );

    errorCode.set(
      this.STOCK_NOT_ENOUGH,
      'Tồn kho không đủ để trả mũi. Vui lòng điều chỉnh lịch hẹn hoặc chọn shop khác',
    );

    errorCode.set(
      this.RSA_SKU_STOCK_RESTRICT_AT_PLACE_ORDER,
      'Vắc xin {name} thuộc sản phẩm TẠM HẾT HÀNG/ HÀNG KHAN HIẾM, nên không thể thanh toán.',
    );

    errorCode.set(
      this.RSA_PREVENTION_PRODUCT_ATTRIBUTE_RARE_GOOD,
      'Vắc xin {name} thuộc sản phẩm HÀNG KHAN HIẾM, nên không thể tạo mới lịch hẹn Chưa thanh toán.',
    );

    errorCode.set(
      this.RSA_PREVENTION_PUSH_ORDER_RARE_GOOD,
      `Đơn hàng có vắc xin {name} thuộc sản phẩm là HÀNG KHAN HIẾM, vui lòng thanh toán trước khi đẩy đơn.`,
    );

    errorCode.set(
      this.RSA_PREVENTION_SAVE_ORDER_RARE_GOOD,
      `Không thể lưu đơn hàng này vì có vắc xin {name} thuộc sản phẩm HÀNG KHAN HIẾM.`,
    );
    errorCode.set(
      this.DIFFERENT_SHIFT_WORKING,
      'Nhân viên sẽ không nhận được yêu cầu vì chưa được phân công đúng nhóm Sale. Vui lòng phân công lại nhóm Sale cho nhân viên.',
    );

    errorCode.set(
      this.NOT_YET_ASSIGN_SHIFT,
      'Nhân viên không thuộc ca làm việc. Vui lòng đổi ca làm hoặc phân công cho nhân viên khác.',
    );

    errorCode.set(
      this.RSA_CUSTOMER_BLOCK_UPDATE_HOST_PERSON,
      'Không thể chọn {personChange} làm người giám hộ vì thành viên này không thuộc gia đình {personPrimary}.',
    );
    errorCode.set(this.RSA_ECOM_ALREADY_HAVE_AFF_ORDER_DEPOSIT_COMPLETED, 'Vui lòng xử lý đơn cọc vệ tinh trước.');
    errorCode.set(this.RSA_FAMILY_PACKAGE_NOT_FOUND, 'Nhóm gia đình không tồn tại');
    errorCode.set(this.ONLINE_ORDER_NOTFOUND, 'Không tìm thấy đơn hàng online');
    errorCode.set(this.ONLINE_ORDER_ASSIGNED, 'Đơn hàng đã được phân công cho nhân viên khác');
    errorCode.set(this.ONLINE_ORDER_PROCESSED, 'Đơn hàng đã được xử lý');
    errorCode.set(this.ONLINE_ORDER_USER_ASSIGNED, 'NNân viên còn đơn hàng chưa xử lý trong ngày');
    errorCode.set(this.ONLINE_ORDER_USER_NOT_ONLINE, 'NNân viên còn đơn hàng chưa xử lý trong ngày');
    errorCode.set(
      this.ORDER_SHIFT_NOT_MATCH_IN_USER_SHIFT,
      'Ca của đơn hàng không khớp với ca làm việc của nhân viên. Vui lòng kiểm tra lại ca làm việc của nhân viên.',
    );
    errorCode.set(
      this.RSA_CHECK_ORDER_PRIORITY_PAYMENTED_AFFILIATE_WEB_APP,
      'Khách hàng có đơn hàng đã thanh toán 100% từ Affiliate hoặc Web/App Tiêm Chủng Long Châu. Vui lòng xử lý đơn {orders} trước.',
    );
    errorCode.set(
      this.RSA_CHECK_ORDER_PRIORITY_AFFILIATE,
      'Khách hàng có đơn đặt cọc từ Affiliate. Vui lòng xử lý đơn {orders} trước.',
    );
    errorCode.set(
      this.RSA_CHECK_ORDER_PRIORITY_WEB_APP,
      'Khách hàng có đơn hàng từ Web/App Tiêm Chủng Long Châu. Vui lòng xử lý đơn {orders} trước.',
    );

    errorCode.set(
      this.M_RSA_OFFLINE_CONSULTANTS_NOT_ALLOWED_EXIST_ORDER,
      'Khách hàng đang có đơn hàng chưa hoàn thành. Vui lòng kiểm tra lại đơn',
    );

    errorCode.set(
      this.ECOM_PENDING_AFFILIATE_WITH_SAME_DISEASE,
      'Khách hàng đã có một đơn cọc vệ tinh có cùng nhóm bệnh {diseaseGroupNames} đã thanh toán. Vui lòng xử lý đơn cọc này trước khi tạo đơn hàng mới',
    );

    // Xuất off message
    errorCode.set(
      this.XO_CAN_CANCEL_ONLY_PENDING,
      'Chỉ có thể hủy phiếu xuất off khi phiếu đang ở trạng thái Chờ xử lý.',
    );

    errorCode.set(
      this.RSA_ECOM_XUAT_OFF_VERIFY_ECOM,
      'Đơn hàng muốn xuất off không phải đơn hàng được tạo từ Ecom. Vui lòng kiểm tra lại.',
    );

    errorCode.set(
      this.RSA_ECOM_XUAT_OFF_VERIFY_AT_SHOP,
      'Đơn hàng muốn xuất off chưa được đẩy xuống shop. Vui lòng kiểm tra lại.',
    );

    errorCode.set(
      this.XO_APPROVE_ROLE_NOT_ALLOWED,
      'Đơn được tạo bởi user role {role} bạn không được phép {action} phiếu Xuất off này.',
    );

    errorCode.set(this.XO_INVALID_STATUS_TRANSITION, 'Không thể chuyển trạng thái từ {current} sang {next}.');

    errorCode.set(
      this.XO_UPDATE_ONLY_PENDING,
      'Chỉ được chỉnh sửa phiếu Xuất off ở trạng thái Chờ xử lý. Vui lòng kiểm tra lại hoặc nhờ người duyệt từ chối để tạo phiếu khác.',
    );

    errorCode.set(
      this.RSA_ECOM_XUAT_OFF_IS_ECOM,
      'Đơn hàng muốn xuất off là đơn hàng được tạo từ Ecom. Vui lòng kiểm tra lại.',
    );

    errorCode.set(this.XO_DETAIL_EXIST, 'Đơn hàng shop {orderCode} đã được tạo tại phiếu xuất off số {xoCode}');

    errorCode.set(
      this.RSA_ECOM_XUAT_OFF_DATE_RANGE,
      'Đơn hàng được tạo quá 30 ngày kể từ ngày tạo đơn Ecom. Vui lòng kiểm tra lại.',
    );

    errorCode.set(
      this.RSA_XO_INVALID_ORDER,
      'Trạng thái đơn đang là {orderStatus}, không hợp lệ để {action} phiếu Xuất off.',
    );

    errorCode.set(
      this.RSA_ECOM_XUAT_OFF_VERIFY_ORDER_STATUS,
      'Đơn Ecom đang ở trạng thái {orderStatus} không hợp lệ để tạo phiếu Xuất off. Vui lòng kiểm tra lại.',
    );

    errorCode.set(this.XO_EXIST, 'Đơn hàng ecom đã được tạo tại phiếu xuất off số {xoCode}');

    errorCode.set(
      this.RSA_ECOM_XUAT_OFF_INVALID_DATE,
      'Đơn hàng được tạo trước ngày tạo đơn Ecom. Vui lòng kiểm tra lại.',
    );

    // End Xuất off message

    errorCode.set(
      this.ECOM_HANDEL_WEB_APP_TTOL_TT,
      'Khách hàng có đơn thanh toán online thất bại. Vui lòng xử lý trước khi tạo đơn hàng mới',
    );

    errorCode.set(
      this.AFFILLIATE_ECOM_NOT_ALLOWED_CANCEL_ORDER,
      'Đơn hàng được chuyển đổi từ đơn Ecom/đơn đặt cọc, bạn không có quyền huỷ đơn này, vui lòng liên hệ shop tạo đơn.',
    );

    errorCode.set(
      this.AFFILLIATE_ECOM_NOT_ALLOWED_CONTINUE_BUYING,
      'Đơn hàng được chuyển đổi từ đơn Ecom/đơn đặt cọc, bạn không có quyền thao tác đơn này, vui lòng liên hệ shop tạo đơn.',
    );

    errorCode.set(this.RSA_MISSING_OTP, 'Vui lòng nhập mã OTP để tiếp tục.');
    return errorCode;
  }
}
