import { Module, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AuthGuard,
  BadRequestExceptionFilter,
  DatabaseConfigService,
  envValidator,
  HttpExceptionFilter,
  LoggingInterceptor,
  RedisConfigService,
  TimeoutInterceptor,
  TransformInterceptor,
  WinstonLoggerConfigService,
} from '@shared';
import { HealthzModule } from '@shared/modules/healthz/health.module';
import { S3Module } from 'ict-nest-s3';
import { ModulesModule } from 'modules/modules';
import { FilesModule } from 'modules/modules/modules/files/files.module';
import { ProductsModule } from 'modules/modules/modules/products/products.module';
import { WinstonModule } from 'nest-winston';
import { S3ConfigService } from '@shared/config/s3.config';
import { CommonModule } from './modules/common/common.module';
import { QuestionModule } from './modules/question/question.module';
import { RabiesModule } from './modules/rabies/rabies.module';
import { RoomsModule } from './modules/rooms/rooms.module';
import { UsersModule } from './modules/users/users.module';
import { VersionsModule } from './modules/versions/versions.module';
import { ReasonsModule } from 'modules/modules/modules/reasons/reasons.module';
import { TicketModule } from 'modules/modules/modules/ticket/ticket.module';
import { OrdersModule } from 'modules/modules/modules/orders/orders.module';
import { LoyaltyModule } from 'modules/modules/modules/loyalty/loyalty.module';
import { CartsModule } from 'modules/modules/modules/carts/carts.module';
import { AdministrativeModule } from 'modules/modules/modules/administrative/administrative.module';
import { CustomersModule } from 'modules/modules/modules/customers/customers.module';
import { ConsultantsModule } from 'modules/modules/modules/consultants/consultants.module';
import { RegimensModule } from 'modules/modules/modules/regimens/regimens.module';
import { InvoiceModule } from 'modules/modules/modules/invoice/module.service';
import { SchedulesModule } from 'modules/modules/modules/schedules/schedules.module';
import { DepositCancelModule } from 'modules/modules/modules/deposit-cancel/depositCancel.module';
import { JourneyModule } from 'modules/modules/modules/journey/journey.module';
import { FamilyModule } from 'modules/modules/modules/family/family.module';
import { PrintCenterModule } from 'modules/modules/modules/print-center/printCenter.module';
import { PaymentModule } from './modules/payment/payment.module';
import { VouchersModule } from 'modules/modules/modules/vouchers/vouchers.module';
import { FtelModule } from './modules/ftel/ftel.module';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { ShopsModule } from 'modules/modules/modules/shops/shops.module';
import { SurveysModule } from 'modules/modules/modules/surveys/surveys.module';
import { OsrModule } from 'modules/modules/modules/osr/osr.module';
import { StocksModule } from 'modules/modules/modules/stocks/stocks.module';
import { ReportModule } from 'modules/modules/modules/reports/reports.module';
import { GameUtopModule } from 'modules/modules/modules/game-utop/game-utop.module';
import { ServiceContractModule } from 'modules/modules/modules/contract/contract.module';
import { EmailOtpModule } from 'modules/modules/modules/email-otp/email-otp.module';
import { ESMModule } from 'modules/modules/modules/esm/esm.module';
import { ShopManagementModule } from 'modules/modules/modules/shop-management/shop-management.module';
import { AllExceptionsFilter } from '@shared/common/filters/all-exception.filter';
import { NotesModule } from 'modules/modules/modules/notes/notes.module';
import { AssignJobModule } from 'modules/modules/modules/assign-job/assign-job.module';
import { NotificationModule } from 'modules/modules/modules/notification/noitification.module';
import { PostVaccinationModule } from './modules/post-vaccination/post-vaccination.module';
import { HistoryModule } from 'modules/modules/modules/history/history.module';
import { SystemErrorModule } from 'modules/modules/modules/system-error/system-error.module';
import { AffiliateModule } from 'modules/modules/modules/affiliate/affiliate.module';
import { CartPricingModule } from 'modules/modules/modules/cart-pricing/cartPricing.module';
import { MultiDoseModule } from 'modules/modules/modules/multi-dose/multi-dose.module';
import { CashbackModule } from 'modules/modules/modules/cashback/cashback.module';
import { RankingModule } from '@libs/modules/ranking/ranking.module';
import { SpeakerModule } from 'modules/modules/modules/speaker/speaker.module';
import { MonitorStcRsa } from './modules/monitor-stc-rsa/monitor-stc-rsa.module';
import { RewardModule } from './modules/reward/reward.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validate: envValidator,
    }),
    WinstonModule.forRootAsync({
      useClass: WinstonLoggerConfigService,
    }),
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfigService,
    }),
    S3Module.forRootAsync({
      useClass: S3ConfigService,
    }),
    RedisModule.forRootAsync({
      useClass: RedisConfigService,
    }),
    ModulesModule,
    VersionsModule,
    JourneyModule,
    CustomersModule,
    CartsModule,
    CartPricingModule,
    AdministrativeModule,
    ConsultantsModule,
    UsersModule,
    RegimensModule,
    ProductsModule,
    PrintCenterModule,
    OrdersModule,
    PaymentModule,
    FamilyModule,
    QuestionModule,
    TicketModule,
    RoomsModule,
    SchedulesModule,
    CommonModule,
    ReasonsModule,
    DepositCancelModule,
    FilesModule,
    RabiesModule,
    LoyaltyModule,
    VouchersModule,
    HealthzModule.forRootAsync(['http-checker', 'typeOrm-checker']), // http-checker,typeOrm-checker, mongo-checker
    InvoiceModule,
    FtelModule,
    ShopsModule,
    SurveysModule,
    OsrModule,
    StocksModule,
    ReportModule,
    GameUtopModule,
    ServiceContractModule,
    EmailOtpModule,
    ESMModule,
    ShopManagementModule,
    NotesModule,
    AssignJobModule,
    NotificationModule,
    PostVaccinationModule,
    HistoryModule,
    SystemErrorModule,
    AffiliateModule,
    MultiDoseModule,
    CashbackModule,
    RankingModule,
    SpeakerModule,
    MonitorStcRsa,
    RewardModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useValue: new ValidationPipe({
        whitelist: false,
        skipMissingProperties: false,
        transform: true,
      }),
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TimeoutInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: BadRequestExceptionFilter,
    },
  ],
})
export class AppModule {}
